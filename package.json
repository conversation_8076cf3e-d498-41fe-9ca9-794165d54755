{"name": "hs-gxetc-issue-manage-service", "version": "4.4.0", "description": "A magical vue admin. An out-of-box UI solution for enterprise applications. Newest development stack of vue. Lots of awesome features", "author": "Pan <<EMAIL>>", "scripts": {"dev": "vue-cli-service serve", "lint": "eslint --ext .js,.vue src", "build:prod": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "clear": "rimraf node_modules"}, "dependencies": {"@antv/g2": "^4.0.15", "axios": "^0.21.4", "clipboard": "2.0.4", "codemirror": "^5.62.3", "core-js": "^2.6.11", "driver.js": "0.9.5", "dropzone": "5.5.1", "echarts": "^4.2.1", "el-tree-transfer": "^2.4.7", "element-ui": "^2.15.6", "file-saver": "2.0.1", "fuse.js": "3.4.4", "js-base64": "^3.7.2", "js-cookie": "2.2.0", "js-md5": "^0.7.3", "jsonlint": "1.6.3", "jszip": "^3.10.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "qrcodejs2": "0.0.2", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "swiper": "^6.5.0", "v-viewer": "^1.6.4", "vue": "^2.6.11", "vue-layer": "^1.2.5", "vue-quill-editor": "^3.0.6", "vue-router": "^3.1.6", "vuex": "^3.1.3"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.9.0", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.9.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "add-asset-html-webpack-plugin": "^3.1.3", "autoprefixer": "9.5.1", "babel-eslint": "^8.2.6", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-jest": "23.6.0", "babel-loader": "^8.0.0-beta.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-import": "^1.13.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "chalk": "2.4.2", "chokidar": "2.1.5", "clean-webpack-plugin": "^0.1.19", "connect": "3.6.6", "cross-env": "^5.2.1", "css-loader": "^2.1.0", "dotenv-flow-webpack": "^1.0.0", "echarts": "^4.9.0", "eslint": "^5.16.0", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.2.1", "eslint-plugin-vue": "^5.2.3", "file-loader": "^6.1.0", "happypack": "^5.0.1", "hard-source-webpack-plugin": "^0.13.1", "html-webpack-plugin": "^3.2.0", "husky": "1.3.1", "jsencrypt": "^3.2.0", "lint-staged": "8.1.5", "lodash": "^4.17.21", "mini-css-extract-plugin": "^0.9.0", "mockjs": "1.0.1-beta3", "moment": "^2.29.1", "node-sass": "^4.14.1", "open-browser-webpack-plugin": "^0.0.5", "optimize-css-assets-webpack-plugin": "^5.0.3", "plop": "^2.7.4", "pre-commit": "^1.2.2", "rimraf": "^3.0.2", "runjs": "4.3.2", "sass": "1.26.2", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "speed-measure-webpack-plugin": "^1.3.1", "style-loader": "^1.1.3", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "terser-webpack-plugin": "^3.0.1", "uglifyjs-webpack-plugin": "^2.2.0", "url-loader": "^4.0.0", "vue-loader": "^15.9.1", "vue-template-compiler": "^2.6.14", "webpack": "^4.43.0", "webpack-bundle-analyzer": "^3.6.1", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.10.3", "webpack-merge": "^4.2.2", "webpack-parallel-uglify-plugin": "^1.1.2", "compression-webpack-plugin": "6.1.0"}, "browserslist": ["> 1%", "last 2 versions"]}