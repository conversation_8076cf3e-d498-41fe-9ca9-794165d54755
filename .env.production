NODE_ENV = production
# just a flag
ENV = 'production'

# base api
VUE_APP_BASE_API = 'https://gateway.gxetc.com.cn'
VUE_APP_GATEWAY_API = 'https://gateway.gxetc.com.cn'
VUE_APP_UAA_API = 'https://gateway.gxetc.com.cn/uaa'
VUE_APP_UPMS_API="https://gateway.gxetc.com.cn/upms"

# test
#VUE_APP_BASE_API = 'https://micro-gateway.gxjettoll.cn:8443'
#VUE_APP_GATEWAY_API = 'https://micro-gateway.gxjettoll.cn:8443/devtools'
#VUE_APP_UAA_API = 'https://micro-gateway.gxjettoll.cn:8443/uaa'
#VUE_APP_UPMS_API="https://micro-gateway.gxjettoll.cn:8443/upms"

VUE_APP_CLINET_ID = 'hs-gxetc-issue-manage-service'
VUE_APP_CLINET_SECRET = ''