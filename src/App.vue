<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {}
  },
  created() {
    console.log(this.$store)
  }
}
</script>
<style lang="scss">
@import './icons/iconfont/iconfont.css';
@import '../src/styles/flex';
.wl-transfer .transfer-title {
  margin: 0;
}
.app-main {
  min-width: 1000px;
  background-color: #f0f2f5;
}

.top_btn {
  background: #fff;
  padding: 10px;
  position: relative;
  .title {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }
  .search {
    .search-list {
      position: relative;
      top: 5px;
      margin-right: 20px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      padding-left: 10px;
      .el-date-editor {
        height: 32px;
      }
      .el-range-separator,
      .el-input__icon {
        line-height: 24px;
      }
      .el-input,
      .el-select {
        width: 150px;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
        }
        .el-select__caret {
          line-height: 32px;
        }
      }
      .el-button--primary {
        background: #1890ff;
      }
      .conditions {
        margin-bottom: 10px;
        margin-right: 10px;
        .el-button--medium {
          height: 32px;
          line-height: 8px;
        }
      }
    }
  }
}

.table {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  ::v-deep thead {
    ::v-deep.cell {
      text-align: center;
    }
  }
  .el-table__row {
    .cell {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
    .el-button--text {
      font-size: 14px;
      color: #1890ff;
      &.is-disabled{
        color: #C0C4CC;
      }
    }
  }
  .pagination {
    text-align: center;
    ::v-deep.btn-prev,
    .btn-next {
      background-color: #fff !important;
    }
    ::v-deep.el-pager {
      li {
        background-color: #fff !important;
      }
    }
    .el-pager li:not(.disabled).active {
      background-color: #fff !important;
      color: #409eff !important;
      border: 1px solid #409eff !important;
    }
  }
}

.form {
  ::v-deepthead {
    ::v-deep.cell {
      text-align: center;
    }
  }
  .d-pagination {
    text-align: center;
    margin-top: 20px;
    ::v-deep.btn-prev,
    ::v-deep.btn-next {
      background-color: #fff;
    }
    ::v-deep.el-pager {
      li {
        background-color: #fff;
      }
    }
    .el-pager li:not(.disabled).active {
      background-color: #fff;
      color: #409eff;
      border: 1px solid #409eff;
    }
  }
}

.line-middle {
  width: 100%;
  height: 20px;
  background-color: #c9c9c9;
}
.el-table tbody tr {
  background-color: #fff !important;
}
.el-table tbody td {
  text-align: center;
}
.el-table th,
.el-table tr {
  background-color: #f5f7fa !important;
  text-align: center;
}

/**
  解决element-ui的table表格控件表头与内容列不对齐问题
 */
.el-table th.gutter {
  display: table-cell !important;
}
</style>
