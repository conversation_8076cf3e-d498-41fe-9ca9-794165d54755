import Vue from "vue"
import ElementUI from "element-ui"
import { Notification, MessageBox, Message } from 'element-ui'
console.log('Message===>>>>',Message)
import "element-ui/lib/theme-chalk/index.css"
import "@/styles/index.scss" // global css
import "normalize.css/normalize.css" // A modern alternative to CSS resets
import { loadding } from './utils/dialogUtils'
import App from "./App"
import store from "./store"
import router from "./router"
import interfaces from './api/interfaces/index';
import permission from './directive/permission'
import request from "@/utils/request"
import "@/icons" // icon
import "@/permission" // permission control
import { setToken } from "@/utils/auth"
import Viewer from 'v-viewer'
import 'viewerjs/dist/viewer.css'
import MyPlugin from '@/utils/print.js';
import axios from 'axios'
import transferDom from './directive/transfer-dom';
import VueQuillEditor from 'vue-quill-editor'
// 引入样式
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
// 引入vue-layer
import layer from 'vue-layer'
import 'vue-layer/lib/vue-layer.css';

import global from '@/utils/global'

Vue.prototype.$layer = layer(Vue);
Vue.directive('transfer-dom', transferDom);
Vue.prototype.$axios = axios
Vue.use(MyPlugin);
Vue.use(global);
// set ElementUI lang to EN
Vue.use(ElementUI)
Vue.use(permission)
Vue.use(VueQuillEditor, /* { 默认全局 } */)
Vue.use(Viewer)
Viewer.setDefaults({
    Options: { 'inline': true, 'button': true, 'navbar': true, 'title': true, 'toolbar': true, 'tooltip': true, 'movable': true, 'zoomable': true, 'rotatable': true, 'scalable': true, 'transition': true, 'fullscreen': true, 'keyboard': true, 'url': 'data-source' }
})
Vue.config.productionTip = false
// 全局组件挂载

Vue.prototype.msgSuccess = function (msg) {
    this.$message({ showClose: true, message: msg, type: "success" })
}

Vue.prototype.msgError = function (msg) {
    this.$message({ showClose: true, message: msg, type: "error" })
}
Vue.prototype.$request = request;
Vue.prototype.$interfaces = interfaces;
console.log(interfaces);
Vue.prototype.msgInfo = function (msg) {
    this.$message.info(msg)
}
Vue.config.productionTip = false
Vue.mixin(loadding)
function render () {
    new Vue({
        router,
        store,
        render: h => h(App)
    }).$mount("#app")
}

export async function bootstrap (props) {
    console.log("one bootstrap")
}

export async function mount (props) {
    console.log("micro-apps", props)

    Vue.prototype.$appName = props.name // 子应用名称
    if (props.props) {
        // 主应用传递的参数，使用此函数设置到prototype中
        Object.keys(props.props).forEach(i => {
            Vue.prototype[`$${i}`] = props.props[i]
        })
    }

    if (Vue.prototype.$shareStore) {
        // 动态注册共享store
        // store.registerModule('share')
        Object.keys(Vue.prototype.$shareStore).forEach(key => {
            store.registerModule(
                [`shareStore-${key}`],
                Vue.prototype.$shareStore[key]
            )
        })
    }

    // 通讯设置
    Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange
    Vue.prototype.$setGlobalState = props.setGlobalState

    props.onGlobalStateChange((state, prev) => {
        console.log("子应用侦听到参数", state, prev)
        Vue.prototype.$qkState = state
        // setToken(state.user.token)

        if (state.sideBar) {
            store.dispatch("app/openSideBar", { withoutAnimation: false })
        } else {
            store.dispatch("app/closeSideBar", { withoutAnimation: false })
        }
    }, true)

    setToken(props.user.token)
    render()
}

export async function unmount () {
    console.log("one unmount")
}

if (!window.__POWERED_BY_QIANKUN__) {
    render()
}

// webpack进行热更新
if (module.hot) {
    module.hot.accept()
}
const Bus = new Vue();
Vue.prototype.$bus = Bus;
