import store from '@/store'

export default {
  inserted(el, binding, vnode) {
    const { value } = binding
    // const all_permission = '*:*:*'
    const permissions = store.getters && store.getters.permissions
    if (value && value instanceof Array && value.length > 0) {
      console.log('permissions', permissions, value)
      let hasPermissions = false // 本地开放全部权限，默认应为false
      for (let i of value) {
        if (permissions.includes(i)) {
          console.log('permissions.includes(i)', i, permissions.includes(i))
          hasPermissions = true
          return
        }
      }

      console.log('检查权限', hasPermissions)
      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
