<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    key() {
      return this.$route.path
    },
    cachedViews() {
      const ary = []
      const tag = this.$store.state.tagsView.cachedViews
      for (const t of tag) {
        const str = t
          .split("/")
          [t.split("/").length - 1].replace(/( |^)[a-z]/g, L => L.toUpperCase())
        ary.push(str)
      }
      return ary
    }
  }
}
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  /* min-height: 100vh; */
  height: calc(100vh - 60px) !important;
  width: 100%;
  position: relative;
  overflow-y:scroll;
}
.fixed-header + .app-main {
  padding-top: 84px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
