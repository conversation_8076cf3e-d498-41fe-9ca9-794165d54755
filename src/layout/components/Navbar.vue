<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened"
               class="hamburger-container"
               @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

   <!--  <div class="right-menu">
      <span class="name">{{ name }}</span>
      <el-dropdown class="avatar-container"
                   trigger="click">
        <div class="avatar-wrapper">
          <div class="user-avatar">{{ name }}</div>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown"
                          class="user-dropdown">
          <router-link to="/">
            <el-dropdown-item>
              首页
            </el-dropdown-item>
          </router-link>
        </el-dropdown-menu>
      </el-dropdown>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from "vuex"
import Breadcrumb from "@/components/Breadcrumb"
import Hamburger from "@/components/Hamburger"
import ChangePasd from "@/views/changePassword/index"
export default {
  components: {
    Breadcrumb,
    <PERSON>er,
    ChangePasd,
  },
  computed: {
    ...mapGetters(["sidebar", "avatar", "name"]),
  },
  data() {
    return {
      dialogFormVisible: false,
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar")
    },
    logOut() {
      this.$store.dispatch("user/loginOut").then(() => {
        this.$store.dispatch("tagsView/delAllViews", [])
        this.$router.push(`/login?redirect=dashboard`)
      })
    },
    changePassword() {
      // this.$router.push(`/changePassword`)
      this.dialogFormVisible = true
    },
    comfirm() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    .name {
      display: inline-block;
      position: relative;
      top: -14px;
      font-size: 14px;
      color: #606266;
      margin-right: 5px;
    }
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 50%;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }

  .changePasd-dialog {
    ::v-deep.el-dialog {
      width: 500px;
    }
  }
}
</style>
