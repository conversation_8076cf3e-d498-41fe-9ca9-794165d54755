import request from "@/utils/uaaRequest"

// 获取验证码
export function getCodeImg() {
  return request({
    url: "captcha",
    method: "get"
  })
}

import { JSEncrypt } from "jsencrypt"

export function login(userinfo) {
  return getKey().then(res => {
    console.log("获取的key", res)
    if (res.code !== 200) {
      // reject()
      return
    }

    var encrypt = new JSEncrypt()
    encrypt.setPublicKey(res.data.publicKey)
    const pwd = encrypt.encrypt(userinfo.password)
    console.log("加密后的信息", pwd)
    const data = {}
    data.username = userinfo.username
    data.password = pwd
    data.keyId = res.data.keyId
    data.uuid = userinfo.uuid
    data.code = userinfo.code

    var params = {
      grantType: "password"
    }

    return request({
      url: "oauth/token",
      method: "POST",
      data,
      // headers: {
      //   'Content-Type': 'application/x-www-form-urlencoded'
      // },
      auth: {
        username: process.env.VUE_APP_CLINET_ID,
        password: process.env.VUE_APP_CLINET_SECRET
      },
      requestType: "login"
    })
  })
}

// 获取后台加密公钥key
export function getKey() {
  return request({
    url: "key",
    method: "GET",
    auth: {
      // username: 'etc-online-admin',
      // password: '123456'
      username: process.env.VUE_APP_CLINET_ID,
      password: process.env.VUE_APP_CLINET_SECRET
    },
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    }
  })
}

export function refreshtoken(data) {
  return request({
    url: "refreshtoken",
    method: "post",
    data
  })
}

export function logout() {
  return request({
    url: "logout",
    method: "post"
  })
}
