import request from "@/utils/request"
import api from "@/api/index"
import qs from 'querystring'
export function list(data) {
    return request({
        url: api.list,
        method: "post",
        data
    })
}

export function adopt(data) {
    return request({
        url: api.adopt,
        method: "post",
        data
    })
}

export function toreject(data) {
    return request({
        url: api.reject,
        method: "post",
        data
    })
}

export function appDet(data) {
    return request({
        url: api.appDet,
        method: "post",
        data
    })
}

export function repDet(data) {
    return request({
        url: api.repDet,
        method: "post",
        data
    })
}
export function record(data) {
    return request({
        url: api.record,
        method: "post",
        data
    })
}
export function toDoOrderList(data) {
    return request({
        url: api.toDoOrderList,
        method: "post",
        data
    })
}
export function orderDetail(data) {
    return request({
        url: api.orderDetail,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function findById(data) {
    return request({
        url: api.findById,
        method: 'post',
        data
        // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function todelete(data) {
    return request({
        url: api.todelete,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function confirmStatus(data) {
    return request({
        url: api.confirmStatus,
        method: "post",
        data
    })
}

export function explain(data) {
    return request({
        url: api.explain,
        method: "post",
        data
    })
}
export function appealQuery(data) {
    return request({
        url: api.appealQuery,
        method: "post",
        data
    })
}

