import request from '@/utils/request'
import upmsRequest from '@/utils/upmsRequest'

export function getChannel (data) {
  return request({
    url: '/dict/channel',
    method: 'post',
    data
  })
}

export function getSupportBank(data) {
  return request({
      url: '/bankCard/supportBank',
      method: 'post',
      data
  })
}

// 字典接口
export function getDict(data) {
  return request({
      url: '/dict',
      method: 'post',
      data
  })
}

// 报表盖章并下载api
export function staticStampDownload (data) {
  return request({
      url: 'report/staticStampDownload',
      method: 'post',
      responseType: 'blob',
      data
  })
}


