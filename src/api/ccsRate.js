import request from '@/utils/request'

//服务费查询
export function serviceRateQuery (data) {
  return request({
    url: '/silkyRate/serviceRateQuery',
    method: 'post',
    data
  })
}

//服务费新增
export function serviceRateApply (data) {
  return request({
    url: '/silkyRate/serviceRateApply',
    method: 'post',
    data
  })
}

//服务费失效
export function serviceRateDelete (data) {
  return request({
    url: '/silkyRate/serviceRateDelete',
    method: 'post',
    data
  })
}

//通道费查询
export function passageRateQuery (data) {
  return request({
    url: '/silkyRate/passageRateQuery',
    method: 'post',
    data
  })
}

//通道费新增
export function passageRateApply (data) {
  return request({
    url: '/silkyRate/passageRateApply',
    method: 'post',
    data
  })
}

//通道费失效
export function passageRateDelete (data) {
  return request({
    url: '/silkyRate/passageRateDelete',
    method: 'post',
    data
  })
}

//滞纳金查询
export function lateRateList (data) {
  return request({
    url: '/lateRate/list',
    method: 'post',
    data
  })
}

//滞纳金新增
export function lateRateConfig (data) {
  return request({
    url: '/lateRate/config',
    method: 'post',
    data
  })
}

//滞纳金新增
export function lateRateVitiation (data) {
  return request({
    url: '/lateRate/vitiation',
    method: 'post',
    data
  })
}

//滞纳金调差
export function manualLateFeeApi (data) {
  return request({
    url: '/deductions/manualLateFee',
    method: 'post',
    data
  })
}
