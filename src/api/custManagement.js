import request from '@/utils/request'
import api from '@/api/index'

export function advancePayment(data) {
    return request({
        url: api.advancePayment,
        method: 'post',
        data
    })
}
// export function importPayment(data) {
//     return request({
//         url: api.importPayment,
//         method: 'post',
//         data
//     })
// }
export function importPayment(data) {
    return request({
        url: api.importPayment,
        method: 'post',
        data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}
export function saveAdvance(data) {
    return request({
        url: api.saveAdvance,
        method: 'post',
        data
    })
}
export function searchRechargeList(data) {
    return request({
        url: api.searchRechargeList,
        method: 'post',
        data
    })
}
export function transfeConfirm(data) {
    return request({
        url: api.transfeConfirm,
        method: 'post',
        data
    })
}

export function transfeDelete(data) {
    return request({
        url: api.transfeDelete,
        method: 'post',
        data
    })
}
export function transfeCancel(data) {
    return request({
        url: api.transfeCancel,
        method: 'post',
        data
    })
}
export function getNetAccountList(data) {
    return request({
        url: api.getNetAccountList,
        method: 'post',
        data
    })
}
export function getSumAvailableAmount(data) {
    return request({
        url: api.getSumAvailableAmount,
        method: 'post',
        data
    })
}
export function transferRefound(data) {
    return request({
        url: api.transferRefound,
        method: 'post',
        data
    })
}
export function transferRecharge(data) {
    return request({
        url: api.transferRecharge,
        method: 'post',
        data
    })
}

export function b2bicList(data) {
    return request({
        url: api.b2bicList,
        method: 'post',
        data
    })
}

export function b2bFlow(data) {
    return request({
        url: api.b2bFlow,
        method: 'post',
        data
    })
}

export function b2bView(data) {
    return request({
        url: api.b2bView,
        method: 'post',
        data
    })
}
export function transferRevoke(data) {
    return request({
        url: api.transferRevoke,
        method: 'post',
        data
    })
}