import request from '@/utils/request'

// 假证筛查稽核

// 稽核任务结果列表
export function falseCertificateResultList(data) {
    return request({
        url: '/forgedDoc/audit/resultList',
        method: 'post',
        data,
    })
}
// 车辆稽核
export function falseCertificateVehicleCheck(data) {
    return request({
        url: '/forgedDoc/audit/vehicleCheck',
        method: 'post',
        data,
    })
}
// 稽核结果车辆列表
export function falseCertificateVehicleList(data) {
    return request({
        url: '/forgedDoc/audit/vehicleList',
        method: 'post',
        data,
    })
}
// 稽核结果车辆结果导出
export function falseCertificateResultExport(data) {
    return request({
        url: '/forgedDoc/audit/resultExport',
        method: 'post',
        responseType: 'blob',
        data,
    })
}
// 车辆详情图片列表
export function falseCertificateSearch(data) {
    return request({
        url: '/archives/search',
        method: 'post',
        data,
    })
}

//厂商列表
export function factoryList(data) {
  return request({
      url: '/manufacturerMast/list',
      method: 'post',
      data
  })
}

//添加厂商
export function addFactory(data) {
  return request({
      url: '/manufacturerMast/edit',
      method: 'post',
      data
  })
}

//删除厂商
export function delFactory(data) {
  return request({
      url: '/manufacturerMast/batchDelete',
      method: 'post',
      data
  })
}

//apk列表
export function apkList(data) {
  return request({
      url: '/manufacturerApk/list',
      method: 'post',
      data
  })
}

//添加/编辑 apk
export function addApk(data) {
  return request({
      url: '/manufacturerApk/edit',
      method: 'post',
      data
  })
}

//删除apk
export function delApk(data) {
  return request({
      url: '/manufacturerApk/batchDelete',
      method: 'post',
      data
  })
}


//pos设备列表
export function posList(data) {
  return request({
      url: '/etcBusinessInterface/terminal/list',
      method: 'post',
      data
  })
}

//添加/编辑 pos设备
export function addPos(data) {
  return request({
      url: '/etcBusinessInterface/terminal/edit',
      method: 'post',
      data
  })
}

//删除pos设备
export function delPos(data) {
  return request({
      url: '/etcBusinessInterface/terminal/batchDelete',
      method: 'post',
      data
  })
}

//pos设备详情
export function posDetail(data) {
  return request({
      url: '/etcBusinessInterface/terminal/detail',
      method: 'post',
      data
  })
}

// 字典
export function posDict(data) {
  return request({
      url: '/etcBusinessInterface/terminal/dicList',
      method: 'post',
      data
  })
}

//卡帐信息查询
export function getCardAccountPage(data) {
  return request({
      url: '/cardAccount/getCardAccountPage',
      method: 'post',
      data
  })
}

//卡账信息查询-导出
export function caExport(data) {
  return request({
      url: '/cardAccount/caExport',
      method: 'post',
      responseType: 'blob',
      data
  })
}

//卡账充值信息查询
export function getCardAccountRechargePage(data) {
  return request({
      url: '/cardAccount/getCardAccountRechargePage',
      method: 'post',
      data
  })
}

//卡账充值信息-导出
export function carExport(data) {
  return request({
      url: '/cardAccount/carExport',
      method: 'post',
      responseType: 'blob',
      data
  })
}


//绑定卡提醒记录查询
export function getCardBindingWarn(data) {
  return request({
      url: '/cardAccount/getCardBindingWarn',
      method: 'post',
      data
  })
}

//绑定卡访问记录查询
export function getCardBindingAsk(data) {
  return request({
      url: '/cardAccount/getCardBindingAsk',
      method: 'post',
      data
  })
}

//操作员查询
export function getEtcUserPage(data) {
  return request({
      url: '/cardAccount/getEtcUserPage',
      method: 'post',
      data
  })
}

/**
 * 日日通异常数据相关业务
 */

// 列表查询
export function queryList(data) {
  return request({
      url: '/ddpErrorData/queryList',
      method: 'post',
      data
  })
}

// 详情查询
export function queryDdpErrorData(data) {
  return request({
      url: '/ddpErrorData/queryDdpErrorData',
      method: 'post',
      data
  })
}

// 上传文件
export function saveFile(data) {
  return request({
      headers: { 'Content-Type': 'multipart/form-data' },
      url: '/ddpErrorData/saveFile',
      method: 'post',
      data,
  })
}

/**
 * 卡面金额回补相关业务
 */

//卡面金额回补查询
export function getCardAmounList(data) {
  return request({
      url: '/cardAccount/getCardAmountReturn',
      method: 'post',
      data
  })
}

//卡面金额回补审核
export function cardAmountReturnCheck(data) {
  return request({
      url: '/cardAccount/cardAmountReturnCheck',
      method: 'post',
      data
  })
}

//全部归集生成待扣款记录
export function cardAmountReturnSum(data) {
  return request({
      url: '/cardAccount/cardAmountReturnSum',
      method: 'post',
      data
  })
}

//批量归集生成待扣款记录
export function cardAmountReturnSumCheck(data) {
  return request({
      url: '/cardAccount/cardAmountReturnSumCheck',
      method: 'post',
      data
  })
}

//查询是否有待归集的记录
export function cardAmountReturnCount(data) {
  return request({
      url: '/cardAccount/cardAmountReturnCount',
      method: 'post',
      data
  })
}

/**
 * 稽核相关业务
 */

//稽核任务列表
export function queryTaskList(data) {
  return request({
      url: '/issueAudit/taskList',
      method: 'post',
      data
  })
}

//创建稽核任务
export function createAuditTask(data) {
  return request({
      url: '/issueAudit/createTask',
      method: 'post',
      data
  })
}

//专项稽核
export function auditSpecial(data) {
  return request({
      url: '/issueAudit/auditSpecial',
      method: 'post',
      data
  })
}

//生成工单
export function createWorkOrder(data) {
  return request({
      url: '/issueAudit/workOrder',
      method: 'post',
      data
  })
}

//批量导入表样下载
export function downloadAuidtExcel(data) {
  return request({
      url: '/issueAudit/download',
      method: 'post',
      responseType: 'blob',
      data
  })
}

//稽核结果修正批量导入
export function correctAudit(data) {
  return request({
      url: '/issueAudit/correctAudit',
      method: 'post',
      data
  })
}

//专项稽核批量导入
export function auditBatch(data) {
  return request({
      url: '/issueAudit/auditBatch',
      method: 'post',
      data
  })
}

//稽核结果列表
export function queryResultList(data) {
  return request({
      url: '/issueAudit/resultList',
      method: 'post',
      data
  })
}

//车辆稽核
export function vehicleCheck(data) {
  return request({
      url: '/issueAudit/vehicleCheck',
      method: 'post',
      data
  })
}

//复核
export function resultCheck(data) {
  return request({
      url: '/issueAudit/resultCheck',
      method: 'post',
      data
  })
}

//复核驳回
export function resultReject(data) {
  return request({
      url: '/issueAudit/resultReject',
      method: 'post',
      data
  })
}

//错误原因编辑
export function errorEdit(data) {
  return request({
      url: '/issueAudit/errorEdit',
      method: 'post',
      data
  })
}

//任务导出
export function resultExport(data) {
  return request({
      url: '/issueAudit/resultExport',
      method: 'post',
      responseType: 'blob',
      data
  })
}

//ETC车辆图片查询
export function vehicleImage(data) {
  return request({
      url: '/issueAudit/vehicleImage',
      method: 'post',
      data
  })
}

//车辆通行图片查询
export function passImage(data) {
  return request({
      url: '/issueAudit/passImage',
      method: 'post',
      responseType: 'blob',
      data
  })
}

//ETC车辆导出
export function vehicleExport(data) {
  return request({
      url: '/issueAudit/vehicleExport',
      method: 'post',
      responseType: 'blob',
      data
  })
}

//整改处理
export function rectifyHandle(data) {
  return request({
      url: '/issueAudit/rectifyHandle',
      method: 'post',
      data
  })
}

// 重新扫描历史实收账单
export function selectBillTask(data) {
  return request({
      url: '/deductions/selectBillTask',
      method: 'post',
      data
  })
}

// 重新扫描历史实收账单执行操作
export function reHandleBill(data) {
  return request({
      url: '/deductions/reHandleBill',
      method: 'post',
      data
  })
}

//拉黑文件模版导出
export function balckTempExport(params) {
  return request({
      url: '/retrans/downloadTemplate',
      method: 'get',
      responseType: 'blob',
      params
  })
}

//卡号不存在的争议流水导入文件模版
export function preDownloadTemplate(params) {
  return request({
      url: '/preDedDisputeManual/downloadTemplate',
      method: 'get',
      responseType: 'blob',
      params
  })
}