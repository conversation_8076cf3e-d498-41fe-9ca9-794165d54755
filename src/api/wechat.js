import request from '@/utils/request'
import api from '@/api/index'
import qs from 'querystring'

//查询素材列表
export function materialQuery(data) {
    return request({
        url: api.materialQuery,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
// 获取素材总数
export function getMaterialCount(data) {
    return request({
        url: api.getMaterialCount,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
// //添加其他类型永久素材
// export function getMaterialCount(data) {
//     return request({
//         url: api.getMaterialCount,
//         method: 'post',
//         data: qs.stringify(data),
//         headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
//     })
// }
// 分页获取图文素材列表
export function getMaterialNewsBatchGet(data) {
    return request({
        url: api.getMaterialNewsBatchGet,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
// 获取图文永久素材
export function getMaterialNewsInfo(data) {
    return request({
        url: api.getMaterialNewsInfo,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
// // 添加图文永久素材
// export function getMaterialCount(data) {
//     return request({
//         url: api.getMaterialCount,
//         method: 'post',
//         data: qs.stringify(data),
//         headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
//     })
// }
// // 添加图文临时素材
// export function getMaterialCount(data) {
//     return request({
//         url: api.getMaterialCount,
//         method: 'post',
//         data: qs.stringify(data),
//         headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
//     })
// }

//二维码列表
export function qrList(data) {
    return request({
        url: '/applet/qr/list',
        method: 'post',
        data
    })
}

//添加二维码
export function addCode(data) {
    return request({
        url: '/applet/qr/add',
        method: 'post',
        data
    })
}

//删除二维码
export function delCode(data) {
    return request({
        url: '/applet/qr/delete',
        method: 'post',
        data
    })
}

//链接列表
export function linkList(data) {
    return request({
        url: '/applet/link/list',
        method: 'post',
        data
    })
}

//添加链接
export function addLink(data) {
    return request({
        url: '/applet/link/add',
        method: 'post',
        data
    })
}

//删除链接
export function delLink(data) {
    return request({
        url: '/applet/link/delete',
        method: 'post',
        data
    })
}
