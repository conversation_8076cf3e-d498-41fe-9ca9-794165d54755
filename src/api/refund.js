import request from '@/utils/request'
import api from '@/api/index'
import qs from 'querystring'

export function setTag(data) {
    return request({
        url: api.setTag,
        method: 'post',
        data,
    })
}

export function getTollConfirm(data) {
    return request({
        url: api.getTollConfirm,
        method: 'post',
        data,
        timeout: 5 * 60 * 1000 //设置超时时间5分钟
    })
}

export function tollConfirmExport(data) {
    return request({
        url: api.tollConfirmExport,
        method: 'post',
        data,
        timeout: 5 * 60 * 1000 //设置超时时间5分钟
    })
}

export function tollConfirm(data) {
    return request({
        url: api.tollConfirm,
        method: 'post',
        data,
    })
}

export function makeTable(data) {
    return request({
        url: api.makeTable,
        method: 'post',
        data,
        timeout: 5 * 60 * 1000 //设置超时时间5分钟
    })
}

export function getTable(data) {
    return request({
        url: api.getTable,
        method: 'post',
        data
    })
}

export function editCardInfo(data) {
    return request({
        url: api.editCardInfo,
        method: 'post',
        data
    })
}

export function issuerAudit(data) {
    return request({
        url: api.issuerAudit,
        method: 'post',
        data
    })
}

export function branchAudit(data) {
    return request({
        url: api.branchAudit,
        method: 'post',
        data
    })
}

export function operationAudit(data) {
    return request({
        url: api.operationAudit,
        method: 'post',
        data
    })
}

export function getDetailImgs(data) {
    return request({
        url: api.getDetailImgs,
        method: 'post',
        data
    })
}

export function getLogoutDetail(data) {
    return request({
        url: api.getLogoutDetail,
        method: 'post',
        data
    })
}

export function getLogoutRefundList(data) {
    return request({
        url: api.getLogoutRefundList,
        method: 'post',
        data
    })
}

export function refundOfflineSearch(data) {
    return request({
        url: api.refundOfflineSearch,
        method: 'post',
        data
    })
}

export function getTollRecordList(data) {
    return request({
        url: api.getTollRecordList,
        method: 'post',
        data
    })
}

export function refundOfflineEdit(data) {
    return request({
        url: api.refundOfflineEdit,
        method: 'post',
        data
    })
}

export function refundOfflineListById(data) {
    return request({
        url: api.refundOfflineListById + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

export function refundResultImport(data) {
    return request({
        url: api.refundResultImport,
        method: 'post',
        data,
        headers: { 'Content-Type': 'multipart/form-data' }
    })
}

export function refundDelete(data) {
    return request({
        url: api.refundDelete,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function getOrgView() {
    return request({
        url: api.getOrgView,
        method: 'post'
    })
}
export function searchOrg(data) {
    return request({
        url: api.searchOrg,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function orgEdit(data) {
    return request({
        url: api.orgEdit,
        method: 'post',
        data
    })
}
export function orgDelete(data) {
    return request({
        url: api.orgDelete,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function refundConfigOrg(data) {
    return request({
        url: api.refundConfigOrg + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function artificialSearch(data) {
    return request({
        url: api.artificialSearch,
        method: 'post',
        data
    })
}
export function addOrUpdateConfirm(data) {
    return request({
        url: api.addOrUpdateConfirm,
        method: 'post',
        data
    })
}
export function artificialDelete(data) {
    return request({
        url: api.artificialDelete,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function showAddOrUpdateDialog(data) {
    return request({
        url: api.showAddOrUpdateDialog + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

export function getRefundOrder(data) {
    return request({
        url: api.getRefundOrder,
        method: 'post',
        data
    })
}
export function refundOrderPreInitAudit(data) {
    return request({
        url: api.refundOrderPreInitAudit,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function refundOrderUpdate(data) {
    return request({
        url: api.refundOrderUpdate,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function refundOrderCancel(data) {
    return request({
        url: api.refundOrderCancel,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function refundOrderData(data) {
    return request({
        url: api.refundOrderData + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function getTypeList() {
    return request({
        url: api.getTypeList,
        method: 'post'
    })
}
export function getChannelType() {
    return request({
        url: api.getChannelType,
        method: 'post'
    })
}
export function getRefundComplete(data) {
    return request({
        url: api.getRefundComplete,
        method: 'post',
        data
    })
}
export function addRefundComplete(data) {
    return request({
        url: api.addRefundComplete,
        method: 'post',
        data
    })
}
export function refundCompleteRecord(data) {
    return request({
        url: api.refundCompleteRecord + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function updateRefundComplete(data) {
    return request({
        url: api.updateRefundComplete,
        method: 'post',
        data
    })
}
export function refundCompleteCancel(data) {
    return request({
        url: api.refundCompleteCancel + '/' + data.id,
        method: 'post',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}
export function manualRefundExport(data) {
    return request({
        url: api.manualRefundExport,
        method: 'post',
        data: qs.stringify(data),
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    })
}

// 退费订单导出
export function refundExport(data) {
    return request({
        url: '/refund/refundExport',
        method: 'post',
        responseType: 'blob',
        data
    })
}

// 产品转换失败导出
export function converExport(data) {
    return request({
        url: '/transform/export',
        method: 'post',
        responseType: 'blob',
        data
    })
}

//退回待制表节点
export function tableReturn(data) {
    return request({
        url: '/refund/tableReturn',
        method: 'post',
        data
    })
}
