import request from '@/utils/request'

//用户重传查询
export function custView (data) {
  return request({
    url: '/retrans/custView',
    method: 'post',
    data
  })
}

//用户重传
export function custUpload (data) {
  return request({
    url: '/retrans/custUpload',
    method: 'post',
    data
  })
}

//卡重传查询
export function cardView (data) {
  return request({
    url: '/retrans/cardView',
    method: 'post',
    data
  })
}

//卡重传
export function cardUpload (data) {
  return request({
    url: '/retrans/cardUpload',
    method: 'post',
    data
  })
}

//obu重传查询
export function obuView (data) {
  return request({
    url: '/retrans/obuView',
    method: 'post',
    data
  })
}

//obu重传
export function oubUpload (data) {
  return request({
    url: '/retrans/oubUpload',
    method: 'post',
    data
  })
}

//卡片黑白名单信息查询
export function blackView (data) {
  return request({
    url: '/retrans/blackView',
    method: 'post',
    data
  })
}
//卡片黑白名状态重传
export function cardBlack (data) {
  return request({
    url: '/retrans/cardBlack',
    method: 'post',
    data
  })
}

//查询发卡待确认列表
export function issueNotCompleteList (data) {
  return request({
    url: '/cardBiz/issueNotCompleteList',
    method: 'post',
    data
  })
}

//手动发卡确认
export function issueCardRepush (data) {
  return request({
    url: '/cardBiz/issueCardRepush',
    method: 'post',
    data
  })
}

//获取报表周期
export function getCycle (data) {
  return request({
    url: '/jhOverdue/cycle',
    method: 'post',
    data
  })
}

//导出模板
export function downloadTemplate (data) {
  return request({
    url: '/addressCheck/downloadTemplate',
    method: 'get',
    responseType: 'blob',
    data
  })
}

//上传文件数据
export function uploadTemplateData (data) {
  return request({
    url: '/addressCheck/uploadTemplateData',
    method: 'post',
    data
  })
}

//查询历史导入记录
export function chekTemplateList (data) {
  return request({
    url: '/addressCheck/job',
    method: 'post',
    data
  })
}

