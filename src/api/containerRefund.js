import request from '@/utils/request'
import api from '@/api/index'
import qs from 'querystring'
export function getContainerRefundList(data) {
  return request({
    url: api.getContainerRefundList,
    method: 'post',
    data,
    timeout: 3 * 60 * 1000 //设置超时时间3分钟
  })
}

export function getContainerRefundAuditDetailList(data) {
  return request({
    url: api.getContainerRefundAuditDetailList,
    method: 'post',
    data,
  })
}

export function containerRefundOpratorDetail(data) {
  return request({
    url: api.containerRefundOpratorDetail,
    method: 'post',
    data,
  })
}

export function getPassRecordDetail(data) {
  return request({
    url: api.getPassRecordDetail,
    method: 'post',
    data,
  })
}

export function containerRefundPass(data) {
  return request({
    url: api.containerRefundPass,
    method: 'post',
    data,
  })
}

export function containerRefundRefuse(data) {
  return request({
    url: api.containerRefundRefuse,
    method: 'post',
    data,
  })
}

export function containerRefundNoticeSearch(data) {
  return request({
    url: api.containerRefundNoticeSearch,
    method: 'post',
    data
  })
}

export function reSendNotice(data) {
  return request({
    url: api.reSendNotice,
    method: 'post',
    data,
  })
}

export function refundReport(data) {
  return request({
    url: api.refundReport,
    method: 'post',
    data
  })
}