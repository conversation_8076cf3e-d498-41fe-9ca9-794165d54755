import request from '@/utils/request'
import api from '@/api/index'
import qs from 'querystring'
export function exportSearch(data) {
  console.log('url', api.exportSearch + data)
  return request({
    url: api.exportSearch,
    method: 'get',
    // data,
  })
}
export function transferDetail(data) {
  return request({
    url: api.transferDetail,
    method: 'post',
    data,
  })
}
export function singleTransfer(data) {
  return request({
    url: api.singleTransfer,
    method: 'post',
    data,
  })
}
export function singleReSend(data) {
  return request({
    url: api.singleReSend,
    method: 'post',
    data,
  })
}
export function getPassDetail(data) {
  return request({
    url: api.getPassDetail,
    method: 'post',
    data,
  })
}
export function getDeductionsPass(data) {
  return request({
    url: api.getDeductionsPass,
    method: 'post',
    data,
  })
}
export function getDeductionsList(data) {
  return request({
    url: api.getDeductionsList,
    method: 'post',
    data,
  })
}
export function deductionsInit(data) {
  return request({
    url: api.deductionsInit,
    method: 'post',
    data,
  })
}
export function transExport(data) {
  return request({
    url: api.transExport,
    method: 'post',
    data,
  })
}
export function deductionExport(data) {
  return request({
    url: api.deductionExport,
    method: 'post',
    data,
  })
}
export function batchTransfer(data) {
  return request({
    url: api.batchTransfer,
    method: 'post',
    data,
  })
}
export function batchReSend(data) {
  return request({
    url: api.batchReSend,
    method: 'post',
    data,
  })
}
export function imports(data) {
  return request({
    url: api.imports,
    method: 'post',
    data,
    headers: { 'Content-Type': 'multipart/form-data' },
    timeout: 1 * 60 * 1000 //设置超时时间1分钟
  })
}
export function importsAuditSearch(data) {
  return request({
    url: api.importsAuditSearch,
    method: 'post',
    data,
  })
}
export function importAuditVersions(data) {
  return request({
    url: api.importAuditVersions,
    method: 'post',
    data,
  })
}
export function checkAuditCancelAuditDetailInfoApply(data) {
  return request({
    url: api.checkAuditCancelAuditDetailInfoApply,
    method: 'post',
    data,
  })
}
export function importAuditRefuse(data) {
  return request({
    url: api.importAuditRefuse,
    method: 'post',
    data,
  })
}
export function cancelAuditApplyRefuse(data) {
  return request({
    url: api.cancelAuditApplyRefuse,
    method: 'post',
    data,
  })
}
export function checkCancelAuditHandleState(data) {
  return request({
    url: api.checkCancelAuditHandleState,
    method: 'post',
    data,
  })
}
export function importAuditPass(data) {
  return request({
    url: api.importAuditPass,
    method: 'post',
    data,
  })
}
export function cancelAuditApplyPass(data) {
  return request({
    url: api.cancelAuditApplyPass,
    method: 'post',
    data,
  })
}
export function cancelAuditApply(data) {
  return request({
    url: api.cancelAuditApply,
    method: 'post',
    data,
  })
}
export function checkAuditHandleState(data) {
  return request({
    url: api.checkAuditHandleState,
    method: 'post',
    data,
  })
}
export function auditRefuse(data) {
  return request({
    url: api.auditRefuse,
    method: 'post',
    data,
  })
}
export function auditBlackSearch(data) {
  return request({
    url: api.auditBlackSearch,
    method: 'post',
    data,
  })
}
export function dictionaries(data) {
  return request({
    url: api.dictionaries,
    method: 'post',
    data,
  })
}
export function auditBlackDetailInfo(data) {
  return request({
    url: api.auditBlackDetailInfo,
    method: 'post',
    data,
  })
}
export function auditPass(data) {
  return request({
    url: api.auditPass,
    method: 'post',
    data,
  })
}
export function hsAuditDisputefindById(data) {
  return request({
      url: api.hsAuditDisputefindById,
      method: 'post',
      data
      // data: qs.stringify(data),
      // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}
export function hsAuditDisputedetail(data) {
  return request({
      url: api.hsAuditDisputedetail,
      method: 'post',
      data
      // data: qs.stringify(data),
      // headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
  })
}

// 取消绑定
export function unbindCard (data) {
  return request({
    url: '/httpInterface/bindingCardInterface/unbind',
    method: 'post',
    data
  })
}

// 注销车辆重传
export function cancelVehiclePost (data) {
  return request({
    url: '/cancelVehicle/resume',
    method: 'post',
    data
  })
}