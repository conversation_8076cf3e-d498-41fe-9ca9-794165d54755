import request from '@/utils/request'
import api from '@/api/index'
export function getPrePayCardList(data) {
    return request({
        url: api.getPrePayCardList,
        method: 'post',
        data
    })
}

export function addPrePayCardLine(data) {
    return request({
        url: api.addPrePayCardLine,
        method: 'post',
        data
    })
}

export function editPrePayCardLine(data) {
    return request({
        url: api.editPrePayCardLine,
        method: 'post',
        data
    })
}
export function vipCarAttribute(data) {
    return request({
        url: api.vipCarAttribute,
        method: 'post',
        data
    })
}

// 互联网变更基本信息
export function changeCompanyName(data) {
    return request({
        url: '/account/changeCompanyName',
        method: 'post',
        data
    })
  }
  
//获取播放地址信息
export function getPlayInfo(data) {
    return request({
        url: '/aliyunVod/getPlayInfo',
        method: 'post',
        data
    })
}
