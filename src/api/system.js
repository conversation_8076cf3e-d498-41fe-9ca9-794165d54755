import request from "@/utils/request"
import api from "@/api/index"
export function getLimitList(data) {
  return request({
    url: api.getLimitList,
    method: "post",
    data
  })
}

export function deleteLimite(data) {
  return request({
    url: api.deleteLimite,
    method: "post",
    data
  })
}

export function rateLimite(data) {
  return request({
    url: api.rateLimite,
    method: "post",
    data
  })
}

export function funcAdd(data) {
  return request({
    url: api.funcAdd,
    method: "post",
    data
  })
}

export function funcPage(data) {
  return request({
    url: api.funcPage,
    method: "post",
    data
  })
}

export function funcUpdate(data) {
  return request({
    url: api.funcUpdate,
    method: "post",
    data
  })
}

export function funcDel(data) {
  return request({
    url: api.funcDel,
    method: "post",
    data
  })
}

export function funcNodeList(data) {
  return request({
    url: api.funcNodeList,
    method: "post",
    data
  })
}

export function functionTree(data) {
  return request({
    url: api.functionTree,
    method: "post",
    data
  })
}

export function rolePage(data) {
  return request({
    url: api.rolePage,
    method: "post",
    data
  })
}

export function rolDel(data) {
  return request({
    url: api.rolDel,
    method: "post",
    data
  })
}

export function rolAdd(data) {
  return request({
    url: api.rolAdd,
    method: "post",
    data
  })
}

export function roleUpdate(data) {
  return request({
    url: api.roleUpdate,
    method: "post",
    data
  })
}

export function rolBind(data) {
  return request({
    url: api.rolBind,
    method: "post",
    data
  })
}

export function roleTree(data) {
  return request({
    url: api.roleTree,
    method: "post",
    data
  })
}

export function orgAdd(data) {
  return request({
    url: api.orgAdd,
    method: "post",
    data
  })
}

export function orgDel(data) {
  return request({
    url: api.orgDel,
    method: "post",
    data
  })
}
export function orgTree(data) {
  return request({
    url: api.orgTree,
    method: "post",
    data
  })
}
export function orgUpdate(data) {
  return request({
    url: api.orgUpdate,
    method: "post",
    data
  })
}

export function orgInfo(data) {
  return request({
    url: api.orgInfo,
    method: "post",
    data
  })
}

export function logInfo(data) {
  return request({
    url: api.logInfo,
    method: "post",
    data
  })
}
export function logList(data) {
  return request({
    url: api.logList,
    method: "post",
    data
  })
}
