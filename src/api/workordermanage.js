import request from "@/utils/request"

//线上注销查询初始化
export function logoutInitData (data) {
  return request({
    url: '/onlineLogout/logoutInitData',
    method: 'post',
    data
  })
}

// 注销列表
export function logoutRecordList (data) {
  return request({
    url: '/onlineLogout/logoutRecordList',
    method: 'post',
    data
  })
}

// 导出
export function exportList (data) {
  return request({
    url: '/report',
    method: 'post',
    data
  })
}

// 订单锁定
export function orderLock (data) {
  return request({
    url: '/onlineLogout/orderLock',
    method: 'post',
    data
  })
}

// 订单解锁
export function orderFree (data) {
  return request({
    url: '/onlineLogout/orderFree',
    method: 'post',
    data
  })
}

// 注销订单详情
export function cancelOrderDetail (data) {
  return request({
    url: '/onlineLogout/logoutRecordDetail',
    method: 'post',
    data
  })
}

// 注销流程 checkFlag: 0-审核通过; 1-审核不通过
export function logoutProcess (data) {
  return request({
    url: '/onlineLogout/logoutProcess',
    method: 'post',
    data
  })
}

// 手动注销
export function logoutDeviceHandle (data) {
  return request({
    url: '/onlineLogout/logoutDeviceHandle',
    method: 'post',
    data
  })
}

// 取消订单
export function logoutRecordCancel (data) {
  return request({
    url: '/onlineLogout/logoutRecordCancel',
    method: 'post',
    data
  })
}

//通知用户处理
export function noticeUserHandle (data) {
  return request({
    url: '/onlineLogout/noticeUserHandle',
    method: 'post',
    data
  })
}

//修改信息
export function logoutModify (data) {
  return request({
    url: '/onlineLogout/logoutModify',
    method: 'post',
    data
  })
}

//注销配置申请
export function postCancelConfig (data) {
  return request({
    url: '/onlineLogout/config',
    method: 'post',
    data
  })
}

//注销配置详情
export function cancelConfigDetail (data) {
  return request({
    url: '/onlineLogout/configDetail',
    method: 'post',
    data
  })
}

//订单汇总统计
export function collectOrder (data) {
  return request({
    url: '/orderStat/collect',
    method: 'post',
    data
  })
}

//环比增长率
export function orderRing (data) {
  return request({
    url: '/orderStat/ring',
    method: 'post',
    data
  })
}

//同比增长率
export function orderYear (data) {
  return request({
    url: '/orderStat/year',
    method: 'post',
    data
  })
}

//具体业务数量统计
export function orderSpecific (data) {
  return request({
    url: '/orderStat/specific',
    method: 'post',
    data
  })
}

//业务量统计
export function orderSummary (data) {
  return request({
    url: '/orderStat/summary',
    method: 'post',
    data
  })
}

// 产品转换列表
export function transformList (data) {
  return request({
    url: '/transform/list',
    method: 'post',
    data
  })
}

//产品转换配置申请
export function transformConfig (data) {
  return request({
    url: '/transform/config',
    method: 'post',
    data
  })
}

//产品转换配置详情
export function transformConfigDetail (data) {
  return request({
    url: '/transform/configDetail',
    method: 'post',
    data
  })
}

//线上发行产品配置价格编辑
export function productConfigEdit (data) {
  return request({
    url: '/release/productConfigEdit',
    method: 'post',
    data
  })
}

//线上发行产品配置查询
export function productConfigQuery (data) {
  return request({
    url: '/release/productConfigQuery',
    method: 'post',
    data
  })
}

/**
 * 抖音发行接口
 */

// 抖音发行配置查询
export function tikTokConfigQuery (data) {
  return request({
    url: '/tikTok/configQuery',
    method: 'post',
    data
  })
}

// 抖音发行配置
export function tikTokConfigure(data) {
  return request({
    url: '/tikTok/configure',
    method: 'post',
    data
  })
}

// 配置商品ID
export function tikTokConfigProduct (data) {
  return request({
    url: '/tikTok/configProduct',
    method: 'post',
    data
  })
}

// 抖音销售订单导入
export function saleImport (data) {
  return request({
    url: '/tikTok/saleImport',
    method: 'post',
    data
  })
}

// 抖音售后订单导入
export function afterSaleImportt (data) {
  return request({
    url: '/tikTok/afterSaleImportt',
    method: 'post',
    data
  })
}

// 查询初始化
export function queryInitialize (data) {
  return request({
    url: '/tikTok/queryInitialize',
    method: 'post',
    data
  })
}

// 订单列表查询
export function orderSearch (data) {
  return request({
    url: '/tikTok/orderSearch',
    method: 'post',
    data
  })
}

// 订单详情
export function orderDetails (data) {
  return request({
    url: '/tikTok/orderDetails',
    method: 'post',
    data
  })
}

// 车辆信息修改
export function vehicleUpdate (data) {
  return request({
    url: '/tikTok/vehicleUpdate',
    method: 'post',
    data
  })
}

// 取消申办
export function applyCancel (data) {
  return request({
    url: '/tikTok/applyCancel',
    method: 'post',
    data
  })
}

// 申办审核
export function applyReview (data) {
  return request({
    url: '/tikTok/applyReview',
    method: 'post',
    data
  })
}

//白名单列表
export function getWhitelist (data) {
  return request({
    url: '/transform/selectCancelWhitelist',
    method: 'post',
    data
  })
}

// 导出合作方车辆注销白名单
export function exportChannelCancelWhiteList (data) {
  return request({
    url: '/transform/exportChannelCancelWhiteList',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 涉路施工数据查询
export function constructionRoadQuery (data) {
  return request({
    url: '/travelService/constructionRoadQuery',
    method: 'post',
    data
  })
}

// 应急事件数据查询
export function emergencyEventsQuery (data) {
  return request({
    url: '/travelService/emergencyEventsQuery',
    method: 'post',
    data
  })
}
