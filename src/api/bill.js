import request from '@/utils/request'

//专票红冲申请列表
export function zpList(data) {
  return request({
      url: '/hsInvoiceZp/list',
      method: 'post',
      data
  })
}

// 专票红冲申请审核
export function zpExamine(data) {
  return request({
      url: '/hsInvoiceZp/examine',
      method: 'post',
      data
  })
}

// 专票红冲申请详情
export function zpDetails(data) {
  return request({
      url: '/hsInvoiceZp/details',
      method: 'post',
      data
  })
}

// 申请红冲的专票信息
export function zpInfo(data) {
  return request({
      url: '/hsInvoiceZp/info',
      method: 'post',
      data
  })
}

// 专票红冲申请
export function zpApply(data) {
  return request({
      url: '/hsInvoiceZp/apply',
      method: 'post',
      data
  })
}

