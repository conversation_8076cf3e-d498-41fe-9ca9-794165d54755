import request from '@/utils/request'
import api from '@/api/index'
// import qs from 'querystring'
export function report (data) {
    return request({
        url: api.report,
        method: 'post',
        data
    })
}
export function channels (data) {
    return request({
        url: api.channels,
        method: 'post',
        data
    })
}
export function queryTable (data) {
    return request({
        url: api.queryTable,
        method: 'post',
        data
    })
}
export function transferExport (data) {
    return request({
        url: api.transferExport,
        method: 'post',
        data
    })
}

export function serviceChargeHcb (data) {
    return request({
        url: api.serviceChargeHcb,
        method: 'post',
        data
    })
}

export function devSale (data) {
    return request({
        url: api.devSale,
        method: 'post',
        data
    })
}

export function yyxStamp (data) {
    return request({
        url: api.yyxStamp,
        method: 'post',
        data
    })
}

export function saleDailyCheckList (data) {
    return request({
        url: api.saleDailyCheckList,
        method: 'post',
        data
    })
}
export function saleDailyOperatorList (data) {
    return request({
        url: api.saleDailyOperatorList,
        method: 'post',
        data
    })
}
export function saleDailyReportAudit (data) {
    return request({
        url: api.saleDailyReportAudit,
        method: 'post',
        data
    })
}
export function saleDailyReportRemark (data) {
    return request({
        url: api.saleDailyReportRemark,
        method: 'post',
        data
    })
}
export function saleDailyReportList (data) {
    return request({
        url: api.saleDailyReportList,
        method: 'post',
        data
    })
}
export function saleDailyReportTitle (data) {
    return request({
        url: api.saleDailyReportTitle,
        method: 'post',
        data
    })
}

// 报表盖章并下载api
export function stampDownloadApi (data) {
    return request({
        url: api.yyxStamp,
        method: 'post',
        responseType: 'blob',
        data
    })
}