import request from '@/utils/request'
import api from '@/api/index'
export function getJobList(data) {
    return request({
        url: api.getJobList,
        method: 'post',
        data
    })
}
export function getAllJobList(data) {
    return request({
        url: api.getAllJobList,
        method: 'post',
        data
    })
}

export function addJob(data) {
    return request({
        url: api.addJob,
        method: 'post',
        data
    })
}
export function openJob(data) {
    return request({
        url: api.openJob + '/' + data.taskId,
        method: 'post',
        data
    })
}
export function getIssueList(data) {
    return request({
        url: api.getIssueList,
        method: 'post',
        data
    })
}
export function getIssueDetail(data) {
    return request({
        url: api.getIssueDetail + '/' + data.ocrAuditId,
        method: 'post'
    })
}
export function getIssueStatistics(data) {
    return request({
        url: api.getIssueStatistics,
        method: 'post',
        data
    })
}

export function exportExcelModel(data) {
    return request({
        url: api.exportExcelModel,
        method: 'get',
        data
    })
}

export function importExcelModel(data) {
    return request({
        url: api.importExcelModel,
        method: 'post',
        data
    })
}
export function exportIssueResult(data) {
    return request({
        url: api.exportIssueResult + '?Authorization='+data.Authorization +'&taskId='+ data.taskId + '&taskName=' + data.taskName,
        method: 'get',
        data
    })
}

// 签约查询
export function currentSign(data) {
    return request({
        url: '/bankSign/currentSign',
        method: 'post',
        data
    })
}

