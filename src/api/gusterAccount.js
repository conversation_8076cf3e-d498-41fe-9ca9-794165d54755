import request from '@/utils/request'
import api from '@/api/index'
export function guestAccountList(data) {
    return request({
        url: api.guestAccountList,
        method: 'post',
        data
    })
}
export function openGuestAccount(data) {
    return request({
        url: api.openGuestAccount,
        method: 'post',
        data
    })
}

export function guestAccountVehicle(data) {
    return request({
        url: api.guestAccountVehicle,
        method: 'post',
        data
    })
}
export function guestBindVehicle(data) {
    return request({
        url: api.guestBindVehicle,
        method: 'post',
        data
    })
}
export function rechargeGuestAccount(data) {
    return request({
        url: api.rechargeGuestAccount,
        method: 'post',
        data
    })
}
export function openedAccountList(data) {
    return request({
        url: api.openedAccountList,
        method: 'post',
        data
    })
}

export function consumptionList(data) {
    return request({
        url: api.consumptionList,
        method: 'post',
        data
    })
}

export function guestAccountRefund(data) {
    return request({
        url: api.guestAccountRefund,
        method: 'post',
        data
    })
}