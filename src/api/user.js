import request from '@/utils/request'
import upmsRequest from '@/utils/upmsRequest'
import api from '@/api/index'
export function getInfo() {
  return upmsRequest({
    url: 'user/userinfo',
    method: 'get',
    params: {
      clientId: process.env.VUE_APP_CLINET_ID
    }
  })
}

export function getMenus(clientId) {
  return upmsRequest({
    url: '/user/menus/' + clientId,
    method: 'get'
  })
}

export function login(data) {
  return request({
    url: api.login,
    method: 'post',
    data
  })
}

export function logOut() {
  return request({
    url: api.logOut,
    method: 'post'
  })
}

export function addUser(data) {
  return request({
    url: api.addUser,
    method: 'post',
    data
  })
}

export function searchUserList(data) {
  return request({
    url: api.searchUserList,
    method: 'post',
    data
  })
}

export function userExite(data) {
  return request({
    url: api.userExite,
    method: 'post',
    data
  })
}

export function changePasd(data) {
  return request({
    url: api.changePasd,
    method: 'post',
    data
  })
}

export function checkUserAuth(data) {
  return request({
    url: api.checkUserAuth,
    method: 'post',
    data
  })
}

export function adminChangePasd(data) {
  return request({
    url: api.adminChangePasd,
    method: 'post',
    data
  })
}

export function getDeptInfo() {
  return upmsRequest({
    url: 'user/profile',
    method: 'get'
  })
}