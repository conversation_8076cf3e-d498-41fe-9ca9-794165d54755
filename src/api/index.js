let auditjob = {
    logInfo: 'system/system-rest/operation-log-info', // 操作日志详情
    logList: 'system/system-rest/operation-log-list', // 操作日志列表
    getJobList: 'auditjob/search', // 查询任务列表
    getAllJobList: 'auditjob/list', // 查询任务列表
    addJob: 'auditjob', // 创建任务
    openJob: 'auditjob/open', // 开启任务
    getIssueList: 'audit/search', // 查询稽查列表
    getIssueDetail: 'audit/search', // 查询稽查详情
    getIssueStatistics: 'auditjob/statistics', // 稽查任务统计
    exportExcelModel: 'auditjob/downloadTemplate',//导出excel模板
    importExcelModel: 'auditjob/uploadTemplateData', //导入excel模板数据
    exportIssueResult: 'auditjob/downloadTemplateData',//导出稽核结果
    getJobField: 'auditjob/field',
    getCarInfoList: 'cancelVehicle/query',//查询车辆信息列表
    getCarInfoDetail: 'cancelVehicle/details',//查询车辆信息详情
    changeNotCompleteList: 'cardBiz/changeNotCompleteList',//查询换卡待确认列表
    changeCardRepush: 'cardBiz/changeCardRepush',//手动换卡确认
    breakFaithList: "/hsBadFaithList/list",//失信名单列表
    breakFaithImport: '/hsBadFaithList/importExcel',//失信名单导入
    breaKFaithAdd: '/hsBadFaithList/add',//失信名单添加
    breakFaithDelete: '/hsBadFaithList/delete',//失信名单删除
    breaKFaithExport: '/hsBadFaithList/export',//失信名单导出
    breakFaithRelease: '/hsBadFaithList/release',//失信名单解除
    auditOperatorRecord: '/hsKeyAuditOpLog/list',//稽核管理操作记录
    auditList: '/hsKeyAuditList/list',//稽核管理列表
    auditDetail: '/hsKeyAuditList/detail',//稽核管理详情
    auditExport: '/hsKeyAuditList/export',//稽核管理导出
    auditJudge: '/hsKeyAuditList/judge',//稽核管理结果
    auditActivateRecord: '/activateRecord/list',//激活记录
    falseCertificateDownload: '/forgedDoc/audit/download', // 稽核批量导入表样下载
    falseCertificateAuditBatch: '/forgedDoc/audit/auditBatch', // 专项稽核批量导入
    contrastCarSearch: '/forgedDoc/audit/queryBatch', // 交管数据查询导入
}

let transfer = {
    advancePayment: 'transfer/advancePayment', //财务业务数据列表
    importPayment: 'transfer/import', //文件导入
    saveAdvance: 'transfer/saveAdvance', //人工录入数据
    searchRechargeList: 'transfer/rechargeList', //充值列表查询
    transfeConfirm: 'transfer/confirm', //批量确认业务数据
    transfeDelete: 'transfer/delete', //删除业务数据
    transfeCancel: 'transfer/cancel', //取消确认业务数据
    getNetAccountList: 'transfer/netAccount', //待充值账户列表
    getSumAvailableAmount: 'transfer/sumAvailableAmount', //财务业务数据可用总余额
    transferRefound: 'transfer/public/refound', //充值订单退
    transferRefoundValidate: "vehicleUpdate/validAccount",//冲正授权
    transferRevoke: '/transfer/revoke ',//撤回删除
    transferRecharge: 'transfer/public/recharge', //批量充值
    clientAccountList: 'transfer/clientAccountList', //查询客账用户列表
    openClientAccount: 'transfer/openClientAccount', //开通客账
    clientVehicleList: 'transfer/vehicleList', //查询客账车辆列表
    clientVehicleBind: '/transfer/vehicleBind' //客账绑定车辆
}

let prePaid = {
    getPrePayCardList: 'prePaid/cfgList', //获取线值配置列表
    editPrePayCardLine: 'prePaid/editCfg', //修改线值配置
    addPrePayCardLine: 'prePaid/addCfg' //新增线值配置
}

let containerRefund = {
    getContainerRefundList: 'containerRefund/containerRefundAuditSearch', //查询集装箱退费申请列表
    getContainerRefundAuditDetailList:
        'containerRefund/containerRefundAuditDetailJsp', //查询集装箱退费申请审核详情
    containerRefundOpratorDetail:
        'containerRefund/containerRefundAuditDetailInfoJsp', //查询集装箱退费申请操作详情
    refundReport: 'report', //退费导出
    getPassRecordDetail: 'containerRefund/passRecordSearchDetail2', //交易信息明细
    containerRefundPass: 'containerRefund/containerRefundAuditPass', //审核申请通过
    containerRefundRefuse: 'containerRefund/containerRefundAuditRefuse', //审核申请驳回
    containerRefundNoticeSearch: 'containerRefund/containerRefundNoticeSearch', //退费推送查询
    reSendNotice: 'containerRefund/reSendNotice',//退费通知重新推送
    containerRefundAuditResultSearch: '/containerRefund/containerRefundAuditResultSearch', //退费审核操作记录查询
    containerRefundAuditUpdatePhotos: '/containerRefund/containerRefundAuditUpdatePhotos' //退费审核操作记录查询
}

// let wechat = {
//   materialQuery: 'material/query',//查询素材列表
//   getMaterialCount: 'material/getMaterialCount',//获取素材总数
//   materialFileUpload: 'material/materialFileUpload',//添加其他类型永久素材
//   getMaterialNewsBatchGet: 'material/materialNewsBatchGet',//分页获取图文素材列表
//   getMaterialNewsInfo: 'material/materialNewsInfo',//获取图文永久素材
//   materialNewsUpload: 'material/materialNewsUpload',//添加图文永久素材
//   mediaUpload: 'material/mediaUpload',//添加图文临时素材
// }
let refund = {
    refundOfflineSearch: 'refundOffline/search', //线下退费查询
    refundOfflineEdit: 'refundOffline/edit', //新增/修改线下退款记录
    refundOfflineListById: 'refundOffline/list', //根据线下退费订单号获取修改历史
    refundResultImport: 'refundOffline/refundResultImport', //线下退款导入
    refundDelete: 'refundOffline/delete', //删除
    refundConfigOrg: 'refundConfigOrg/findById', //删除

    getOrgView: 'refundConfigOrg/orgView', //绑定银行类型字典表
    searchOrg: 'refundConfigOrg/searchOrgPageInfo', //退费机构查询
    orgEdit: 'refundConfigOrg/edit', //退费机构新增/修改
    orgDelete: 'refundConfigOrg/delete', //退费机构删除
    getChannelType: 'refundConfigOrg/channelType', //退费机构退款渠道字典

    artificialSearch: 'refundConfigOffline/searchPageOfflineInfo', //人工查询
    showAddOrUpdateDialog: 'refundConfigOffline/showAddOrUpdateDialog', //对话框回填数据 拼接id
    addOrUpdateConfirm: 'refundConfigOffline/addOrUpdateConfirm', //人工增加/修改
    artificialDelete: 'refundConfigOffline/deleteOfflineInfoById', //人工删除

    getRefundOrder: 'business/refundManage/refundOrder/search', //退费订单查询
    refundOrderPreInitAudit: 'business/refundManage/refundOrder/preInitAudit', //退费订单确认退款
    manualRefundExport: 'business/refundManage/refundOrder/manualRefundExport', //退费订单人工退款导出
    refundOrderData: 'business/refundManage/refundOrder/showUpdateDialog', //显示添加或更新弹出框
    refundOrderUpdate: 'business/refundManage/refundOrder/update', //编辑
    refundOrderCancel: 'business/refundManage/refundOrder/refundCancel', //撤销
    getTypeList: 'business/refundManage/refundOrder/list', //页面字典
    updatePrice: 'business/refundManage/refundOrder/modifyAmount', //修改金额
    confirmRefund: 'business/refundManage/refundOrder/confirmRefundable', //确认可退款
    getBindRefundOrder: 'refund/findByHsRefundOrderId', //关联退款订单
    queryManByOpId: 'hsContactManager/queryLogId', //查找有效联系人byopid
    queryManByData: 'hsContactManager/queryData', //查找有效联系人bydata
    getRefundTbaleList: 'business/refundManage/refundOrder/refundQuery', //原路退款制表查询
    makeRefundTbale: 'business/refundManage/refundOrder/tabulation', //原路退款制表

    getRefundComplete: 'refundCompleteRecord/searchRefundCompleteRecord', //退费完成记录列表
    addRefundComplete: 'refundCompleteRecord/addRefundCompleteRecord', //新建退费进度
    refundCompleteRecord: 'refundCompleteRecord/refundCompleteRecord', //退费进度修改页面,拼接id
    updateRefundComplete: 'refundCompleteRecord/updateRefundCompleteRecord', //退费进度修改
    refundCompleteCancel: 'refundCompleteRecord/refundCompleteCancel', //删除进度,拼接id

    getTollRecordList: 'business/transactionFee/monitor/search', //退费审核查询页  通行费监控
    getTollConfirm: 'business/transactionFee/refundRecord/search',//通行费待确认查询
    tollConfirmExport: 'business/transactionFee/refundRecord/export',//通行费待确认导出
    tollConfirm: 'business/transactionFee/refundRecord/confirm',//通行费确认

    getLogoutRefundList: 'refund/reviewed', //待审核列表
    getTable: 'refund/makeTableQuery', //制表列表查询
    getLogoutDetail: 'refund/details', //待审核详情
    getDetailImgs: 'archives/search', //获取档案
    issuerAudit: 'refund/issuerAudit', //发行人员审核
    branchAudit: 'refund/branchAudit', //店长审核
    operationAudit: 'refund/operationAudit', //运营部审核
    editCardInfo: 'refund/update', //更改银行卡号信息
    makeTable: 'refund/makeTable', //制表 
    setTag: 'refund/urgentSign', //加急处理
    financialRevision: 'refund/financialRevision', //财务修改转账结果
    makeTableAudit: 'refund/makeTableAudit', //退回运营部审核
    getDeptName: 'dept/getDeptName', //退回运营部审核
    rechargeNet: 'refund/rechargeNet', //注销退费退至互联网账户

}
let report = {
    report: 'report', //报账
    channels: 'cardSign/channels',
    queryTable: 'refund/queryTable', //查询退费转账表（批复表）
    transferExport: 'refund/transferExport', // 财务导出转账表
    transferImport: 'refund/transferImport', //财务上传转账结果表
    transferMatch: 'refund/transferMatch', //财务转账结果匹配处理
    billList: 'vipCard/billList', //月月行账单列表
    verifyBill: 'vipCard/verifyBill', //月账单审核
    serviceChargeHcb: "serviceChargeHcb",//金融服务费-货车帮
    applyInvoice: 'invoice/applyInvoice',//开票
    redFlush: 'invoice/redFlush',//发票红冲
    query: 'invoice/query',//发票查询

    monthOpenInvoice: 'hsInvoice/blue',//月结账单开票
    monthInviceRed: 'hsInvoice/red',//月结账单红冲
    monthInviceQuery: 'hsInvoice/query',//月结账单查看发票
    monthInviceMergeList: 'hsInvoice/query/bizOrders', //月结账单合并开票查询
    getInvoiceCompanyInfo: 'hsInvoice/getInvoiceCompanyInfo',//购方名称模糊查询
    redFlushApply: 'hsInvoiceZp/apply',//发票专票红冲申请

    billForfeit: '/vipCard/billForfeit',//月账单滞纳金
    invoicesearch: 'invoice/search',//专票查询
    invoicedetail: 'invoice/detail',//专票详情
    specialInvoiceconfirm: 'specialInvoice/confirm',//专票申请确认
    specialInvoicerefuse: 'specialInvoice/refuse',//专票申请拒绝
    specialInvoicecancel: 'specialInvoice/cancel',//专票撤销申请
    specialInvoicepass: 'specialInvoice/pass',//专票审核通过
    invoiceexport: 'invoice/export',//专票导出
    invoiceimport: 'invoice/import',//专票导入
    autoQuery: 'apply/autoQuery',//自动审核查询
    autoCheck: 'apply/autoCheck',//自动审核
    reportdept: 'report/dept',//部门查询
    operatorList: 'report/user',//操作员查询
    yffCardList: '/cardBiz/yffCardList',//预付费可转捷通预付费列表
    yffConvert: '/cardBiz/yffConvert',//预付费转捷通预付费
    yffConvertedList: '/cardBiz/yffConvertedList',//预付费已经转捷通预付费列表
    getVipCustomerList: '/vipCard/custListNew',//用户列表
    billListExport: '/vipCard/billListExport', // 月月行账单导出
    yyxStamp: '/report/stampDownload',//月月行报表盖电子章

    // devSale: "devSale", //设备销售
    // squareRestituteReport:'squareRestituteReport'//欠费补交对账

    saleDailyCheckList: '/checkSell/checkList', //日结网点销售充值应收实收款日报表查询审核列表
    saleDailyOperatorList: '/checkSell/historyList', //日结网点销售充值应收实收款日报表查询操作记录
    saleDailyReportAudit: '/checkSell/check', //日结网点销售充值应收实收款日报审核
    saleDailyReportRemark: '/checkSell/updateRemark', //日结网点销售充值应收实收款日报表编辑备注
    saleDailyReportList: '/checkSell/reportList', //日结网点销售充值应收实收款日报表编辑报表列表
    saleDailyReportTitle: '/checkSell/reportTitle', //日结网点销售充值应收实收款日报表报表列表报表表头

}
let common = {
    archives: '/archives/search',
    customerBizList: '/customerBiz/list',
    getCarinfo: '/vehicleBiz/search',//查车辆信息
    // 短信验证码
    sendSms: '/account/sms',
    // 图形验证码
    getCaptcha: '/account/captcha',
    getAccountView: '/account/view', // 获取互联网账户信息
    getAccountList: '/account/list', // 获取互联网账户信息列表
    accountinfo: '/account/info', // 获取互联网账户信息
    monthlyBillPay: '/vipCard/billPay', // 月结账单还款
    changeCompanyAccount: 'account/changeCompanyAccount',//修改互联网账户手机号码
    netnetAccount: 'net/netAccount',//互联网用户列表
    netrefoundBank: 'net/refoundBank',//互联网账户退费
    refundPrint: '/internetRefund/refundPrint',//互联网退款单打印
    refundDtffer: '/vipCard/transferDate',//月月行还款调差
    vipCardForfeits: '/vipCard/forfeits',//vip卡账单可调差滞纳金明细
    vipCardRmForfeits: '/vipCard/rmForfeits',//vip卡账单滞纳金调差
    vipCardForfeitsExport: '/vipCard/forfeitsExport',//滞纳金明细导出
    vipCarResend: '/vipCard/resend',//月月行账单重发邮件
    b2bicList: '/b2bic/localList',//平安银行账户列表
    b2bFlow: '/b2bic/localFlow',//平安账户流水
    b2bView: '/b2bic/view', //平安账户详情,
    b2bViewClearing: '/b2bic/localClearing',//账户清分(本地)
    netBalanPayList: '/net/balanPayList',//互联网划拨卡账流水  
    netWalletPays: '/net/walletPays',//  互联网消费流水
    netRechargeList: '/net/list',//  互联网充值流水  


    changeCompanyAccount: '/account/changeCompanyAccount',//修改互联网账户手机号码
    subscribeRecord: '/wechatMessage/records',//消息提醒记录查询列表
    pilotLibrary: '/zeroProduct/list',//日日通零元试点库
    removePilotLibrary: '/zeroProduct/change',//移除日日通零元试点库
    yyxWarnInfo: '/vipCard/warn/info',//月结账单逾期提醒设置信息
    yyxWarnUpdate: '/vipCard/warn/update',//月结账单逾期提醒设置更新

    tollRecord: '/cardBiz/cardPayInfo',//消费记录查询
    getAddress: 'release/address',//获取地址
    dailyReceiveList: '/checkSell/reportCheckList',//应收实收记录查询
    dailyReceiveListTotal: "/checkSell/reportCheckTotal",//应收实收记录汇总
    occupyVehicleList: '/vehicleOccupy/list',//车牌解占列表
    occupyVehicleDetail: '/vehicleOccupy/view',//车牌解占详情
    occupyOweData: '/vehicleOccupy/overdueList',//车牌解占欠费记录
    hsKeyAuditListRecord: '/hsKeyAuditList/cardPayInfo', // 卡片通行流水
}
let approval = {
    appDet: 'apply/appDet', //申请单详情-新办
    repDet: 'apply/repDet', //申请单详情-售后
    record: 'apply/record', //申请单记录
    toDoOrderList: 'order/workflow/toDoOrderList', //待处理的销售订单列表
    orderDetail: 'order/workflow/orderDetail', //销售订单详情
    shipment: 'equipmentMailing/shipment', //出货并邮寄
    search: 'equipmentMailing/search', //邮寄查询
    getMaterialList: 'order/materialList', //查询物资信息
    findById: 'equipmentMailing/findById', //查看详情
    update: '/equipmentMailing/update', //邮寄更新
    todelete: '/equipmentMailing/delete', //邮寄删除
    confirmStatus: '/equipmentMailing/ConfirmReceiptVO', //确认收货
    afterSaleList: 'sale/list', //售后列表
    afterSaleDetail: 'sale/detail', //售后详情
    afterSaleOperate: 'sale/operate', //操作记录
    confirmGoods: '/apply/confirm',//确认收货
    refundMoney: '/apply/refundIf',//退款
    sfExpressInfo: '/sfExpress/queryRoute',//顺丰物流信息
    list: 'apply/list', //审核列表
    // adopt: 'apply/adopt', //审核通过
    // reject: 'apply/reject', //审核驳回
    examine: '/apply/examine',//审核
    explain: '/apply/explain',//申诉审核
    appealQuery: '/apply/appealQuery', //申述信息查询
}

let bindManagement = {
    //补缴交易管理
    listAuditVersions: 'business/audit/blackList/listAuditVersions', //版本号all列表查询
    auditBlackSearch: 'business/audit/blackList/auditBlackSearch', //补缴订单查询
    dictionaries: 'business/audit/blackList/dictionaries',//字典接口
    auditBlackDetailInfo: 'business/audit/blackList/auditBlackDetailInfo', //详情
    auditPass: 'business/audit/blackList/auditPass', //审核通过
    auditRefuse: 'business/audit/blackList/auditRefuse', //审核拒绝
    checkAuditHandleState: 'business/audit/blackList/checkAuditHandleState', //审核前检查处理状态
    cancelAuditApply: 'business/audit/blackList/cancelAuditApply', //提交撤销申请
    cancelAuditApplyPass: 'business/audit/blackList/cancelAuditApplyPass', //撤销申请审核通过
    importAuditPass: 'business/audit/blackList/importAuditPass', //补缴导入审核通过
    checkCancelAuditHandleState: 'business/audit/blackList/checkCancelAuditHandleState', //撤销申请前检查
    cancelAuditApplyRefuse: 'business/audit/blackList/cancelAuditApplyRefuse', //撤销申请审核驳回
    importAuditRefuse: 'business/audit/blackList/importAuditRefuse', //补缴导入审核驳回
    checkAuditCancelAuditDetailInfoApply: 'business/audit/blackList/checkAuditCancelAuditDetailInfoApply', //撤销申请审核前检查
    importAuditVersions: 'business/audit/blackList/importAuditVersions', //版本号列表查询
    importsAuditSearch: 'business/audit/blackList/importsAuditSearch', //补缴导入审核列表
    imports: 'business/audit/blackList/imports', //补缴导入
    exportSearch: 'business/audit/blackList/exportSearch', //补缴导入
    //扣款交易监控
    batchReSend: 'deductions/batchReSend', //批量作废重发
    batchTransfer: 'deductions/batchTransfer', //批量转补缴
    deductionExport: 'deductions/deductionExport', //请款订单导出
    transExport: 'deductions/export', //扣款交易记录导出
    deductionsInit: 'deductions/init', //扣款交易接口初始化
    getDeductionsList: 'deductions/list', //扣款交易记录查询
    getDeductionsPass: 'deductions/pass', //通行记录查询
    getPassDetail: 'deductions/passDetail', //通行记录明细查询
    singleReSend: 'deductions/singleReSend', //单笔作废重发
    singleTransfer: 'deductions/singleTransfer', //单笔转补缴
    transferDetail: 'deductions/transferDetail', //单笔转补缴详情
    hsAuditDispute: 'hsAuditDispute/list',//申述列表查询
    hsAuditDisputefindById: 'hsAuditDispute/findById',//根据主键ID,查欠费补缴用户申述详情
    hsAuditDisputeupdate: 'hsAuditDispute/update',//申述审批
    hsAuditDisputedetail: 'hsAuditDispute/detail',//根据补缴信息ID，查欠费补缴用户申述详情
    upImg: '/archives/upload',//上传图片
    delPic: '/archives/delete',//删除图片
    cacheImgUplaod: '/archives/img/upload',// 临时图片上传接口
    getAfterCardPay: '/reimburse/record', //卡账补缴记录
    deductionCancel: '/deductions/deductionCancel',//扣款作废
    refundTrans: '/deductions/refundTrans', //退费交易信息
    supplementary: '/deductions/supplementary', //手动发起补扣


}
let SMSManagement = {
    smsquery: '/sms/query',//查询短信模板
    smscreate: '/sms/create',//创建短信模板
    smsdelete: '/sms/delete',//删除短信模板
    smsupdate: '/sms/update',//修改短信模板
}

let vipCar = {
    getVipCarList: '/freeVehicleList/list',//高频车列表
    addVipCar: '/freeVehicleList/add',//高频车增加
    delVipCar: '/freeVehicleList/delete', //高频车删除
    updateVipCar: '/freeVehicleList/update',//高频车修改车和备注
    exportVipCar: '/freeVehicleList/importFreeVehicleList',//高频车导入
    vipCarAttribute: '/freeVehicleList/dict',//高频车属性枚举
}
let elecSignatures = {
    getSignList: '/contract/queryContracts',//电子合同列表
    downSign: '/contract/download',//合同下载
    downLoadExport: '/report/download', //列表导出
    previewSign: '/contract/preview',//合同下载
    getSignDetai: '/contract/queryContractsDetails',//合同详情,
    checkContracts: '/clientAccount/checkContracts',//转客账签署协议校验
    createContract: '/contract/card',//生成电子协议
    contractStatus: '/contract/status',//查询电子协议签署状态

}

let updateDevice = {
    getUpdateDeviceList: 'entry/orderList',//设备更新查询
    addUPdateDeviceOrder: 'entry/orderRaise',//新增
    downTemplate: 'entry/templateDownload',//模板下载
    updateDeviceUpload: 'entry/orderUpload',//模板导入
    viewImgs: 'entry/viewPictures',//查看图片
    modifyDevice: 'entry/orderModify',//修改
    delDevice: 'entry/orderDelete',//删除
    getIsTrunk: 'entry/vehicleInfo' //获取客货
}

let monitor = {
    addMsgMonitor: 'monitor/cancel',//新增注销凭证
    getMsgMonitorDict: 'monitor/init',//消息接收监控查询dict
    getMsgMonitorList: 'monitor/query',//消息接收监控查询
    getMsgMonitorUpload: 'monitor/batchUpload',//导入批量新增注销凭证
}

let cardManagement = {
    cardAccountBusType: 'business/card/accountBaseOperation/cardAccountBusType',//卡账操作记录查询-操作类型
    queryCardRecord: 'business/card/accountBaseOperation/cardAccountOperationRecordSearch',//卡账操作记录查询
    queryCardExport: 'business/card/accountBaseOperation/exportCardAccountOperationSearch',//卡账操作记录查询导出

    queryExceptionMonitor: 'cardbinding/exceptionMonitor/exceptionMonitorSearch',//异常监控查询
    exceptionMonitorResend: 'cardbinding/exceptionMonitor/exceptionMonitorResend',//异常重发
    bindCardQuery: 'cardbinding/relationship/listPage',//绑定关系分页列表
    bindCardExport: 'cardbinding/relationship/exportSearch',//绑定关系导出
    getCardbindingData: 'cardbinding/exceptionMonitor/init',//异常监控，绑定关系查询字典接口
    getExceptionMonitorData: 'exceptionMonitor/init',//扣款异常监控字典接口
    getExceptionMonitor: 'exceptionMonitor/search',//扣款异常查询
    exceptionResend: 'exceptionMonitor/resend',//扣款异常重发
    personHandle: 'exceptionMonitor/handle',//手动填写bankAccountDate解决扣款异常
    getSendMonitorData: 'sendMonitor/init',//消息接收字典接口
    logSearch: 'sendMonitor/logSearch',//消息发送查发送日志
    cardIncrease: 'load/cardIncrease',//卡账加值金额
    cardIncreaseList: 'load/cardIncreaseList',//卡账加值记录
    cardStop: 'httpInterface/bindingCardInterface/simulation/cardStop',//注销车辆停用启用


}

//客账相关
let guestAccount = {
    guestAccountList: '/transfer/clientAccountList', //查询客账用户列表
    openGuestAccount: '/transfer/openClientAccount', //开通客账
    guestAccountVehicle: '/transfer/vehicleList',//查询客账车辆列表
    guestBindVehicle: '/transfer/vehicleBind', //客账绑定车辆
    rechargeGuestAccount: '/transfer/public/rechargeAccount', // 对公充值客账
    openedAccountList: '/transfer/guestAccount', //已开通客账用户列表
    consumptionList: '/transfer/clentAccountFlow', //充值消费流水
    guestAccountRefund: '/clientAccount/refound',//客账退费
    guestRefundPrint: '/clientAccount/refundPrint',//客账退费单打印
    batchSignUrl: '/clientAccount/batchSignUrl',//一键转客账生成签署地址
    batchConvertCust: '/clientAccount/batchConvertCust', //一键转客账
    getVehicleAccountList: '/transfer/vehicleAccountList', // 支持转客帐车辆

    updateAccountLine: '/clientAccount/updateLine', // 修改提醒线
    bindingAccountVehicleList: '/clientAccount/bindingVehicleList', // 客账绑定车辆列表
    updateAccountBlackLine: '/clientAccount/updateBlackLine' // 修改预存线
}
//退款账户信息管理
let hsContactManager = {
    hsContactManagerList: 'hsContactManager/list', // 联系人列表
    addHsContactManager: 'hsContactManager/addContactManager', // 添加联系人
    editHsContactManager: 'hsContactManager/editContactManager', // 编辑联系人
    examineContactManager: 'hsContactManager/examineContactManager', //审核联系人
    hsContactManagerDetails: 'hsContactManager/details', //审核联系人
    selectAccountList: 'hsContactManager/chooseUser', //选择录入用户
    selectVehicleList: 'hsContactManager/chooseCar', //选择录入车辆
}

// 线上新办二期
let newApplyV2 = {
    newApplyList: '/release/list',//列表查询
    addressUpdate: '/release/addressUpdate',//更新收货地址
    amountUpdate: '/release/amountUpdate',//更新金额
    vehicleUpdate: '/release/carUpdate',//车辆信息修改
    getApplyType: '/release/checkType',//获取新办审核方式
    configOptions: '/release/config',//发行业务规则配置
    applyOrderDetail: '/release/detail',//订单详情
    expressDetail: "/release/expressDetail",//物流详情
    goodsUpdate: "/release/goodsUpdate",//取货方式修改
    searchInit: '/release/initalData',//新办查询初始化
    batchAudit: '/release/orderBatch',//批量审核
    orderCancel: '/release/orderCancel',//订单取消
    orderCheck: '/release/orderCheck',//订单审核
    saleCheck: '/release/postSaleCheck',//售后审核
    saleList: '/release/postSaleList',//售后列表
    refundApply: '/release/returnApply',//退货申请
    refundApplyPrint: '/release/returnGoodsPrint',//售后退款单打印
    orderLock: '/release/orderLock',//锁定订单
    orderOpen: '/release/orderFree',//订单释放
    getPayMoney: '/release/devicePrice',//获取设备金额
    afterSaleUpdate: '/release/postSaleUpdate',//修改售后审核结果
    afterSaleAudit: '/release/postSaleCheck',//售后单审核
    receiveGoods: '/release/signReceive',//确认收货
    refundAllMoney: '/release/amountUpdate',//全额退款
    afterCancel: "/release/cancelPostApply",//撤销售后单
    activateApplyList: '/activateApply/list',//激活列表
    activateConfigDetail: "/activateConfig/details",//配置详情
    activateConfig: "/activateConfig/add",//激活业务规则配置,
    activateBindChannel: "/activateApply/bindingBankType",//激活绑定渠道查询
    activateDetail: '/activateApply/detail',//激活详情
    activateAudit: '/activateApply/audit',//激活审核
    activateExport: '/activateApply/export',//激活导出
    activateOperationRecord: '/hsActivateApplyOpLog/list',//激活操作记录
    expressUpdate: '/release/expressUpdate',//物流信息修改
    applyConfigDetail: '/release/configDetail',//配置详情

    afterSaleList: '/afterSales/exchangeAndReissue/order/list',//补办更换列表
    afterSaleListExport: "/afterSales/exchangeAndReissue/order/export",//补办更换列表导出
    afterSaleListDetail: '/afterSales/exchangeAndReissue/order/view',//补办更换详情查询
    afterSaleInfoEdit: '/afterSales/exchangeAndReissue/order/edit',//补办更换信息编辑
    afterSaleStatusEdit: '/afterSales/exchangeAndReissue/order/editStatus',//补办更换审核
    afterSaleGetAmount: '/afterSales/exchangeAndReissue/order/getAmount',//获取补办更换金额
    afterSaleConfirm: "/afterSales/exchangeAndReissue/order/confirmAmount",//待更换设备确认接口
    afterSaleIssue: '/afterSales/exchangeAndReissue/order/issue',//待更换/补办发行
    afterSalePost: '/afterSales/exchangeAndReissue/order/post',//发货
    afterChangeStatusReport: '/afterSales/exchangeAndReissue/report/exchange',//线上更换状态统计表
    afterRemakeStatusReport: '/afterSales/exchangeAndReissue/report/reissue',//线上补办状态统计表
    afterSaleReceiveReport: "/afterSales/exchangeAndReissue/report/receive",//线上售后业务应收统计表
    afterSaleConfig: '/afterSales/exchangeAndReissue/feeConfig/list',//更换补办收费类型配置
    afterSaleConfigSave: '/afterSales/exchangeAndReissue/feeConfig/edit',//更换补办收费类型业务规则配置保存
    afterSaleArchives: '/afterSales/exchangeAndReissue/order/imgList',//获取更换补办档案

    isRefundMoney: '/release/ifRefund',//是否退款
    isEditInstall: '/release/ifModify',//判断是否能修改取货信息，
    queryExpressStatus: 'release/expressStatus',//查询物流状态

    afterSaleConfigQuery: '/afterSales/exchangeAndReissue/activateConfig/details',//售后业务规则配置查询
    afterSaleConfigEdit: '/afterSales/exchangeAndReissue/activateConfig/add',//售后业务规则修改
    afterSaleFreeOrder: '/afterSales/exchangeAndReissue/order/liberate',//释放锁定订单
}

let vipCard = {
    serviceConfigView: "vipConfig/service/view", //获取月月行服务费配置详情
    serviceConfigList: "vipConfig/service/list", //获取月月行服务费配置历史列表
    serviceConfigUpdate: "vipConfig/service/update", //更新月月行服务费配置
    forfeitConfigView: "vipConfig/forfeit/view", //获取月月行滞纳金配置详情
    forfeitConfigList: "vipConfig/forfeit/list", //获取月月行滞纳金配置历史列表
    forfeitConfigUpdate: "vipConfig/forfeit/update", //更新月月行滞纳金配置
    invoiceStatus: '/vipCard/invoiceStatus' // 月月行账单开票状态
}

let userBalance = {
    searchUserBalance: "load/accountAmountDownload", //搜索接口
    userBalanceJob: "load/accountAmountJob", //任务调用接口
}

let productConver = {
    converPriceList: '/transform/selectProductChangeFeeList',
    converPriceUpdate: '/transform/updateProductChangeFee',
    converCarAuthList: '/transform/selectCarAuthList',
    exportCarAuth: '/transform/importCarsAuth',
    updateCarsAuth: '/transform/updateCarsAuth',
    carsAuthDownloadTemplate: '/transform/downloadTemplate',
   
}

export default {
    ...common,
    ...auditjob,
    ...transfer,
    ...prePaid,
    ...containerRefund,
    ...refund,
    ...report,
    ...approval,
    ...bindManagement,
    ...SMSManagement,
    ...vipCar,
    ...elecSignatures,
    ...updateDevice,
    ...monitor,
    ...cardManagement,
    ...guestAccount,
    ...hsContactManager,
    ...newApplyV2,
    ...vipCard,
    ...userBalance,
    ...productConver
}
