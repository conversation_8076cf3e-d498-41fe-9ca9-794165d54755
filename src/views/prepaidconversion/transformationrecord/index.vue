<template>
  <div class="transformationrecord">
    <dart-search :formSpan="24"
                 :gutter="20"
                 ref="searchForm1"
                 :searchOperation='false'
                 label-position="right"
                 :rules="rules"
                 :model="search">
      <template slot="search-form">

        <dart-search-item label="预付费卡号:"
                          prop="cpu_card_id">
          <el-input v-model="search.cpu_card_id"
                    placeholder=""
                    onkeyup="this.value = this.value.replace(/[^\d.]/g,'');"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号:"
                          prop="vehicle_code">
          <el-input v-model="search.vehicle_code"
                    placeholder=""
                    onkeyup="this.value = this.value.replace(/\s+/g,'');"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色："
                          prop="vehicle_color">
          <el-select v-model="search.vehicle_color"
                     placeholder="请选择"
                     clearable>
            <el-option v-for="item in licenseColorOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="用户编号:"
                          prop="customer_id">
          <el-input v-model="search.customer_id"
                    placeholder=""
                    onkeyup="this.value = this.value.replace(/[^\d.]/g,'');"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item>
          <div class="btn-wrapper">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onReSetHandle('searchForm1')">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table v-loading="loading"
                :data="tableData"
                border
                height="100%"
                style="width: 100%; margin-bottom: 20px"
                :row-style="{ height: '40px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '40px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="customerId"
                         align="center"
                         label="用户编号" />
        <el-table-column prop="cardNo"
                         align="center"
                         label="预付费卡号"
                         min-width="180" />
        <el-table-column prop="vehicleCode"
                         align="center"
                         label="车牌号码"
                         min-width="100" />
        <el-table-column prop="vehicleColor"
                         align="center"
                         label="车牌颜色">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.vehicleColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="cardType"
                         align="center"
                         label="卡类型">
          <template slot-scope="scope">
            {{scope.row.cardType == '23' ? '记账卡' : '储值卡'}}
          </template>
        </el-table-column>
        <el-table-column prop="srcGxCardType"
                         align="center"
                         label="原广西卡类型"
                         min-width="140">
          <template slot-scope="scope">
            {{getallGxCardType(scope.row.srcGxCardType)}}
          </template>
        </el-table-column>
        <el-table-column prop="gxCardType"
                         align="center"
                         label="现广西卡类型"
                         min-width="140">
          <template slot-scope="scope">
            {{getallGxCardType(scope.row.gxCardType)}}
          </template>
        </el-table-column>
        <el-table-column prop="createBranch"
                         align="center"
                         label="操作网点"
                         min-width="140" />
        <el-table-column prop="createOp"
                         align="center"
                         label="操作员" />
        <el-table-column prop="convertTime"
                         align="center"
                         label="操作时间"
                         min-width="160" />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination background
                     @current-change="changePage"
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import {
  getVehicleColor,
  getallGxCardType,
} from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      loading: false,
      search: {
        customerId: '',
        cpuCardId: '',
        vehicleCode: '',
        carNoColor: '',
        pageNum: 1,
        pageSize: 10,
      },
      licenseColorOption: licenseColorOption,
      tableData: [],
      total: 0,
      rules: {
        customer_id: [
          {
            min: 1,
            max: 8,
            message: '长度在 1 到 8 个字符',
            trigger: 'change',
          },
        ],
        cpu_card_id: [
          { min: 20, max: 20, message: '长度在 20 个字符', trigger: 'change' },
        ],
        vehicle_code: [
          {
            min: 1,
            max: 12,
            message: '长度在 1 到 12 个字符',
            trigger: 'change',
          },
        ],
        vehicle_color: [
          { min: 1, max: 1, message: '长度在 1  个字符', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.getlist()
  },
  methods: {
    getVehicleColor,
    getallGxCardType,
    onSearchHandle() {
      this.search.pageNum = 1
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.getlist()
        } else {
          return false
        }
      })
    },
    changePage(e) {
      this.search.pageNum = e
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.getlist()
        } else {
          return false
        }
      })
    },
    getlist() {
      let data = {
        customerId: this.search.customer_id ? this.search.customer_id : null,
        cpuCardId: this.search.cpu_card_id ? this.search.cpu_card_id : null,
        vehicleCode: this.search.vehicle_code ? this.search.vehicle_code : null,
        carNoColor: this.search.vehicle_color
          ? this.search.vehicle_color
          : null,
        pageNum: this.search.pageNum,
        pageSize: this.search.pageSize,
      }
      request({
        url: api.yffConvertedList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch(() => {})
    },
    onReSetHandle(formName) {
      this.$refs[formName].$children[0].resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.transformationrecord {
  height: 100%;
  padding: 20px;
  display: flex;
  flex-flow: column;
  .table {
    height: 0;
    flex: 1;
  }
  .pagination {
    padding: 10px 0;
    background-color: #fff;
    text-align: center;
  }
}
</style>