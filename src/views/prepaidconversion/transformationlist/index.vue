<template>
    <div class="page-list">
        <dart-search ref="searchForm1" :formSpan="24" :gutter="20" class="search" :searchOperation='false'
            label-position="right" :rules="rules" :model="search">
            <template slot="search-form">

                <dart-search-item label="预付费卡号:" prop="cpuCardId">
                    <el-input v-model="search.cpuCardId" placeholder="" oninput="value =value.replace(/[^\d.]/g,'');"
                        clearable></el-input>
                </dart-search-item>
                <dart-search-item label="车牌号:" prop="vehicleCode">
                    <el-input v-model="search.vehicleCode" placeholder="" oninput="value = value.replace(/\s+/g,'');"
                        clearable></el-input>
                </dart-search-item>
                <dart-search-item label="车牌颜色：" prop="carNoColor">
                    <el-select v-model="search.carNoColor" placeholder="请选择" clearable>
                        <el-option v-for="item in licenseColorOption" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </dart-search-item>
                <dart-search-item label="用户编号:" prop="customerId">
                    <el-input v-model="search.customerId" placeholder="" oninput="value = value.replace(/[^\d.]/g,'');"
                        clearable></el-input>
                </dart-search-item>
                <dart-search-item>
                    <div class="btn-wrapper">
                        <el-button type="primary" size="mini" native-type="submit"
                            @click="onSearchHandle('searchForm1')">搜索</el-button>

                        <el-button size="mini" @click="onReSetHandle('searchForm1')">重置</el-button>
                        <el-button type="primary" size="mini" @click="onOpenChange">转换</el-button>
                    </div>
                </dart-search-item>
            </template>
        </dart-search>
        <div class="table-box">
            <el-table v-loading="loading" :data="tableData" border height="100%"
                style="width: 100%; margin-bottom: 20px" :row-style="{ height: '40px' }"
                :cell-style="{ padding: '0px' }" :header-row-style="{ height: '40px' }"
                :header-cell-style="{ padding: '0px' }" @selection-change="handleSelectionChange" row-key="id">
                <el-table-column type="selection" width="55">
                </el-table-column>
                <el-table-column prop="customerId" align="center" label="用户编号" />
                <el-table-column prop="cardNo" align="center" label="预付费卡号" min-width="180" />
                <el-table-column prop="vehicleCode" align="center" label="车牌号码" min-width="100" />
                <el-table-column prop="vehicleColor" align="center" label="车牌颜色">
                    <template slot-scope="scope">
                        {{ getVehicleColor(scope.row.vehicleColor) }}
                    </template>
                </el-table-column>
                <el-table-column prop="cardType" align="center" label="卡类型">
                    <template slot-scope="scope">
                        {{ scope.row.cardType == '23' ? '记账卡' : '储值卡' }}
                    </template>
                </el-table-column>
                <el-table-column prop="gxCardType" align="center" label="广西卡类型" min-width="140">
                    <template slot-scope="scope">
                        {{ getallGxCardType(scope.row.gxCardType) }}
                    </template>
                </el-table-column>
                <el-table-column prop="openTime" align="center" label="发行日期" min-width="160" />
                <el-table-column prop="endDate" align="center" label="失效日期" min-width="120" />
                <el-table-column prop="updateBranch" align="center" label="操作网点" min-width="140" />
                <el-table-column prop="updateOp" align="center" label="操作员" />
                <el-table-column prop="updateTime" align="center" label="操作时间" min-width="160" />
                <!-- <el-table-column prop="gxCardType"
                         align="center"
                         label="操作">
          <template slot-scope="scope">
            <el-button size="mini"
                       type="primary"
                       @click="tochange(scope.row)">转换
            </el-button>
          </template>
        </el-table-column> -->
            </el-table>

        </div>
        <changebox v-if="changeDialog" :visible.sync='changeDialog' @golist="getCustomerInfo"
            :multipleSelection='multipleSelection' :changeobj='this.changeitem'
            :isAccount='searchCustomerInfo.user_client_type'></changebox>
    </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import changebox from './change.vue'
import request from '@/utils/request'
import api from '@/api/index'
import { licenseColorOption } from '@/common/const/optionsData'
import {
    getVehicleColor,
    getallGxCardType,
} from '@/common/method/formatOptions'
export default {
    components: {
        dartSearch,
        dartSearchItem,
        changebox,
    },
    data() {
        return {
            loading: false,
            changeDialog: false,
            changeitem: {},
            search: {
                customerId: '',
                cpuCardId: '',
                vehicleCode: '',
                carNoColor: '',
            },
            tableData: [],
            licenseColorOption: licenseColorOption,
            rules: {
                customerId: [
                    {
                        min: 1,
                        max: 8,
                        message: '长度在 1 到 8 个字符',
                        trigger: 'change',
                    },
                ],
                cpuCardId: [
                    { min: 20, max: 20, message: '长度在 20 个字符', trigger: 'change' },
                ],
                vehicleCode: [
                    {
                        min: 1,
                        max: 12,
                        message: '长度在 1 到 12 个字符',
                        trigger: 'change',
                    },
                ],
                carNoColor: [
                    { min: 1, max: 1, message: '长度在 1  个字符', trigger: 'change' },
                ],
            },
            multipleSelection: [],
            searchCustomerInfo: {},
        }
    },
    methods: {
        getVehicleColor,
        getallGxCardType,
        handleSelectionChange(row) {
            this.multipleSelection = row
            console.log(row, 'handleSelectionChange')
        },
        onOpenChange() {
            if (this.multipleSelection && this.multipleSelection.length == 0) {
                this.$message({
                    message: '请选择需要转换车辆',
                    type: 'warning',
                })
                return
            }
            if(!(this.searchCustomerInfo && this.searchCustomerInfo.customer_id)){
                this.$message({
                    message: '转换车辆信息异常，请重新查询',
                    type: 'warning',
                })
                return
            }
            this.changeDialog = true
        },
        onSearchHandle(formName) {
            if (
                this.search.customerId == '' &&
                this.search.cpuCardId == '' &&
                this.search.vehicleCode == '' &&
                this.search.carNoColor == ''
            ) {
                this.$message({
                    message: '请输入搜索条件',
                    type: 'warning',
                })
                return
            }
            if (this.search.vehicleCode == '' && this.search.carNoColor != '') {
                this.$message({
                    message: '请输入车牌',
                    type: 'warning',
                })
                return
            }
            this.$refs[formName].$children[0].validate((valid) => {
                if (valid) {
                    this.getCustomerInfo()
                } else {
                    return false
                }
            })
        },
        sendCustomerBizList(row) {
            let params = {
                customerId: row.customerId ? row.customerId : null,
            }
            request({
                url: api.customerBizList,
                method: 'post',
                data: params,
            }).then((res) => {
                if (res.code == 200) {
                    this.searchCustomerInfo = res.data[0];
                }
            })
        },
        getCustomerInfo() {
            let params = {
                customerId: this.search.customerId ? this.search.customerId : null,
                cpuCardId: this.search.cpuCardId ? this.search.cpuCardId : null,
                vehicleCode: this.search.vehicleCode ? this.search.vehicleCode : null,
                carNoColor: this.search.carNoColor ? this.search.carNoColor : null,
            }
            this.getlist(params)
        },
        getlist(parmas) {
            this.tableData = []
            this.loading = true
            request({
                url: api.yffCardList,
                method: 'post',
                data: parmas,
            })
                .then((res) => {
                    this.tableData = res.data
                    this.loading = false;
                    if (this.tableData && this.tableData.length) {
                        this.sendCustomerBizList(this.tableData[0]);
                    }
                })
                .catch(() => {
                    this.loading = false
                })
        },
        onReSetHandle(formName) {
            this.$refs[formName].$children[0].resetFields()
            this.tableData = []
        },
        tochange(item) {
            this.changeitem = item
            this.changeDialog = true
        },
    },
}
</script>

<style lang="scss" scoped>
.page-list {
    height: 100%;
    position: relative;
    padding: 0 20px;
    flex-flow: column;
    display: flex;
}

.page-list .search {
    margin-top: 20px;
}

.page-list .table-box {
    padding: 20px 20px 10px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
}

.page-list .pagination {
    padding: 0px 20px 10px 20px;
    background-color: #fff;
}
</style>