<template>
  <div>
    <el-dialog title="预付费转自营"
               :visible.sync="dialogFormVisible"
               center
               width="60%">

      <el-form ref="ruleForm"
               :model="formData"
               label-width="120px"
               :rules="rules"
               class="demo-ruleForm">
        <el-table :data="multipleSelection"
                  border
                  height="300"
                  style="width: 100%;margin-bottom: 10px;">
          <el-table-column prop="customerId"
                           align="center"
                           label="用户编号" />
          <el-table-column prop="cardNo"
                           align="center"
                           label="预付费卡号"
                           width="180" />
          <el-table-column prop="vehicleCode"
                           align="center"
                           label="车牌号码" />
          <el-table-column prop="vehicleColor"
                           align="center"
                           label="车牌颜色">
            <template slot-scope="scope">
              {{getVehicleColor(scope.row.vehicleColor)}}
            </template>
          </el-table-column>
          <el-table-column prop="gxCardType"
                           align="center"
                           label="广西卡类型"
                           min-width="120">
            <template slot-scope="scope">
              {{getallGxCardType(scope.row.gxCardType)}}
            </template>
          </el-table-column>
        </el-table>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="转换类型："
                          prop="newGxCardType">
              <el-radio-group v-model="formData.newGxCardType">
                <el-radio v-if="isAccount&&isAccount!='00'"
                          label="9">捷通日日通记账卡（客账扣款）</el-radio>
                <el-radio v-else
                          label="5">捷通日日通记账卡</el-radio>
                <el-radio label="7">捷通月月行记账卡</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="btnbox">
        <el-button @click="dialogFormVisible=false">取消</el-button>
        <el-button type="primary"
                   @click="submitForm('ruleForm')">转换</el-button>

      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import {
  getVehicleColor,
  getallGxCardType,
} from '@/common/method/formatOptions'
import { getToken } from '@/utils/auth'
import axios from 'axios'
export default {
  props: {
    changeobj: {
      type: Object,
      default: {},
    },
    multipleSelection: {
      type: Array,
      default: [],
    },
    visible: {
      type: Boolean,
      default: false,
    },
    isAccount: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableData: [],
      dialogFormVisible: false,
      innerVisible: false,
      formData: {
        newGxCardType: null,
      },
      rules: {
        newGxCardType: [
          { required: true, message: '请选择转换类型', trigger: 'blur' },
        ],
      },
      recursive: 0,
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible
    })
  },
  methods: {
    getVehicleColor,
    getallGxCardType,
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.tochange()
        } else {
          return false
        }
      })
    },
    onRecursiveHandle() {
      let _self = this
      if (this.recursive == this.multipleSelection.length) {
        this.recursive = 0
        this.endLoading()
        let item = this.multipleSelection.filter((item) => {
          return item.result && item.result.code != 200
        })
        console.log(item, '----')
        if (!item.length) {
          this.$message({
            message: '转换成功',
            type: 'success',
          })
          _self.$emit('golist')
          _self.dialogFormVisible = false
        } else {
          try {
            this.$message.error(`转换失败，${item[0].result.msg}`)
          } catch (error) {
            this.$message.error(`转换失败`)
          }
        }
        return
      }
      let params = this.multipleSelection[this.recursive]
      this.startLoading()
      this.sendRequest(params)
        .then((res) => {
          _self.$set(this.multipleSelection[this.recursive], 'result', res.data)
          _self.recursive++
          _self.onRecursiveHandle()
        })
        .catch((err) => {
          _self.$set(this.multipleSelection[this.recursive], 'result', {
            code: 999,
            msg: err.message || '系统错误',
          })
          _self.recursive++
          _self.onRecursiveHandle()
        })
    },
    sendRequest(param) {
      let url = process.env.VUE_APP_BASE_API + '/issue-web/' + api.yffConvert
      let config = {
        headers: {
          Authorization: getToken(),
        },
      }
      let params = {
        ...param,
        customer_id: param.customerId,
        cpuCardId: param.cardNo,
        vehicle_code: param.vehicleCode,
        vehicle_color: param.vehicleColor,
        newGxCardType: parseInt(this.formData.newGxCardType),
      }
      return new Promise((resolve, reject) => {
        axios
          .post(url, params, config)
          .then((res) => {
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    tochange() {
      let _self = this
      if (this.recursive) return
      if (this.formData.newGxCardType == 5) {
        this.titletext = '是否确认转换为捷通日日通记账卡'
      }
      if (this.formData.newGxCardType == 7) {
        this.titletext = '是否确认转换为捷通月月行记账卡'
      }
      if (this.formData.newGxCardType == 9) {
        this.titletext = '是否确认转换为捷通日日通记账卡（客账扣款）'
      }
      this.$confirm(this.titletext, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: (action) => {
          if (action == 'confirm') {
            _self.onRecursiveHandle()
          }
        },
      })
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.btnbox {
  width: 100%;
  text-align: center;
}
::v-deep .el-dialog {
  min-width: 650px;
}
::v-deep .el-col-12 {
  height: 40px;
}
// ::v-deep .el-dialog__body{
//   height: calc(100% - 104px);
// }
// ::v-deep .el-form{
//   padding: 20px;
//   height: 100% ;
//   overflow-y: auto ;
// }
// .foot {
//   margin: 20px 0;
//   text-align: center;
// }
// ::v-deep .el-form::-webkit-scrollbar {
//     width: 10px;
//     height: 10px;
// }
// ::v-deep .el-form::-webkit-scrollbar-thumb {
//     background-color: #409EFF;
//     border-radius: 4px;
// }
</style>