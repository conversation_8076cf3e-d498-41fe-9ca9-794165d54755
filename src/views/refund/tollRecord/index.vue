<template>
  <div class="toll-record">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌颜色：" prop="plateNumColor">
            <el-select v-model="search.plateNumColor" placeholder="请选择">
              <el-option
                v-for="item in typeList.carColors"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="plateNum">
            <el-input v-model="search.plateNum" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input v-model="search.cardNo" placeholder=""></el-input>
          </dart-search-item>

          <dart-search-item label="原始交易/行程ID" prop="passId">
            <el-input v-model="search.passId" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item
            label="退费记录更新时间起始："
            prop="updateRecordDateBegin"
          >
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.updateRecordDateBegin"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item
            label="退费记录更新时间截至："
            prop="updateRecordDateEnd"
          >
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.updateRecordDateEnd"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button
                size="mini"
                type="primary"
                @click="onExportHandle('one')"
              >
                <i class="el-icon-download"></i> 导出</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="550"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column
          prop="passId"
          align="center"
          min-width="250"
          label="原始交易/行程ID"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.passId }}
              </div>
              <span>{{ scope.row.passId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="180"
          label="ETC卡号"
        />
        <el-table-column
          prop="plateNum"
          align="center"
          min-width="100"
          label="车牌"
        />
        <el-table-column prop="plateNumColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.plateNumColor) }}
          </template>
        </el-table-column>
        <el-table-column prop="payBankName" align="center" label="代扣机构">
          <!-- <template slot-scope="scope">
            {{ getType(typeList.payOrgIds, scope.row.tollRecord.payOrgId) }}
          </template> -->
        </el-table-column>
        <el-table-column
          prop="chargeType"
          min-width="160"
          align="center"
          label="扣费类型"
        >
          <template slot-scope="scope">
            {{ getType(chargeTypeList, scope.row.chargeType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="transNum"
          align="center"
          label="原始交易笔数"
          min-width="110"
        />
        <el-table-column
          prop="transAmount"
          align="center"
          label="原始交易金额"
          min-width="110"
        >
          <template slot-scope="scope">
            {{ scope.row.transAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="deductionAmount"
          align="center"
          label="已请款金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.deductionAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="enterAmount"
          align="center"
          label="已入账金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.enterAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankAdvanceAmount"
          align="center"
          label="银行垫付金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.bankAdvanceAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="notEnterAmount"
          align="center"
          label="未入账金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.notEnterAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundedAmount"
          align="center"
          label="已退费金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.refundedAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateRecordDate"
          align="center"
          label="退费记录更新时间"
          min-width="180"
        />
        <el-table-column
          prop="updateDeductionDate"
          align="center"
          label="请款情况变动时间"
          min-width="180"
        />
        <el-table-column
          prop="updateRefundDate"
          align="center"
          label="退费情况变动时间"
          min-width="180"
        />
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[10, 20, 50]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      loading: false,
      center: 'center',
      total: '',
      search: {
        passId: '', //退费流水号
        plateNumColor: '', //车牌颜色
        plateNum: '', //车牌
        cardNo: '', //卡号
        updateRecordDateBegin: '', //退费情况变动开始时间
        updateRecordDateEnd: '', //退费情况变动结束时间
        pageNo: 1,
        pageSize: 20,
      },
      tableData: [],
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [], //退费状态字典
      },
      //交易类型 历史数据为null 1-ETC门架TAC交易 2-刷卡交易 3-ETC门架OBU交易 4-ETC门架图像交易 5-ETC门架拟合路径交易
      chargeTypeList: [
        {
          value: '1',
          label: 'ETC门架TAC交易',
        },
        {
          value: '2',
          label: '刷卡交易',
        },
        {
          value: '3',
          label: 'ETC门架OBU交易',
        },
        {
          value: '4',
          label: 'ETC门架图像交易',
        },
        {
          value: '5',
          label: 'ETC门架拟合路径交易',
        },
      ],
    }
  },
  created() {
    this.getTollRecordList()
    this.getTypeList()
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getTollRecordList() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.updateRecordDateBegin = params.updateRecordDateBegin
        ? moment(params.updateRecordDateBegin).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.updateRecordDateEnd = params.updateRecordDateEnd
        ? moment(params.updateRecordDateEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.$store
        .dispatch('refund/getTollRecordList', params)
        .then((res) => {
          this.loading = false
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //导出
    onExportHandle() {
      let params = {
        name: 'transactionMonitor',
        ...this.search,
        // ...this.searchTimeStr,
      }
      params.updateRecordDateBegin = params.updateRecordDateBegin
        ? moment(params.updateRecordDateBegin).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.updateRecordDateEnd = params.updateRecordDateEnd
        ? moment(params.updateRecordDateEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      delete params.pageNo
      delete params.pageSize
      console.log('导出入參', params)
      this.$store
        .dispatch('containerRefund/refundReport', params)
        .then((res) => {
          console.log('res=====', res)
          let url = res
          let decodeUrl = decode(url)
          console.log('res=====', decodeUrl)
          window.open(decodeUrl)
        })
        .catch((err) => {})
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch((err) => {})
    },
    changePage(page) {
      this.search.pageNo = page
      this.getTollRecordList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getTollRecordList()
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNo = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.search.pageNo = 1
      this.getTollRecordList()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.toll-record {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    // margin-left: -120px;
    margin-left: 40px;
    margin-top: 10px;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
    width: 150px !important;
    white-space: nowrap;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
