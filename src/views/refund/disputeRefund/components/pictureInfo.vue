<template>
  <div class="archives">
    <div class="archives-box">
      <template v-for="item in currnetList">
        <div class="archives-item"
             :key="item.key"
             v-if="item.file_url">
          <viewer class="imgbox">
            <img :src="item.file_url"
                 style="width: 100%" />
          </viewer>
          <span class="demonstration">{{item.lable}}</span>
        </div>
      </template>

    </div>
  </div>
</template>

<script>

export default {
  props: {
    pictureList: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      currnetList: [],
    }
  },
  watch: {
    pictureList(val) {
      this.currnetList = val
    },

  },
  created() {
    this.currnetList = this.pictureList
  },
  components: {},

  computed: {},

  methods: {

  },
}
</script>
<style >
.archives {
  background-color: #fff;
}
.archives-box {
  display: flex;
  flex-wrap: wrap;
  -moz-box-pack: start;
  -ms-box-pack: start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  justify-content: flex-start;
}
.archives-box .archives-item {
  margin-right: 20px;
  margin-bottom: 20px;
}
.archives-box .archives-item .demonstration {
  display: block;
  color: #8492a6;
  width: 100%;
  text-align: center;
  font-size: 15px;
}
.archives-box .archives-item .imgbox {
  width: 160px;
  height: 160px;
}
.archives-box .archives-item .imgbox img {
  width: 160px;
  height: 160px;
}
</style>