<template>
  <div>
    <el-table :data="tableData"
              border
              style="width: 100%">
      <el-table-column prop="createdBy"
                       label="操作人">
      </el-table-column>
      <el-table-column prop="opTime"
                       label="操作时间">
      </el-table-column>
      <el-table-column prop="content"
                       label="操作内容">
      </el-table-column>
      <el-table-column prop="handleType"
                       label="操作结果">
      </el-table-column>
      <el-table-column prop="remark"
                       label="备注">
      </el-table-column>
    </el-table>

  </div>
</template>

<script>

export default {
  props: {

    opLogList: {
      type: Array,
      default: []
    },
  },
  data() {
    return {
      tableData: []
    };
  },
  watch: {
    opLogList(val) {
      this.tableData = val;
    }
  },
  created() {
    this.tableData = this.opLogList;
  },
  components: {},

  computed: {},

  methods: {
   
  }
}
</script>
<style lang='sass'>
</style>