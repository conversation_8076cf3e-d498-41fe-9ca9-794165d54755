<template>
  <div>
    <el-dialog
      title="车辆选择"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="70%"
      center
    >
      <div class="account-list">
        <dart-search
          ref="selectVehicle"
          class="search"
          label-position="right"
          :model="search"
          :searchOperation="false"
          :formSpan="24"
          :gutter="20"
          :fontWidth="2"
        >
          <template slot="search-form" style="padding-left: 10px">
            <dart-search-item label="车牌号：" prop="carNo">
              <el-input v-model="search.carNo" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="车牌颜色：" prop="carColor">
              <el-select
                v-model="search.carColor"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in licenseColorOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="ETC卡号：" prop="cardNo">
              <el-input v-model="search.cardNo" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="OBU号：" prop="obuNo">
              <el-input v-model="search.obuNo" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="车型：" prop="carType">
              <el-select
                v-model="search.carType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in vehicleType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="绑定渠道：" prop="orgId">
              <el-select v-model="search.orgId" placeholder="请选择" clearable>
                <el-option
                  v-for="(item, index) in bindingBankTypeOptions"
                  :key="index"
                  :label="item.fieldNameDisplay"
                  :value="item.fieldValue"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="代扣渠道：" prop="payOrgId">
              <el-select
                v-model="search.payOrgId"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="(item, index) in bindingBankTypeOptions"
                  :key="index"
                  :label="item.fieldNameDisplay"
                  :value="item.fieldValue"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item :is-button="true" :span="8">
              <div class="g-flex g-flex-end">
                <el-button
                  type="primary"
                  size="mini"
                  @click="onSearchHandle"
                  native-type="submit"
                  >查询</el-button
                >
                <el-button size="mini" @click="onResetHandle">重置</el-button>
              </div>
            </dart-search-item>
          </template>
        </dart-search>
        <div class="table-box">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            height="300"
            @selection-change="handleSelectionChange"
            style="width: 100%"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="carNo" align="center" label="车牌号" />
            <el-table-column prop="carColor" align="center" label="车牌颜色">
              <template slot-scope="scope">
                {{ getVehicleColor(scope.row.carColor) }}
              </template>
            </el-table-column>
            <el-table-column prop="carType" align="center" label="车型">
              <template slot-scope="scope">
                {{ getVehicleType(scope.row.carType) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="cardNo"
              align="center"
              label="ETC卡号"
              min-width="110"
            >
              <template slot-scope="scope">
                <el-tooltip class="tooltip-item" effect="dark" placement="top">
                  <div slot="content">
                    {{ scope.row.cardNo }}
                  </div>
                  <span>{{ scope.row.cardNo }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="cardStatus" align="center" label="ETC卡状态">
              <template slot-scope="scope">
                <el-tooltip class="tooltip-item" effect="dark" placement="top">
                  <div slot="content">
                    {{ getCpuStatus(scope.row.cardStatus) }}
                  </div>
                  <span
                    :class="{
                      'span-red':
                        scope.row.cardStatus == 4 || scope.row.cardStatus == 12
                    }"
                    >{{ getCpuStatus(scope.row.cardStatus) }}</span
                  >
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="cardType"
              align="center"
              label="ETC卡类型"
              min-width="80"
            >
              <template slot-scope="scope">
                <el-tooltip class="tooltip-item" effect="dark" placement="top">
                  <div slot="content">
                    {{ getallGxCardType(scope.row.gxCardType) }}
                  </div>
                  <span>{{ getallGxCardType(scope.row.gxCardType) }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="obuNo" align="center" label="OBU号">
              <template slot-scope="scope">
                <el-tooltip class="tooltip-item" effect="dark" placement="top">
                  <div slot="content">
                    {{ scope.row.obuNo }}
                  </div>
                  <span>{{ scope.row.obuNo }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="orgIdStr" label="绑定渠道">
            </el-table-column>
            <el-table-column
              prop="payOrgIdStr"
              label="代扣渠道"
            ></el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="onSubmitHandle" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import {
  licenseColorOption,
  vehicleType,
  gxCardTypeOptions
} from '@/common/const/optionsData'
import {
  getVehicleColor,
  getallGxCardType,
  getCpuStatus,
  getVehicleType
} from '@/common/method/formatOptions'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    accountRow: {
      type: Object,
      default() {
        return {}
      }
    }
  },

  data() {
    return {
      licenseColorOption,
      gxCardTypeOptions,
      vehicleType,
      dialogVisible: false,
      search: {
        carColor: '', // 车牌颜色
        carNo: '', // 车牌号码
        carType: '', // 车辆类型
        cardNo: '', // 卡号
        cardType: '', // 卡类型
        gxCardType: '', //gx卡类型
        custMastId: '',
        obuNo: '', // obu序号
        orgId: '', //绑定渠道
        payOrgId: '' //代扣渠道
      },
      loading: false,
      center: 'center',
      tableData: [],
      multipleSelection: [],
      bindingBankTypeOptions: []
    }
  },

  watch: {
    visible: function(val) {
      this.init()
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  components: {
    dartSearch,
    dartSearchItem
  },

  computed: {},
  created() {
    this.init()
  },
  methods: {
    getVehicleColor,
    getallGxCardType,
    getCpuStatus,
    getVehicleType,
    init() {
      this.multipleSelection = []
      this.tableData = []
      for (let key in this.search) {
        this.search[key] = ''
      }
      this.$nextTick(() => {
        this.dialogVisible = this.visible
      })
      if (this.visible) {
        this.getDictionariesList()
        this.getSelectVehicleList()
      }
    },
    getDictionariesList() {
      this.$request({
        url: this.$interfaces.dictionaries,
        method: 'post'
      })
        .then(res => {
          if (res.code == 200 && res.data) {
            this.bindingBankTypeOptions = res.data.bindingBankType
          }
        })
        .catch(error => {})
    },
    onSearchHandle() {
      this.getSelectVehicleList()
    },
    onResetHandle() {
      this.$refs.selectVehicle.resetForm()
      this.getSelectVehicleList()
    },
    getSelectVehicleList() {
      this.search.custMastId = this.accountRow.custMastId
      this.$request({
        url: this.$interfaces.selectVehicleList,
        method: 'post',
        data: this.search
      })
        .then(res => {
          if (res.code == 200) {
            this.tableData = res.data && res.data.length ? res.data : []
          }
        })
        .catch(error => {})
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    onSubmitHandle() {
      this.$emit('on-submit', this.multipleSelection)
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss"  scoped>
.repayment {
  position: relative;
  .view-item {
    margin-bottom: 6px;
  }
  .captcha {
    width: 120px;
    height: 32px;
    margin-left: 10px;
  }
  .sendSMS {
    width: 120px;
    color: #409eff;
    margin-left: 10px;
  }
}
.photograph {
  position: absolute;
  top: 0;
  left: 50%;
}

.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.span-red {
  color: red;
}
</style>