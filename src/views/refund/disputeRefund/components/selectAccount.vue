<template>
  <div>
    <el-dialog title="快速录入"
               append-to-body
               :close-on-click-modal="false"
               :visible.sync="dialogVisible"
               width="70%"
               center>
      <div class="account-list">
        <dart-search ref="selectAccount"
                     class="search"
                     label-position="right"
                     :model="search"
                     :searchOperation='false'
                     :formSpan="24"
                     :gutter="20"
                     :fontWidth="2">
          <template slot="search-form"
                    style="padding-left: 10px">
            <dart-search-item label="车牌号："
                              prop="carNo">
              <el-input v-model="search.carNo"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="证件号："
                              prop="custIdNo">
              <el-input v-model="search.custIdNo"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="预留手机号："
                              prop="custMobile">
              <el-input v-model="search.custMobile"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item :is-button="true"
                              :span="24">
              <div class="g-flex g-flex-end">
                <el-button type="primary"
                           size='mini'
                           @click="onSearchHandle"
                           native-type="submit">查询</el-button>
                <el-button size='mini'
                           @click="onResetHandle">重置</el-button>
              </div>

            </dart-search-item>
          </template>
        </dart-search>
        <div class="table-box">
          <el-table v-loading="loading"
                    :data="tableData"
                    border
                    height="300"
                    @row-click="singleElection"
                    style="width: 100%">
            <el-table-column label=""
                             width="50">
              <template slot-scope="scope">
                <el-checkbox :value="templateSelection==scope.row.custMastId"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column prop="custName"
                             align="center"
                             label="用户名称" />
            <el-table-column prop="custType"
                             align="center"
                             label="账户类型">
              <template slot-scope="scope">
                {{ getcustomerType(scope.row.custType )}}
              </template>
            </el-table-column>
            <el-table-column prop="custContact"
                             align="center"
                             label="联系人" />
            <el-table-column prop="custIdNo"
                             align="center"
                             label="证件号">
            </el-table-column>
            <el-table-column prop="custMobile"
                             align="center"
                             label="预留手机号" />
          </el-table>
        </div>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="onSubmitHandle"
                   type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { getcustomerType } from '@/common/method/formatOptions'
var moment = require('moment')

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dialogVisible: false,
      search: {
        carNo: '',
        custIdNo: '',
        custMobile: ''
      },
      loading: false,
      center: 'center',
      tableData: [],
      templateSelection: '',
      currentAccountInfo: null,
    }
  },

  watch: {
    visible: function (val) {
      this.init();
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: {
    dartSearch,
    dartSearchItem
  },

  computed: {},
  created() {
    this.init();
  },
  methods: {
    getcustomerType,
    init() {
      this.templateSelection = '';
      this.currentAccountInfo = '';
      this.tableData = [];
      for (let key in this.search) {
        this.search[key] = '';
      }
      this.$nextTick(() => {
        this.dialogVisible = this.visible

      })
    },
    onSearchHandle() {
      this.getSelectAccountList()
    },
    onResetHandle() {
      this.$refs.selectAccount.resetForm();
      this.getSelectAccountList();
    },
    getSelectAccountList() {
      this.templateSelection = '';
      this.currentAccountInfo = '';
      this.$request({
        url: this.$interfaces.selectAccountList,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data && res.data.length ? res.data : [];
          }
        })
        .catch((error) => {

        })
    },
    singleElection(row, column, event) {
      if (event.target.tagName === 'INPUT') {
        return;
      }
      if (this.templateSelection == row.custMastId) {
        this.templateSelection = '';
        this.currentAccountInfo = '';
        return;
      }
      this.templateSelection = row.custMastId;
      this.currentAccountInfo = row;
    },
    onSubmitHandle() {

      if (this.currentAccountInfo && Object.keys(this.currentAccountInfo).length) {
        console.log(this.currentAccountInfo, 'this.currentAccountInfo')
        this.$emit('on-submit', this.currentAccountInfo);
        this.dialogVisible = false;
      } else {
        this.$message({
          message: '请选择用户',
          type: 'warning'
        });
      }
    },

  },
}
</script>
<style lang="scss"  scoped>
.repayment {
  position: relative;
  .view-item {
    margin-bottom: 6px;
  }
  .captcha {
    width: 120px;
    height: 32px;
    margin-left: 10px;
  }
  .sendSMS {
    width: 120px;
    color: #409eff;
    margin-left: 10px;
  }
}
.photograph {
  position: absolute;
  top: 0;
  left: 50%;
}
</style>