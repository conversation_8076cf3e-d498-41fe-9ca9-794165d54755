<template>
  <div>
    <el-descriptions class="margin-top"
                     :column="3"
                     :size="size"
                     border>
      <el-descriptions-item>
        <template slot="label">

          用户名称
        </template>
        {{accountRow.custName}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">

          账户类型
        </template>
        {{getcustomerType(accountRow.custType)}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">

          联系人
        </template>
        {{accountRow.custContact}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          证件号
        </template>
        {{accountRow.custIdNo}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">
          预留手机号
        </template>
        {{accountRow.custMobile}}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getcustomerType } from '@/common/method/formatOptions'
export default {
  props: {

    accountRow: {
      type: Object,
      default() {
        return {}
      }
    },
  },
  data() {
    return {
      size: ''
    };
  },

  components: {},

  computed: {},

  methods: {
    getcustomerType
  }
}
</script>
<style lang='sass'>
</style>