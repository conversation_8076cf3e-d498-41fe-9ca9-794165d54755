<template>
  <div>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column prop="carNo" align="center" label="车牌号" />
      <el-table-column prop="carColor" align="center" label="车牌颜色">
        <template slot-scope="scope">
          {{ getVehicleColor(scope.row.carColor) }}
        </template>
      </el-table-column>
      <el-table-column prop="carType" align="center" label="车型">
        <template slot-scope="scope">
          {{ getVehicleType(scope.row.carType) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="cardNo"
        align="center"
        label="ETC卡号"
        min-width="110"
      >
        <template slot-scope="scope">
          <el-tooltip class="tooltip-item" effect="dark" placement="top">
            <div slot="content">
              {{ scope.row.cardNo }}
            </div>
            <span>{{ scope.row.cardNo }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="cardStatus" align="center" label="ETC卡状态">
        <template slot-scope="scope">
          <el-tooltip class="tooltip-item" effect="dark" placement="top">
            <div slot="content">
              {{ getCpuStatus(scope.row.cardStatus) }}
            </div>
            <span
              :class="{ 'span-red': scope.row.cardStatus == 4 || scope.row.cardStatus == 12}"
              >{{ getCpuStatus(scope.row.cardStatus) }}</span
            >
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="cardType"
        align="center"
        label="ETC卡类型"
        min-width="80"
      >
        <template slot-scope="scope">
          <el-tooltip class="tooltip-item" effect="dark" placement="top">
            <div slot="content">
              {{ getallGxCardType(scope.row.gxCardType) }}
            </div>
            <span>{{ getallGxCardType(scope.row.gxCardType) }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="obuNo" align="center" label="OBU号">
        <template slot-scope="scope">
          <el-tooltip class="tooltip-item" effect="dark" placement="top">
            <div slot="content">
              {{ scope.row.obuNo }}
            </div>
            <span>{{ scope.row.obuNo }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="orgIdStr" label="绑定渠道"> </el-table-column>
      <el-table-column prop="payOrgIdStr" label="代扣渠道"></el-table-column>
    </el-table>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getallGxCardType,
  getCpuStatus,
  getVehicleType
} from '@/common/method/formatOptions'

export default {
  props: {
    vehicleList: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  watch: {
    vehicleList(val) {
      this.tableData = val
    }
  },
  created() {
    this.tableData = this.vehicleList
  },
  components: {},

  computed: {},

  methods: {
    getVehicleColor,
    getallGxCardType,
    getCpuStatus,
    getVehicleType
  }
}
</script>
<style lang="scss">
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.span-red {
  color: red;
}
</style>