<template>
  <div class="refund">
    <dart-search
      ref="searchForm1"
      class="search"
      label-position="right"
      :model="search"
      :formSpan="24"
      :gutter="20"
      :fontWidth="2"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="用户名称：" prop="custName">
          <el-input
            v-model="search.custName"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <dart-search-item label="证件号：" prop="custIdNo">
          <el-input
            v-model="search.custIdNo"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <dart-search-item label="预留手机号：" prop="custMobile">
          <el-input
            v-model="search.custMobile"
            placeholder=""
            clearable
            maxlength="11"
          ></el-input>
        </dart-search-item>
        <dart-search-item label="银行卡号：" prop="bankNo">
          <el-input v-model="search.bankNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号：" prop="carNo">
          <el-input v-model="search.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="录入方式：" prop="enterType">
          <el-select v-model="search.enterType" clearable placeholder="请选择">
            <el-option
              v-for="item in enterOption"
              :key="item.index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <template v-if="isCollapse">
          <dart-search-item label="审核状态：" prop="auditStatus">
            <el-select
              v-model="search.auditStatus"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusOption"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="生效时间：" prop="effectiveTime">
            <el-date-picker
              v-model="effectiveTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="失效时间：" prop="failureTime">
            <el-date-picker
              v-model="failureTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              :default-time="['00:00:00', '23:59:59']"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="更新开始时间：" prop="staUpdatedTime">
            <el-date-picker
              type="datetime"
              placeholder="选择时间"
              default-time="00:00:00"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              v-model="search.staUpdatedTime"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="更新结束时间：" prop="endUpdatedTime">
            <el-date-picker
              type="datetime"
              placeholder="选择时间"
              default-time="23:59:59"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              v-model="search.endUpdatedTime"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="审核开始时间：" prop="staAuditTime">
            <el-date-picker
              type="datetime"
              placeholder="选择审核时间"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              v-model="search.staAuditTime"
              default-time="00:00:00"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="审核结束时间：" prop="endAuditTime">
            <el-date-picker
              type="datetime"
              placeholder="选择审核时间"
              :value-format="'yyyy-MM-dd HH:mm:ss'"
              v-model="search.endAuditTime"
              default-time="23:59:59"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="账户类型：" prop="custType">
            <el-select v-model="search.custType" clearable placeholder="请选择">
              <el-option
                v-for="item in custOption"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="开户行：" prop="bankName">
            <el-input
              v-model="search.bankName"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
        </template>
        <dart-search-item :is-button="true" :span="24">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >查询</el-button
          >
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onAddHandle"
            >新增</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
          <el-button type="warning" size="mini" @click="exportHandle"
            >导出</el-button
          >
          <span class="collapse" v-if="!isCollapse" @click="isCollapse = true"
            >展开</span
          >
          <span class="collapse" v-else @click="isCollapse = false">收起</span>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column prop="custName" align="center" label="用户名称" />
        <el-table-column prop="custType" align="center" label="账户类型">
          <template slot-scope="scope">
            {{ getcustomerTypeFilter(scope.row.custType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="custIdNo"
          align="center"
          label="证件号"
          min-width="200"
        />
        <el-table-column prop="custContact" align="center" label="联系人">
        </el-table-column>
        <el-table-column
          prop="custMobile"
          align="center"
          label="预留手机号"
          min-width="120"
        />
        <el-table-column
          prop="bankAccount"
          align="center"
          label="银行账户名"
          min-width="120"
        >
        </el-table-column>
        <el-table-column prop="bankName" align="center" label="开户行" />
        <el-table-column
          prop="bankNo"
          align="center"
          label="银行卡号"
          min-width="200"
        />
        <el-table-column
          prop="carNos"
          align="center"
          label="关联车辆"
          min-width="120"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.carNos }}
              </div>
              <span>{{ scope.row.carNos }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="effectiveTime"
          align="center"
          label="生效时间"
          min-width="180"
        />
        <el-table-column
          prop="failureTime"
          align="center"
          min-width="180"
          label="失效时间"
        />
        <el-table-column
          prop="enterType"
          align="center"
          min-width="120"
          label="录入方式"
        >
          <template slot-scope="scope">
            <div>
              {{ enterTypeFilter(scope.row.enterType) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="updatedTime"
          align="center"
          min-width="180"
          label="信息更新时间"
        />
        <el-table-column
          prop="createdBy"
          align="center"
          min-width="150"
          label="信息录入/审核人员"
        />
        <el-table-column
          prop="auditStatus"
          align="center"
          min-width="120"
          label="审核状态"
        >
          <template slot-scope="scope">
            {{ auditStatusFilter(scope.row.auditStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="auditTime"
          align="center"
          min-width="180"
          label="审核时间"
        />
        <el-table-column
          align="left"
          header-align="center"
          width="220"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              @click="onAuditHandle(scope.row)"
              v-if="scope.row.auditStatus == 0"
              size="mini"
              type="primary"
              >审核</el-button
            >
            <el-button
              @click="tochange(scope.row)"
              v-if="scope.row.auditStatus != 0"
              type="mini"
              >修改</el-button
            >
            <el-button @click="onDetailHandle(scope.row)" type="mini"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-start">
      <el-pagination
        background
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>
    <!-- 新增银行账户 -->
    <dartSlide
      :visible.sync="flowSlideVisiable"
      :title="title"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <addAccount
        @on-submit="onAddAccountHandle"
        @on-cancel="flowSlideVisiable = false"
        v-if="flowSlideVisiable"
      ></addAccount>
    </dartSlide>
    <!-- 编辑银行账户 -->
    <dartSlide
      :visible.sync="editSlideVisiable"
      title="银行账户详情"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <editAccount
        v-if="editSlideVisiable"
        @on-refresh="onRefreshHandle"
        @on-cancel="editSlideVisiable = false"
        :detailId="detailId"
      ></editAccount>
    </dartSlide>
    <!-- 银行账户详情 -->
    <dartSlide
      :visible.sync="detailsSlideVisiable"
      title="银行账户详情"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <accountDetails
        v-if="detailsSlideVisiable"
        @on-refresh="onRefreshHandle"
        @on-cancel="detailsSlideVisiable = false"
        :handelType="handelType"
        :detailId="detailId"
      ></accountDetails>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import dartDateRange from '@/components/DateRange/date-range'
import dartSlide from '@/components/dart/Slide/index.vue'
import addAccount from './addAccount.vue'
import accountDetails from './accountDetails.vue'
import editAccount from './editAccount.vue'
import { getcustomerType } from '@/common/method/formatOptions'
import { queryReport } from '@/views/reportstatistics/components/service.js'

var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    dartDateRange,
    dartSlide,
    addAccount,
    accountDetails,
    editAccount
  },
  data() {
    return {
      loading: false,
      center: 'center',
      effectiveTime: [],
      failureTime: [],
      search: {
        custName: '',
        custIdNo: '',
        custMobile: '',
        bankNo: '',
        carNo: '',
        enterType: '',
        auditStatus: '',
        staEffectiveTime: '',
        endEffectiveTime: '',
        staFailureTime: '',
        endFailureTime: '',
        staUpdatedTime: '',
        endUpdatedTime: '',
        staAuditTime: '',
        endAuditTime: '',
        custType: '',
        bankName: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      isCollapse: false,
      defaultTime: ['00:00:00', '23:59:59'],
      enterOption: [
        {
          label: '客户自助',
          value: '0'
        },
        {
          label: '后台录入',
          value: '1'
        }
      ],
      statusOption: [
        {
          label: '待审核',
          value: '0'
        },
        {
          label: '审核通过',
          value: '1'
        },
        {
          label: '审核驳回',
          value: '2'
        },
        {
          label: '无需审核',
          value: '3'
        }
      ],
      custOption: [
        {
          label: '个人',
          value: '0'
        },
        {
          label: '企业',
          value: '1'
        }
      ],
      tableData: [],
      flowSlideVisiable: false, // 新增弹窗
      detailsSlideVisiable: false, // 详情弹窗
      editSlideVisiable: false, // 编辑弹窗
      title: '新增银行账户',
      detailId: '',
      handelType: '' // 详情类型 audit  detail
    }
  },
  created() {
    this.getList()
  },
  computed: {},

  methods: {
    getList() {
      this.loading = true
      console.log(this.effectiveTime)
      this.search.staEffectiveTime =
        this.effectiveTime && this.effectiveTime[0]
          ? moment(this.effectiveTime[0]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      this.search.endEffectiveTime =
        this.effectiveTime && this.effectiveTime[1]
          ? moment(this.effectiveTime[1]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      this.search.staFailureTime =
        this.failureTime && this.failureTime[0]
          ? moment(this.failureTime[0]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      this.search.endFailureTime =
        this.failureTime && this.failureTime[1]
          ? moment(this.failureTime[1]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      this.search.staUpdatedTime = this.search.staUpdatedTime
        ? moment(this.search.staUpdatedTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.search.endUpdatedTime = this.search.endUpdatedTime
        ? moment(this.search.endUpdatedTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.search.staAuditTime = this.search.staAuditTime
        ? moment(this.search.staAuditTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.search.endAuditTime = this.search.endAuditTime
        ? moment(this.search.endAuditTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.tableData = []
      this.$request({
        url: this.$interfaces.hsContactManagerList,
        method: 'post',
        data: this.search
      })
        .then(res => {
          console.log(res)
          this.loading = false
          this.tableData = res.data.records
          this.total = res.data.total
          this.search.pageNum = res.data.current
          this.search.pageSize = res.data.size
        })
        .catch(error => {
          this.loading = false
          console.log('err', error)
        })
    },
    onSearchHandle() {
      this.getList()
    },
    onAddHandle() {
      this.title = '新增银行账户'
      this.flowSlideVisiable = true
    },
    tochange(row) {
      this.detailId = row.id
      this.editSlideVisiable = true
    },
    onAuditHandle(row) {
      this.detailId = row.id
      this.detailsSlideVisiable = true
      this.handelType = 'audit'
    },
    onDetailHandle(row) {
      this.detailId = row.id
      this.title = '银行账户详情'
      this.handelType = 'detail'
      this.detailsSlideVisiable = true
    },
    changePage(page) {
      this.search.pageNum = page
      this.onSearchHandle()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.effectiveTime = []
      this.failureTime = []
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    onAddAccountHandle() {
      this.flowSlideVisiable = false
      this.onRefreshHandle()
    },
    onRefreshHandle() {
      this.flowSlideVisiable = false
      this.detailsSlideVisiable = false
      this.editSlideVisiable = false
      this.onSearchHandle()
    },
    auditStatusFilter(val) {
      let label = ''
      for (let i = 0; i < this.statusOption.length; i++) {
        if (this.statusOption[i].value == val) {
          label = this.statusOption[i].label
        }
      }
      return label
    },
    enterTypeFilter(val) {
      let label = ''
      for (let i = 0; i < this.enterOption.length; i++) {
        if (this.enterOption[i].value == val) {
          label = this.enterOption[i].label
        }
      }
      return label
    },
    getcustomerTypeFilter(val) {
      return getcustomerType(val)
    },
    /**
     * 导出
     */
    exportHandle() {
      let { staUpdatedTime, endUpdatedTime } = this.search
      if (!staUpdatedTime || !endUpdatedTime) {
        this.$message.error('请填写更新开始时间和结束时间！')
        return
      }
      if (moment(staUpdatedTime).isAfter(endUpdatedTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      let params = {
        name: 'refundBankAccountExportReport',
        staUpdatedTime,
        endUpdatedTime
      }
      console.log(params)

      queryReport(params)
    }
  }
}
</script>
<style lang="scss" scoped>
.refund {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.refund .search {
  margin-top: 20px;
}
.refund .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.refund .pagination {
  margin: 10px 0;
}

.refund .collapse {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
  font-size: 14px;
}

.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
