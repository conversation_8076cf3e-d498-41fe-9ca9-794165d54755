<template>
  <div class="account-page">
    <div class="account-page_bd">
      <div class="down">
        <div class="thetable">
          <div class="title">用户基础信息</div>
          <div class="downnav">
            <accountInfo :accountRow="accountRow"></accountInfo>
          </div>
        </div>
      </div>
      <div class="down">
        <div class="thetable">
          <div class="title">关联车辆列表</div>
          <div class="downnav">
            <associatedVehicle :vehicleList="vehicleList"></associatedVehicle>
          </div>
        </div>
      </div>
      <div class="down">
        <div class="thetable" style="padding-bottom: 0px">
          <div class="title">银行账户信息</div>
          <div class="downnav">
            <el-form label-width="100px" size="small">
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="银行账户名">
                    <div>{{ detail.bankAccount }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开户行">
                    <div>{{ detail.bankName }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="银行卡号">
                    <div>{{ detail.bankNo }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="生效时间">
                    <div>{{ detail.effectiveTime  }}</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="失效时间">
                    <div>{{ detail.failureTime }}</div>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item label="附件">
                    <pictureInfo :pictureList="pictureList"></pictureInfo>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>
      <div class="down" v-if="detail.auditStatus != 3">
        <template>
          <div class="thetable" style="padding-bottom: 0px">
            <div class="title">审核信息</div>
            <!-- 审核操作 -->
            <div class="downnav" v-if="isAudit">
              <el-form label-width="100px" size="small" :model="formData">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="审核结果">
                      <el-select
                        v-model="formData.auditStatus"
                        placeholder="请选择"
                        @change="auditStatusChange"
                        clearable
                      >
                        <el-option
                          v-for="item in auditStatusOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="不通过的原因">
                      <el-select
                        v-model="formData.failReason"
                        :disabled="formData.auditStatus == 1"
                        placeholder="请选择"
                        clearable
                      >
                        <el-option
                          v-for="item in failReasonOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.label"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="24">
                    <el-form-item>
                      <div slot="label">审核备注</div>
                      <el-input
                        type="textarea"
                        rows="3"
                        v-model="formData.remarks"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
            <!-- 审核结果 -->
            <div class="downnav" v-else>
              <el-form label-width="100px" size="small">
                <el-row :gutter="24">
                  <el-col :span="8">
                    <el-form-item label="审核结果">
                      <template v-if="detail.auditStatus == 0">待审核</template>
                      <template v-if="detail.auditStatus == 1"
                        >审核通过</template
                      >
                      <template v-if="detail.auditStatus == 2"
                        >审核驳回</template
                      >
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="detail.auditStatus == 2">
                    <el-form-item label="不通过的原因">
                      {{ detail.failReason }}
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="24">
                    <el-form-item label="审核备注">
                      {{ detail.remarks }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
        </template>
      </div>
      <div class="down">
        <div class="thetable">
          <div class="title">操作记录</div>
          <div class="downnav">
            <operatingLog :opLogList="opLogList"></operatingLog>
          </div>
        </div>
      </div>
    </div>
    <div class="page-drawer-footer" v-if="isAudit">
      <el-button size="medium" @click="onCancelHandle">返回</el-button>
      <el-button size="medium" @click="onAuditHandle" type="primary"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import accountInfo from './components/accountInfo'
import associatedVehicle from './components/associatedVehicle'
import pictureInfo from './components/pictureInfo'
import selectAccount from './components/selectAccount'
import selectVehicle from './components/selectVehicle'
import operatingLog from './components/operatingLog'
export default {
  props: {
    detailId: [String, Number],
    handelType: {
      type: String,
      default: 'detail',
    },
  },
  data() {
    return {
      selectAccountVisible: false,
      selectVehicleVisible: false,
      fileUUID: '',
      handleType: '1',
      pictureList: [
        // 档案列表
        {
          lable: '银行卡',
          photo_code: '',
          file_url: '',
          file_serial: '',
          isShow: true,
          key: 'bankPhotoUrl',
        },
        {
          lable: '附件',
          photo_code: '',
          file_url: '',
          file_serial: '',
          isShow: true,
          key: 'attorneyUrl',
        },
      ],
      accountRow: {
        // 用户信息
        custContact: '',
        custIdNo: '',
        custMastId: '',
        custMobile: '',
        custName: '',
        custType: '',
      },
      vehicleList: [], // 车辆信息
      opLogList: [], // 操作日志
      isLoading: false,
      failReasonOptions: [
        { value: '1', label: '授权材料不清晰' },
        { value: '2', label: '授权材料有误' },
        { value: '3', label: '银行账号有误' },
        { value: '4', label: '开户行信息有误' },
      ],
      auditStatusOptions: [
        { value: '1', label: '审核通过' },
        { value: '2', label: '审核驳回' },
      ],
      detail: {}, // 详情
      formData: {
        auditStatus: '',
        failReason: '',
        id: '',
        remarks: '',
      },
      isAudit: false,
    }
  },

  components: {
    accountInfo,
    associatedVehicle,
    selectAccount,
    selectVehicle,
    operatingLog,
    pictureInfo,
  },

  computed: {},
  created() {
    this.isAudit = false
    this.getDetails()
    if (this.handelType == 'audit') {
      this.isAudit = true
    }
  },
  methods: {
    getDetails() {
      this.$request({
        url: this.$interfaces.hsContactManagerDetails,
        method: 'post',
        data: {
          id: this.detailId,
        },
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.vehicleList = res.data.listCar
            this.opLogList = res.data.opLogList
            this.detail = res.data
            for (let key in this.accountRow) {
              this.accountRow[key] = res.data[key] || this.accountRow[key]
            }
            for (let i = 0; i < this.pictureList.length; i++) {
              if (this.detail[this.pictureList[i]['key']]) {
                this.pictureList[i]['file_url'] =
                  this.detail[this.pictureList[i]['key']]
              }
            }
          }
        })
        .catch((error) => {})
    },
    auditStatusChange(val) {
      if (val == 1) {
        this.formData.failReason = ''
      }
    },
    validForm() {
      if (!this.formData.auditStatus) {
        this.$message({
          message: '请选择审核结果',
          type: 'warning',
        })
        return false
      }
      if (this.formData.auditStatus == 2) {
        if (!this.formData.failReason) {
          this.$message({
            message: '请选择不通过的原因',
            type: 'warning',
          })
          return false
        }
      }
      return true
    },
    onAuditHandle() {
      if (!this.validForm()) return
      this.formData.id = this.detail.id
      if (this.isLoading) return
      this.isLoading = true

      this.$request({
        url: this.$interfaces.examineContactManager,
        method: 'post',
        data: this.formData,
      })
        .then((res) => {
          this.isLoading = false
          if (res.code == 200) {
            this.$message({
              message: '审核成功',
              type: 'success',
            })
            this.$emit('on-refresh')
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((error) => {
          this.isLoading = false
        })
    },
    onCancelHandle() {
      this.$emit('on-cancel')
    },
  },
}
</script>
<style lang="scss" scoped>
.down {
  padding: 10px 15px;
  .thetable {
    background-color: #fff;
    padding-bottom: 20px;
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .downnav {
    padding: 0px 24px;
    .itembox {
      line-height: 40px;
      .item {
        margin: auto;
        font-size: 14px;
        span {
          display: inline-block;
          padding-right: 10px;
          color: #606266;
          font-weight: 600;
          min-width: 100px;
          text-align: right;
        }
      }
    }
  }
}
.account-page {
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  position: absolute;
  z-index: 10;
  overflow-y: auto;
  flex-flow: column;
  display: flex;
}
.account-page_bd {
  flex: 1;
  background: #f5f7f9;
  overflow-y: scroll;
}
.page-drawer-footer {
  width: 100%;
  height: 53px;
  border-top: 1px solid #e8e8e8;
  padding-right: 15px;
  text-align: right;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>