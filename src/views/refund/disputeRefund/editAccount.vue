<template>
  <div class="account-page">
    <div class="account-page_bd">
      <div class="down">
        <div class="thetable">
          <div class="title">用户基础信息</div>
          <div class="downnav">
            <accountInfo :accountRow="accountRow"></accountInfo>
            <!-- <div style="width:100%;margin-top: 10px;">
              <el-button size="medium"
                         icon="el-icon-plus"
                         style="width:100%;border-style: dashed;"
                         @click="selectAccountVisible=true">快速录入</el-button>
            </div>-->
          </div>
        </div>
      </div>
      <div class="down">
        <div class="thetable">
          <div class="title">关联车辆列表</div>
          <div class="downnav">
            <associatedVehicle :vehicleList="vehicleList"></associatedVehicle>
            <div style="width: 100%; margin-top: 10px">
              <el-button
                size="medium"
                icon="el-icon-plus"
                style="width: 100%; border-style: dashed"
                @click="onVehicleVisibleHandle"
                >选择车辆</el-button
              >
            </div>
          </div>
        </div>
      </div>
      <div class="down">
        <div class="thetable">
          <div class="title">银行账户信息</div>
          <div class="downnav">
            <el-form label-width="100px" size="small" :model="formData">
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="银行账户名">
                    <el-input v-model="formData.bankAccount"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开户行">
                    <el-input v-model="formData.bankName"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="银行卡号">
                    <el-input v-model="formData.bankNo"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="8">
                  <el-form-item label="有效期至">
                    <el-date-picker
                      v-model="formData.failureTime"
                      type="datetime"
                      placeholder="选择日期时间"
                      align="right"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item label="附件">
                    <photograph
                      :pictureList="pictureList"
                      :handleType="handleType"
                      :fileUUID="fileUUID"
                      @on-change="onPhotographChange"
                    ></photograph>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <div class="page-drawer-footer">
      <el-button size="medium" @click="onCancelHandle">返回</el-button>
      <el-button size="medium" @click="onEditHandle" type="primary"
        >保存</el-button
      >
    </div>
    <!-- 选择用户 -->
    <selectAccount
      :visible.sync="selectAccountVisible"
      @on-submit="selectAccountSubmit"
    ></selectAccount>
    <!-- 选择车辆 -->
    <selectVehicle
      :visible.sync="selectVehicleVisible"
      :accountRow="accountRow"
      @on-submit="selectVehicleSubmit"
    ></selectVehicle>
  </div>
</template>

<script>
import { findLast } from 'lodash'
import accountInfo from './components/accountInfo'
import associatedVehicle from './components/associatedVehicle'
import photograph from './components/photograph'
import selectAccount from './components/selectAccount'
import selectVehicle from './components/selectVehicle'
var moment = require('moment')
export default {
  props: {
    detailId: [String, Number],
  },
  data() {
    return {
      selectAccountVisible: false,
      selectVehicleVisible: false,
      formData: {
        attorneyUrl: '',
        attorneyUrlCode: '', //档案code
        bankAccount: '',
        bankName: '',
        bankNo: '',
        effectiveTime: '',
        failureTime: '',
        id: '',
        listCar: [],
        userManagerDTO: {},
      },
      fileUUID: '',
      handleType: '1',
      pictureList: [
        {
          lable: '附件',
          photo_code: 'attorneyUrl',
          file_url: '',
          file_serial: '',
          isShow: true,
          key: 'attorneyUrl',
        },
      ],
      accountRow: {
        custContact: '',
        custIdNo: '',
        custMastId: '',
        custMobile: '',
        custName: '',
        custType: '',
      },
      vehicleList: [],
      isLoading: false,
      details: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        },
      },
    }
  },

  components: {
    accountInfo,
    associatedVehicle,
    photograph,
    selectAccount,
    selectVehicle,
  },

  computed: {},
  created() {
    this.getDetails()
  },
  methods: {
    selectAccountSubmit(row) {
      this.accountRow = row
      this.formData.userManagerDTO = row
      if (this.details.custMastId != row.custMastId) {
        this.vehicleList = []
        this.formData.listCar = []
      }
    },
    selectVehicleSubmit(data) {
      // this.vehicleList = this.removeDuplicate(this.vehicleList)
      this.vehicleList = this.vehicleList.concat(data)
      this.vehicleList = this.getDifferent(this.vehicleList, 'cardMastId')
      this.formData.listCar = this.vehicleList
      console.log(this.vehicleList)
    },
    onVehicleVisibleHandle() {
      if (this.accountRow && this.accountRow.custMastId) {
        this.selectVehicleVisible = true
      } else {
        this.$message({
          message: '请选择用户',
          type: 'warning',
        })
      }
    },
    onPhotographChange(data) {
      // this.formData.attorneyUrl = data.fileUrl || ''
      //档案优化
      this.formData.attorneyUrl = data.code || ''
      if (this.detailId) {
        this.formData.attorneyUrlCode = data.code || ''
      }
      for (let i = 0; i < this.pictureList.length; i++) {
        if (this.pictureList[i].photo_code == data.photo_code) {
          this.$set(this.pictureList[i], 'file_url', data.fileUrl || '')
        }
      }
    },
    showTips(callback) {
      let failureTime = this.formData.failureTime
      let nowTime = moment(new Date()).format('YYYY-MM-DD')
      nowTime = moment(nowTime).add(1, 'year').format('YYYY-MM-DD')
      if (failureTime) {
        failureTime = moment(failureTime).format('YYYY-MM-DD HH:mm:ss')
        if (moment(failureTime).isBefore(nowTime)) {
          this.$confirm(
            '当前设置的账户信息有效期不足一年，确认是否继续提交?',
            '提示',
            {
              confirmButtonText: '继续提交',
              cancelButtonText: '返回修改',
              type: 'warning',
              callback: (action) => {
                if (action == 'confirm') {
                  callback()
                }
              },
            }
          )
        } else {
          callback()
        }
      }
    },
    formatParams() {
      let params = JSON.parse(JSON.stringify(this.formData))
      // params.effectiveTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      // if (!params.failureTime) {
      //   params.failureTime = moment(params.effectiveTime)
      //     .add(1, 'year')
      //     .format('YYYY-MM-DD HH:mm:ss')
      // } else {
      //   params.failureTime = moment(params.failureTime).format(
      //     'YYYY-MM-DD HH:mm:ss'
      //   )
      // }
      if (params.failureTime) {
        params.failureTime = moment(params.failureTime).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      params.userManagerDTO = this.accountRow
      return params
    },
    validForm() {
      if (!(this.accountRow && Object.keys(this.accountRow).length)) {
        this.$message({
          message: '请选择用户',
          type: 'warning',
        })
        return false
      }
      if (!(this.vehicleList && this.vehicleList.length)) {
        this.$message({
          message: '请选择车辆列表',
          type: 'warning',
        })
        return
      }
      if (!this.formData.bankAccount) {
        this.$message({
          message: '请输入银行账户名',
          type: 'warning',
        })
        return
      }
      if (!this.formData.bankName) {
        this.$message({
          message: '请输入开户行',
          type: 'warning',
        })
        return
      }
      if (!this.formData.bankNo) {
        this.$message({
          message: '请输入银行卡号',
          type: 'warning',
        })
        return
      }
      if (!this.formData.failureTime) {
        this.$message({
          message: '请输入有效期',
          type: 'warning',
        })
        return
      }
      // if (this.formData.failureTime) {
      //   let curTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      //   let nextTime = moment(curTime)
      //     .add(1, 'year')
      //     .format('YYYY-MM-DD HH:mm:ss')
      //   console.log('curTime', curTime, 'nextTime', nextTime)
      //   let flag = moment(this.formData.failureTime).isBetween(
      //     curTime,
      //     nextTime,
      //     'second'
      //   )
      //   if (!flag) {
      //     this.$message({
      //       message: '请填写有效日期范围',
      //       type: 'warning',
      //     })
      //     return
      //   }
      // }
      if (this.formData.bankAccount != this.accountRow.custName) {
        if (!this.formData.attorneyUrl) {
          this.$message({
            message: '请上传附件图片',
            type: 'warning',
          })
          return
        }
      }
      return true
    },
    onEditHandle() {
      if (!this.validForm()) return
      if (this.isLoading) return
      this.showTips(() => {
        this.isLoading = true
        this.$request({
          url: this.$interfaces.editHsContactManager,
          method: 'post',
          data: this.formatParams(),
        })
          .then((res) => {
            this.isLoading = false
            if (res.code == 200) {
              this.$message({
                message: '修改成功',
                type: 'success',
              })
              this.$emit('on-refresh')
            } else {
              this.$message.error(res.msg)
            }
          })
          .catch((error) => {
            this.isLoading = false
          })
      })
    },
    onCancelHandle() {
      this.$emit('on-cancel')
    },
    getDetails() {
      this.$request({
        url: this.$interfaces.hsContactManagerDetails,
        method: 'post',
        data: {
          id: this.detailId,
        },
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            let result = res.data
            this.details = result
            this.vehicleList = res.data.listCar
            for (let key in this.formData) {
              this.formData[key] = res.data[key] || this.formData[key]
            }
            for (let key in this.accountRow) {
              this.accountRow[key] = res.data[key] || this.accountRow[key]
            }
            for (let i = 0; i < this.pictureList.length; i++) {
              if (result[this.pictureList[i]['key']]) {
                this.pictureList[i]['file_url'] =
                  result[this.pictureList[i]['key']]
              }
            }
          }
        })
        .catch((error) => {})
    },
    /**
     * 数组去重
     * @param arr 去重数组
     * @param field 根据字段去重，不传则普通数组去重
     */
    getDifferent(arr, field) {
      let output = []
      if (!field) {
        output = Array.from(new Set(arr))
      } else {
        arr.forEach((item) => {
          if (!output.find((outItem) => outItem[field] == item[field])) {
            output.push(item)
          }
        })
      }
      return output
    },
  },
}
</script>
<style lang="scss" scoped>
.down {
  padding: 10px 15px;
  .thetable {
    background-color: #fff;
    padding-bottom: 20px;
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .downnav {
    padding: 0px 24px;
    .itembox {
      line-height: 40px;
      .item {
        margin: auto;
        font-size: 14px;
        span {
          display: inline-block;
          padding-right: 10px;
          color: #606266;
          font-weight: 600;
          min-width: 100px;
          text-align: right;
        }
      }
    }
  }
}
.account-page {
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  position: absolute;
  z-index: 10;
  overflow-y: auto;
  flex-flow: column;
  display: flex;
}
.account-page_bd {
  flex: 1;
  background: #f5f7f9;
  overflow-y: scroll;
}
.page-drawer-footer {
  width: 100%;
  height: 53px;
  border-top: 1px solid #e8e8e8;
  padding-right: 15px;
  text-align: right;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>