<template>
  <div class="add-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title">退费进度新建</span>
        </div>
      </template>
      <div class="desc">
        <span
          >资金退回方式：[1-储值卡补卡额 2-退回ETC主账户 3-退回ETC副账户
          4-银行原路退回 5-银行转账 6-支付宝 7-微信 9-其它 10-客户放弃]</span
        >
      </div>
      <div class="desc-item">
        <el-descriptions
          labelClassName="desc-1"
          :column="3"
          border
          style="border-bottom: 0"
        >
          <el-descriptions-item contentStyle="width:450px">
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>退费交易编号：</span>
            </template>
            <el-input v-model="formData.refundId" placeholder=""></el-input
          ></el-descriptions-item>
          <el-descriptions-item
            contentClassName="selector"
            label="资金退回方式："
            ><el-select v-model="formData.refundPath" placeholder="请选择">
              <el-option
                v-for="item in refundPath"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              /> </el-select
          ></el-descriptions-item>
          <el-descriptions-item
            contentClassName="selector"
            label="退费最终状态："
          >
            <el-select v-model="formData.refundStatus" placeholder="请选择">
              <el-option
                v-for="item in refundStatus"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions labelClassName="desc-2" :column="3" border>
          <el-descriptions-item
            contentClassName="desc-2"
            label="一阶段退费完成时间："
          >
            <el-date-picker
              v-model="formData.refundFirstTime"
              type="datetime"
              placeholder="选择日期时间"
              :default-time="getTime()"
            >
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item
            contentClassName="desc-2"
            label="二阶段退费完成时间："
          >
            <el-date-picker
              v-model="formData.refundSecondTime"
              type="datetime"
              placeholder="选择日期时间"
              :default-time="getTime()"
            >
            </el-date-picker
          ></el-descriptions-item>
        </el-descriptions>
        <el-descriptions labelClassName="desc-3" :column="1" border>
          <el-descriptions-item label="退费失败原因：">
            <textarea
              class="textarea"
              name="suggestion"
              id=""
              cols="100"
              rows="10"
              placeholder="请输入退费失败原因"
              v-model="formData.refundReason"
            ></textarea>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions labelClassName="desc-5" :column="4" border>
          <template
            v-if="
              formData.refundPath == '01' ||
              formData.refundPath == '02' ||
              formData.refundPath == '03'
            "
          >
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC内部流水号："
            >
              <el-input placeholder="" v-model="etcItem.etcTransNum"></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC主账户号："
            >
              <el-input
                placeholder=""
                v-model="etcItem.etcFirstAccountNum"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC副账户号："
            >
              <el-input
                placeholder=""
                v-model="etcItem.etcSecondAccountNum"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item contentClassName="desc-5" label="圈存时间：">
              <el-date-picker
                v-model="etcItem.firstTransferTime"
                type="datetime"
                placeholder="选择日期时间"
                :default-time="getTime()"
              >
              </el-date-picker>
            </el-descriptions-item>
          </template>
          <!-- 银行 -->
          <template
            v-if="formData.refundPath == '04' || formData.refundPath == '05'"
          >
            <el-descriptions-item contentClassName="desc-5" label="银行名称：">
              <el-input v-model="bankItem.bank" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item contentClassName="desc-5" label="银行账号：">
              <el-input v-model="bankItem.bankAccount" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="银行交易流水号："
            >
              <el-input v-model="bankItem.bankTransNum" placeholder=""></el-input>
            </el-descriptions-item>
          </template>
          <!-- 支付宝 -->
          <template v-if="formData.refundPath == '06'">
            <el-descriptions-item
              contentClassName="desc-5"
              label="支付宝账号："
            >
              <el-input
                v-model="alipayItem.alipayNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="支付宝交易流水号："
            >
              <el-input
                v-model="alipayItem.aliTransNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
          <!-- 微信 -->
          <template v-if="formData.refundPath == '07'">
            <el-descriptions-item contentClassName="desc-5" label="微信账号：">
              <el-input v-model="wxItem.wechatNum" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="微信交易流水号："
            >
              <el-input
                v-model="wxItem.wechatAccount"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
          <!-- 其他 -->
          <template v-if="formData.refundPath == '09'">
            <el-descriptions-item contentClassName="desc-5" label="其他账号：">
              <el-input
                v-model="otherItem.remarkAccountNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="其他交易流水号："
            >
              <el-input
                v-model="otherItem.remarkTransNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div
          v-if="
            formData.refundPath == '01' ||
            formData.refundPath == '02' ||
            formData.refundPath == '03'
          "
        >
          <span class="tips">圈存时间:储值卡补卡额才填</span>
        </div>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="add()">添加</el-button>
        <el-button @click="close()">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { typeAdapter } from '@/common/method/formatOptions'
import { refundPath } from '@/common/const/optionsData'
var moment = require('moment')
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    refundOrderId: {
      type: Number,
    },
    // typeList: {
    //   type: Object,
    //   default: {},
    // },
  },
  data() {
    return {
      refundPath,
      isFullscreen: false,
      showLoading: false,
      nowTime: new Date(),
      formData: {
        refundId: '', //退费交易编号
        refundPath: '', //资金回退方式
        refundStatus: '', //退费状态
        refundFirstTime: '', //一阶段退费完成时间
        refundSecondTime: '', //二阶段退费完成时间
        refundReason: '', //退费失败原因
      },
      etcItem: {
        etcTransNum: '', //ETC内部流水号
        etcFirstAccountNum: '', //ETC主账户号
        etcSecondAccountNum: '', //ETC副账户号
        firstTransferTime: '', //一阶段圈存完成时间
      },
      bankItem: {
        bank: '', //银行名称
        bankAccount: '', //银行账号
        bankTransNum: '', //银行交易流水
      },
      alipayItem: {
        alipayNum: '', //支付宝账号
        aliTransNum: '', //支付宝交易流水号
      },
      wxItem: {
        wechatNum: '', //微信账号
        wechatAccount: '', //微信交易流水号
      },
      otherItem: {
        remarkAccountNum: '', //其他账号
        remarkTransNum: '', //其他交易流水号
      },
      refundStatus: [
        {
          value: '',
          label: '未知留空',
        },
        {
          value: '1',
          label: '成功',
        },
        {
          value: '2',
          label: '失败',
        },
      ],
    }
  },
  // watch: {
  //   dialogFormVisible(val) {
  //     if (val) {
  //       // console.log(this.refundOrderId)
  //     }
  //   },
  // },
  methods: {
    typeAdapter,
    getTime() {
      return moment(new Date()).format('HH:mm:ss')
    },
    typeFilter(suspendReason) {
      if (this.reasonType.length === 0) {
        suspendReason.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          this.reasonType.push(lvObj)
        })
      }
    },
    add() {
      this.showLoading = true
      let params = JSON.parse(JSON.stringify(this.formData))

      params.refundFirstTime = params.refundFirstTime
        ? moment(params.refundFirstTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.refundSecondTime = params.refundSecondTime
        ? moment(params.refundSecondTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.firstTransferTime = params.firstTransferTime
        ? moment(params.firstTransferTime).format('YYYY-MM-DD HH:mm:ss')
        : ''

      if (
        params.refundPath == '01' ||
        params.refundPath == '02' ||
        params.refundPath == '03'
      ) {
        params = { ...params, ...this.etcItem }
      } else if (params.refundPath == '04' || params.refundPath == '05') {
        params = { ...params, ...this.bankItem }
      } else if (params.refundPath == '06') {
        params = { ...params, ...this.alipayItem }
      } else if (params.refundPath == '07') {
        params = { ...params, ...this.wxItem }
      } else if (params.refundPath == '09') {
        params = { ...params, ...this.otherItem }
      }

      console.log('入参', params)
      this.$store
        .dispatch('refund/addRefundComplete', params)
        .then((res) => {
          //修改成功
          this.showLoading = false
          this.close()
          this.$emit('on-submit')
          this.$message({
            message: '添加成功',
            type: 'success',
          })
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    getType(typeObj, value) {
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      // width: 200px;
      white-space: nowrap;
    }
  }
  .desc-2 {
    border-top: 0;
    border-bottom: 0;
  }
  .selector {
    width: 250px;
  }
}

.desc {
  & > span {
    display: block;
    margin-bottom: 20px;
  }
}

.textarea {
  width: 100%;
  border-color: #e8e8e8;
  padding: 10px;
}

.tips {
  display: block;
  color: red;
  text-align: right;
  margin-top: 8px;
}
// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
