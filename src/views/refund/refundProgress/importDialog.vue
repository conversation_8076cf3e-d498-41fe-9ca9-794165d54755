<template>
  <div class="import-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      :title="type === 'add' ? '退款进度新建导入' : '退款进度更新导入'"
      :before-close="handleCloseIcon"
      width="65%"
    >
      <fieldset class="fieldset">
        <legend>附件上传</legend>
        <div slot="tip" class="el-upload__tip">仅支持xls格式的Excel文件</div>
        <template v-if="type === 'add'">
          <div slot="tip" class="el-upload__tip">
            1、涉及到时间的列，统一采用格式：YYYY-MM-DD
            HH:mm:ss(例如：2020-08-28 13:06:05)
          </div>
          <div slot="tip" class="el-upload__tip">2、退费交易编号必填</div>
          <div slot="tip" class="el-upload__tip">
            3、资金退回方式：[1-储值卡补卡额 2-退回ETC主账户 3-退回ETC副账户
            4-银行原路退回 5-银行转账 6-支付宝 7-微信 9-其它 10-客户放弃]
          </div>
          <div slot="tip" class="el-upload__tip">
            4、退费最终状态：[1-成功 2-失败]
          </div>
        </template>
        <template v-else>
          <div slot="tip" class="el-upload__tip">
            1、涉及到时间的列，统一采用格式：YYYY-MM-DD
            HH:mm:ss(例如：2020-08-28 13:06:05)
          </div>
          <div slot="tip" class="el-upload__tip">
            2、退费交易编号/资金退回方式/一阶段退费完成时间(发行方退款给银行或退至卡账时间)/退费最终状态必填
          </div>
          <div slot="tip" class="el-upload__tip">
            3、资金退回方式：[1-储值卡补卡额 2-退回ETC主账户 3-退回ETC副账户
            4-银行原路退回 5-银行转账 6-支付宝 7-微信 9-其它 10-客户放弃]
            <div>①若资金退回方式是1，则ETC内部流水号必填；</div>
            <div>①若资金退回方式是2，则ETC主账号/ETC内部流水号必填；</div>
            <div>
              ①若资金退回方式是3，则ETC主账号/ETC副账号/ETC内部流水号必填；
            </div>
            <div>②若资金退回方式是4，则银行名称必填；</div>
            <div>
              ③若资金退回方式是5，则银行名称/银行账号/银行交易流水号必填；
            </div>
            <div>④若资金退回方式是6，则支付宝账号/支付宝交易流水号必填；</div>
            <div>⑤若资金退回方式是7，则微信账号/微信交易流水号必填；</div>
            <div>⑥若资金退回方式是9，则其他账号/其他交易流水号必填；</div>
            <div>⑦储值卡不支持4到9的退回方式，记账卡不支持1和3的退回方式；</div>
          </div>
          <div slot="tip" class="el-upload__tip">
            4、退费最终状态：[1-成功 2-失败]
          </div>
        </template>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      showLoading: false,
      formData: {
        file: '',
        // bankType: '',
      },
      fileList: [],
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      if (this.type === 'add') {
        //新建上传
        this.addUpload()
      } else {
        //更新上传
        this.updateUpload()
      }
    },
    addUpload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/refundCompleteRecord/importRefundCompleteRecordForAdd'
      console.log('url', url)
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken(),
          },
        })
        .then((res) => {
          this.showLoading = false
          if (res.data.code !== 200) {
            this.$confirm(
              res.data.msg,
              '错误提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'error',
              }
            )
            return
          }
          this.$message({
            type: 'success',
            message: '导入成功',
          })
          this.$refs.upload.clearFiles()
          // this.formData.bankType = ''
          this.formData.file = ''
          this.$emit('uploadSuccess')
        })
        .catch((err) => {
          this.showLoading = false
          this.$confirm(
            '读取Excel数据失败,请检查以下情况: 1.Excel文件中没有任何数据 2.缺失字段 3.字段名称不正确',
            '错误提示',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
            }
          )
        })
      // this.$store
      //   .dispatch('refund/refundResultImport', formData)
      //   .then((res) => {
      //     this.showLoading = false
      //     this.$refs.upload.clearFiles()
      //     this.formData.file = ''
      //     this.$message({
      //       type: 'success',
      //       message: '导入成功',
      //     })
      //     this.$emit('uploadSuccess')
      //   })
      //   .catch((err) => {
      //     this.showLoading = false
      //     this.$refs.upload.clearFiles()
      //     this.formData.file = ''
      //   })
    },
    updateUpload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/refundCompleteRecord/importRefundCompleteRecordForUpdate'
      console.log('url', url)
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken(),
          },
        })
        .then((res) => {
          this.showLoading = false
          if (res.data.code !== 200) {
            this.$confirm(res.data.msg, '错误提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
            })
            return
          }
          this.$message({
            type: 'success',
            message: '导入成功',
          })
          this.$refs.upload.clearFiles()
          // this.formData.bankType = ''
          this.formData.file = ''
          this.$emit('uploadSuccess')
        })
        .catch((err) => {
          this.showLoading = false
          this.$confirm(
            '读取Excel数据失败,请检查以下情况: 1.Excel文件中没有任何数据 2.缺失字段 3.字段名称不正确',
            '错误提示',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
            }
          )
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>