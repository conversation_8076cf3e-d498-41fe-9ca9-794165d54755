<template>
  <div class="refund-progress">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="退费交易编号：" prop="refundId">
            <el-input v-model="search.refundId" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input v-model="search.cardNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="卡类型：" prop="carNoColor">
            <el-select v-model="search.cardType" placeholder="请选择">
              <el-option
                v-for="item in etcCardType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            label="退费进度录入时间起始："
            prop="createTimeFrom"
          >
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.createTimeFrom"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="退费进度录入时间截止：" prop="createTimeTo">
            <el-date-picker
              v-model="search.createTimeTo"
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="资金退回方式：" prop="refundPath">
            <el-select v-model="search.refundPath" placeholder="请选择">
              <el-option
                v-for="item in refundPath"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <template v-if="isCollapse">
            <dart-search-item label="工单编号：" prop="orderId">
              <el-input v-model="search.orderId" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="车牌：" prop="carNo">
              <el-input v-model="search.carNo" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="绑定机构：" prop="bankId">
              <el-select v-model="search.bankId" placeholder="请选择">
                <el-option
                  v-for="item in orgList"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="代扣机构：" prop="payOrgId">
              <el-select v-model="search.payOrgId" placeholder="请选择">
                <el-option
                  v-for="item in orgList"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
          </template>
          <dart-search-item :is-button="true" :span="24">
            <div class="g-flex">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="importDialog('add')"
                ><i class="el-icon-upload"></i> 新建导入</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="importDialog('update')"
              >
                <i class="el-icon-refresh"></i> 更新导入</el-button
              >
              <el-button
                size="mini"
                type="warning"
                @click="onExportHandle('all')"
                ><i class="el-icon-download"></i> 全部导出</el-button
              >
              <el-button
                size="mini"
                type="warning"
                @click="onExportHandle('more')"
                >勾选导出</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="addDialogVisible = true"
                >新增</el-button
              >
              <el-button size="mini" type="warning" @click="showEditDialog()"
                >更新</el-button
              >
              <el-button size="mini" type="danger" @click="del()"
                >删除</el-button
              >
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
              <!-- <a
                class="import-text_download"
                type="info"
                href="/static/refund.xls"
                target="_blank"
                >导入模板下载</a
              > -->
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="isCollapse ? 460 : 550"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column label="选择" width="55" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.row.recordId"
              @change="getCurrentRow(scope.row.recordId)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column align="center" width="80" label="全选">
          <template slot="header">
            <div>
              导出
              <el-checkbox
                v-model="checkAll"
                @change="handleCheckAll"
              ></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="handleCheckOne"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          prop="refundId"
          align="center"
          min-width="310"
          label="退费交易编号"
        />
        <!-- 跨省情况：01-本省交易、02-跨省交易 -->
        <el-table-column
          prop="provinceType"
          align="center"
          min-width="100"
          label="跨省情况"
        >
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.provinceType, provinceType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardId"
          align="center"
          min-width="200"
          label="ETC卡号"
        />
        <!-- 1储值卡；2记账卡 -->
        <el-table-column
          prop="etcCardType"
          align="center"
          min-width="100"
          label="ETC卡类型"
        >
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.etcCardType, etcCardType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="车牌"
        />
        <el-table-column prop="carColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.carColor, 'getVehicleColor') }}
          </template>
        </el-table-column>
        <!-- 类型转换资金退回方式：01-储值卡补卡额/02-退回ETC主账户/03-退回ETC副账户/04-原路退回/05-银行转账/06-支付宝/07-微信/08-现金/09-其他/10-客户放弃 -->
        <el-table-column
          prop="refundPath"
          align="center"
          min-width="150"
          label="资金退回方式"
        >
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.refundPath, 'getRefundPath') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundFee"
          align="center"
          min-width="120"
          label="退费金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.refundFee | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="orderId"
          align="center"
          min-width="150"
          label="工单编号"
        />
        <el-table-column
          prop="transactionId"
          align="center"
          min-width="310"
          label="原交易编号"
        />
        <el-table-column
          prop="etcFirstAccountNum"
          align="center"
          min-width="150"
          label="ETC主账户号"
        />
        <el-table-column
          prop="etcSecondAccountNum"
          align="center"
          min-width="150"
          label="ETC副账户号"
        />
        <el-table-column
          prop="etcTransNum"
          align="center"
          min-width="150"
          label="ETC内部流水号"
        />
        <el-table-column
          prop="bank"
          align="center"
          min-width="120"
          label="银行名称"
        />
        <el-table-column
          prop="bankAccount"
          align="center"
          min-width="180"
          label="银行账号"
        />
        <el-table-column
          prop="bankTransNum"
          align="center"
          min-width="300"
          label="银行交易流水号"
        />
        <!-- 找不到字段 -->
        <el-table-column
          prop="time"
          align="center"
          min-width="100"
          label="第三方账号"
        />
        <!-- 找不到字段 -->
        <el-table-column
          prop="time"
          align="center"
          min-width="140"
          label="第三方交易流水号"
        />
        <el-table-column
          prop="refundFirstTime"
          align="center"
          min-width="160"
          label="一阶段退费时间"
        />
        <el-table-column
          prop="refundSecondTime"
          align="center"
          min-width="160"
          label="二阶段退费时间"
        />
        <!-- 银行退费状态：1成功 2失败 -->
        <el-table-column
          prop="refundStatus"
          align="center"
          min-width="110"
          label="退费最终状态"
        >
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.refundStatus, refundStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankId"
          align="center"
          min-width="120"
          label="绑定机构"
        >
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.bankId, orgList) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="payOrgId"
          align="center"
          min-width="120"
          label="代扣机构"
        >
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.payOrgId, orgList) }}
          </template>
        </el-table-column>
        <!-- 上传状态: 0初始未上传 、1上传成功、2上传失败 -->
        <el-table-column prop="uploadStatus" align="center" label="上传状态">
          <template slot-scope="scope">
            {{ getTypeFun(scope.row.uploadStatus, uploadStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          min-width="160"
        />
        <el-table-column prop="creatorName" align="center" label="创建人" />
        <el-table-column
          prop="updateTime"
          align="center"
          label="更新时间"
          min-width="160"
        />
        <el-table-column prop="creatorName" align="center" label="更新人" />
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[10, 20, 50]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <add-dialog
      :dialogFormVisible.sync="addDialogVisible"
      @on-submit="onUpdateList"
    ></add-dialog>
    <edit-dialog
      :dialogFormVisible.sync="editDialogVisible"
      :refundOrderId="id"
      @on-submit="onUpdateList"
    ></edit-dialog>
    <import-dialog
      :visible.sync="importDialogVisible"
      :type="uploadType"
      @uploadSuccess="uploadSuccess"
    >
    </import-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { licenseColorOption, refundPath } from '@/common/const/optionsData'
import { typeAdapter, getTypeFun } from '@/common/method/formatOptions'
import { decode } from 'js-base64'
import addDialog from './addDialog'
import editDialog from './editDialog'
import importDialog from './importDialog'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    addDialog,
    editDialog,
    importDialog,
  },
  data() {
    return {
      licenseColorOption,
      refundPath,
      loading: false,
      checkAll: false,
      addDialogVisible: false,
      editDialogVisible: false,
      importDialogVisible: false,
      isCollapse: false,
      center: 'center',
      uploadType: 'add',
      total: '',
      id: null,
      search: {
        bankId: '', //绑定机构
        carNo: '', //车牌
        cardNo: '', //卡号
        cardType: '', //卡类型
        createTimeFrom: '', //起始时间
        createTimeTo: '', //截止时间
        orderId: '', //工单编号
        page: 1,
        pageSize: 10,
        payOrgId: '', //代扣机构
        refundPath: '', //资金退回方式
        refundId: '', //退费交易编号
      },
      orgList: [],
      radio: [],
      tableData: [],
      selection: [],
      provinceType: [
        {
          value: '01',
          label: '本省交易',
        },
        {
          value: '02',
          label: '跨省交易',
        },
      ],
      etcCardType: [
        //卡类型
        {
          value: '1',
          label: '储值卡',
        },
        {
          value: '2',
          label: '记账卡',
        },
      ],
      refundStatus: [
        //银行退费状态
        {
          value: '1',
          label: '成功',
        },
        {
          value: '2',
          label: '失败',
        },
      ],
      uploadStatus: [
        // <!-- 上传状态: 0初始未上传 、1上传成功、2上传失败 -->
        {
          value: '0',
          label: '初始未上传',
        },
        {
          value: '1',
          label: '上传成功',
        },
        {
          value: '2',
          label: '上传失败',
        },
      ],
    }
  },
  created() {
    this.getOrgViewList()
    this.getRefundCompleteList()
  },
  methods: {
    typeAdapter,
    getTypeFun,
    getOrgViewList() {
      this.$store.dispatch('refund/getOrgView').then((res) => {
        // console.log('银行数据', res.orgIds)
        let list = res.orgIds
        //过滤数据
        list.forEach((item) => {
          let org = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          this.orgList.push(org)
        })
      })
    },
    getRefundCompleteList() {
      this.loading = true
      // console.log('orgList', this.orgList)
      let params = JSON.parse(JSON.stringify(this.search))
      params.createTimeFrom = params.createTimeFrom
        ? moment(params.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeTo = params.createTimeTo
        ? moment(params.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''

      console.log('入参', params)
      this.$store
        .dispatch('refund/getRefundComplete', params)
        .then((res) => {
          console.log('列表', res)
          this.loading = false
          if (res.data) {
            this.tableData = res.data
          } else {
            this.tableData = []
          }
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    getCurrentRow(id) {
      this.id = id
    },
    handleCheckAll(val) {
      console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        this.$set(this.tableData[i], 'checked', val)
      }
    },
    handleCheckOne(val) {
      console.info('check one change is ', val)
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量
      this.tableData.forEach((item) => {
        if (item.checked) {
          someStatusCount++
        }
      })
      this.checkAll = totalCount === someStatusCount ? true : false
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.tableData.forEach((item) => {
        item.checked = false
      })
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = 1
      this.getRefundCompleteList()
    },
    onUpdateList() {
      this.checkAll = false
      this.radio = []
      this.id = null
      // this.selection = []
      this.search.page = 1
      this.getRefundCompleteList()
    },
    changePage(page) {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = page
      this.getRefundCompleteList()
    },
    handleSizeChange(pageSize) {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.pageSize = pageSize
      this.getRefundCompleteList()
    },
    showEditDialog() {
      if (this.radio.length == 0 && !this.id) {
        this.$message({
          message: '请先选中一条记录！',
          type: 'warning',
        })
        return
      }
      this.editDialogVisible = true
    },
    del() {
      this.confirmDilog('确定要删除该条数据吗？', '删除操作', 'danger')
        .then(() => {
          this.$store
            .dispatch('refund/refundCompleteCancel', { id: this.id })
            .then((res) => {
              this.message('删除成功', 'success')
              this.onUpdateList()
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    importDialog(type) {
      this.uploadType = type
      this.importDialogVisible = true
    },
    uploadSuccess() {
      this.checkAll = false
      this.radio = []
      this.importDialogVisible = false
      this.getRefundCompleteList()
    },
    //导出
    onExportHandle(type) {
      // let params = {
      //   name: '退费订单勾选导出报表',
      //   args: { recordIds: [] },
      // }

      if (type === 'all') {
        let args = JSON.parse(JSON.stringify(this.search))
        args.createTimeFrom = args.createTimeFrom
          ? moment(args.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.createTimeTo = args.createTimeTo
          ? moment(args.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
          : ''
        delete args.pageSize
        delete args.page
        delete args.payOrgId
        delete args.bankId
        let params = {
          name: '退费进度报表',
          ...args,
        }
        this.doExport(params)
      } else {
        //勾选导出,筛选勾选数据
        let selection = []
        this.tableData.forEach((item) => {
          if (item.checked && !selection.includes(item.recordId)) {
            selection.push(item.recordId)
          }
        })

        if (selection.length == 0) {
          this.$message({
            message: '请先勾选要导出的数据！',
            type: 'warning',
          })
          return
        }
        let strList = ''
        selection.forEach((item) => {
          strList = strList + item + ','
        })
        strList = strList.substr(0, strList.length - 1)
        let params = {
          name: '退费进度勾选导出报表',
          recordIds: strList,
        }
        this.doExport(params)
      }
    },
    doExport(params) {
      this.$store
        .dispatch('containerRefund/refundReport', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          window.open(decodeUrl)
        })
        .catch((err) => {})
    },
    confirmDilog(msg, title, type) {
      return new Promise((resolve, reject) => {
        // if (useType === 'moreConfirm') {
        if (this.radio.length === 0) {
          reject({ msg: '请先选中一条记录！', type: 'warning' })
          return
        }
        // }
        this.$confirm(msg, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject({ msg: '取消确认', type: 'info' })
          })
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.refund-progress {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    margin-left: -120px;
    margin-top: 10px;
  }
  .import-text_download {
    text-decoration-line: underline;
    margin-left: 15px;
    &:hover {
      cursor: pointer;
      color: #409eff;
    }
  }

  .collapse {
    display: inline-block;
    vertical-align: top;
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }

  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 160px) !important;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
    width: 160px !important;
    white-space: nowrap;
  }
  ::v-deep.el-radio__label {
    padding-left: 0;
  }
  ::v-deep.el-radio__input.is-checked + .el-radio__label {
    padding-left: 0;
  }
}
</style>
