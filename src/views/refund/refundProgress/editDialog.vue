<template>
  <div class="add-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title">退费进度修改</span>
        </div>
      </template>
      <div class="desc">
        <span
          >资金退回方式：[1-储值卡补卡额 2-退回ETC主账户 3-退回ETC副账户
          4-银行原路退回 5-银行转账 6-支付宝 7-微信 9-其它 10-客户放弃]</span
        >
      </div>
      <div class="desc-item">
        <el-descriptions
          labelClassName="desc-1"
          :column="4"
          border
          style="border-bottom: 0"
        >
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>ETC卡号：</span>
            </template>
            {{ updateDetail.cardId }}</el-descriptions-item
          >
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>卡类型：</span> </template
            >{{
              getTypeFun(updateDetail.etcCardType, etcCardType)
            }}</el-descriptions-item
          >
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>退费金额(元)：</span> </template
            >{{ updateDetail.refundFee | moneyFilter }}</el-descriptions-item
          >
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>退费交易编号：</span>
            </template>
            {{ updateDetail.refundId }}</el-descriptions-item
          >
        </el-descriptions>
        <el-descriptions labelClassName="desc-2" :column="4" border>
          <el-descriptions-item contentClassName="desc-2">
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>原交易编号：</span>
            </template>
            {{ updateDetail.transactionId }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="desc-2">
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>工单编号：</span>
            </template>
            {{ updateDetail.orderId }}
          </el-descriptions-item>
          <el-descriptions-item contentClassName="desc-2">
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>资金退回方式：</span>
            </template>
            <el-select v-model="formData.refundPath" placeholder="请选择">
              <el-option
                v-for="item in refundPath"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item
            contentClassName="desc-2"
            label="退费最终状态："
          >
            <el-select v-model="formData.refundStatus" placeholder="请选择">
              <el-option
                v-for="item in refundStatus"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions labelClassName="desc-3" :column="3" border>
          <el-descriptions-item
            contentClassName="desc-3"
            label="一阶段退费完成时间："
          >
            <el-date-picker
              v-model="formData.refundFirstTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item
            contentClassName="desc-3"
            label="二阶段退费完成时间："
          >
            <el-date-picker
              v-model="formData.refundSecondTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item contentClassName="desc-3">
            <template slot="label">
              <span style="color: red; margin-right: 5px">*</span>
              <span>跨省情况：</span>
            </template>
            本省交易
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions labelClassName="desc-4" :column="1" border>
          <el-descriptions-item
            contentClassName="desc-4"
            label="退费失败原因："
          >
            <textarea
              class="textarea"
              name="suggestion"
              id=""
              cols="100"
              rows="10"
              placeholder="请输入退费失败原因"
              v-model="formData.refundReason"
            ></textarea>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions labelClassName="desc-5" :column="4" border>
          <template
            v-if="
              formData.refundPath == '01' ||
              formData.refundPath == '02' ||
              formData.refundPath == '03'
            "
          >
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC内部流水号："
            >
              <el-input
                placeholder=""
                v-model="formData.etcTransNum"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC主账户号："
            >
              <el-input
                placeholder=""
                v-model="formData.etcFirstAccountNum"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="ETC副账户号："
            >
              <el-input
                placeholder=""
                v-model="formData.etcSecondAccountNum"
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item contentClassName="desc-5" label="圈存时间：">
              <el-date-picker
                v-model="formData.firstTransferTime"
                type="datetime"
                placeholder="选择日期时间"
                :default-time="getTime()"
              >
              </el-date-picker>
            </el-descriptions-item>
          </template>
          <!-- 银行 -->
          <template
            v-if="formData.refundPath == '04' || formData.refundPath == '05'"
          >
            <el-descriptions-item contentClassName="desc-5" label="银行名称：">
              <el-input v-model="formData.bank" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item contentClassName="desc-5" label="银行账号：">
              <el-input v-model="formData.bankAccount" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="银行交易流水号："
            >
              <el-input v-model="formData.bankTransNum" placeholder=""></el-input>
            </el-descriptions-item>
          </template>
          <!-- 支付宝 -->
          <template v-if="formData.refundPath == '06'">
            <el-descriptions-item
              contentClassName="desc-5"
              label="支付宝账号："
            >
              <el-input v-model="formData.alipayNum" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="支付宝交易流水号："
            >
              <el-input
                v-model="formData.aliTransNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
          <!-- 微信 -->
          <template v-if="formData.refundPath == '07'">
            <el-descriptions-item contentClassName="desc-5" label="微信账号：">
              <el-input v-model="formData.wechatNum" placeholder=""></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="微信交易流水号："
            >
              <el-input
                v-model="formData.wechatAccount"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
          <!-- 其他 -->
          <template v-if="formData.refundPath == '09'">
            <el-descriptions-item contentClassName="desc-5" label="其他账号：">
              <el-input
                v-model="formData.remarkAccountNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
            <el-descriptions-item
              contentClassName="desc-5"
              label="其他交易流水号："
            >
              <el-input
                v-model="formData.remarkTransNum"
                placeholder=""
              ></el-input>
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <div
          v-if="
            formData.refundPath == '01' ||
            formData.refundPath == '02' ||
            formData.refundPath == '03'
          "
        >
          <span class="tips">圈存时间:储值卡补卡额才填</span>
        </div>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="edit()">添加</el-button>
        <el-button @click="close()">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { typeAdapter, getTypeFun } from '@/common/method/formatOptions'
import { refundPath } from '@/common/const/optionsData'
var moment = require('moment')
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    refundOrderId: {
      type: Number,
    },
    // typeList: {
    //   type: Object,
    //   default: {},
    // },
  },
  data() {
    return {
      refundPath,
      isFullscreen: false,
      showLoading: false,
      formData: {
        recordId: '', //id
        refundPath: '', //资金回退方式
        refundStatus: '', //退费状态
        refundFirstTime: '', //一阶段退费完成时间
        refundSecondTime: '', //二阶段退费完成时间
        refundReason: '', //退费失败原因

        etcTransNum: '', //ETC内部流水号
        etcFirstAccountNum: '', //ETC主账户号
        etcSecondAccountNum: '', //ETC副账户号
        firstTransferTime: '', //一阶段圈存完成时间

        bank: '', //银行名称
        bankAccount: '', //银行账号
        bankTransNum: '', //银行交易流水
        alipayNum: '', //支付宝账号
        aliTransNum: '', //支付宝交易流水号
        wechatNum: '', //微信账号
        wechatAccount: '', //微信交易流水号
        remarkAccountNum: '', //其他账号
        remarkTransNum: '', //其他交易流水号
      },
      updateDetail: {
        cardId: '', //etc卡号
        etcCardType: '', //卡类型
        refundFee: '', //退费金额
        refundId: '', //退费交易编号
        transactionId: '', //原交易编号
        orderId: '', //工单编号
      },
      refundStatus: [
        {
          value: '',
          label: '未知留空',
        },
        {
          value: '1',
          label: '成功',
        },
        {
          value: '2',
          label: '失败',
        },
      ],
      provinceType: [
        {
          value: '01',
          label: '本省交易',
        },
        {
          value: '02',
          label: '跨省交易',
        },
      ],
      etcCardType: [
        //卡类型
        {
          value: '1',
          label: '储值卡',
        },
        {
          value: '2',
          label: '记账卡',
        },
      ],
      refundStatus: [
        //银行退费状态
        { value: '', label: '未知' },
        { value: '1', label: '成功' },
        { value: '2', label: '失败' },
      ],
    }
  },
  watch: {
    dialogFormVisible(val) {
      console.log(val, this.refundOrderId)
      if (val && this.refundOrderId) {
        this.getUpdateList()
      }
    },
  },
  methods: {
    typeAdapter,
    getTypeFun,
    getTime() {
      return moment(new Date()).format('HH:mm:ss')
    },
    typeFilter(suspendReason) {
      if (this.reasonType.length === 0) {
        suspendReason.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          this.reasonType.push(lvObj)
        })
      }
    },
    getUpdateList() {
      this.$store
        .dispatch('refund/refundCompleteRecord', { id: this.refundOrderId })
        .then((res) => {
          console.log('回填数据', res)

          //数据回填
          Object.keys(this.formData).forEach((key) => {
            this.formData[key] = res[key]
          })
          Object.keys(this.updateDetail).forEach((key) => {
            this.updateDetail[key] = res[key]
          })
        })
        .catch((err) => {})
    },
    edit() {
      this.showLoading = true
      // params.id = this.refundOrderId
      let params = JSON.parse(JSON.stringify(this.formData))
      params = { ...params, ...this.updateDetail }

      params.refundFirstTime = params.refundFirstTime
        ? moment(params.refundFirstTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.refundSecondTime = params.refundSecondTime
        ? moment(params.refundSecondTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.firstTransferTime = params.firstTransferTime
        ? moment(params.firstTransferTime).format('YYYY-MM-DD HH:mm:ss')
        : ''

      console.log('入参', params)
      this.$store
        .dispatch('refund/updateRefundComplete', params)
        .then((res) => {
          //修改成功
          this.showLoading = false
          this.close()
          this.$emit('on-submit')
          this.$message({
            message: '修改成功',
            type: 'success',
          })
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    getType(typeObj, value) {
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      // width: 200px;
      white-space: nowrap;
    }
  }
  .desc-2,
  .desc-4 {
    border-top: 0;
    border-bottom: 0;
  }
  // .selector {
  //   width: 250px;
  // }
}

.desc {
  & > span {
    display: block;
    margin-bottom: 20px;
  }
}

.textarea {
  width: 100%;
  border-color: #e8e8e8;
  padding: 10px;
}

.tips {
  display: block;
  color: red;
  text-align: right;
  margin-top: 8px;
}
// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
