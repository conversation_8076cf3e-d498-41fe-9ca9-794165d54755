<template>
  <div>
    <div class="dft-box">
      <input type="file"
             id="myInput"
             ref="myInput"
             class="imagebase"
             v-show="false"
             :accept="imgList"
             @change="getImgBase" />
    </div>
    <div class="pic-box">
      <div class="imgitem"
           v-for="(item, index) in imgurlList"
           :key="index">
        <div v-if="item.isShow">
          <div v-if="!item.file_url"
               class="add"
               @click="openFDialog(item)">
            <!-- <i class="el-icon-plus"
            @click="openFDialog(item)"></i> -->
            <img class="addimg"
                 src="@/image/local.png" />
            <p class="title">{{ item.lable }}</p>
          </div>
          <div v-else
               v-viewer="{ inline: false }"
               class="add">
            <img class="picture_img"
                 :src="item.file_url"
                 height="115"
                 width="125" />
            <p class="title">{{ item.lable }}</p>
            <div class="delete">
              <i class="el-icon-close"
                 @click="deleteimg(item)"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  props: {
    customerId: {
      type: [String, Number],
      default: null,
    },
    vehicle_code: {
      type: String,
      default: '',
    },
    vehicle_color: {
      type: String,
      default: '',
    },
    other_code: {
      type: String,
      default: '',
    },
    urlList: {
      type: Array,
      default: [],
    },
    fileUUID: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      imgList: [
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/png',
        'image/bmp',
      ],
      imgBase: '',
      imgurlList: [],
      nowindex: null,
      photo_code: '',
    }
  },
  created() {
    this.imgurlList = this.urlList
    console.log(this.$route.query, ' this.$route.query.refundType')
  },
  computed: {
    ...mapGetters(['initInfo']),
  },
  methods: {
    ...mapActions([]),
    openFDialog(item) {
      this.photo_code = item.photo_code
      let _self = this
      this.$nextTick(() => {
        _self.$refs.myInput.value = ''
        _self.$refs.myInput.click()
      })
    },
    deleteimg(item) {
      // this.imgurlList.splice(index, 1)
      this.$confirm('是否确定删除该档案图片？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '删除成功!'
          // });
          this.delPic(item)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    delPic(item) {
      let data = {
        customer_id: this.customerId,
        file_serial: item.file_serial,
        photo_code: item.photo_code,
      }
      request({
        url: api.delPic,
        method: 'post',
        data: data,
      })
        .then((res) => {
          console.log(res)
          if (res.code == 200) {
            this.$message({
              message: '删除成功',
              type: 'success',
            })
            this.$emit('getDetailImgs', {})
          }
        })
        .catch(() => {})
    },
    upload(obj) {
      console.log(this.other_code)
      let info = { ...this.initInfo }
      let param = new FormData() //创建form对象
      param.append('customer_id', this.customerId)
      // param.append('special_code', info.refund_serial)
      param.append('vehicle_code', this.vehicle_code)
      param.append('vehicle_color', this.vehicle_color)
      if (this.other_code) {
        param.append('other_code', this.other_code)
      }
      param.append('photo_code', obj.photo_code)
      param.append('scene', obj.scene)
      let blob = this.dataUrlToBlob(obj.imgBase64)
      param.append('file', blob, 'img.jpg')
      let url = process.env.VUE_APP_BASE_API + '/issue-web' + api.upImg
      let config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: getToken(),
        },
      } //添加请求头
      axios
        .post(url, param, config)
        .then((response) => {
          console.log(response)
          if (response.data.code == 200) {
            this.$emit('getDetailImgs', {})
            // if (this.nowindex) {
            //   this.imgurlList.splice(
            //     this.nowindex,
            //     1,
            //     response.data.data.file_url
            //   )
            //   this.nowindex = null
            // } else {
            //   this.imgurlList.push(response.data.data.file_url)
            // }
            // this.imgBase64 = response.data.data.file_url
            // obj.imgBase64 = null;
            // obj.changed = false;
            // let data = response.data.data;
            // this.$set(this.pictureList, index, {
            //   file_code: "",
            //   file_name: data.file_name,
            //   file_url: data.file_url,
            //   file_serial: data.file_serial,
            //   photo_code: obj.photo_code,
            // });
          } else {
            this.$msgbox({
              title: '提示',
              message: response.data.msg,
              customClass: 'my_msgBox singelBtn',
              // showCancelButton: true,
              confirmButtonText: '确定',
              type: 'error',
            })
          }
          // console.log(this.pictureList, "11111111");
        })
        .catch((err) => {
          _this.$alert(err.message, '提示', {
            dangerouslyUseHTMLString: true,
            showClose: false,
            confirmButtonText: '确定',
          })
        })
    },

    getImgBase() {
      let _this = this
      let event = event || window.event
      let file = event.target.files[0]
      if (parseInt(file.size / 1024 / 1024) >= 20) {
        // 超过10M，提示无法上传
        this.$msgbox({
          title: '温馨提示',
          message: '上传图片大小不得超过20M',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      if (this.imgList.indexOf(file.type) === -1) {
        this.$msgbox({
          title: '温馨提示',
          message: '请上传图片格式的文件',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      var maxSize = 400 * 1024
      this.changeImage(file, maxSize, this.getFileBase64)
      return
    },
    changeImage(file, size, callback, type) {
      let _self = this
      if (file.size > size) {
        // 文件大于size 则进行压缩处理
        var reader = new FileReader()
        reader.readAsDataURL(file)
        var img = new Image()
        reader.onload = function (e) {
          console.log('reader', e)
          img.src = e.target.result
          img.onload = function () {
            var data = _self.compress(img)
            console.log('压缩后', data)
            var text = window.atob(data.split(',')[1])
            var buffer = new Uint8Array(text.length)
            for (var i = 0; i < text.length; i++) {
              buffer[i] = text.charCodeAt(i)
            }
            console.log('文件类型', file.type)
            var rev = _self.getBlob([buffer], file.type)
            console.log('压缩转码完成', rev)
            var rfile = new File([rev], file.name)
            callback(rfile, type)
          }
        }
      } else {
        callback(file, type)
      }
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function () {
          imgResult = reader.result
        }
        reader.onerror = function (error) {
          reject(error)
        }
        reader.onloadend = function () {
          resolve(imgResult)
        }
      })
    },
    getFileBase64(file) {
      let _this = this
      console.log(file.size / 1024 / 1024)
      this.getBase64(file).then((res) => {
        // _this.imgBase64 = res;
        let param = {
          imgBase64: res,
          scene: '',
          photo_code: this.photo_code,
        }
        switch (this.$route.query.refundType) {
          case '9':
            param.scene = '14'
            break
          case '8':
            param.scene = '15'
            break
          case '11':
            param.scene = '15'
            break
          case '1':
            param.scene = '6'
            break
          case '12':
            param.scene = '26'
            break
        }
        _this.upload(param)
        console.log(param)
        console.log(res)
        // _this.$emit("onOperation", param);
      })
    },
    compress(img) {
      //    用于压缩图片的canvas
      var canvas = document.createElement('canvas')
      var ctx = canvas.getContext('2d')
      //    瓦片canvas
      var tCanvas = document.createElement('canvas')
      var tctx = tCanvas.getContext('2d')
      var initSize = img.src.length
      var width = img.width
      var height = img.height
      // 如果图片大于四百万像素，计算压缩比并将大小压至400万以下
      var ratio
      if ((ratio = (width * height) / 4000000) > 1) {
        ratio = Math.sqrt(ratio)
        width /= ratio
        height /= ratio
      } else {
        ratio = 1
      }
      canvas.width = width
      canvas.height = height
      //        铺底色
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      // 如果图片像素大于100万则使用瓦片绘制
      var count
      if ((count = (width * height) / 1000000) > 1) {
        count = ~~(Math.sqrt(count) + 1) // 计算要分成多少块瓦片
        //            计算每块瓦片的宽和高
        var nw = ~~(width / count)
        var nh = ~~(height / count)
        tCanvas.width = nw
        tCanvas.height = nh
        for (var i = 0; i < count; i++) {
          for (var j = 0; j < count; j++) {
            tctx.drawImage(
              img,
              i * nw * ratio,
              j * nh * ratio,
              nw * ratio,
              nh * ratio,
              0,
              0,
              nw,
              nh
            )
            ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh)
          }
        }
      } else {
        ctx.drawImage(img, 0, 0, width, height)
      }
      // 进行最小压缩
      var ndata = canvas.toDataURL('image/jpeg', 0.5)
      console.log('压缩前：' + initSize)
      console.log('压缩后：' + ndata.length)
      console.log(
        '压缩率：' + ~~((100 * (initSize - ndata.length)) / initSize) + '%'
      )
      tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0
      return ndata
    },
    getBlob(buffer, format) {
      try {
        return new Blob(buffer, { type: format })
      } catch (e) {
        var bb = new (window.BlobBuilder ||
          window.WebKitBlobBuilder ||
          window.MSBlobBuilder)()
        buffer.forEach(function (buf) {
          bb.append(buf)
        })
        return bb.getBlob(format)
      }
    },
    dataUrlToBlob(dataURI) {
      let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0] // mime类型
      let byteString = atob(dataURI.split(',')[1]) //base64 解码
      let arrayBuffer = new ArrayBuffer(byteString.length) //创建缓冲数组
      let intArray = new Uint8Array(arrayBuffer) //创建视图
      for (let i = 0; i < byteString.length; i++) {
        intArray[i] = byteString.charCodeAt(i)
      }
      return new Blob([intArray], { type: mimeString })
    },
  },
  computed: {},
  watch: {
    urlList(val) {
      this.imgurlList = val
    },
  },
}
</script>

<style scoped>
.pic-box {
  padding: 10px 90px;
  display: flex;
  flex-wrap: wrap;
}
.add {
  text-align: center;
}
.add .addimg {
  width: 125px;
}
.title {
  position: absolute;
  width: 100%;
  text-align: center;
  margin: 0;
  bottom: 0;
  line-height: 25px;
  color: white;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.5);
}
.add .el-icon-plus {
  border: 1px solid #ebeef5;
  margin-bottom: 3px;
}
img {
  cursor: pointer;
  /* margin: 0 5px; */
}
.imgitem {
  position: relative;
  cursor: pointer;
}
.imgitem > div {
  width: 125px;
  height: 125px;
  margin: 0 10px 10px 0;
  border-radius: 6px;
  border: 1px dashed #e5e5e5;
  overflow: hidden;
  position: relative;
}
.imgitem :hover .delete {
  display: block;
}
.delete {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 4px;
  right: 4px;
  width: 22px;
  height: 22px;
  color: white;
  border-radius: 50%;
  display: none;
}
/* .el-icon-plus {
  cursor: pointer;
  font-size: 125px;
} */
.el-icon-close {
  font-size: 22px;
}
</style>
