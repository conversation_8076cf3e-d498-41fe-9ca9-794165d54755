<template>
  <div class="toll-record">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
        :rules="rules"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" clearable placeholder="请选择">
              <el-option
                v-for="item in typeList.carColors"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="carNo">
            <el-input
              v-model="search.carNo"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="退费类型：" prop="refund_type">
            <el-select
              v-model="search.refund_type"
              placeholder="请选择"
              @change="refundTypeChange"
            >
              <el-option
                v-for="item in refundType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="审核状态：" prop="state">
            <el-select v-model="search.state" placeholder="请选择">
              <el-option
                v-for="item in stateList"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input
              v-model="search.cardNo"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item
            v-if="search.refund_type == '1' || search.refund_type == '8'"
            label="
                            退款类型："
            prop="gxCarType"
          >
            <el-select
              v-model="search.needRefund"
              @change="gxCarTypeChange"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in needRefundList"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <div class="collapse-wrapper" v-if="isCollapse">
            <dart-search-item label="用户名称：" prop="custName">
              <el-input
                v-model="search.custName"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="银行账户名：" prop="bankAccount">
              <el-input
                v-model="search.bankAccount"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="银行卡号：" prop="bankNo">
              <el-input
                v-model="search.bankNo"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>

            <dart-search-item label="退费单号：" prop="id">
              <el-input v-model="search.id" clearable placeholder=""></el-input>
            </dart-search-item>
            <template v-if="search.refund_type == '9'">
              <dart-search-item label="互联网账户ID：" prop="netId">
                <el-input
                  v-model="search.netId"
                  clearable
                  placeholder=""
                ></el-input>
              </dart-search-item>
              <dart-search-item label="互联网账户手机号：" prop="netMobile">
                <el-input
                  v-model="search.netMobile"
                  clearable
                  placeholder=""
                ></el-input>
              </dart-search-item>
              <dart-search-item label="互联网账户用户名：" prop="netName">
                <el-input
                  v-model="search.netName"
                  clearable
                  placeholder=""
                ></el-input>
              </dart-search-item>
            </template>
            <dart-search-item label="创建时间起始：" prop="startTime">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.startTime"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="创建时间截至：" prop="endTime">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.endTime"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="来源：" prop="source">
              <el-select v-model="search.source" clearable placeholder="请选择">
                <el-option
                  v-for="item in sourceList"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <!-- <dart-search-item label="审核状态："
                              prop="state">
              <el-select v-model="search.state"
                         placeholder="请选择">
                <el-option v-for="item in stateList"
                           :key="item.index"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item> -->
            <dart-search-item
              v-if="search.state == 1"
              label="订单流程："
              prop="state"
            >
              <el-select
                v-model="search.orderStatus"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in orderStatusList"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="身份证号：" prop="custIdNo">
              <el-input
                v-model="search.custIdNo"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="审批标识：" prop="paymentFlag">
              <el-select
                v-model="search.paymentFlag"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in paymentFlagOptions"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item
              v-if="search.refund_type == '1'"
              label="广西卡类型："
              prop="gxCarType"
            >
              <el-select
                v-model="search.gxCarType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in gxCardTypeAllOptions"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item
              v-if="search.refund_type == '9'"
              label="互联网退费流水号："
              prop="otherPayId"
            >
              <el-input
                v-model="search.otherPayId"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="创建部门：" prop="otherPayId">
              <el-select
                filterable
                remote
                clearable
                reserve-keyword
                :remote-method="initBranchList"
                placeholder="输入关键词搜索"
                :value-key="'id'"
                :value="branchItem.name"
                @change="branchChange"
              >
                <el-option
                  class="select-wrapper"
                  v-for="item in branchList"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                  <div class="select-item">
                    <div>{{ item.name }}</div>
                  </div>
                </el-option>
              </el-select>
            </dart-search-item>
          </div>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="toMakeTable">
                可制表列表</el-button
              >
              <el-button size="mini" type="primary" @click="exportHandle()">
                <i class="el-icon-download"></i> 归档订单导出</el-button
              >
              <el-button
                v-if="search.refund_type == '1'"
                size="mini"
                type="warning"
                v-permisaction="['refund:rechargeNet']"
                @click="refundNetHandle('more')"
              >
                批量重试</el-button
              >
              <el-button size="mini" type="primary" @click="exportFile" v-permisaction="['refund:refundExport']">
                导出</el-button
              >
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        @sort-change="sortChange"
      >
        <el-table-column
          align="center"
          width="80"
          :key="97"
          label="导出全选"
          v-if="checkAuth(['refund:refundExport'])"
        >
          <template slot="header">
            <div>
              导出全选
              <el-checkbox
                v-model="checkAllExport"
                @change="handleCheckAllExport"
              ></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.exportChecked"
              @change="exportHandleCheckOne"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          width="80"
          label="重试全选"
          :key="98"
          v-if="checkAuth(['refund:rechargeNet']) && search.refund_type == '1'"
        >
          <template slot="header">
            <div>
              重试全选
              <el-checkbox
                v-model="checkAll"
                @change="handleCheckAll"
              ></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope" v-if="scope.row.orderStatus == '7'">
            <el-checkbox
              v-model="scope.row.checked"
              @change="handleCheckOne"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          prop="urgentSign"
          align="center"
          min-width="120"
          label="处理状态"
          :key="1"
        >
          <template slot-scope="scope">
            <el-tag v-if="scope.row.urgentSign == 1" type="danger">加急</el-tag>
            <el-tag v-else type="info">普通</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="urgentSign"
          align="center"
          min-width="140"
          :key="2"
          label=" 是否放弃退款提起"
        >
          <template slot-scope="scope">
            {{ scope.row.reNeedRefund == '1' ? '是' : '否' }}
          </template>
        </el-table-column>

        <el-table-column
          prop="id"
          align="center"
          min-width="100"
          sortable="custom"
          label="订单号"
          :key="3"
        />

        <el-table-column
          prop="custName"
          align="center"
          min-width="100"
          label="用户名称"
          :key="4"
        />

        <el-table-column
          prop="orderStatus"
          align="center"
          min-width="120"
          label="订单流程"
          :key="5"
        >
          <template slot-scope="scope">
            {{ getType(orderStatusList, scope.row.orderStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="completeTime"
          align="center"
          min-width="160"
          sortable="custom"
          label="订单完成时间"
          :key="6"
        />
        <!-- 退费类型==8||==9 隐藏 -->
        <template
          v-if="
            search.refund_type != '8' &&
              search.refund_type != '9' &&
              search.needRefund != '2'
          "
        >
          <el-table-column
            prop="cardNo"
            align="center"
            min-width="250"
            label="ETC卡号"
            :key="7"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.cardNo }}
                </div>
                <span>{{ scope.row.cardNo }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="carNo"
            align="center"
            min-width="100"
            label="车牌号"
            :key="8"
          />
          <el-table-column prop="carColor" align="center" label="车牌颜色">
            <template slot-scope="scope">
              {{ getType(typeList.carColors, scope.row.carColor) }}
            </template>
          </el-table-column>
        </template>
        <el-table-column
          v-if="search.refund_type == '1'"
          prop="gxCarType"
          align="center"
          min-width="150"
          label="广西卡类型"
          :key="9"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ getType(gxCardTypeAllOptions, scope.row.gxCarType) }}
              </div>
              <span>{{
                getType(gxCardTypeAllOptions, scope.row.gxCarType)
              }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <!-- 退费类型==9 隐藏 -->
        <div v-if="search.refund_type == '9'">
          <el-table-column
            prop="netId"
            align="center"
            min-width="120"
            label="互联网账户ID"
            :key="10"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.netId }}
                </div>
                <span>{{ scope.row.netId }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="netMobile"
            align="center"
            min-width="140"
            label="互联网账户手机号"
            :key="11"
          />
          <el-table-column
            prop="netCompanyName"
            align="center"
            min-width="150"
            label="互联网账户公司名"
            :key="12"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.netCompanyName }}
                </div>
                <span>{{ scope.row.netCompanyName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="netName"
            align="center"
            min-width="140"
            label="互联网账户用户名"
            :key="13"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.netName }}
                </div>
                <span>{{ scope.row.netName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="bankNo"
            align="center"
            min-width="160"
            label="互联网退费流水号"
            :key="14"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.otherPayId }}
                </div>
                <span>{{ scope.row.otherPayId }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </div>
        <el-table-column
          prop="amount"
          align="center"
          label="退款金额(元)"
          min-width="130"
          :key="15"
        >
          <template slot-scope="scope"
            ><span v-if="scope.row.amount">
              ￥{{ scope.row.amount }}
            </span></template
          >
        </el-table-column>
        <el-table-column
          prop="amount"
          align="center"
          label="退款类型"
          min-width="120"
          :key="16"
        >
          <template slot-scope="scope">{{
            getType(needRefundList, scope.row.needRefund)
          }}</template>
        </el-table-column>
        <div v-if="search.refund_type == '8' && search.needRefund == '2'">
          <el-table-column
            prop="bankAccount"
            align="center"
            min-width="120"
            label="账户名"
            :key="17"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.bankAccount }}
                </div>
                <span>{{ scope.row.bankAccount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="bankNo"
            align="center"
            min-width="200"
            label="互联网用户编号"
            :key="18"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.bankNo }}
                </div>
                <span>{{ scope.row.bankNo }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="bankName"
            align="center"
            min-width="130"
            label="互联网账户名"
            :key="19"
          />
        </div>
        <div v-else>
          <el-table-column
            prop="bankAccount"
            align="center"
            min-width="120"
            label="银行/互联网账户名"
            :key="20"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.bankAccount }}
                </div>
                <span>{{ scope.row.bankAccount }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="bankNo"
            align="center"
            min-width="200"
            label="银行卡号/账户编号"
            :key="21"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.bankNo }}
                </div>
                <span>{{ scope.row.bankNo }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="bankName"
            align="center"
            min-width="130"
            label="开户行/手机号"
            :key="22"
          />
        </div>
        <el-table-column
          prop="bankNo"
          align="center"
          min-width="200"
          label="身份证号"
          :key="23"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.custIdNo }}
              </div>
              <span>{{ scope.row.custIdNo }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="createdBranchName"
          align="center"
          min-width="130"
          label="创建部门"
          :key="24"
        />
        <el-table-column
          prop="createdByName"
          min-width="100"
          align="center"
          label="创建人"
          :key="25"
        />
        <el-table-column
          prop="createdTime"
          min-width="160"
          align="center"
          sortable="custom"
          label="创建时间"
          :key="26"
        />
        <!-- <el-table-column
          prop="opBranchName"
          align="center"
          label="操作员部门"
          min-width="150"
        />
        <el-table-column
          prop="opName"
          align="center"
          label="操作员"
          min-width="150"
        /> -->
        <el-table-column
          prop="updatedTime"
          align="center"
          label="更新时间"
          sortable="custom"
          min-width="160"
          :key="27"
        />
        <el-table-column prop="source" align="center" label="来源" :key="28">
          <template slot-scope="scope">
            {{ getType(sourceList, scope.row.source) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="paymentFlag"
          align="center"
          label="审批标识"
          :key="29"
        >
          <template slot-scope="scope">
            {{ scope.row.paymentFlag == '1' ? '重提' : '首次' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="tabFlag" align="center" label="制表状态">
          <template slot-scope="scope">
            {{ getType(tabFlagList, scope.row.tabFlag) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="transferMatch"
          align="center"
          label="财务转账结果比对"
          min-width="140"
        >
          <template slot-scope="scope">
            {{ getType(transferMatchList, scope.row.transferMatch) }}
          </template>
        </el-table-column> -->
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="220"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="danger"
              v-if="scope.row.urgentSign == 0"
              size="mini"
              @click="setTag(scope.row.id, scope.row.urgentSign)"
              >加急</el-button
            >
            <el-button
              type="info"
              v-else-if="scope.row.urgentSign == 1"
              size="mini"
              @click="setTag(scope.row.id, scope.row.urgentSign)"
              >取消加急</el-button
            >
            <el-button
              type="warning"
              v-if="scope.row.orderStatus == 7"
              size="mini"
              v-permisaction="['refund:rechargeNet']"
              @click="refundNetHandle('one', scope.row.id)"
              >重试</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="toDetail(scope.row.id, scope.row.refundType)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="total-price">运营部待复核金额：￥{{ allAmount }}元</div>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageNo"
        :page-sizes="[50, 100, 150, 200, 250, 300, 350, 400, 450, 500]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import {
  paymentFlagOptions,
  gxCardTypeAllOptions,
  refundType,
  redundOrderStatusList
} from '@/common/const/optionsData.js'
import { refundExport } from '@/api/refund'

var moment = require('moment')
export default {
  name: 'LogoutRefund',
  components: {
    dartSearch,
    dartSearchItem
  },
  data() {
    return {
      loading: false,
      isCollapse: false,
      center: 'center',
      total: 0,
      search: {
        gxCarType: '', //广西卡类型
        paymentFlag: '', //审批标识
        custIdNo: '', //身份证号
        // passId: '', //退费流水号
        carColor: '', //车牌颜色
        carNo: '', //车牌
        cardNo: '', //卡号
        bankAccount: '', //银行账户名
        bankNo: '', //银行卡号
        custName: '',
        refund_type: '1', //1-注销退费，2-通行费退费 3-卡账退费
        orderStatus: '', //订单流程
        source: '',
        startTime: '', //申请开始时间
        endTime: '', //申请截止时间
        state: 0, //0是待审核，1是全部
        pageNo: 1,
        pageSize: 50,
        sort: '',
        orderByField: '',
        otherPayId: '', //互联网退费流水号
        needRefund: '', //退费途径
        netId: '', //互联网账户ID
        netMobile: '', //互联网账户手机号
        netName: '', //互联网账户用户名
        branchName: '' //部门
      },
      tableData: [],
      checkAll: false,
      checkAllExport:false,
      allAmount: null, //退款总金额
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [] //退费状态字典
      },
      refundType: refundType, //退费类型
      //来源：1-网点；2-C端
      stateList: [
        {
          value: 0,
          label: '待审核'
        },
        {
          value: 1,
          label: '全部'
        }
      ],
      //订单状态：0-用户申请退款1-业务员审核2-清算中3-网点审核4-运营部复核5-制表6-财务打款9-已归档
      orderStatusList: redundOrderStatusList,
      //来源：1-网点；2-C端
      sourceList: [
        {
          value: 1,
          label: '网点'
        },
        {
          value: 2,
          label: 'C端'
        }
      ],
      //制表状态：0-未制表；1—重新制表
      tabFlagList: [
        {
          value: 0,
          label: '未制表'
        },
        {
          value: 1,
          label: '重新制表'
        }
      ],
      //0-未比对；1-比对成功；2-比对失败
      transferMatchList: [
        {
          value: 0,
          label: '未比对'
        },
        {
          value: 1,
          label: '比对成功'
        },
        {
          value: 2,
          label: '比对失败'
        }
      ],
      rules: {
        refund_type: [{ required: true }]
      },
      needRefundList: [
        { value: '', label: '全部' },
        { value: '0', label: '放弃退款' },
        { value: '1', label: '退到银行卡' },
        { value: '2', label: '退到互联网账户' }
      ],
      needRefundListAll: [
        //退费途径
        { value: '', label: '全部' },
        { value: '0', label: '放弃退款' },
        { value: '1', label: '退到银行卡' },
        { value: '2', label: '退到互联网账户' }
      ], //退费类型
      rules: {
        refund_type: [{ required: true }]
      },
      paymentFlagOptions,
      gxCardTypeAllOptions,
      branchList: [],
      branchItem: {
        name: '',
        id: ''
      }
    }
  },
  watch: {
    'search.refund_type'(val) {
      this.tableData = []
      this.total = 0
      this.allAmount = 0
      this.checkAll = false
    }
  },
  created() {
    if (this.$route.query.search) {
      let search = JSON.parse(this.$route.query.search)
      this.search = { ...search }
    }
    this.getLogoutRefundList()
    this.getTypeList()
  },
  activated() {
    this.getLogoutRefundList()
  },
  methods: {
    //部门模糊查询
    initBranchList(query) {
      console.log('query', query)
      if (!query) {
        return
      }
      this.query = query
      this.startLoading()
      let params = {
        name: query
      }
      this.$request({
        url: this.$interfaces.getDeptName,
        data: params,
        method: 'post'
      })
        .then(res => {
          console.log('res', res)
          this.endLoading()
          if (res.code == 200) {
            this.branchList = res.data
          }
        })
        .catch(error => {
          this.endLoading()
        })
    },
    //模糊查询
    branchChange(item) {
      // this.branchItem = item
      this.branchItem.id = item.id
      this.branchItem.name = item.name
    },
    handleCheckAllExport(val) {
      // console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
          this.$set(this.tableData[i], 'exportChecked', val)
      }
    },
    exportHandleCheckOne() {
      // console.info('check one change is ', val)
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量

      this.tableData.forEach(item => {
        if (item.exportChecked) {
          someStatusCount++
        }
      })
      this.checkAllExport = totalCount === someStatusCount ? true : false
    },
    handleCheckAll(val) {
      // console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].orderStatus == '7') {
          this.$set(this.tableData[i], 'checked', val)
        }
      }
    },
    handleCheckOne() {
      // console.info('check one change is ', val)
      let totalCount
      let someStatusCount = 0 //选到的数量

      let filterArr = this.tableData.filter(item => {
        return item.orderStatus == '7'
      })
      totalCount = filterArr.length //单选的数量

      this.tableData.forEach(item => {
        if (item.checked) {
          someStatusCount++
        }
      })
      this.checkAll = totalCount === someStatusCount ? true : false
    },
    refundNetHandle(type, id) {
      let refundOrderIds = []
      if (type == 'one') {
        refundOrderIds = [id]
      } else {
        let checkedArr = this.tableData.filter(item => item.checked)
        if (checkedArr.length == 0) {
          this.$message({
            message: '请至少选择一条转补缴的扣款记录！',
            type: 'warning'
          })
          return
        }
        let idArr = checkedArr.map(item => {
          return item.id
        })
        refundOrderIds = idArr
      }
      this.refundNet(refundOrderIds)
    },
    refundNet(refundOrderIds) {
      this.startLoading()
      this.$request({
        url: this.$interfaces.rechargeNet,
        data: { refundOrderIds },
        method: 'post'
      })
        .then(res => {
          this.endLoading()
          console.log('重试接口', res)
          if (res.code == 200) {
            let totalMsg = '总共:' + res.data.total + '条,'
            let successMsg = '成功:' + res.data.success + '条,'
            let failMsg = '失败:' + res.data.fail + '条。'
            this.$message({
              message: totalMsg + successMsg + failMsg,
              type: 'success'
            })
            this.getLogoutRefundList()
          }
        })
        .catch(error => {})
    },
    refundTypeChange(val) {
      let tmpAtrr = JSON.parse(JSON.stringify(this.needRefundListAll))
      console.log(tmpAtrr, '<<---------val')
      if (val == '8') {
        tmpAtrr.filter((item, index) => {
          if (item.value == '0') {
            tmpAtrr.splice(index, 1)
          }
        })
      }
      this.needRefundList = tmpAtrr
    },
    gxCarTypeChange() {
      this.onSearchHandle()
    },
    sortChange(e) {
      let params = JSON.parse(JSON.stringify(this.search))
      params.sort = ''
      params.orderByField = ''
      if (e.order == 'descending') {
        params.sort = 'desc'
      }
      if (e.order == 'ascending') {
        params.sort = 'asc'
      }
      if (params.sort) {
        params.orderByField = e.prop || ''
      }
      this.search = params
      this.search.pageNo = 1
      this.getLogoutRefundList()
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getLogoutRefundList() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.startTime = params.startTime
        ? moment(params.startTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.endTime = params.endTime
        ? moment(params.endTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      if (params.refund_type != '1' && params.refund_type != '8') {
        delete params.gxCarType
        delete params.needRefund
      }
      if (params.refund_type != '9') {
        //只有互联网退费才有互联网退费流水号与互联网账户信息
        delete params.otherPayId
        delete params.netMobile
        delete params.netId
        delete params.netName
      }
      if (params.refund_type == '8' && params.needRefund == '2') {
        params.refund_type = '11'
      }

      if (params.refund_type == '8' && !params.needRefund) {
        //客账退费选择全部退款类型时传8和11
        params.refund_type = '8,11'
        console.log('params', params)
      }
      //新增创建部门查询
      params.branchName = this.branchItem.id

      this.$store
        .dispatch('refund/getLogoutRefundList', params)
        .then(res => {
          this.loading = false
          this.tableData = res.pageList.records
          this.total = res.pageList.total
          this.allAmount = res.allAmount
        })
        .catch(err => {
          this.loading = false
        })
    },
    setTag(id, sign) {
      console.log('sign', sign)
      let params = {
        ids: [id],
        refund_type: 1,
        sign: sign == 1 ? 0 : 1
      }
      this.$store
        .dispatch('refund/setTag', params)
        .then(res => {
          this.getLogoutRefundList()
        })
        .catch(err => {})
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach(key => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch(err => {})
    },
    changePage(page) {
      this.search.pageNo = page
      this.getLogoutRefundList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getLogoutRefundList()
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.branchItem.name = ''
      this.branchItem.id = ''
      // this.search.refund_type = 1
      this.search.state = 1
      this.search.needRefund = '1'
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.pageNo = 1
      this.search.pageSize = 50
    },
    onSearchHandle() {
      this.search.pageNo = 1
      this.getLogoutRefundList()
    },
    //跳转制表页面
    toMakeTable() {
      this.$router.push({
        path: './makeTable'
      })
    },
    toDetail(id, type) {
      //跳转审核页面
      this.$router.push({
        path: './tollRefundDetail',
        query: {
          id: id,
          refundType: type,
          search: JSON.stringify(this.search)
        }
      })
    },
    // 归档导出
    exportHandle() {
      let { startTime, endTime } = this.search
      console.log(startTime, endTime, 'search')
      if (!startTime || !endTime) {
        this.$message.error('请输入创建时间范围')
        return
      }
      if (moment(startTime).isAfter(endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (moment(endTime).diff(moment(startTime), 'months') > 12) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不能大于三个月'
        })
        return
      }

      let params = {
        name: 'refundFinish',
        refundStaTime: moment(startTime).format('YYYY-MM-DD'),
        refundEndTime: moment(endTime).format('YYYY-MM-DD')
      }
      // console.log(params, 'exportHandle params')
      this.sendReportRequest(params)
    },
    // 报表请求
    async sendReportRequest(params) {
      this.loading = true
      this.$store
        .dispatch('report/report', params)
        .then(res => {
          let url = res
          let decodeUrl = decode(url)
          // console.log(decodeUrl,'地址')
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
    exportFile() {
      
      let checkedArr = this.tableData.filter(item => item.exportChecked)
      let ids = checkedArr.map(item => {
          return item.id
        })
      console.log(this.search, ids,'this.search')
      // return
      let {
        startTime,
        endTime,
        refund_type,
        custName,
        branchName,
        needRefund
      } = this.search
      console.log(startTime, endTime, 'search')
      if ((!startTime || !endTime )&& ids.length <= 0) {
        this.$message.error('请选择创建时间起始及截至时间')
        return
      }
      if (moment(startTime).isAfter(endTime)&& ids.length <= 0) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (moment(endTime).diff(moment(startTime), 'months') > 11 && ids.length <= 0) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不能大于一年'
        })
        return
      }
      
      let params = {
        startTime: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: moment(endTime).format('YYYY-MM-DD HH:mm:ss'),
        refund_type,
        custName,
        branchName,
        needRefund
      }
      //新增创建部门查询
      params.branchName = this.branchItem.id
      if (params.refund_type == '8' && params.needRefund == '2') {
        params.refund_type = '11'
      }

      if (params.refund_type == '8' && !params.needRefund) {
        //客账退费选择全部退款类型时传8和11
        params.refund_type = '8,11'
        console.log('params', params)
      }
      // 如果单选或多选导出
      if(ids.length > 0){
        params.ids = ids
        delete params.startTime
        delete params.endTime
      }
      console.log(params, 'params')

      this.downloadClick(params)
    },
    /**
     * 下载
     */
    async downloadClick(params) {
      const response = await refundExport(params)
      // window.open(uploadPath)
      const link = document.createElement('a')
      let blob = new Blob([response]) //构造一个blob对象来处理数据
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      let titleObj = this.refundType.filter(item => item.value == this.search.refund_type)[0]
      link.download = `${titleObj.label}订单.xlsx` //下载的文件名
      document.body.appendChild(link)
      link.click() // 执行下载
      document.body.removeChild(link) // 释放标签
    },
    /**
    * 权限校验
    */
    checkAuth(auth){
      const permissions = this.$store.getters && this.$store.getters.permissions
      let hasPermissions = false // 本地开放全部权限，默认应为false
      for (let i of auth) {
        if (permissions.includes(i)) {
          hasPermissions = true
        }
      }
      console.log(hasPermissions)
      return hasPermissions
    }
  }
}
</script>

<style lang="scss" scoped>
// .toll-record {
//   padding: 20px;
//   .table {
//     margin: 0px 0 10px 0;
//   }
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    margin-top: 10px;
    color: red;
  }
  .btn-wrapper {
    margin-left: 40px;
    margin-top: 10px;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
    width: 140px !important;
    white-space: nowrap;
  }

  //操作穿透样式临时解决
  ::v-deep.el-table__fixed-body-wrapper {
    top: 40px !important;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  //操作穿透样式临时解决
  ::v-deep.el-table__fixed-body-wrapper {
    top: 40px !important;
  }
}
</style>
