<template>
  <div>
    <el-dialog
      width="30%"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
      <div class="tiele">请填写驳回理由:</div>
      <el-input
        type="textarea"
        :rows="3"
        resize="none"
        placeholder="请输入驳回理由"
        v-model="remark"
      >
      </el-input>

      <div class="foot">
        <el-button size="small" @click="reject()" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      remark: '',
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    reject() {
      this.$emit('setremark', this.remark)
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}  
::v-deep .el-dialog {
  margin-top: 0 !important;
  max-width: 500px;
  min-width: 400px;
}
.tiele {
  font-weight: 700;
  margin-bottom: 15px;
}
.textarea {
  text-align: left;
  margin: 0px 20px;
  height: 100px;
  width: 100%;
  margin: auto;
  padding: 4px;
  border-radius: 3px;
  border: 1px solid #b7bac0;
  // resize: vertical;
  overflow: auto;
  outline: none;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.foot {
  margin-top: 20px;
  text-align: center;
}
</style>