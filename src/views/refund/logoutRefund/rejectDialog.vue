<template>
  <el-dialog
    title="订单驳回确认"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="reject-content">
      <!-- 订单详情信息 -->
      <div class="order-info">
        <div class="info-row">
          <span class="label">用户编号：</span>
          <span class="value">{{ orderData.custMastId || '--' }}</span>
          <span class="label">银行户名：</span>
          <span class="value">{{ orderData.bankAccount || '--' }}</span>
        </div>
        <div class="info-row">
          <span class="label">银行卡号：</span>
          <span class="value">{{ orderData.bankNo || '--' }}</span>
          <span class="label">开户行：</span>
          <span class="value">{{ orderData.bankName || '--' }}</span>
        </div>
        <div class="info-row">
          <span class="label">退费金额（元）：</span>
          <span class="value amount">{{ orderData.amount || '--' }}</span>
        </div>
      </div>

      <!-- 驳回流程选择 -->
      <div class="reject-process">
        <div class="process-title">订单流程驳回至：</div>
        <el-select 
          v-model="selectedProcess" 
          placeholder="请选择驳回节点"
          style="width: 100%;"
        >
          <el-option
            v-for="item in processOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>


    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'RejectDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedProcess: '',
      // 流程节点选项 
      processOptions: [
        { value: '3', label: '网点审核' },
        { value: '4', label: '运营部复核' }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
      if (!val) {
        this.resetForm()
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    handleConfirm() {
      if (!this.selectedProcess) {
        this.$message.warning('请选择驳回节点')
        return
      }

      // 准备驳回数据
      const rejectData = {
        orderId: this.orderData.id,
        processNode: this.selectedProcess,
        orderData: this.orderData
      }

      // 触发确认事件
      this.$emit('confirm', rejectData)
      this.dialogVisible = false
    },
    resetForm() {
      this.selectedProcess = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.reject-content {
  .order-info {
    background: #f5f7fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;

    .info-row {
      display: flex;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 120px;
        color: #606266;
        font-size: 14px;
      }

      .value {
        flex: 1;
        color: #303133;
        font-size: 14px;
        margin-right: 20px;

        &.amount {
          color: #e6a23c;
          font-weight: bold;
        }
      }
    }
  }

  .reject-process {
    margin-bottom: 20px;

    .process-title {
      margin-bottom: 8px;
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
