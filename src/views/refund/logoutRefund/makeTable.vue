<template>
  <div class="make-table" v-loading.fullscreen.lock="showLoading">
    <div class="search">
      <dart-search :formSpan="24" :gutter="20" ref="searchForm1" label-position="right" :model="search" :fontWidth="2">
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="制表状态：" prop="tabFlag">
            <el-select v-model="search.tab_flag" placeholder="请选择">
              <el-option v-for="item in tabFlagList" :key="item.index" :label="item.label" :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="创建时间起始：" prop="time_sta">
            <el-date-picker type="datetime" placeholder="选择日期时间" v-model="search.time_sta">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="创建时间截至：" prop="time_end">
            <el-date-picker type="datetime" placeholder="选择日期时间" default-time="23:59:59" v-model="search.time_end">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="退费类型：" prop="refund_type">
            <el-select v-model="search.refund_type" placeholder="请选择">
              <el-option v-for="item in refundType" :key="item.index" :label="item.label" :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="审批标识：" prop="payment_flag">
            <el-select v-model="search.payment_flag" placeholder="请选择">
              <el-option v-for="item in paymentFlagOptions" :key="item.index" :label="item.label" :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle"><i
                  class="el-icon-search"></i> 搜索</el-button>
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="makeTable">
                <i class="el-icon-download"></i> 勾选制表</el-button>
              <el-button size="mini" type="primary" v-if="tableData.length > 0" @click="makeTable('all')">
                <i class="el-icon-download"></i> 全部制表
              </el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table-info">退款总金额：{{ refundAmount }}元</div>
    <div class="table">
      <el-table v-loading="loading" :data="tableData" :align="center" :header-align="center" border height="100%"
        style="width: 100%; margin-bottom: 20px" :row-style="{ height: '40px' }" :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }" :header-cell-style="{ padding: '0px' }" row-key="id"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40"></el-table-column>
        <el-table-column prop="id" align="center" min-width="100" label="订单号" />
        <el-table-column prop="custMastId" align="center" min-width="100" label="用户编号" />
        <!-- <el-table-column
          prop="orderStatus"
          align="center"
          min-width="120"
          label="申请单状态"
        >
          <template slot-scope="scope">
            {{ getType(orderStatusList, scope.row.orderStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="completeTime"
          align="center"
          min-width="160"
          label="订单完成时间"
        />
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="250"
          label="ETC卡号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.cardNo }}
              </div>
              <span>{{ scope.row.cardNo }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column prop="carColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.carColor) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="bankAccount" align="center" min-width="120" label="银行账户名" />
        <el-table-column prop="bankNo" align="center" min-width="200" label="银行卡号">
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.bankNo }}
              </div>
              <span>{{ scope.row.bankNo }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="bankName" align="center" min-width="130" label="开户行" />
        <!-- <el-table-column
          prop="createdBranchName"
          align="center"
          min-width="130"
          label="创建部门"
        />
        <el-table-column
          prop="createdByName"
          min-width="100"
          align="center"
          label="创建人"
        />
        -->
        <el-table-column prop="amount" align="center" label="退款金额(元)" min-width="100">
          <template slot-scope="scope"> ￥{{ scope.row.amount }} </template>
        </el-table-column>
        <!-- <el-table-column
          prop="opBranchName"
          align="center"
          label="操作员部门"
          min-width="150"
        />
        <el-table-column
          prop="opName"
          align="center"
          label="操作员"
          min-width="150"
        />
        <el-table-column
          prop="updatedTime"
          align="center"
          label="更新时间"
          min-width="160"
        />
        <el-table-column prop="source" align="center" label="来源">
          <template slot-scope="scope">
            {{ getType(sourceList, scope.row.source) }}
          </template>
        </el-table-column> -->
        <!-- <el-table-column prop="tabFlag" align="center" label="制表状态">
          <template slot-scope="scope">
            {{ getType(tabFlagList, scope.row.tabFlag) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="createdTime" min-width="160" align="center" label="创建时间" />
        <el-table-column prop="updatedTime" align="center" label="更新时间" min-width="160" />
        <!-- <el-table-column
          prop="transferMatch"
          align="center"
          label="财务转账结果比对"
          min-width="140"
        >
          <template slot-scope="scope">
            {{ getType(transferMatchList, scope.row.transferMatch) }}
          </template>
        </el-table-column> -->
        <el-table-column fixed="right" label="操作" header-align="center" min-width="160" align="center">
          <template slot-scope="scope">
            <el-button type="primary" size="mini" @click="toDetail(scope.row.id)">详情</el-button>
            <el-button  v-permisaction="['refund:tableReturnOrderStatus']" type="danger" size="mini" @click="handleReject(scope.row)"
              style="margin-left: 5px;">驳回</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="paymentFlag" align="center" label="审批标识" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.paymentFlag == '1' ? '重提' : '首次' }}
          </template>
        </el-table-column>
      </el-table>
      <!-- <div v-if="total > search.page_size" class="pagination"> -->
    </div>
    <div class="pagination">
      <el-pagination background @size-change="handleSizeChange" @current-change="changePage"
        :current-page="search.page_index" :page-sizes="[20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500]"
        :page-size="search.page_size" layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>

    <!-- 驳回弹窗 -->
    <reject-dialog :visible.sync="rejectDialogVisible" :order-data="currentRejectOrder"
      @confirm="handleRejectConfirm" />
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import rejectDialog from './rejectDialog'
import {
  paymentFlagOptions,
  refundTypeAll
} from '@/common/const/optionsData.js'

import { decode } from 'js-base64'
import {
  refundType,
  redundOrderStatusList
} from '@/common/const/optionsData.js'
import { tableReturnOrderStatus } from '@/api/refund'
var moment = require('moment')
import float from '@/common/method/float'
export default {
  name: 'MakeTable',
  components: {
    dartSearch,
    dartSearchItem,
    rejectDialog
  },
  data() {
    return {
      refundAmount: 0, // 退款金额
      loading: false,
      showLoading: false,
      disabled: true,
      center: 'center',
      total: 0,
      rejectDialogVisible: false, // 驳回弹窗显示状态
      currentRejectOrder: {}, // 当前要驳回的订单数据

      search: {
        // passId: '', //退费流水号
        // carColor: '', //车牌颜色
        // carNo: '', //车牌
        // cardNo: '', //卡号
        // bankAccount: '', //银行账户名
        // bankNo: '', //银行卡号
        // custMastId: '',
        refund_type: '0', //0-全部,1-注销退费，2-通行费退费 3-卡账退费
        // source: '',
        time_sta: '', //创建开始时间
        time_end: '', //创建截止时间
        // state: 1, //0是待审核，1是全部
        tab_flag: 0, ///0未制表，1是已制表
        page_index: 1,
        page_size: 20,
        payment_flag: '0'
      },
      tableData: [],
      multipleSelection: [],
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [] //退费状态字典
      },
      //来源：1-网点；2-C端
      stateList: [
        {
          value: 0,
          label: '待审核'
        },
        {
          value: 1,
          label: '全部'
        }
      ],
      refundType: refundTypeAll, //退费类型
      //订单流程：0-用户申请退款1-业务员审核2-清算中3-网点审核4-运营部复核5-制表6-财务打款9-已归档
      orderStatusList: redundOrderStatusList,
      //来源：1-网点；2-C端
      sourceList: [
        {
          value: 1,
          label: '网点'
        },
        {
          value: 2,
          label: 'C端'
        }
      ],
      //制表状态：0-未制表；1—重新制表
      tabFlagList: [
        {
          value: 0,
          label: '未制表'
        },
        {
          value: 1,
          label: '重新制表'
        }
      ],
      //0-未比对；1-比对成功；2-比对失败
      transferMatchList: [
        {
          value: 0,
          label: '未比对'
        },
        {
          value: 1,
          label: '比对成功'
        },
        {
          value: 2,
          label: '比对失败'
        }
      ],
      paymentFlagOptions
    }
  },
  created() {
    this.refundType.filter(item => {
      if (item.value == '8') {
        item.label = '客账退款到互联网账户'
      }
    })
    this.getTableList()
    this.getTypeList()
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getTableList() {
      //列表查询前判断按钮显示
      this.search.refund_type == '0'
        ? (this.disabled = true)
        : (this.disabled = false)

      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.time_sta = params.time_sta
        ? moment(params.time_sta).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.time_end = params.time_end
        ? moment(params.time_end).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.$store
        .dispatch('refund/getTable', params)
        .then(res => {
          console.log('调用接口', res)
          this.multipleSelection = []
          this.loading = false
          this.tableData = res.records
          this.total = res.total
        })
        .catch(err => {
          this.loading = false
        })
    },
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.multipleSelection = []
      selection.forEach(item => {
        if (!this.multipleSelection.includes(item.id)) {
          this.multipleSelection.push(item.id)
        }
      })
      this.refundAmount = 0
      if (selection && selection.length) {
        for (let i = 0; i < selection.length; i++) {
          this.refundAmount = float.add(this.refundAmount, selection[i].amount)
        }
      }
    },
    toDetail(id) {
      //跳转审核页面
      this.$router.push({
        path: './tollRefundDetail',
        query: {
          id: id
        }
      })
    },
    //制表
    makeTable(allFlag) {
      console.log(this.multipleSelection, 'this.multipleSelection')
      if (allFlag != 'all' && this.multipleSelection.length == 0) {
        this.$message({
          message: '请先选中一条记录！',
          type: 'warning'
        })
        return
      }
      this.showLoading = true

      let tmpSearch = JSON.parse(JSON.stringify(this.search))

      let params = {
        id: this.multipleSelection,
        refund_type: this.search.refund_type,
        tab_flag: this.search.tab_flag,
        all_flag: allFlag == 'all' ? 1 : 0,
        payment_flag: this.search.payment_flag,
        time_sta: tmpSearch.time_sta
          ? moment(tmpSearch.time_sta).format('YYYY-MM-DD HH:mm:ss')
          : '',
        time_end: tmpSearch.time_end
          ? moment(tmpSearch.time_end).format('YYYY-MM-DD HH:mm:ss')
          : ''
      }
      console.log('params=====', params)
      this.$store
        .dispatch('refund/makeTable', params)
        .then(res => {
          // window.open(res.file_path)
          let that = this
          this.getBlob(res.file_path, function (blob) {
            that.saveAs(blob, '退费制表.xls')
          })
          this.showLoading = false
          this.getTableList()
        })
        .catch(err => {
          this.showLoading = false
        })
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function () {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach(key => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch(err => { })
    },
    changePage(page) {
      this.search.page_index = page
      this.getTableList()
    },
    handleSizeChange(page_size) {
      this.search.page_size = page_size
      this.getTableList()
    },
    //重置
    onReSetHandle() {
      // for (const key in this.search) {
      //   this.search[key] = ''
      // }
      this.multipleSelection = []
      this.search.payment_flag = '0'
      this.search.refund_type = '0'
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.page_index = 1
      this.search.page_size = 20
    },
    onSearchHandle() {
      this.search.page_index = 1
      this.getTableList()
    },
    // 处理驳回操作
    handleReject(row) {
      this.currentRejectOrder = row
      this.rejectDialogVisible = true
    },
    // 确认驳回
    async handleRejectConfirm(rejectData) {
      console.log('驳回确认', rejectData)

      // 准备接口参数
      const params = {
        refund_type: this.currentRejectOrder.refundType, // 使用搜索条件中的退费类型
        orderStatus: rejectData.processNode, // 驳回到的流程节点
        id: this.currentRejectOrder.id
      }

      // 调用驳回接口
      let res = await tableReturnOrderStatus(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.rejectDialogVisible = false
        // 刷新列表
        this.getTableList()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.make-table {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table-info {
    position: relative;
    padding: 8px 48px 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid #abdcff;
    background-color: #f0faff;
    color: #515a6e;
    font-weight: 500;
  }

  .table {
    padding: 20px 20px 20px 20px;
    flex: 1;
    height: 0;
    margin-top: 0px !important;
    background-color: #fff;
  }

  .pagination {
    margin: 10px 0;
  }

  .btn-wrapper {
    margin-left: 40px;
    margin-top: 10px;
  }

  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  //   margin-bottom: 0;
  // }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }

  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
  //   width: 150px !important;
  //   white-space: nowrap;
  // }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
