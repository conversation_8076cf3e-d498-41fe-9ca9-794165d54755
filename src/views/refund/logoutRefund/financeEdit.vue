<template>
  <div>
    <el-dialog
      width="500px"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      :appendToBody="true"
      class="form_dialog"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
      <div class="title" slot="title">修改财务打款信息</div>
      <el-form ref="form" label-width="140px" :model="form" :rules="rules">
        <el-form-item label="财务打款时间：" prop="paymentTime">
          <el-date-picker
            type="datetime"
            style="width: 100%"
            placeholder="选择日期时间"
            v-model="form.paymentTime"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="财务打款结果：" prop="paymentResultsCode">
          <el-select
            v-model="form.paymentResultsCode"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in paymentResultsCodeList"
              :key="item.index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.paymentResultsCode == '0'"
          label="（打款失败）原因："
          prop="paymentFailureReason"
        >
          <el-input v-model="form.paymentFailureReason"></el-input>
        </el-form-item>
        <el-form-item label="修改意见：">
          <el-input v-model="form.remark" maxlength="22"></el-input>
        </el-form-item>
      </el-form>
      <div v-show="showTip">
        修改提示：<span style="color: red; line-height: 24px"
          >打款时间必须大于上一次网点审核时间，您当前查看的转账结果不是最新的转账结果。</span
        >
      </div>
      <div class="foot">
        <el-button
          size="small"
          :disabled="loading"
          @click="update()"
          type="primary"
          >修改</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
var moment = require('moment')
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
    },
    opList: {
      type: Array,
    },
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: false,
      showTip: false,
      form: {
        paymentResultsCode: '',
        paymentFailureReason: '',
        paymentTime: '',
        remark: '',
        id: null,
        refundType: null,
      },
      opArrList: [],
      paymentResultsCodeList: [
        //退费途径
        { value: '0', label: '失败' },
        { value: '1', label: '成功' },
      ], //退费类型
      rules: {
        paymentTime: [
          { required: true, message: '请选择财务打款时间', trigger: 'blur' },
        ],
        paymentResultsCode: [
          { required: true, message: '选择财务打款结果', trigger: 'change' },
        ],
      },
    }
  },
  watch: {
    'form.paymentTime'(time) {
      // console.log('选择的时间', time)
      if (time) {
        let arrList = this.opArrList.filter((item) => {
          return item.orderStatus == 3
        })
        let opTime = arrList[0].opTime
        let selectTime = moment(time).format('YYYY-MM-DD HH:mm:ss')
        if (moment(selectTime).isBefore(opTime)) {
          this.showTip = true
          this.loading = true
        } else {
          this.showTip = false
          this.loading = false
        }
      }
    },
    opList(val) {
      this.opArrList = val
    },
    detailData(val) {
      this.setFormData(val)
    },
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    //回填数据
    setFormData(val) {
      this.form.id = val.id
      // this.form.paymentResults = val.paymentResults || ''
      // this.form.paymentTime = val.paymentTime || ''
      // this.form.paymentTime = '2021-12-04 09:50:03'
      // this.form.remark = val.remark || ''
      this.form.refundType = val.refundType
    },
    update() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.form))
      params.paymentTime = params.paymentTime
        ? moment(params.paymentTime).format('YYYY-MM-DD HH:mm:ss')
        : ''

      if (params.paymentResultsCode == '1') {
        //成功不需要失败原因
        delete params.paymentFailureReason
      }

      console.log(params)
      this.$request({
        url: this.$interfaces.financialRevision,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.loading = false
          if (res) {
            this.$emit('updateCard')
            this.handleCloseIcon()
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('err', err)
        })
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .form_data .el-form-item__content {
  display: flex;
}
// ::v-deep .form_data .el-form-item__label {
//   margin-left: -80px;
// }
.title {
  text-align: center;
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 20px;
}
.foot {
  margin-top: 20px;
  text-align: center;
}
</style>