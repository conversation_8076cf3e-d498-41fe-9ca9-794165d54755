<template>
  <el-dialog
    title="放弃退款"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="abandon-content">
      <el-form :model="form" :rules="rules" ref="abandonForm" label-width="150px">
        <el-form-item label="网点主管账户：" prop="managerAccount">
          <el-input
            v-model="form.managerAccount"
            placeholder="请输入网点主管账户"
            clearable
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="网点主管密码：" prop="managerPassword">
          <el-input
            v-model="form.managerPassword"
            type="password"
            placeholder="请输入网点主管密码"
            clearable
            show-password
            autocomplete="new-password"
          />
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AbandonRefundDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: {
        managerAccount: '',
        managerPassword: ''
      },
      rules: {
        managerAccount: [
          { required: true, message: '请输入网点主管账户', trigger: 'blur' }
        ],
        managerPassword: [
          { required: true, message: '请输入网点主管密码', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
      if (!val) {
        this.resetForm()
      }
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },
    handleConfirm() {
      this.$refs.abandonForm.validate((valid) => {
        if (valid) {
          this.loading = true
          
          // 模拟验证过程
          setTimeout(() => {
            this.loading = false
            
            // 触发确认事件，传递表单数据
            this.$emit('confirm', {
              managerAccount: this.form.managerAccount,
              managerPassword: this.form.managerPassword
            })
            
            this.dialogVisible = false
          }, 1000)
        } else {
          return false
        }
      })
    },
    resetForm() {
      if (this.$refs.abandonForm) {
        this.$refs.abandonForm.resetFields()
      }
      this.form = {
        managerAccount: '',
        managerPassword: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.abandon-content {
  padding: 20px 0;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-form-item {
  margin-bottom: 20px;

  .el-form-item__label {
    font-weight: 500;
    text-align: right;
    padding-right: 12px;
    line-height: 40px;
  }

  .el-form-item__content {
    line-height: 40px;
  }

  .el-input__inner {
    height: 40px;
    line-height: 40px;
  }
}
</style>
