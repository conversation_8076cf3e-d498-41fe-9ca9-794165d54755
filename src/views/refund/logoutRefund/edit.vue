<template>
  <div>
    <el-dialog
      width="50%"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
      <div class="title">修改银行信息</div>
      <el-form ref="form" class="form_data" :model="form" :rules="rules">
        <el-form-item label="开户行：" prop="bankName">
          <el-autocomplete
            class="my_input"
            style="width: 100%"
            clearable
            :fetch-suggestions="queryBankName"
            @select="selectBankName"
            v-model="form.bankName"
            placeholder="银行账户开户行"
          ></el-autocomplete>
        </el-form-item>
        <el-form-item label="银行账户名：" prop="bankAccount">
          <el-input v-model="form.bankAccount"></el-input>
        </el-form-item>
        <el-form-item label="银行卡号：" prop="bankNo">
          <el-input v-model="form.bankNo" maxlength="30"></el-input>
        </el-form-item>
      </el-form>
      <div class="foot">
        <el-button size="small"
                   @click="update()"
                   type="primary">修改</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { bankName } from '@/common/const/optionsData.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
    },
  },
  data() {
    return {
      bankName,
      dialogFormVisible: false,
      form: {
        bankAccount: '',
        bankNo: '',
        bankName: '',
        id: null,
        orderStatus: '',
        refund_type: 1,
      },
      rules: {
        bankName: [
          { required: true, message: '请输入开户行', trigger: 'blur' },
        ],
        bankAccount: [
          { required: true, message: '请输入银行账户名', trigger: 'blur' },
        ],
        bankNo: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' },
        ],
      },
    }
  },
  watch: {
    detailData(val) {
      this.setFormData(val)
    },
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    //回填数据
    setFormData(val) {
      this.form.id = val.id
      this.form.bankAccount = val.bankAccount
      this.form.bankNo = val.bankNo
      this.form.bankName = val.bankName
      this.form.orderStatus = val.orderStatus
      this.form.refund_type = val.refundType
    },
    update() {
      this.$refs['form'].validate((valid) => {
      if (valid) {
        this.$store
          .dispatch('refund/editCardInfo', this.form)
          .then((res) => {
            this.$emit('updateCard')
            this.handleCloseIcon()
          })
          .catch((err) => {})
      } else {
        return false
      }
      })
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
    queryBankName(queryString, cb) {
      let bankName = this.bankName.map((item) => {
        return {
          value: item.label,
          address: item.value,
        }
      })
      let results = queryString
        ? bankName.filter(this.createFilter(queryString))
        : bankName
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    selectBankName(item) {
      this.form.bankName = item.value
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        )
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .form_data .el-form-item__content {
  display: flex;
}
.title {
  text-align: center;
  font-weight: 700;
  margin-bottom: 15px;
  font-size: 20px;
}

.foot {
  margin-top: 20px;
  text-align: center;
}
</style>