<template>
  <div class="detail refund-detail" v-loading.fullscreen.lock="showLoading">
    <div class="info">
      <div class="btn-title">
        <div class="title">工单详情</div>
        <div
          class="btn"
          v-if="
            (detail.orderStatus == 3 && detail.control == 1) ||
            detail.orderStatus == 1
          "
        >
          <el-button
            v-permisaction="['refund:update']"
            type="primary"
            size="mini"
            @click="editBankHandle"
            >修改银行信息</el-button
          >
        </div>
      </div>

      <el-form label-width="180px" class="refund-detail-box">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="退费单号：">
              <div>{{ detail.id }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名称：">
              <div>
                {{ detail.custName ? detail.custName : detail.netLoginName }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户编号：">
              <div>
                {{ detail.custMastId ? detail.custMastId : detail.netUserNo }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-if="
            detail.refundType != '9' &&
            detail.refundType != '8' &&
            detail.refundType != '11'
          "
          :gutter="24"
        >
          <el-col :span="8">
            <el-form-item label="车牌颜色：">
              <div>{{ getType(typeList.carColors, detail.carColor) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号：">
              <div>{{ detail.carNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="ETC卡号：">
              <div>{{ detail.cardNo }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="detail.refundType == '11'">
          <el-col :span="8">
            <el-form-item label="账户名：">
              <div>{{ detail.bankAccount }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="互联网账户编号：">
              <div>{{ detail.bankNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="互联网账户名：">
              <div>{{ detail.bankAccount }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-else>
          <el-col :span="8">
            <el-form-item label="银行账户名：">
              <div>{{ detail.bankAccount }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行卡号：">
              <div>{{ detail.bankNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行：">
              <div>{{ detail.bankName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="金额：">
              <div>{{ detail.amount }} 元</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单流程：">
              <div>{{ getType(orderStatusList, detail.orderStatus) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              :label="
                detail.refundType == '11' ? '转账时间：' : '财务打款时间：'
              "
            >
              <div>{{ detail.paymentTime }}</div>
              <!-- <el-form-item label="退费类型：">
              <div>{{ getType(refundType, detail.refundType) }}</div>-->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              :label="
                detail.refundType == '11' ? '转账结果：' : '财务打款结果：'
              "
            >
              <div>{{ detail.paymentResults }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="detail.refundType != '11'">
            <el-form-item label="放弃退款：">
              <template v-if="detail.needRefund">
                <div
                  v-if="detail.needRefund == '1' || detail.needRefund == '2'"
                >
                  否
                </div>
                <div v-else-if="detail.needRefund == '0'">是</div>
                <div v-else>未知</div>
              </template>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款原因：">
              <div>{{ detail.reason }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="refund_type == '1' || refund_type == '12'">
          <el-col :span="8">
            <el-form-item label="广西卡类型：">
              <div>{{ getType(gxCardTypeAllOptions, detail.gxCarType) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卡发行时间：">
              <div>{{ detail.releaseDate2 }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="refund_type == '9'">
          <el-col :span="8">
            <el-form-item label="互联网账户ID：">
              <div>{{ detail.netId }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="互联网账户手机号：">
              <div>{{ detail.netMobile }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="互联网账户公司名：">
              <div>{{ detail.netCompanyName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="refund_type == '9'">
          <el-col :span="8">
            <el-form-item label="互联网账户用户名：">
              <div>{{ detail.netName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24" v-if="refund_type == '9'">
          <el-col :span="6">
            <el-form-item label="关联ETC用户：">
              <div>{{ detail.etcName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="ETC用户编号：">
              <div>{{ detail.etcId }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="ETC用户证件类型：">
              <div v-if="detail.etcType == '0'">
                {{ getType(personalOCRType, detail.etcTypeCode) }}
              </div>
              <div v-if="detail.etcType == '1'">
                {{ getType(enterpriseOCRType, detail.etcTypeCode) }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="ETC用户证件号码：">
              <div>{{ detail.etcNo }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="info" style="margin-top: 20px" v-if="refund_type == '2'">
      <div class="title">退款账户信息</div>
      <el-form label-width="180px" class="refund-detail-box">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="姓名：">
              <div>{{ contactInfo.custName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号：">
              <div>{{ contactInfo.custMobile }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="银行账户名：">
              <div>{{ contactInfo.bankAccount }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行卡号：">
              <div>{{ contactInfo.bankNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行：">
              <div>{{ contactInfo.bankName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div
      class="info"
      style="margin-top: 20px"
      v-if="refund_type == '2' && hasContact"
    >
      <div class="title">联系人附件</div>
      <div class="contact-box">
        <div class="box" v-if="attorneyUrl">
          <el-image
            class="contact-img"
            :src="attorneyUrl"
            :preview-src-list="[attorneyUrl]"
          ></el-image>
          <p class="title">附件</p>
        </div>
        <div class="box" v-if="bankPhotoUrl">
          <el-image
            class="contact-img"
            :src="bankPhotoUrl"
            :preview-src-list="[bankPhotoUrl]"
          ></el-image>
          <p class="title">银行卡</p>
        </div>
      </div>
    </div>
    <div class="info" style="margin-top: 20px">
      <div class="title">操作详情</div>
      <el-form label-width="180px" class="refund-detail-box">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="创建部门：">
              <div>{{ detail.createdBranchName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人：">
              <div>{{ detail.createdByName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间：">
              <div>{{ detail.createdTime }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="当前操作部门：">
              <div>{{ detail.opBranchName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="当前操作员：">
              <div>{{ detail.opName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="更新时间：">
              <div>{{ detail.updatedTime }}</div>
            </el-form-item>
          </el-col>
        </el-row>-->
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="来源：">
              <div>{{ getType(sourceList, detail.source) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制表状态：">
              <!-- <div>{{ getType(tabFlagList, detail.tabFlag) }}</div> -->
              <div v-if="detail.tabFlag == 0 && detail.orderStatus == 5">
                制表中
              </div>
              <div v-if="detail.tabFlag == 1 && detail.orderStatus == 5">
                重新制表中
              </div>
              <div v-if="detail.orderStatus < 5">未制表</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="财务转账结果比对：">
              <div>{{ getType(transferMatchList, detail.transferMatch) }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="info" style="margin-top: 20px" v-if="refund_type != '2'">
      <div class="title">档案资料</div>
      <div
        class="archives-box"
        v-if="detail.orderStatus == 3 || detail.orderStatus == 1"
        style="padding: 30px"
      >
        <photograph
          :urlList.sync="imgList"
          :customerId="
            detail.refundType == 9 ? detail.netUserNo : detail.custMastId
          "
          :vehicle_code="detail.carNo"
          :vehicle_color="detail.carColor"
          :other_code="otherCode || ''"
          @getDetailImgs="getDetailImgs"
          :fileUUID="fileUUID"
        />
      </div>
      <div v-else class="archives-box" style="padding: 10px">
        <div class="imgdiv">
          <div v-for="(item, index) in imgList" :key="index">
            <div class="archives-item" v-if="item.file_url">
              <el-image
                :preview-src-list="previewImgList"
                :src="item.file_url"
              ></el-image>
              <p class="demonstration">{{ item.lable }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="refund_type == '2'" class="info" style="margin-top: 20px">
      <div class="title">关联退款订单</div>
      <div class="table" style="padding: 20px">
        <el-table
          :data="refundTableData"
          :align="center"
          :header-align="center"
          border
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '54px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '54px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column
            prop="refundOrder.id"
            align="center"
            label="订单号"
            min-width="160"
          />
          <el-table-column
            prop="refundOrder.cardNo"
            align="center"
            label="ETC卡号"
            min-width="180"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">{{ scope.row.refundOrder.cardNo }}</div>
                <span>{{ scope.row.refundOrder.cardNo }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundOrder.carNo"
            align="center"
            label="车牌"
            min-width="100"
          />
          <el-table-column
            prop="refundOrder.refundDetailId"
            align="center"
            label="退费交易编号"
            min-width="150"
          >
            <!-- <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.refundOrder.refundDetailId }}
                </div>
                <span>{{ scope.row.refundOrder.refundDetailId }}</span>
              </el-tooltip>
            </template> -->
            <template slot-scope="scope">
              <span
                @click="showTransactionDialog(scope.row.refundOrder)"
                class="line-text"
                >{{ scope.row.refundOrder.transactionOrPassId }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="refundOrder.bankRefundSern"
            align="center"
            min-width="150"
            label="退费流水号"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.refundOrder.bankRefundSern }}
                </div>
                <span>{{ scope.row.refundOrder.bankRefundSern }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundOrder.refundAmount"
            align="center"
            min-width="150"
            label="退费金额(元)"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.refundOrder.refundAmount | moneyFilter }}
                </div>
                <span>
                  {{ scope.row.refundOrder.refundAmount | moneyFilter }}
                </span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundOrder.amount"
            align="center"
            min-width="150"
            label="路方退费金额(元)"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.refundOrder.amount | moneyFilter }}
                </div>
                <span>{{ scope.row.refundOrder.amount | moneyFilter }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundOrder.sern"
            align="center"
            min-width="150"
            label="原扣款流水号"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">{{ scope.row.refundOrder.sern }}</div>
                <span>{{ scope.row.refundOrder.sern }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            align="center"
            min-width="200"
            label="备注"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">{{ scope.row.refundOrder.remark }}</div>
                <span>{{ scope.row.refundOrder.remark }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="pageStatus.total > pageStatus.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="pageStatus.page"
          :page-sizes="[10, 20, 50]"
          :page-size="pageStatus.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pageStatus.total"
        ></el-pagination>
      </div>
    </div>
    <div class="info" style="margin-top: 20px">
      <div class="title">操作记录</div>
      <div class="table" style="padding: 20px">
        <el-table
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '54px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '54px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column
            prop="createdTime"
            align="center"
            label="创建时间"
            min-width="160"
          />
          <el-table-column
            prop="orderStatus"
            align="center"
            label="订单流程"
            min-width="100"
          >
            <template slot-scope="scope">{{
              getType(orderStatusList, scope.row.orderStatus)
            }}</template>
          </el-table-column>
          <el-table-column prop="handleType" align="center" label="操作状态">
            <template slot-scope="scope">
              <span style="color: red" v-if="scope.row.handleType == 3">
                {{ getType(handleTypeList, scope.row.handleType) }}
              </span>
              <span v-else>
                {{ getType(handleTypeList, scope.row.handleType) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="opBranchName"
            align="center"
            label="操作网点"
          />
          <el-table-column prop="opName" align="center" label="操作人" />
          <el-table-column
            prop="opTime"
            align="center"
            min-width="160"
            label="操作时间"
          />
          <el-table-column
            prop="paymentResults"
            align="center"
            min-width="150"
            label="打款结果"
          />
          <el-table-column
            prop="paymentTime"
            align="center"
            min-width="160"
            label="打款时间"
          />
          <el-table-column
            prop="bankAccount"
            align="center"
            min-width="160"
            label="银行账户名"
          />
          <el-table-column
            prop="bankNo"
            align="center"
            min-width="150"
            label="银行卡号"
          />
          <el-table-column
            prop="bankName"
            align="center"
            min-width="160"
            label="开户行"
          />
          <el-table-column
            prop="remark"
            align="center"
            min-width="250"
            label="处理意见"
          />
          <el-table-column
            prop="operateType"
            align="center"
            min-width="100"
            label="操作方式"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.operateType == '0'">人工</span>
              <span v-if="scope.row.operateType == '1'">导入</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="info" style="margin-top: 20px">
      <div class="title">处理意见：</div>
      <div class="textarea-wrapper">
        <el-input
          ref="textarea"
          v-model="remark"
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 2 }"
          placeholder="请输入处理意见"
        ></el-input>
      </div>
    </div>
    <div class="foot">
      <div v-if="detail.orderStatus == 1">
        <el-button
          v-permisaction="['refund:issuerAudit']"
          style="margin: 0 20px"
          type="danger"
          @click="handle(2, detail.orderStatus)"
          >驳回</el-button
        >
        <el-button
          v-permisaction="['refund:issuerAudit']"
          style="margin: 0 20px"
          type="primary"
          v-loading="loading"
          @click="handle(1, detail.orderStatus, detail)"
          >通过</el-button
        >
      </div>
      <div v-if="detail.orderStatus == 3 && detail.control == 1">
        <el-button
          v-permisaction="['refund:branchAudit']"
          style="margin: 0 20px"
          type="danger"
          @click="handle(2, detail.orderStatus)"
          >驳回</el-button
        >
        <el-button
          v-permisaction="['refund:branchAudit']"
          style="margin: 0 20px"
          type="primary"
          v-loading="loading"
          @click="handle(1, detail.orderStatus, detail)"
          >通过</el-button
        >
      </div>
      <div v-if="detail.orderStatus == 4">
        <el-button
          v-permisaction="['refund:operationAudit']"
          style="margin: 0 20px"
          type="danger"
          @click="handle(2, detail.orderStatus)"
          >驳回</el-button
        >
        <el-button
          v-permisaction="['refund:operationAudit']"
          style="margin: 0 20px"
          type="primary"
          v-loading="loading"
          @click="handle(1, detail.orderStatus, detail)"
          >通过</el-button
        >
      </div>
      <el-button type="normal" style="margin: 0 20px" @click="back"
        >返回</el-button
      >
      <el-button
        v-permisaction="['refund:financialRevision']"
        v-if="
          (detail.orderStatus == '6' || detail.orderStatus == '9') &&
          detail.refundType != '11' &&
          detail.refundType != 8
        "
        style="margin: 0 20px"
        type="warning"
        @click="editFinanceVisible = true"
        >修改</el-button
      >
      <el-button
        v-permisaction="['refund:makeTableAudit']"
        v-if="detail.orderStatus == '5'"
        style="margin: 0 20px"
        type="info"
        @click="backTo(detail.id)"
        >返回运营部复核</el-button
      >
    </div>
    <reject :visible.sync="rejectVisible" @setremark="handle"></reject>
    <edit
      :visible.sync="editVisible"
      :detailData="detail"
      @updateCard="updateCard"
    ></edit>
    <financeEdit
      :visible.sync="editFinanceVisible"
      :detailData="detail"
      :opList="tableData"
      @updateCard="updateCard"
    ></financeEdit>
    <!-- 退费关联交易原始详情 -->
    <transactionDetail
      ref="transaction"
      :dialogFormVisible.sync="dialogFormVisible"
      :transactionId="transactionId"
      :cardNo="cardNo"
    ></transactionDetail>
  </div>
</template>

<script>
import reject from './reject'
import edit from './edit'
import financeEdit from './financeEdit'
import photograph from './photograph'
import transactionDetail from './transactionDetail'
import {
  gxCardTypeAllOptions,
  personalOCRType,
  enterpriseOCRType,
  refundType,
  redundOrderStatusList,
} from '@/common/const/optionsData.js'
export default {
  components: {
    reject,
    edit,
    financeEdit,
    photograph,
    transactionDetail
  },
  data() {
    return {
      gxCardTypeAllOptions,
      personalOCRType,
      enterpriseOCRType,
      loading: false,
      showLoading: false,
      editVisible: false,
      editFinanceVisible: false,
      rejectVisible: false,
      hasContact: false,
      dialogFormVisible: false, //退费交易详情
      center: 'center',
      refund_type: '1',
      //来源：1-网点；2-C端
      id: null,
      remark: '',
      search: '',
      fileUUID: '',
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [], //退费状态字典
      },
      previewImgList: [],
      imgdom: [
        {
          lable: '注销回执单',
          photo_code: '74',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '退款申请单',
          photo_code: '21',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '身份证(人像面)',
          photo_code: '22',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '身份证(国徽面)',
          photo_code: '23',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: this.$route.query.refundType == 9 ? '单位证件' : '单位介绍信',
          photo_code: '24',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '委托代办书',
          photo_code: '25',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '代办人身份证(人像面)',
          photo_code: '26',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '代办人身份证(国徽面)',
          photo_code: '27',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '单位有效证件',
          photo_code: '2',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
        {
          lable: '银行卡照片',
          photo_code: '33',
          file_url: '',
          file_serial: '',
          isShow: false,
        },
      ],
      cancelImgList: [
        {
          lable: '身份证正面',
          photo_code: '1',
          file_url: '',
          file_serial: '',
          type: '0',
          isShow: true,
        },
        {
          lable: '身份证反面',
          photo_code: '11',
          file_url: '',
          file_serial: '',
          type: '0',
          isShow: true,
        },
        {
          lable: '行驶证正副页',
          photo_code: '3',
          file_url: '',
          file_serial: '',
          type: '3',
          isShow: true,
        },
        {
          lable: '机动车登记证书',
          photo_code: '44',
          file_url: '',
          file_serial: '',
          type: '3',
          isShow: true,
        },
        {
          lable: '购车发票',
          photo_code: '45',
          file_url: '',
          file_serial: '',
          type: '3',
          isShow: true,
        },
        {
          lable: '回执单',
          photo_code: '46',
          file_url: '',
          file_serial: '',
          type: '3',
          isShow: true,
        },
        {
          lable: '营业执照',
          photo_code: '42',
          file_url: '',
          file_serial: '',
          type: '1',
          isShow: true,
        },
        {
          lable: '委托书',
          photo_code: '25',
          file_url: '',
          file_serial: '',
          type: '1',
          isShow: true,
        },
      ],
      imgList: [
        // {
        //   lable: '退款申请单',
        //   photo_code: '21',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: true,
        // },
        // {
        //   lable: '身份证(人像面)',
        //   photo_code: '22',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: true,
        // },
        // {
        //   lable: '身份证(国徽面)',
        //   photo_code: '23',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: true,
        // },
        // {
        //   lable: '单位介绍信',
        //   photo_code: '24',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: true,
        // },
        // {
        //   lable: '委托代办书',
        //   photo_code: '25',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: false,
        // },
        // {
        //   lable: '代办人身份证(人像面)',
        //   photo_code: '26',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: false,
        // },
        // {
        //   lable: '代办人身份证(国徽面)',
        //   photo_code: '27',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: false,
        // },
        // {
        //   lable: '单位有效证件',
        //   photo_code: '2',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: false,
        // },
        // {
        //   lable: '银行卡照片',
        //   photo_code: '33',
        //   file_url: '',
        //   file_serial: '',
        //   // isShow: false,
        // },
      ],
      handleTypeList: [
        {
          value: 0,
          label: '未处理',
        },
        {
          value: 1,
          label: '通过',
        },
        {
          value: 2,
          label: '退回',
        },
        {
          value: 3,
          label: '修改',
        },
      ],
      //订单状态：0-用户申请退款1-业务员审核2-清算中3-网点审核4-运营部复核5-制表6-财务打款9-已归档
      orderStatusList: redundOrderStatusList,
      sourceList: [
        {
          value: 1,
          label: '网点',
        },
        {
          value: 2,
          label: 'C端',
        },
      ],
      // //制表状态：0-未制表；1—已制表
      // tabFlagList: [
      //   {
      //     value: 0,
      //     label: '未制表',
      //   },
      //   {
      //     value: 1,
      //     label: '已制表',
      //   },
      // ],
      //0-未比对；1-比对成功；2-比对失败
      transferMatchList: [
        {
          value: 0,
          label: '未比对',
        },
        {
          value: 1,
          label: '比对成功',
        },
        {
          value: 2,
          label: '比对失败',
        },
      ],
      refundType: refundType, //退费类型
      detail: {},
      tableData: [],
      ordinaryothers: ['21', '22', '23', '24', '25', '26', '27', '2', '33'], //注销退费
      refundInternetAccountCode: [
        '22',
        '23',
        '24',
        '25',
        '26',
        '27',
        '2',
        '74',
      ], //注销退费到互联网账户
      Individualothers: ['21', '22', '23', '25', '26', '27', '33'], //互联网个人
      Unitothers: ['21', '24', '25', '26', '27', '33'], //互联网单位
      otherCode: '', // 查询档案other-code
      refundTableData: [], //关联退款订单
      contactInfo: {},
      attorneyUrl: '', //通行费退费附件URL
      bankPhotoUrl: '', //银行卡照片
      pageStatus: {
        page: 1,
        total: 0,
        pageSize: 10,
      },
      transactionId: '', //原始交易id
      cardNo: '', //原始交易卡号
    }
  },
  created() {
    this.id = parseInt(this.$route.query.id)
    this.search = this.$route.query.search
    this.getTypeList()
    this.getDetail()
  },
  methods: {
    editBankHandle() {
      this.editVisible = !this.editVisible
    },
    showTransactionDialog(refundOrder) {
      console.log('打开数据',refundOrder)
      //打开交易明细并加载数据。
      this.transactionId = refundOrder.transactionOrPassId
      this.cardNo = refundOrder.cardNo
      this.dialogFormVisible = true
    },

    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    //获取列表
    getDetail() {
      this.showLoading = true
      this.refund_type = this.$route.query.refundType
      let params = { id: this.id, refund_type: this.$route.query.refundType }
      this.$store
        .dispatch('refund/getLogoutDetail', params)
        .then((res) => {
          this.showLoading = false
          this.detail = res

          // 通行费才查这些信息
          if (this.detail.refundType == 2) {
            this.getContactMan(res)
            this.getRefundOrder()
          }

          this.tableData = res.opLogList
          this.fileUUID = res.fileUuid
          // 互联网账户退费 取订单详情的fileUuid
          if (
            this.detail.refundType == 9 ||
            this.detail.refundType == 8 ||
            this.detail.refundType == 11
          ) {
            this.otherCode = this.detail.fileUuid || ''
          }
          // 注销退费 取订单详情的卡号
          if (this.detail.refundType == 1) {
            this.otherCode = this.detail.cardNo || ''
          }
          this.getDetailImgs(res)
          let nowtype = []
          if (
            res.refundType != 9 ||
            res.refundType != 8 ||
            res.refundType != 11
          ) {
            nowtype = this.ordinaryothers
          } else if (res.userType == 1) {
            nowtype = this.Individualothers
          } else {
            nowtype = this.Unitothers
          }
          // 注销退款到互联网账户
          if (res.refundType == 1 && res.needRefund == 2) {
            nowtype = this.refundInternetAccountCode
          }
          console.log(nowtype)
          this.changeList(nowtype)
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    getContactMan(detail) {
      let params = {}
      let url = ''
      if (detail.hsContactManagerLogId) {
        url = this.$interfaces.queryManByOpId
        params.opLogId = detail.hsContactManagerLogId
      } else {
        url = this.$interfaces.queryManByData
        params.carColor = detail.carColor
        params.carNo = detail.carNo
        params.custMastId = detail.custMastId
      }
      this.$request({
        url: url,
        data: params,
        method: 'post',
      })
        .then((res) => {
          console.log('联系人', res)
          if (res.code == 200 && res.data) {
            // this.refundTableData = res.data.data
            this.contactInfo = res.data
            this.hasContact = true
            this.attorneyUrl = res.data.attorneyUrl
            this.bankPhotoUrl = res.data.bankPhotoUrl
          }
        })
        .catch((error) => {})
    },
    getRefundOrder() {
      let params = {
        id: this.id,
        page: this.pageStatus.page,
        pageSize: this.pageStatus.pageSize,
      }
      this.$request({
        url: this.$interfaces.getBindRefundOrder,
        data: params,
        method: 'post',
      })
        .then((res) => {
          console.log('res', res)
          if (res.code == 200) {
            this.refundTableData = res.data.data
          } else {
            this.refundTableData = []
          }

          this.pageStatus.total = res.data.total
        })
        .catch((error) => {})
    },
    //获取档案
    getDetailImgs(detail) {
      console.log(detail, '==========111========')
      let params = {
        customer_id:
          this.detail.refundType == 9
            ? this.detail.netUserNo
            : this.detail.custMastId,
        vehicle_code: this.detail.carNo,
        vehicle_color: this.detail.carColor,
        scene:
          this.detail.refundType == 9
            ? '14'
            : this.detail.refundType == '8' || this.detail.refundType == '11'
            ? '15'
            : '6',
        other_code: '',
      }

      switch (this.detail.refundType) {
        case 9:
          params.scene = '14'
          break
        case 8:
          params.scene = '15'
          break
        case 11:
          params.scene = '15'
          break
        case 1:
          params.scene = '6'
          break
        case 12:
          params.scene = '26'
          break
      }
      // 互联网账户退费 取订单详情的fileUuid
      if (
        this.detail.refundType == 9 ||
        this.detail.refundType == 8 ||
        this.detail.refundType == 11
      ) {
        params['other_code'] = this.detail.fileUuid || ''
      }
      // 注销退费 取订单详情的卡号
      if (this.detail.refundType == 1) {
        params['other_code'] = this.detail.cardNo || ''
      }

      this.$store
        .dispatch('refund/getDetailImgs', params)
        .then((res) => {
          this.changeImgList(res)
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //筛选图片的数据
    changeImgList(imgList) {
      let arr = []
      if (this.detail.refundType == '12') {
        arr = JSON.stringify(this.cancelImgList)
      } else {
        arr = JSON.stringify(this.imgdom)
      }
      this.imgList = JSON.parse(arr)
      imgList.forEach((item) => {
        this.imgList.forEach((item1) => {
          if (item.photo_code == item1.photo_code) {
            // item1.file_url = item.file_url
            // item1.file_serial = item.file_serial
            this.$set(item1, 'file_url', item.file_url || '')
            this.$set(item1, 'file_serial', item.file_serial || '')
          }
        })
      })

      // if(this.$route.query.refundType == 9){
      //   this.imgList.forEach((item) => {
      //     if(!item.file_url){
      //       item.isShow = false
      //     }
      //   })
      // }
      let previewImgArr = this.imgList.filter((item) => item.file_url)
      previewImgArr.forEach((item) => {
        this.previewImgList.push(item.file_url)
      })
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch((err) => {})
    },
    //处理handle
    handle(type, status, detail) {
      // console.log('remark====', remark, status)
      // this.rejectVisible = false
      if (this.refund_type == '2') {
        //通行费退费需要判断联系人
        if (
          type == '1' &&
          !this.hasContact &&
          (!detail.bankAccount || !detail.bankNo || !detail.bankName)
        ) {
          //没有联系人提示去添加
          let desc =
            '1、根据运营管理退费要求：当前退费账户不存在有效的联系人信息，请仔细审核！'
          this.handleTips(desc)
        } else {
          this.doAudit(type, status, detail)
        }
      } else {
        this.doAudit(type, status, detail)
      }
    },
    doAudit(type, status, detail) {
      let url = ''
      if (status == 1) {
        //业务员审核
        url = 'refund/issuerAudit'
        this.submit(type, url)
      } else if (status == 3) {
        //网点审核
        url = 'refund/branchAudit'
        //2022-6-16，加一个弹框判断银行卡信息为空的情况。
        if (
          type == '1' &&
          (!detail.bankAccount || !detail.bankNo || !detail.bankName) &&
          this.refund_type != '2'
        ) {
          let desc =
            '1、根据运营管理退费要求：银行卡信息【开户行】【银行账户名】【银行卡号】不能为空，请仔细审核！'
          this.handleTips(desc)
        } else {
          this.submit(type, url)
        }
      } else if (status == 4) {
        //运营部复审
        url = 'refund/operationAudit'
        this.submit(type, url)
      }
    },
    handleTips(desc) {
      const h = this.$createElement
      this.$msgbox({
        title: '提示',
        message: h('p', null, [
          h('div', null, desc),
          h('div', { style: 'color: red' }, '2、请仔细查看档案资料是否齐全！'),
        ]),
        showCancelButton: false,
        confirmButtonText: '确定',
      }).then((action) => {
        console.log('action', action)
        if (action == 'confirm') {
          // this.submit(type, url)
        }
      })
    },
    //提交
    submit(type, url) {
      if (type == 2 && this.remark == '') {
        this.$message({
          message: '请先填写处理意见再操作！',
          type: 'warning',
        })
        this.$refs.textarea.focus()
        return
      }
      let params = {
        id: this.id,
        handleType: type,
        refund_type: this.$route.query.refundType,
        remark: this.remark,
      }
      console.log('params=============', params)
      this.$store
        .dispatch(url, params)
        .then((res) => {
          console.log('操作记录============res', res)
          this.$message({
            showClose: true,
            message: '操作成功！',
            type: 'success',
          })
          this.remark = ''
          this.getDetail()
        })
        .catch((err) => {})
    },
    updateCard() {
      this.getDetail()
    },
    //返回前一页并关闭当前页面包屑
    back() {
      //关闭当前页面包屑
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(
          (item) => item.path === this.$route.path
        ),
        1
      )
      console.log('当前路由', this.$route.path)
      this.$router.push({
        path: './logoutRefund',
        replace: true,
        query: {
          search: this.search,
        },
      })
    },
    //退回运营部复核
    backTo(id) {
      // price = this.$options.filters['moneyFilter'](price)

      this.$confirm('需要返回订单[' + id + ']到运营部复核吗', '复核确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        // inputPattern:
        //   /(^[1-9]([0-9]{0,15})(\.[0-9]{1,2})?$)|(^[0-9]{1}(\.[0-9]{1,2})?$)/,
        // inputErrorMessage: '金额格式不正确',
      })
        .then(() => {
          // if (value) {
          let params = {
            id: id,
            handleType: '2', //2退回
            orderStatus: '5', //5制表
            refundType: this.refund_type,
            remark: this.remark || '无',
          }
          this.$request({
            url: this.$interfaces.makeTableAudit,
            data: params,
            method: 'post',
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: res.msg,
                })
                this.remark = ''
                this.getDetail()
              }
            })
            .catch((error) => {})
          // }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入',
          })
        })
    },
    changeList(arr) {
      let _self = this
      this.imgdom.forEach((item) => {
        _self.$set(item, 'isShow', !!arr.includes(item.photo_code))
      })
    },
    changePage(page) {
      this.pageStatus.page = page
      this.getRefundOrder()
    },
    handleSizeChange(pageSize) {
      this.pageStatus.pageSize = pageSize
      this.getRefundOrder()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return ''
      }
    },
  },
}
</script>

<style scoped lang="scss">
.detail {
  padding: 20px;
  .info {
    background-color: #fff;
    .btn-title {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      align-items: center;
      padding-right: 20px;
      .title {
        border: 0;
        margin: 0;
      }
    }
    .textarea-wrapper {
      padding: 0 20px 30px 20px;
    }
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      // border-bottom: 1px solid #f0f0f0;
    }
  }
  .nat-form.nat-form-list .el-form-item {
    margin-bottom: 0px;
  }
  ::v-deep .el-form-item__label {
    min-width: 140px !important;
  }

  .archives-box {
    display: flex;
    flex-wrap: wrap;
    -moz-box-pack: start;
    -ms-box-pack: start;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -moz-justify-content: flex-start;
    justify-content: flex-start;
  }
  .archives-box .imgdiv {
    padding: 10px 90px;
    display: flex;
    flex-wrap: wrap;
    -moz-box-pack: start;
    -ms-box-pack: start;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -moz-justify-content: flex-start;
    justify-content: flex-start;
  }

  .archives-box .archives-item {
    width: 180px;
    height: 180px;
    margin: 0 10px 10px 0;
    position: relative;
  }
  .archives-box .archives-item .demonstration {
    position: absolute;
    width: 100%;
    text-align: center;
    margin: 0;
    bottom: 0;
    line-height: 25px;
    color: white;
    white-space: nowrap;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 0 0 6px 6px;
  }
  .archives-box .archives-item .el-image {
    width: 180px;
    height: 180px;
    border: 1px dashed #e5e5e5;
    border-radius: 6px;

    // box-shadow: 0px 0px 1px #ccc;
  }
  .contact-box {
    display: flex;
    // flex-direction: column;
    // padding: 30px;
    background: #ffffff;
  }
  .box {
    display: flex;
    padding: 0 30px;
    flex-direction: column;
    background: #ffffff;
  }
  .contact-box .contact-img {
    width: 180px;
    height: 180px;
    border: 1px dashed #e5e5e5;
    border-radius: 6px;
  }
  .contact-box .title {
    margin-left: 40px;
  }
  .foot {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    background-color: #fff;
    padding: 20px;
    text-align: center;
  }
}
.refund-detail .refund-detail-box .el-form-item {
  margin-bottom: 5px !important;
}

.pagination {
  padding-bottom: 20px;
  margin: 0 20px;
}
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.line-text {
  text-decoration: underline;
  &:hover {
    cursor: pointer;
  }
  color: #006eff;
}
</style>
