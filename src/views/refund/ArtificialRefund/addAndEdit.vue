<template>
  <div class="form">
    <el-dialog
      :title="dialogType === 'add' ? '新增' : '修改'"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        :key="type"
      >
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="12">
            <el-form-item label="卡号：" prop="cardNo">
              <el-input type="text" oninput="if(value.length>11)value=value.slice(0,20)" v-model="ruleForm.cardNo" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款机构名称：" prop="bankAccount">
              <el-input v-model="ruleForm.bankAccount" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="12">
            <el-form-item label="退款人姓名：" prop="bankAccountName">
              <el-input v-model="ruleForm.bankAccountName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款账户号码：" prop="bankAccountNo">
              <el-input v-model="ruleForm.bankAccountNo" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
var moment = require('moment')
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
    originData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      licenseColorOption: licenseColorOption,
      dialogFormVisible: false,
      dialogType: 'add',
      dialogOriginData: {},
      ruleForm: {
        bankAccount: '',
        bankAccountName: '',
        bankAccountNo: '',
        cardNo: '',
      },
      rules: {
        cardNo: [
          {
            required: true,
            message: '[卡号]不能为空!',
            trigger: 'blur',
          },
        ],
        bankAccount: [
          {
            required: true,
            message: '[退款机构名称]不能为空!',
            trigger: 'blur',
          },
        ],
        bankAccountName: [
          {
            required: true,
            message: '[退款人姓名]不能为空!',
            trigger: 'blur',
          },
        ],
        bankAccountNo: [
          {
            required: true,
            message: '[退款账户号码]不能为空!',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    originData(val) {
      this.dialogOriginData = val
    },
    type(val) {
      console.log('val', val)
      this.dialogType = val
      if (this.dialogType === 'edit') {
        this.$store
          .dispatch('refund/showAddOrUpdateDialog', {
            id: this.originData.id,
          })
          .then((res) => {
            console.log('res', res)

            let originalData = res
            delete originalData.cardMastId
            this.dialogOriginData = originalData
            this.ruleForm = originalData

            // this.ruleForm = dialogOriginData
            // this.dialogOriginData = dialogOriginData
            console.log('回填后的数据', this.ruleForm)
          })
      } else {
        this.ruleForm = {}
      }
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  methods: {
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          if (this.dialogType === 'add') {
            this.add()
          } else {
            this.edit()
          }
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // changeOriginData() {},
    add() {
      this.dialogType = ''
      console.log('this.formdata', this.ruleForm)
      this.$store
        .dispatch('refund/addOrUpdateConfirm', this.ruleForm)
        .then((res) => {
          console.log('添加成功', res)
          this.$message({
            message: '添加成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.$emit('on-submit')
        })
        .catch((err) => {})
    },
    edit() {
      this.dialogType = ''
      console.log('编辑提交的数据', this.ruleForm)
      this.$store
        .dispatch('refund/addOrUpdateConfirm', this.ruleForm)
        .then((res) => {
          console.log('修改成功', res)
          this.$message({
            message: '修改成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.$emit('on-submit')
        })
        .catch((err) => {
          //错误后继续回填数据
          this.ruleForm = this.dialogOriginData
        })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
.el-form-item__label {
  text-align: center;
  white-space: nowrap;
}
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner,
.el-input__inne {
  width: 100%;
}
</style>
