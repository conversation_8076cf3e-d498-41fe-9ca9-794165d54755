<template>
  <div class="artificial-refund">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" placeholder="请选择">
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="carNo">
            <el-input v-model="search.carNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input v-model="search.cardNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="add()"
                >新增</el-button
              >
              <el-button size="mini" type="warning" @click="add('edit')"
                >修改</el-button
              >
              <el-button size="mini" type="danger" @click="del()"
                >删除</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="500"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column label="选择" width="50" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change="getCurrentRow(scope.$index, scope.row.id)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="custName" align="center" label="用户名称" />
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="180"
          label="卡号"
        />
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column prop="carColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.carColor, 'getVehicleColor') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankAccount"
          align="center"
          label="退款机构名称"
          min-width="180"
        />
        <el-table-column
          prop="bankAccountName"
          align="center"
          label="退款人姓名"
          min-width="120"
        />
        <el-table-column
          prop="bankAccountNo"
          align="center"
          label="退款账户号码"
          min-width="180"
        />
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          min-width="160"
        />
        <el-table-column prop="creatorName" align="center" label="创建人" />
        <el-table-column
          prop="updateTime"
          align="center"
          min-width="160"
          label="更新时间"
        />
        <el-table-column prop="updaterName" align="center" label="更新人" />
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[10, 20, 50]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <add-and-edit
      :visible.sync="dialogAddVisible"
      :type.sync="dialogType"
      :originData="originData"
      @on-submit="onUpdateList"
    ></add-and-edit>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { licenseColorOption } from '@/common/const/optionsData'
import { typeAdapter } from '@/common/method/formatOptions'
import addAndEdit from './addAndEdit'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    addAndEdit,
  },
  data() {
    return {
      licenseColorOption,
      loading: false,
      center: 'center',
      dialogAddVisible: false,
      id: '',
      dialogType: 'add',
      total: '',
      radio: [],
      search: {
        carColor: '',
        carNo: '',
        cardNo: '',
        page: 1,
        pageSize: 10,
      },
      tableData: [],
      originData: {},
    }
  },
  created() {
    this.getArtificialList()
  },
  methods: {
    // moment,
    typeAdapter,
    getArtificialList() {
      this.loading = true
      this.$store
        .dispatch('refund/artificialSearch', this.search)
        .then((res) => {
          console.log('res', res)
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //操作按钮打开对话框
    add(dialogType = 'add') {
      console.log('this.radio', this.radio)
      if (dialogType === 'edit' && this.radio.length == 0) {
        this.message('请先选中一条记录！', 'warning')
        return
      }
      this.dialogType = dialogType
      this.dialogAddVisible = true
    },
    //获取选中
    getCurrentRow(index, id) {
      this.originData = this.tableData[index]
      console.log(
        '数据回填~~~~~~~~~~~~',
        index,
        this.tableData[index],
        this.originData
      )
      this.id = id
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.radio = []
      this.search.page = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.radio = []
      this.search.page = 1
      this.getArtificialList()
    },
    changePage(page) {
      this.radio = []
      this.search.page = page
      this.getArtificialList()
    },
    handleSizeChange(pageSize) {
      this.radio = []
      this.search.pageSize = pageSize
      this.getArtificialList()
    },
    //更新
    onUpdateList() {
      this.radio = []
      this.getArtificialList()
    },
    uploadSuccess() {
      this.radio = []
      this.importDialogVisible = false
      this.getRefundList()
    },
    del() {
      this.confirmDilog('确定要删除该条数据吗？', '删除操作', 'danger')
        .then(() => {
          this.$store
            .dispatch('refund/artificialDelete', { id: this.id })
            .then((res) => {
              this.message('删除成功', 'success')
              this.onUpdateList()
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    confirmDilog(msg, title, type) {
      return new Promise((resolve, reject) => {
        // if (useType === 'moreConfirm') {
        if (this.radio.length === 0) {
          reject({ msg: '请先选中一条记录！', type: 'warning' })
          return
        }
        // }
        this.$confirm(msg, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject({ msg: '取消确认', type: 'info' })
          })
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.artificial-refund {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    margin-left: 20px;
    margin-top: 10px;
  }
}
</style>
