<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）车辆办理情况日报表 2022.7.04需求 盖章并下载
  * @author:zhangys&zcq
  * @date:2022/05/11 10:58:55
!-->
<template>
  <div class="user">
    <div class="section-1">
      <reportExport
        type="datetimerange"
        title="退款扣费类型日报表"
        startDateName="roCreateTimeStart"
        endDateName="roCreateTimeEnd"
        reportName="refundDeductionTypeDailyReport"
      >
      </reportExport>
    </div>
    <div class="section-2">
      <reportExport
        type="datetimerange"
        title="退款订单监控总表"
        startDateName="roCreateTimeStart"
        endDateName="roCreateTimeEnd"
        reportName="refundOrderMonitoringReport"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        type="datetimerange"
        title="退款交易场景日报表"
        startDateName="roCreateTimeStart"
        endDateName="roCreateTimeEnd"
        reportName="refundTransactionScenarioDailyReport"
      >
      </reportExport>
    </div>
    <div class="section-4">
      <reportExport
        type="datetimerange"
        title="ETC交易退款日报表"
        startDateName="roCreateTimeStart"
        endDateName="roCreateTimeEnd"
        reportName="transactionRefundDailyReport"
      >
      </reportExport>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import stampDownload from '../../riritongmanagement/components/stampDownload'
import reportExport from '../../riritongmanagement/components/reportExport'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
    reportExport,
  },
  data() {
    return {
      receiveTime: '',
      quarterValue: [],
      yearValue: '',
      // loading: false,
      // search: {
      //   thedate: '',
      // },
      // tableHeight: 0,
      // rules: {
      //   thedate: [
      //     { required: true, message: '请选择统计日期', trigger: 'change' },
      //   ],
      // },
    }
  },
  methods: {
    // downReport(val) {
    //   this.downReportDate = val
    // },
  },
  // created() {
  //   this.search.thedate = moment().subtract(1, 'day').format('YYYY-MM-DD')
  // },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
