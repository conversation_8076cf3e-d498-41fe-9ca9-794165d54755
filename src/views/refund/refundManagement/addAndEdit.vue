<template>
  <div class="form" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :title="dialogType === 'add' ? '新增' : '修改'"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        :key="type"
      >
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="卡号：" prop="cardNo">
              <el-input
                type="text"
                oninput="if(value.length>11)value=value.slice(0,20)"
                v-model="ruleForm.cardNo"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号：" prop="carNo">
              <el-input v-model="ruleForm.carNo" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="car-type" label="车牌颜色：" prop="carColor">
              <el-select v-model="ruleForm.carColor" placeholder="请选择">
                <el-option
                  v-for="item in licenseColorOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                /> </el-select
            ></el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="合作机构：" prop="bankName">
              <el-input v-model="ruleForm.bankName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已退金额(元)：" prop="amount">
              <el-input
                type="number"
                oninput="if(value.length>11)value=value.slice(0,11)"
                v-model="ruleForm.amount"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款渠道：" prop="refundChannel">
              <el-input type="text" v-model="ruleForm.refundChannel" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="退款日期：" prop="refundTime">
              <el-date-picker
                v-model="ruleForm.refundTime"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称：" prop="custName">
              <el-input type="text" v-model="ruleForm.custName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收款人：" prop="bankAccountName">
              <el-input type="text" v-model="ruleForm.bankAccountName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="银行账号：" prop="bankCardNo">
              <el-input
                oninput="if(value.length>11)value=value.slice(0,19)"
                v-model="ruleForm.bankCardNo"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户行：" prop="bankAccount">
              <el-input type="text" v-model="ruleForm.bankAccount" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
var moment = require('moment')
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
    originData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      licenseColorOption: licenseColorOption,
      dialogFormVisible: false,
      showLoading: false,
      dialogType: 'add',
      dialogOriginData: {},
      ruleForm: {
        amount: '',
        bankAccount: '',
        bankAccountName: '',
        bankCardNo: '',
        bankName: '',
        carColor: '',
        carNo: '',
        cardNo: null,
        custName: '',
        refundChannel: '',
        refundTime: '',
      },
      rules: {
        cardNo: [
          {
            required: true,
            message: '[卡号]不能为空!',
            trigger: 'blur',
          },
        ],
        carNo: [
          {
            required: true,
            message: '[车牌号]不能为空!',
            trigger: 'blur',
          },
        ],
        carColor: [
          {
            required: true,
            message: '请选择[车牌颜色]',
            trigger: 'change',
          },
        ],
        bankName: [
          { required: true, message: '[合作机构]不能为空!', trigger: 'blur' },
        ],
        amount: [
          { required: true, message: '[已退金额]不能为空!', trigger: 'blur' },
          {
            pattern: /(^(([1-9]([0-9]+)?)|(0))(\.[0-9]{2})?$)/,
            message: '请输入正确的金额格式',
          },
        ],
        bankCardNo: [
          {
            pattern: /^[0-9]*$/,
            message: '请输入正确的格式',
          },
        ],
        refundChannel: [
          {
            required: true,
            message: '[退款渠道]不能为空!',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    originData(val) {
      this.dialogOriginData = val
    },
    type(val) {
      console.log('val', val)
      this.dialogType = val
      if (this.dialogType === 'edit') {
        // this.$store
        //   .dispatch('refund/refundOfflineListById', {
        //     id: this.originData.id,
        //   })
        //   .then((res) => {
        //     console.log('res', res)
        //     this.dialogOriginData = res
        //     this.ruleForm = JSON.parse(JSON.stringify(res[res.length - 1]))
        //   })
        console.log('changeOriginData', this.originData)
        this.dialogOriginData = JSON.parse(JSON.stringify(this.originData))
        delete this.dialogOriginData.createTime
        delete this.dialogOriginData.createBy
        delete this.dialogOriginData.delFlag
        delete this.dialogOriginData.updateBy
        delete this.dialogOriginData.updateTime
        this.dialogOriginData.amount = (
          this.dialogOriginData.amount / 100
        ).toFixed(2)
        //用户信息数据回填
        this.ruleForm = this.dialogOriginData
        console.log('数据合并', this.ruleForm)
      } else {
        this.ruleForm = {}
      }
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  methods: {
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          if (this.dialogType === 'add') {
            this.add()
          } else {
            this.edit()
          }
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString()
      try {
        m += s1.split('.')[1].length
      } catch (e) {}
      try {
        m += s2.split('.')[1].length
      } catch (e) {}
      return (
        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
        Math.pow(10, m)
      )
    },
    // changeOriginData() {},
    add() {
      this.showLoading = true
      this.dialogType = ''
      this.ruleForm.amount = this.accMul(this.ruleForm.amount, 100)
      this.ruleForm.refundTime = this.ruleForm.refundTime
        ? moment(this.ruleForm.refundTime).format('YYYY-MM-DD')
        : ''
      delete this.ruleForm.id
      console.log('this.formdata', this.ruleForm)
      this.$store
        .dispatch('refund/refundOfflineEdit', this.ruleForm)
        .then((res) => {
          this.dialogFormVisible = false
          this.showLoading = false
          this.$emit('on-submit')
          this.$message({
            message: '添加成功',
            type: 'success',
          })
        })
        .catch((err) => {
          this.showLoading = false
          this.dialogFormVisible = false
        })
    },
    edit() {
      this.showLoading = true
      this.dialogType = ''
      this.ruleForm.amount = this.accMul(this.ruleForm.amount, 100)
      this.ruleForm.refundTime = this.ruleForm.refundTime
        ? moment(this.ruleForm.refundTime).format('YYYY-MM-DD')
        : ''
      console.log('编辑提交的数据', this.ruleForm)
      this.$store
        .dispatch('refund/refundOfflineEdit', this.ruleForm)
        .then((res) => {
          this.showLoading = false
          this.dialogFormVisible = false
          this.$emit('on-submit')
          // this.dialogOriginData = this.formData
          this.$message({
            message: '修改成功',
            type: 'success',
          })
        })
        .catch((err) => {
          //错误后继续回填数据
          this.showLoading = false
          this.ruleForm = this.dialogOriginData
        })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
.el-form-item__label {
  text-align: center;
  white-space: nowrap;
}
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner,
.el-input__inne {
  width: 100%;
}
</style>
