<template>
  <div class="import-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      title="导入文件"
      :before-close="handleCloseIcon"
      width="65%"
    >
      <!-- <div class="selector g-flex g-flex-start g-flex-align-center">
        <div class="label">银行：</div>
        <el-select v-model="formData.bankType" placeholder="请选择">
          <el-option
            v-for="item in bankTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div> -->
      <fieldset class="fieldset">
        <legend>附件上传</legend>
        <div slot="tip" class="el-upload__tip">注意事项：</div>
        <div slot="tip" class="el-upload__tip">1、仅支持xls格式的Excel文件</div>
        <div slot="tip" class="el-upload__tip">
          2、时间格式：yyyy-MM-dd，举例：2020-10-10
        </div>
        <div slot="tip" class="el-upload__tip">
          3、导入字段包含：[ ETC卡号 | 车牌号 | 车牌颜色 | 客户名称 | 合作机构 |
          已退金额 | 退款渠道 | 退款时间 | 银行账号 | 收款人 | 开户行 ]
        </div>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      showLoading: false,
      formData: {
        file: '',
        // bankType: '',
      },
      fileList: [],
      // bankTypeList: [
      //   { value: '1', label: '农信' },
      //   { value: '2', label: '建行' },
      //   { value: '3', label: '柳行' },
      //   { value: '4', label: '邮政' },
      // ],
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      this.upload()
    },
    upload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/refundOffline/refundResultImport'
      console.log('url', url)
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken(),
          },
        })
        .then((res) => {
          this.showLoading = false
          if (res.data.code !== 200) {
            this.$confirm(
              res.data.msg,
              '错误提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'error',
              }
            )
            return
          }
          this.$message({
            type: 'success',
            message: '导入成功',
          })
          this.$refs.upload.clearFiles()
          // this.formData.bankType = ''
          this.formData.file = ''
          this.$emit('uploadSuccess')
        })
        .catch((err) => {
          this.showLoading = false
          this.$confirm(
            '读取Excel数据失败,请检查以下情况: 1.Excel文件中没有任何数据 2.缺失字段 3.字段名称不正确',
            '错误提示',
            {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
            }
          )
        })
      // this.$store
      //   .dispatch('refund/refundResultImport', formData)
      //   .then((res) => {
      //     this.showLoading = false
      //     this.$refs.upload.clearFiles()
      //     this.formData.file = ''
      //     this.$message({
      //       type: 'success',
      //       message: '导入成功',
      //     })
      //     this.$emit('uploadSuccess')
      //   })
      //   .catch((err) => {
      //     this.showLoading = false
      //     this.$refs.upload.clearFiles()
      //     this.formData.file = ''
      //   })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>