<template>
  <div class="make-table" v-loading.fullscreen.lock="showLoading">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="制表类型：" prop="refundStatus">
            <el-select v-model="search.refundStatus" placeholder="请选择">
              <el-option
                v-for="item in refundStatusList"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <!-- <dart-search-item label="审批标识：" prop="payment_flag">
            <el-select v-model="search.payment_flag" placeholder="请选择">
              <el-option
                v-for="item in paymentFlagOptions"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item> -->

          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="makeTable()">
                <i class="el-icon-download"></i> 勾选制表</el-button
              >
              <el-button
                size="mini"
                type="primary"
                v-if="tableData.length > 0"
                @click="makeTable('all')"
              >
                <i class="el-icon-download"></i> 全部制表</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <!-- <div class="table-info">退款总金额：{{ refundAmount }}元</div> -->
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="40"></el-table-column>
        <el-table-column prop="id" align="center" label="订单号" />
        <el-table-column
          :key="1"
          prop="refundChannel"
          align="center"
          label="退款渠道"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ getType(typeList.refundChannels, scope.row.refundChannel) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="2"
          prop="amount"
          align="center"
          min-width="120"
          label="退款金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.amount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          :key="3"
          prop="bankAccountName"
          align="center"
          label="户名"
          min-width="100"
        />
        <el-table-column
          :key="4"
          prop="bankAccountNo"
          align="center"
          label="银行账号"
          min-width="170"
        />
        <!-- <el-table-column
          :key="5"
          prop="refundType"
          align="center"
          label="退费类型"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(refundType, scope.row.refundType) }}
          </template>
        </el-table-column> -->
        <el-table-column
          :key="6"
          prop="bankId"
          align="center"
          label="合作机构"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(typeList.payOrgIds, scope.row.bankId) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="7"
          prop="payOrgId"
          align="center"
          label="代扣机构"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(typeList.payOrgIds, scope.row.payOrgId) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="8"
          prop="createTime"
          align="center"
          min-width="160"
          label="创建时间"
        />
        <el-table-column
          :key="9"
          prop="updateTime"
          align="center"
          min-width="160"
          label="更新时间"
        />
        <!-- <el-table-column prop="accountingTime" align="center" label="更新人" /> -->
        <el-table-column
          :key="10"
          prop="updaterName"
          align="center"
          label="操作人"
          min-width="100"
        />
        <el-table-column
          prop="remark"
          align="center"
          label="备注"
          min-width="300"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.remark }}</div>
              <span>{{ scope.row.remark }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div v-if="total > search.pageSize" class="pagination"> -->
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageIndex"
        :page-sizes="[20, 50, 100, 150, 200, 250, 300, 350, 400, 450, 500]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'

import { decode } from 'js-base64'
var moment = require('moment')
import float from '@/common/method/float'
export default {
  components: {
    dartSearch,
    dartSearchItem
  },
  data() {
    return {
      // refundAmount: 0, // 退款金额
      loading: false,
      showLoading: false,
      center: 'center',
      total: 0,
      search: {
        refundStatus: 8, //2已退款，成功表，8待确认，申请表
        pageIndex: 1,
        pageSize: 20
      },
      tableData: [],
      multipleSelection: [],
      //状态
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [] //退费状态字典
      },
      refundStatusList: [
        {
          value: 8,
          label: '申请明细表'
        },
        {
          value: 2,
          label: '成功明细表'
        }
      ]
    }
  },
  created() {
    this.getTypeList()
    this.getTableList()
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        lvTypeList.push({
          label: '全部',
          value: ''
        })
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach(key => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch(err => {})
    },
    getTableList() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      this.$request({
        url: this.$interfaces.getRefundTbaleList,
        data: params,
        method: 'post'
      })
        .then(res => {
          console.log('制表查询数据', res)
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
        .catch(error => {})
    },
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.multipleSelection = []
      selection.forEach(item => {
        if (!this.multipleSelection.includes(item.id)) {
          this.multipleSelection.push(item.id)
        }
      })
      this.refundAmount = 0
      if (selection && selection.length) {
        for (let i = 0; i < selection.length; i++) {
          this.refundAmount = float.add(this.refundAmount, selection[i].amount)
        }
      }
    },
    //制表
    makeTable(allFlag) {
      if (allFlag != 'all' && this.multipleSelection.length == 0) {
        this.$message({
          message: '请先选中一条记录！',
          type: 'warning'
        })
        return
      }
      this.showLoading = true

      let params = {
        tableList: this.multipleSelection,
        refundStatus: this.search.refundStatus,
        tableFlag: allFlag == 'all' ? 0 : 1
      }
      if (allFlag == 'all') {
        delete params.tableList
      }
      this.$request({
        url: this.$interfaces.makeRefundTbale,
        data: params,
        method: 'post'
      })
        .then(res => {
          console.log('制表', res)
          this.showLoading = false
          if (res.code == 200) {
            // window.open(res.data.file_path)
            let that = this
            this.getBlob(res.data.file_path, function(blob) {
              that.saveAs(blob, '原路退款制表.xls')
            })
            this.showLoading = false
            this.getTableList()
          }
        })
        .catch(error => {
          this.showLoading = false
        })
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function() {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    changePage(page) {
      this.search.pageIndex = page
      this.getTableList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getTableList()
    },
    //重置
    onReSetHandle() {
      // for (const key in this.search) {
      //   this.search[key] = ''
      // }
      this.multipleSelection = []
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.pageIndex = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.search.pageIndex = 1
      this.getTableList()
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.make-table {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table-info {
    position: relative;
    padding: 8px 48px 8px 20px;
    border-radius: 4px;
    font-size: 14px;
    line-height: 20px;
    border: 1px solid #abdcff;
    background-color: #f0faff;
    color: #515a6e;
    font-weight: 500;
  }
  .table {
    padding: 20px 20px 20px 20px;
    flex: 1;
    height: 0;
    margin-top: 0px !important;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .btn-wrapper {
    margin-left: 40px;
    margin-top: 10px;
  }
  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  //   margin-bottom: 0;
  // }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
  //   width: 150px !important;
  //   white-space: nowrap;
  // }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
