<template>
  <div class="refund-order">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" clearable placeholder="请选择">
              <el-option
                v-for="item in typeList.carColors"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="carNo">
            <el-input
              v-model="search.carNo"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input
              v-model="search.cardNo"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="退费交易类型：" prop="refundStatus">
            <el-select
              v-model="search.refundTransactionType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in refundTransactionTypeList"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="退费日期起始：" prop="refundTimeStart">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.refundTimeStart"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="退费日期截至：" prop="refundTimeEnd">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.refundTimeEnd"
            >
            </el-date-picker>
          </dart-search-item>
          <div v-show="isCollapse">
            <dart-search-item label="合作机构：" prop="bankId">
              <el-select v-model="search.bankId" clearable filterable placeholder="请选择">
                <el-option
                  v-for="item in typeList.payOrgIds"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="代扣机构：" prop="payOrgId">
              <el-select
                v-model="search.payOrgId"
                clearable
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in typeList.payOrgIds"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="退费交易编号：" prop="refundDetailId">
              <el-input
                v-model="search.refundDetailId"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="退费状态：" prop="refundStatus">
              <el-select
                v-model="search.refundStatus"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in typeList.refundStatus"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="退款渠道：" prop="refundChannel">
              <el-select
                v-model="search.refundChannel"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in typeList.refundChannels"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item
              label="通行交易编号/passId："
              prop="transactionOrPassId"
            >
              <el-input
                v-model="search.transactionOrPassId"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="订单创建时间起始：" prop="createTimeStart">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.createTimeStart"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="订单创建时间截至：" prop="createTimeEnd">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.createTimeEnd"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="退费流水号：" prop="bankRefundSern">
              <el-input
                v-model="search.bankRefundSern"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
            <dart-search-item label="更新时间起始：" prop="updateTimeStart">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.updateTimeStart"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="更新时间截至：" prop="updateTimeEnd">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.updateTimeEnd"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="扣款流水号：" prop="sern">
              <el-input
                v-model="search.sern"
                clearable
                placeholder=""
              ></el-input>
            </dart-search-item>
          </div>
          <dart-search-item isButton :span="24">
            <div class="g-flex">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="confirmRefund()"
                >确认退款</el-button
              >
              <el-button size="mini" type="primary" @click="refundExport()">
                <i class="el-icon-download"></i> 人工退款导出</el-button
              >
              <!-- <el-button size="mini" type="warning" @click="showEditDialog()"
                >编辑</el-button
              >
              <el-button size="mini" type="danger" @click="refundCancel()"
                >撤销</el-button
              > -->
              <el-button
                size="mini"
                type="primary"
                @click="importDialogVisible = true"
              >
                <i class="el-icon-upload"></i> 退款结果导入</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="onExportHandle('all')"
              >
                <i class="el-icon-download"></i> 导出</el-button
              >
              <el-button size="mini" type="primary" @click="toMakeTable">
                原路退款制表</el-button
              >
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
            </div>
          </dart-search-item>
          <div style="color: red; padding-left: 20px; background: #ffffff">
            2023年4月10日 以后的数据，请勿使用人工退款导出按钮
          </div>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        ref="table"
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="isCollapse ? 420 : 550"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column :key="1" align="center" width="160" label="全选">
          <template slot="header">
            <div>确认退款/人工导出</div>
            <div>
              全选
              <el-checkbox
                v-model="checkAll"
                @change="handleCheckAll"
              ></el-checkbox>
            </div>
          </template>
          <!-- <template
            slot-scope="scope"
            v-if="
              scope.row.refundOrder.refundChannel == '4' &&
              (scope.row.refundOrder.refundStatus == '0' ||
                scope.row.refundOrder.refundStatus == '8')
            "
          > -->
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="handleCheckOne"
            ></el-checkbox>
          </template>
        </el-table-column>
        <!-- <el-table-column label="编辑/撤销" width="100" align="center">
          <template
            slot-scope="scope"
            v-if="showRadio(scope.row.refundOrder.refundStatus)"
          >
            <el-radio
              v-model="radio"
              :label="scope.row.refundOrder.id"
              @change="getCurrentRow(scope.row.refundOrder.id)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column> -->
        <el-table-column prop="refundOrder.id" align="center" label="订单号" />
        <el-table-column
          :key="2"
          prop="refundOrder.refundStatus"
          align="center"
          label="订单状态"
          min-width="120"
        >
          <template slot-scope="scope">
            {{
              getType(typeList.refundStatus, scope.row.refundOrder.refundStatus)
            }}
          </template>
        </el-table-column>
        <el-table-column
          :key="3"
          prop="refundOrder.hsRefundOrderStatus_str"
          align="center"
          label="退费工单状态"
          min-width="120"
        />
        <!-- 不设定条件dwz5.26 -->
        <!-- <el-table-column
          :key="3"
          v-if="search.refundChannel == 4"
          prop="refundOrder.hsRefundOrderStatus_str"
          align="center"
          label="退费工单状态"
          min-width="120"
        /> -->
        <el-table-column
          :key="4"
          prop="refundOrder.refundChannel"
          align="center"
          label="退款渠道"
          min-width="100"
        >
          <template slot-scope="scope">
            {{
              getType(
                typeList.refundChannels,
                scope.row.refundOrder.refundChannel
              )
            }}
          </template>
        </el-table-column>
        <el-table-column
          :key="5"
          prop="refundOrder.refundAmount"
          align="center"
          min-width="120"
          label="退款金额(元)"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.refundOrder.refundAmount">
              {{ scope.row.refundOrder.refundAmount | moneyFilter }}</span
            >
            <span v-else> {{ scope.row.refundOrder.refundAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :key="6"
          v-if="search.refundChannel == 4 || search.refundChannel == 1"
          prop="refundOrder.refundTime"
          align="center"
          label="退款时间"
          min-width="160"
        />
        <el-table-column
          :key="7"
          prop="refundOrder.amount"
          align="center"
          min-width="130"
          label="路方退费金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.refundOrder.amount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          :key="8"
          prop="refundOrder.cardNo"
          align="center"
          min-width="180"
          label="ETC卡号"
        />
        <el-table-column
          :key="9"
          prop="cardMast.cardType"
          align="center"
          min-width="100"
          label="ETC卡类型"
        >
          <template slot-scope="scope">
            {{ getType(typeList.cardTypes, scope.row.cardMast.cardType || '') }}
          </template>
        </el-table-column>
        <el-table-column
          :key="10"
          prop="refundOrder.carNo"
          align="center"
          min-width="100"
          label="车牌"
        />
        <el-table-column
          :key="11"
          prop="refundOrder.carColor"
          align="center"
          label="车牌颜色"
        >
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.refundOrder.carColor) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="12"
          v-if="search.refundChannel == ''"
          prop="cardMast.custIdNo"
          min-width="160"
          align="center"
          label="证件号"
        />
        <el-table-column
          :key="13"
          v-if="search.refundChannel == ''"
          prop="cardMast.custMobile"
          min-width="120"
          align="center"
          label="手机号"
        />
        <el-table-column
          :key="14"
          v-if="search.refundChannel == ''"
          prop="refundOrder.refundDetailId"
          align="center"
          min-width="160"
          label="退费交易编号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.refundOrder.refundDetailId }}
              </div>
              <span>{{ scope.row.refundOrder.refundDetailId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="15"
          v-if="search.refundChannel == ''"
          prop="refundOrder.bankRefundSern"
          align="center"
          min-width="160"
          label="退费流水号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.refundOrder.bankRefundSern }}
              </div>
              <span>{{ scope.row.refundOrder.bankRefundSern }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="16"
          prop="time"
          align="center"
          min-width="100"
          label="公路收费方"
        />
        <el-table-column
          :key="17"
          prop="refundOrder.bankId"
          align="center"
          label="合作机构"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(typeList.payOrgIds, scope.row.refundOrder.bankId) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="18"
          prop="refundOrder.payOrgId"
          align="center"
          label="代扣机构"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(typeList.payOrgIds, scope.row.refundOrder.payOrgId) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="19"
          v-if="search.refundChannel == 4"
          prop="refundOrder.bankAccount"
          align="center"
          label="退款银行"
        />
        <el-table-column
          :key="20"
          v-if="search.refundChannel == 4"
          prop="refundOrder.bankAccountName"
          align="center"
          label="户名"
          min-width="100"
        />
        <el-table-column
          :key="21"
          v-if="search.refundChannel == 4"
          prop="refundOrder.bankAccountNo"
          align="center"
          label="银行账号"
          min-width="170"
        />
        <el-table-column
          :key="32"
          v-if="search.refundChannel == 1"
          prop="refundOrder.bankRefundMsg"
          align="center"
          label="合作方退款结果"
          min-width="250"
          ><template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.refundOrder.bankRefundMsg }}
              </div>
              <span>{{ scope.row.refundOrder.bankRefundMsg }}</span>
            </el-tooltip>
          </template></el-table-column
        >
        <el-table-column
          :key="34"
          v-if="search.refundChannel == 1"
          prop="refundOrder.reviewerName"
          align="center"
          label="确认退款人"
          min-width="100"
        />
        <el-table-column
          :key="35"
          v-if="search.refundChannel == 1"
          prop="refundOrder.auditTime"
          align="center"
          label="确认退款时间"
          min-width="160"
        />
        <el-table-column
          prop="refundOrder.remark"
          align="center"
          label="备注"
          min-width="300"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.refundOrder.remark }}</div>
              <span>{{ scope.row.refundOrder.remark }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="22"
          v-if="search.refundChannel == 4 || search.refundChannel == 1"
          prop="refundOrder.tradingPeriod"
          align="center"
          min-width="180"
          label="交易时段"
        />
        <el-table-column
          :key="23"
          v-if="search.refundChannel == 4 || search.refundChannel == 1"
          prop="refundOrder.refundTransactionType_str"
          align="center"
          label="退费交易类型"
          min-width="160"
        />
        <el-table-column
          :key="24"
          v-if="search.refundChannel == ''"
          prop="refundOrder.refundType"
          align="center"
          label="退费类型"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getType(refundType, scope.row.refundOrder.refundType) }}
          </template>
        </el-table-column>
        <el-table-column
          :key="25"
          v-if="search.refundChannel == ''"
          prop="refundOrder.transactionOrPassId"
          align="center"
          label="通行交易编号/passId"
          min-width="160"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.refundOrder.transactionOrPassId }}
              </div>
              <span>{{ scope.row.refundOrder.transactionOrPassId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="26"
          v-if="search.refundChannel == ''"
          prop="cardBindingDeduction.bankCardNo"
          align="center"
          label="代扣金融账户编号"
          min-width="160"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.cardBindingDeduction.bankCardNo || '' }}
              </div>
              <span>{{ scope.row.cardBindingDeduction.bankCardNo || '' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="27"
          v-if="search.refundChannel == ''"
          prop="refundOrder.sern"
          align="center"
          min-width="160"
          label="原扣款流水号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.refundOrder.sern }}</div>
              <span>{{ scope.row.refundOrder.sern }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          :key="28"
          v-if="search.refundChannel == ''"
          prop="cardBindingDeduction.amount"
          align="center"
          label="原扣款金额(元)"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.cardBindingDeduction.amount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          :key="29"
          prop="refundOrder.createTime"
          align="center"
          min-width="160"
          label="创建时间"
        />
        <el-table-column
          :key="30"
          prop="refundOrder.updateTime"
          align="center"
          min-width="160"
          label="更新时间"
        />
        <!-- <el-table-column prop="accountingTime" align="center" label="更新人" /> -->
        <el-table-column
          :key="31"
          prop="refundOrder.updaterName"
          align="center"
          label="操作人"
          min-width="100"
        />
        <el-table-column
          :key="33"
          v-if="search.refundChannel == ''"
          prop="refundOrder.suspendReason"
          align="center"
          label="不可退款原因"
          min-width="110"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{
                  getType(suspendReason, scope.row.refundOrder.suspendReason)
                }}
              </div>
              <span>{{
                getType(suspendReason, scope.row.refundOrder.suspendReason)
              }}</span> </el-tooltip
            >
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          align="center"
          min-width="210"
        >
          <template slot-scope="scope">
            <el-button
              v-if="showRadio(scope.row.refundOrder.refundStatus)"
              type="primary"
              size="mini"
              @click="showEditDialog(scope.row.refundOrder.id)"
              >编辑</el-button
            >
            <el-button
              v-if="showRadio(scope.row.refundOrder.refundStatus)"
              type="danger"
              size="mini"
              @click="refundCancel(scope.row.refundOrder.id)"
              >撤销</el-button
            >
            <el-button
              v-if="
                scope.row.refundOrder.refundStatus == 9 ||
                scope.row.refundOrder.refundStatus == 10
              "
              style="margin-top: 5px"
              type="warning"
              size="mini"
              @click="
                updatePrice(
                  scope.row.refundOrder.id,
                  scope.row.refundOrder.refundAmount
                )
              "
              >修改金额</el-button
            >
            <el-button
              v-if="
                scope.row.refundOrder.refundStatus == 9 ||
                scope.row.refundOrder.refundStatus == 10
              "
              type="primary"
              size="mini"
              @click="beforeConfirm(scope.row.refundOrder.id)"
              >确认可退款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="total-price" v-if="someStatusCount > 0">
        <div style="margin-right: 30px">共：[ {{ someStatusCount }} ] 笔</div>
        <div>退款合计金额：[ {{ totalPrice }} ] 元</div>
      </div>
      <div class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[20, 50, 100, 200]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <edit-dialog
      :dialogFormVisible.sync="editDialogVisible"
      :refundOrderId="id"
      :typeList="typeList"
      @on-submit="onUpdateList"
    >
    </edit-dialog>
    <import-dialog
      :visible.sync="importDialogVisible"
      @uploadSuccess="uploadSuccess"
    >
    </import-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import float from '@/common/method/float.js'
import {
  licenseColorOption,
  refundTransactionTypeList,
  hsRefundOrderStatusList,
} from '@/common/const/optionsData'
import editDialog from './editDialog'
import importDialog from './importDialog'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    editDialog,
    importDialog,
  },
  data() {
    return {
      licenseColorOption,
      refundTransactionTypeList,
      hsRefundOrderStatusList,
      loading: false,
      checkAll: false,
      isCollapse: false,
      editDialogVisible: false,
      importDialogVisible: false,
      center: 'center',
      total: 0,
      id: null,
      value: '',
      refundStatus: '',
      refundOrderId: null,
      search: {
        bankId: '', //合作机构
        bankRefundSern: '', //退费流水号
        carColor: '', //车牌颜色
        carNo: '', //车牌
        cardNo: '', //卡号
        createTimeEnd: '', //结束创建时间,yyyy-MM-dd HH:mm:ss
        createTimeStart: '', //开始创建时间,yyyy-MM-dd HH:mm:ss
        // orderByField: '', //
        page: 1,
        pageSize: 20,
        payOrgId: '', //代扣机构
        refundChannel: '', //退款渠道 0-非后付费专用 1-原路退款、2-合作方退款、3-支付宝退款、4-人工退款、5-合作方垫付退款 6-其他
        refundDetailId: '', //退费交易编号
        refundStatus: '', //退费订单状态 0-待退款、1-退款中、2-已退款、3-退款失败、4-已撤销
        refundType: '', //退费类型 1-transactionId 2-passId
        sern: '', //扣款流水号
        // sort: '', //
        transactionOrPassId: '', //交易编号
        updateTimeEnd: '', //
        updateTimeStart: '', //
        refundTimeStart: '', //退费日期起始
        refundTimeEnd: '', //退费日期截止
        refundTransactionType: '', //退费交易类型
      },
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [], //退费状态字典
      },
      refundType: [
        {
          value: '1',
          label: '交易编号',
        },
        {
          value: '2',
          label: '行程编号',
        },
      ],
      suspendReason: [
        {
          value: '1',
          label: '客户放弃',
        },
        {
          value: '2',
          label: '超过最大请款金额',
        },
        {
          value: '3',
          label: '银行垫付',
        },
        {
          value: '4',
          label: '其他',
        },
      ],
      radio: [], //单选
      tableData: [],
      selection: [], //多选
      someStatusCount: 0, //多选的数量
      totalPrice: 0, //多选的金额
      refundPrice: '', //需要修改的退款金额
    }
  },
  created() {
    this.getRefundOrderList()
    this.getTypeList()
  },
  watch: {
    // tableData(val){
    //    let selection = []
    //   this.tableData.forEach((item) => {
    //     if (item.checked && !selection.includes(item.refundOrder.id)) {
    //       selection.push(item.refundOrder.id)
    //     }
    //   })
    // }
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value + '') {
          return typeObj[i].label
        }
      }
      return ''
    },
    showRadio(status) {
      if (status == 0) {
        return true
      } else if (status == 3) {
        return true
      } else if (status == 6) {
        return true
      } else if (status == 7) {
        return true
      }
      return false
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        lvTypeList.push({
          label: '全部',
          value: '',
        })
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch((err) => {})
    },
    getRefundOrderList() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.createTimeStart = params.createTimeStart
        ? moment(params.createTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeEnd = params.createTimeEnd
        ? moment(params.createTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.updateTimeStart = params.updateTimeStart
        ? moment(params.updateTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.updateTimeEnd = params.updateTimeEnd
        ? moment(params.updateTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.refundTimeStart = params.refundTimeStart
        ? moment(params.refundTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.refundTimeEnd = params.refundTimeEnd
        ? moment(params.refundTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''

      console.log('入参', params)
      this.$store
        .dispatch('refund/getRefundOrder', params)
        .then((res) => {
          console.log('退费订单', res)
          this.loading = false
          this.selection = []
          this.someStatusCount = 0
          this.totalPrice = 0
          if (res.data) {
            this.tableData = res.data
          } else {
            this.tableData = []
          }

          this.total = res.total
          for (let i = 0; i < this.tableData.length; i++) {
            this.$set(this.tableData[i], 'checked', false)
          }
          this.checkAll = false
        })
        .catch((err) => {
          this.loading = false
        })
    },
    getCurrentRow(id) {
      // console.log('状态', this.selection, id, this.selection.includes(id))
      this.id = id
    },
    handleCheckAll(val) {
      // console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        this.$set(this.tableData[i], 'checked', val)
      }

      if (val) {
        let totalPrice = 0
        this.someStatusCount = this.tableData.length
        this.tableData.forEach((item) => {
          if (item.checked) {
            if (
              item.refundOrder.refundAmount ||
              item.refundOrder.refundAmount == 0
            ) {
              totalPrice += item.refundOrder.refundAmount
            } else {
              totalPrice += item.refundOrder.amount
            }
          }
        })

        this.totalPrice = this.$options.filters['moneyFilter'](totalPrice)
      } else {
        this.someStatusCount = 0
        this.totalPrice = 0
      }
    },
    handleCheckOne(val) {
      // console.info('check one change is ', val)
      let totalPrice = 0
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量
      this.tableData.forEach((item) => {
        if (item.checked) {
          someStatusCount++
          if (
            item.refundOrder.refundAmount ||
            item.refundOrder.refundAmount == 0
          ) {
            totalPrice += item.refundOrder.refundAmount
          } else {
            totalPrice += item.refundOrder.amount
          }
        }
      })
      this.checkAll = totalCount === someStatusCount ? true : false
      this.someStatusCount = someStatusCount
      this.totalPrice = this.$options.filters['moneyFilter'](totalPrice)
    },
    showEditDialog(id) {
      this.id = id
      if (!id) {
        this.$message({
          message: '请先选中一条记录！',
          type: 'warning',
        })
        return
      }
      this.editDialogVisible = true
      // this.refundOrderId = this.id
    },
    refundExport() {
      //获取selection
      let selection = []
      this.tableData.forEach((item) => {
        if (item.checked && !selection.includes(item.refundOrder.id)) {
          selection.push(item.refundOrder.id)
        }
      })
      // this.selection = selection
      if (selection.length == 0) {
        this.$message({
          message: '请先勾选需要[人工退款导出]的选项！',
          type: 'warning',
        })
        return
      }
      this.$confirm('此操作将执行[人工退款导出], 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.loading = true
          this.$store
            .dispatch('refund/refundOrderPreInitAudit', {
              exportIds: selection,
            })
            .then((res) => {
              this.loading = false
              console.log(res)
              this.$message({
                message: res,
                type: 'success',
              })
              this.onExportHandle('more', selection)
              this.manRefund(selection)
            })
            .catch((err) => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    //人工退款导出接口调用
    manRefund(selection) {
      //退款前接口校验
      this.loading = true
      this.$store
        .dispatch('refund/manualRefundExport', {
          exportIds: selection,
        })
        .then((res) => {
          this.loading = false
          //成功调用导出接口
          this.getRefundOrderList()
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //导出
    onExportHandle(type, selection) {
      // let params = {
      //   name: '退费订单勾选导出报表',
      //   args: { recordIds: [] },
      // }
      console.log('typetypetype', type)
      if (type === 'all') {
        let args = JSON.parse(JSON.stringify(this.search))
        args.createTimeStart = args.createTimeStart
          ? moment(args.createTimeStart).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.createTimeEnd = args.createTimeEnd
          ? moment(args.createTimeEnd).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.updateTimeStart = args.updateTimeStart
          ? moment(args.updateTimeStart).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.updateTimeEnd = args.updateTimeEnd
          ? moment(args.updateTimeEnd).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.refundTimeStart = args.refundTimeStart
          ? moment(args.refundTimeStart).format('YYYY-MM-DD HH:mm:ss')
          : ''
        args.refundTimeEnd = args.refundTimeEnd
          ? moment(args.refundTimeEnd).format('YYYY-MM-DD HH:mm:ss')
          : ''

        delete args.pageSize
        delete args.page
        let params = {
          name: '退费订单报表',
          ...args,
        }
        this.doExport(params)
      } else {
        //勾选导出,筛选勾选数据
        // let selection = []
        // this.tableData.forEach((item) => {
        //   if (item.checked && !selection.includes(item.recordId)) {
        //     selection.push(item.recordId)
        //   }
        // })

        if (selection.length == 0) {
          this.$message({
            message: '请先勾选要导出的数据！',
            type: 'warning',
          })
          return
        }
        let strList = ''
        selection.forEach((item) => {
          console.log('item', item)
          strList = strList + item + ','
        })
        strList = strList.substr(0, strList.length - 1)
        console.log('strList', selection, strList)
        let params = {
          name: '退费订单勾选导出报表',
          ids: strList,
        }
        this.doExport(params)
      }
    },
    doExport(params) {
      this.$store
        .dispatch('containerRefund/refundReport', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          window.open(decodeUrl)
        })
        .catch((err) => {})
    },
    updatePrice(id, price) {
      if (price) {
        price = this.$options.filters['moneyFilter'](price) + '元'
      } else {
        price = ''
      }

      this.$prompt(
        '需要修改订单[' + id + ']的退款金额' + price + '为：',
        '修改退款金额',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern:
            /(^[1-9]([0-9]{0,15})(\.[0-9]{1,2})?$)|(^[0-9]{1}(\.[0-9]{1,2})?$)/,
          inputErrorMessage: '金额格式不正确',
        }
      )
        .then(({ value }) => {
          if (value) {
            let params = {
              id: id,
              amount: float.mul(value, 100),
            }
            this.$request({
              url: this.$interfaces.updatePrice,
              data: params,
              method: 'post',
            })
              .then((res) => {
                console.log('res===>>>', res)
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '金额修改成功',
                  })
                  this.getRefundOrderList()
                }
              })
              .catch((error) => {})
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入',
          })
        })
    },
    //确认可退款操作
    beforeConfirm(id) {
      this.$confirm('此操作将确认可退款, 是否继续?', '确认可退款操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          // this.$message({
          //   type: 'success',
          //   message: '删除成功!',
          // })

          let params = {
            id: id,
          }
          this.$request({
            url: this.$interfaces.confirmRefund,
            data: params,
            method: 'post',
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '确认可退款成功',
                })
                this.getRefundOrderList()
              }
            })
            .catch((error) => {})
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消操作',
          })
        })
    },
    confirmRefund() {
      //获取selection
      let selection = []
      this.tableData.forEach((item) => {
        if (item.checked && !selection.includes(item.refundOrder.id)) {
          selection.push(item.refundOrder.id)
        }
      })
      // this.selection = selection
      if (selection.length == 0) {
        this.$message({
          message: '请先勾选需要[确认退款]的选项！',
          type: 'warning',
        })
        return
      }
      this.$confirm('此操作将执行[确认退款]审核, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.loading = true
          this.$store
            .dispatch('refund/refundOrderPreInitAudit', {
              exportIds: selection,
            })
            .then((res) => {
              this.loading = false
              console.log(res)
              this.$message({
                message: res,
                type: 'success',
              })
              // this.selection = []
              this.getRefundOrderList()
            })
            .catch((err) => {
              this.loading = false
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    export() {},
    refundCancel(id) {
      let status = ''
      //获取退费状态
      this.tableData.forEach((item) => {
        if (item.refundOrder.id === id) {
          status = item.refundOrder.refundStatus
        }
      })
      if (!id) {
        this.$message({
          message: '请先选中一条记录！',
          type: 'warning',
        })
        return
      } else if (status != 3 && status !== 0) {
        this.$message({
          message: '该状态订单不可撤销！',
          type: 'error',
        })
        return
      }

      //创建撤销对话框
      const h = this.$createElement
      let _self = this
      this.$msgbox({
        title: '撤销操作',
        center: true,
        message: h('div', null, [
          h(
            'div',
            { style: 'margin-bottom: 20px' },
            '确定撤销订单[' + id + ']吗？'
          ),
          h(
            'div',
            {
              style:
                'display: inline-block;vertical-align: top;margin-right:10px;margin-top:40px',
            },
            '撤销原因：'
          ),
          h('textarea', {
            key: id,
            style: 'height:80px;width:160px',
            on: {
              input: function (event) {
                _self.value = event.target.value
              },
            },
          }),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            console.log('value', _self.value)

            //撤销接口提交
            _self.doCancel(id, (res) => {
              console.log('res----------', res)
              if (res == '200') {
                //撤销成功
                _self.$message({
                  type: 'success',
                  message: '撤销成功',
                })
                _self.getRefundOrderList()
                _self.value = ''
                instance.confirmButtonText = '确定'
                instance.confirmButtonLoading = false
                done()
              } else if (res == 404) {
                _self.$message({
                  message: '请先填写撤销原因！',
                  type: 'warning',
                })
                _self.value = ''
                instance.confirmButtonText = '确定'
                instance.confirmButtonLoading = false
                // done()
              } else {
                _self.value = ''
                instance.confirmButtonText = '确定'
                instance.confirmButtonLoading = false
              }
            })
          } else {
            done()
          }
        },
      }).catch(() => {
        this.value = ''
      })
    },
    //撤销提交
    doCancel(id, callback) {
      if (!this.value) {
        callback(404)
        return
      }
      let params = {
        orderId: id,
        remark: this.value,
      }
      this.$store
        .dispatch('refund/refundOrderCancel', params)
        .then((res) => {
          if (res) {
            callback(200)
          }
        })
        .catch((err) => {
          callback(999)
        })
    },
    changePage(page) {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = page
      this.getRefundOrderList()
    },
    handleSizeChange(pageSize) {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.pageSize = pageSize
      this.getRefundOrderList()
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.tableData.forEach((item) => {
        item.checked = false
      })
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.checkAll = false
      this.id = null
      this.radio = []
      // this.selection = []
      this.search.page = 1
      this.getRefundOrderList()
    },
    onUpdateList() {
      this.checkAll = false
      this.editDialogVisible = false
      this.radio = []
      // this.selection = []
      this.search.page = 1
      this.getRefundOrderList()
    },
    uploadSuccess() {
      this.checkAll = false
      this.radio = []
      this.importDialogVisible = false
      this.getRefundOrderList()
    },
    //跳转制表页面
    toMakeTable() {
      this.$router.push({
        path: './makeRefundTable',
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.refund-order {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    margin-left: -120px;
    margin-top: 10px;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
    width: 150px !important;
    white-space: nowrap;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
}
</style>
