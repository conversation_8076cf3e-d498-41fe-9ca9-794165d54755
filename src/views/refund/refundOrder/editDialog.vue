<template>
  <div class="edit-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 退费订单修改 </span>
        </div>
      </template>
      <div class="desc-item">
        <el-descriptions class="margin-top" :column="3" border>
          <el-descriptions-item label="卡号：">{{
            refundOrder.cardNo
          }}</el-descriptions-item>
          <el-descriptions-item label="车牌号：">{{
            refundOrder.carNo
          }}</el-descriptions-item>
          <el-descriptions-item label="车牌颜色：">{{
            getType(typeList.carColors, refundOrder.carColor)
          }}</el-descriptions-item>
          <el-descriptions-item label="退费交易编号：">{{
            refundOrder.refundDetailId
          }}</el-descriptions-item>
          <el-descriptions-item label="退费类型：">{{
            getType(refundType, refundOrder.refundType)
          }}</el-descriptions-item>
          <el-descriptions-item label="退款渠道：">{{
            getType(typeList.refundChannels, refundOrder.refundChannel)
          }}</el-descriptions-item>
          <el-descriptions-item label="交易编号/passId：">{{
            refundOrder.transactionOrPassId
          }}</el-descriptions-item>
          <el-descriptions-item label="退款机构名称：">{{
            getType(typeList.payOrgIds, refundOrder.bankAccount)
          }}</el-descriptions-item>
          <el-descriptions-item label="代扣机构：">{{
            getType(typeList.payOrgIds, refundOrder.payOrgId)
          }}</el-descriptions-item>
          <el-descriptions-item label="订单号：">{{
            refundOrder.id
          }}</el-descriptions-item>
          <el-descriptions-item label="退款人姓名：">{{
            refundOrder.bankAccountName
          }}</el-descriptions-item>
          <el-descriptions-item label="退款账户：">{{
            refundOrder.bankAccountNo
          }}</el-descriptions-item>
          <el-descriptions-item label="当前退费订单状态：">{{
            getType(typeList.refundStatus, refundOrder.refundStatus)
          }}</el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red">*</span> 退费订单状态修改为：
            </template>
            <el-select v-model="updateForm.refundStatus" placeholder="请选择">
              <el-option
                v-for="item in refundStatus"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red">*</span> 不可退原因：
            </template>
            <el-select v-model="updateForm.suspendReason" placeholder="请选择">
              <el-option
                v-for="item in reasonType"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="update()">修改</el-button>
        <el-button @click="close()">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { typeAdapter } from '@/common/method/formatOptions'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    refundOrderId: {
      type: Number,
    },
    typeList: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      isFullscreen: false,
      showLoading: false,
      refundOrder: {},
      reasonType: [],
      refundStatus: [
        { value: '7', label: '不可退款' },
        { value: '0', label: '待退款' },
        { value: '3', label: '退款失败' },
      ],
      refundType: [
        { value: '1', label: '交易编号' },
        { value: '2', label: '行程编号' },
      ],
      updateForm: {
        refundStatus: '',
        suspendReason: '',
      },
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        // console.log(this.refundOrderId)
        this.getOrderDetail()
      }
    },
  },
  methods: {
    typeAdapter,
    typeFilter(suspendReason) {
      if (this.reasonType.length === 0) {
        suspendReason.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          this.reasonType.push(lvObj)
        })
      }
    },
    getOrderDetail() {
      this.showLoading = true
      let params = { id: this.refundOrderId }
      console.log('交易明细入参', params)
      this.$store
        .dispatch('refund/refundOrderData', params)
        .then((res) => {
          this.showLoading = false
          console.log('订单详情', res)
          this.refundOrder = res.refundOrder
          this.typeFilter(res.suspendReasons)
          this.updateForm.refundStatus = res.refundOrder.refundStatus + ''
          this.updateForm.suspendReason = res.refundOrder.suspendReason
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    update() {
      console.log('update')
      this.showLoading = true
      let params = JSON.parse(JSON.stringify(this.updateForm))
      params.id = this.refundOrderId
      console.log('入参', params)
      this.$store
        .dispatch('refund/refundOrderUpdate', params)
        .then((res) => {
          //修改成功
          this.showLoading = false
          this.$emit('on-submit')
          this.$message({
            message: '修改成功',
            type: 'success',
          })
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    getType(typeObj, value) {
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      width: 200px;
      white-space: nowrap;
    }
  }
}

// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
