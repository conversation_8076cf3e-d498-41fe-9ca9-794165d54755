<template>
  <div class="toll-record" v-loading.fullscreen.lock="showLoading">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <!-- <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" placeholder="请选择">
              <el-option
                v-for="item in typeList.carColors"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="carNo">
            <el-input v-model="search.carNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input v-model="search.cardNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="用户编号：" prop="custMastId">
            <el-input v-model="search.custMastId" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="申请时间起始：" prop="startTime">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.startTime"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="申请时间截至：" prop="endTime">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.endTime"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="来源：" prop="source">
            <el-select v-model="search.source" placeholder="请选择">
              <el-option
                v-for="item in sourceList"
                :key="item.index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item> -->
          <dart-search-item :is-button="true" :span="24">
            <div class="g-flex">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <!-- <el-button size="mini" type="primary"  @click="onReSetHandle">导出</el-button> -->
              <el-button size="mini" type="primary" @click="onExportHandle()">
                <i class="el-icon-download"></i> 导出</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="550"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column
          prop="passId"
          align="center"
          min-width="250"
          label="原始交易/行程ID"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.passId }}
              </div>
              <span>{{ scope.row.passId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="plateNum"
          align="center"
          min-width="100"
          label="车牌"
        />
        <el-table-column prop="plateNumColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.plateNumColor) }}
          </template>
        </el-table-column>
        <el-table-column prop="payBankName" align="center" label="代扣机构">
          <!-- <template slot-scope="scope"> -->
          <!-- {{ getType(typeList.payOrgIds, scope.row.tollRecord.payOrgId) }} -->
          <!-- {{ payBankName }} -->
          <!-- </template> -->
        </el-table-column>
        <el-table-column
          prop="chargeType"
          min-width="160"
          align="center"
          label="扣费类型"
        >
          <template slot-scope="scope">
            {{ getType(chargeTypeList, scope.row.chargeType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="transNum"
          align="center"
          label="原始交易笔数"
          min-width="110"
        />
        <el-table-column
          prop="transAmount"
          align="center"
          label="原始交易金额"
          min-width="110"
        >
          <template slot-scope="scope">
            {{ scope.row.transAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="deductionAmount"
          align="center"
          label="已请款金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.deductionAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="enterAmount"
          align="center"
          label="已入账金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.enterAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankAdvanceAmount"
          align="center"
          label="银行垫付金额"
          min-width="110"
        >
          <template slot-scope="scope">
            {{ scope.row.bankAdvanceAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="notEnterAmount"
          align="center"
          label="未入账金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.notEnterAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundAmount"
          align="center"
          label="本次退费金额"
          min-width="110"
        >
          <template slot-scope="scope">
            {{ scope.row.refundAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundedAmount"
          align="center"
          label="已退费金额"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.refundedAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundedAmount"
          align="center"
          label="状态"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.status }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="退费请求时间"
          min-width="180"
        />
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">
            <!-- <el-button
              type="primary"
              size="mini"
              @click="tollConfirm(scope.row.id)"
              >确认</el-button
            > -->
            <el-popconfirm
              confirm-button-text="确认"
              cancel-button-text="取消"
              title="通行费确认？"
              @confirm="tollConfirm(scope.row.id)"
            >
              <el-button
                slot="reference"
                style="margin: 0 20px"
                type="primary"
                size="mini"
                v-loading="loading"
                >确认</el-button
              >
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.page_index"
          :page-sizes="[10, 20, 50]"
          :page-size="search.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
// import { decode } from 'js-base64'
import { getToken } from '@/utils/auth'
// var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      showLoading: false,
      loading: false,
      center: 'center',
      total: '',
      search: {
        // passId: '', //退费流水号
        // carColor: '', //车牌颜色
        // carNo: '', //车牌
        // cardNo: '', //卡号
        // custMastId: '',
        // refund_type: 1, //1-注销退费，2-通行费退费 3-卡账退费
        // source: '',
        // startTime: '', //申请开始时间
        // endTime: '', //申请截止时间
        page_index: 1,
        page_size: 20,
      },
      tableData: [],
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [], //退费状态字典
      },
      //来源：1-网点；2-C端
      sourceList: [
        {
          value: 1,
          label: '网点',
        },
        {
          value: 2,
          label: 'C端',
        },
      ],
      //交易类型 历史数据为null 1-ETC门架TAC交易 2-刷卡交易 3-ETC门架OBU交易 4-ETC门架图像交易 5-ETC门架拟合路径交易
      chargeTypeList: [
        {
          value: '1',
          label: 'ETC门架TAC交易',
        },
        {
          value: '2',
          label: '刷卡交易',
        },
        {
          value: '3',
          label: 'ETC门架OBU交易',
        },
        {
          value: '4',
          label: 'ETC门架图像交易',
        },
        {
          value: '5',
          label: 'ETC门架拟合路径交易',
        },
      ],
    }
  },
  created() {
    this.getTollConfirm()
    this.getTypeList()
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getTollConfirm() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      // params.startTime = params.startTime
      //   ? moment(params.startTime).format('YYYY-MM-DD HH:mm:ss')
      //   : ''
      // params.endTime = params.endTime
      //   ? moment(params.endTime).format('YYYY-MM-DD HH:mm:ss')
      //   : ''
      this.$store
        .dispatch('refund/getTollConfirm', params)
        .then((res) => {
          this.loading = false
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    tollConfirm(id) {
      this.$store
        .dispatch('refund/tollConfirm', { confirmId: id })
        .then((res) => {
          this.$message({
            message: '确认成功',
            type: 'success',
          })
          this.getTollConfirm()
        })
        .catch((err) => {})
    },
    //导出
    onExportHandle() {
      // this.showLoading = true
      // this.$store
      //   .dispatch('refund/tollConfirmExport')
      //   .then((res) => {
      //     this.showLoading = false
      //     window.open(res)
      //   })
      //   .catch((err) => {
      //     this.showLoading = false
      //   })
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/business/transactionFee/refundRecord/export?Authorization=Bearer ' +
        getToken()

      // for (let i in params) {
      //   if (params[i] != '') {
      //     url += '&' + i + '=' + params[i] 
      //   }
      // }
      // console.log('url', url)
      window.open(url)
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch((err) => {})
    },
    changePage(page) {
      this.search.page_index = page
      this.getTollConfirm()
    },
    handleSizeChange(pageSize) {
      this.search.page_size = pageSize
      this.getTollConfirm()
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.page_index = 1
      this.search.page_size = 20
    },
    onSearchHandle() {
      this.search.page_index = 1
      this.getTollConfirm()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.toll-record {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    margin-left: -120px;
    margin-top: 10px;
  }
  ::v-deep.dart-search-wrapper
    .dart-search-container
    .el-form--inline
    .el-form-item {
    margin-bottom: 0;
  }
  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  //   width: calc(100% - 150px) !important;
  // }
  // ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
  //   width: 150px !important;
  //   white-space: nowrap;
  // }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
