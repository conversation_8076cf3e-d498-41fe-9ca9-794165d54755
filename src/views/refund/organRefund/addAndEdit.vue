<template>
  <div class="form" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :title="dialogType === 'add' ? '新增' : '修改'"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
        :key="type"
      >
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="银行：" prop="bankId">
              <el-select v-model="ruleForm.bankId" placeholder="请选择">
                <el-option
                  v-for="item in orgList"
                  :key="item.index"
                  :label="item.fieldNameDisplay"
                  :value="item.fieldValue"
                /> </el-select
            ></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款渠道：" prop="refundChannel">
              <el-select v-model="ruleForm.refundChannel" placeholder="请选择">
                <el-option
                  v-for="item in refundChannel"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款地址：" prop="refundAddress">
              <el-input type="text" v-model="ruleForm.refundAddress" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { licenseColorOption, refundChannel } from '@/common/const/optionsData'
var moment = require('moment')
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
    originData: {
      type: Object,
      default: {},
    },
    orgList: {
      type: Array,
      default: [],
    },
    channelType: {
      type: Array,
      default: [],
    },
  },

  data() {
    return {
      licenseColorOption,
      refundChannel,
      showLoading: false,
      dialogFormVisible: false,
      dialogType: 'add',
      dialogOriginData: {},
      refundChannelList: [],
      ruleForm: {
        bankId: '',
        refundAddress: '',
        refundChannel: '',
      },
      rules: {
        bankId: [
          {
            required: true,
            message: '请选择[银行]',
            trigger: 'change',
          },
        ],
        refundChannel: [
          {
            required: true,
            message: '请选择[退款渠道]',
            trigger: 'change',
          },
        ],
        refundAddress: [
          {
            required: true,
            message: '[退款地址]不能为空!',
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    originData(val) {
      this.dialogOriginData = val
    },
    type(val) {
      console.log('val', val)
      console.log('id', this.originData.id)
      this.dialogType = val
      if (this.dialogType === 'edit') {
        this.$store
          .dispatch('refund/refundConfigOrg', {
            id: this.originData.id,
          })
          .then((res) => {
            console.log('res', res)
            //信息数据回填
            let dialogOriginData = {
              id: res.id,
              bankId: res.bindingOrgId,
              refundAddress: res.refundAddress,
              refundChannel: res.refundChannel,
            }
            //渠道回填
            // if (this.refundChannelList.length === 0) {
            //   channelType.forEach((item) => {
            //     let channelList = {
            //       label: item.fieldNameDisplay,
            //       value: item.fieldValue,
            //     }
            //     this.refundChannelList.push(channelList)
            //   })
            // }
            console.log('this.refundChannelList', this.refundChannelList)

            this.ruleForm = dialogOriginData
            this.dialogOriginData = dialogOriginData
            console.log('回填后的数据', this.ruleForm)
          })
      } else {
        this.ruleForm = {}
      }
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  methods: {
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          if (this.dialogType === 'add') {
            this.add()
          } else {
            this.edit()
          }
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    add() {
      this.showLoading = true
      this.dialogType = ''
      delete this.ruleForm.id
      console.log('this.formdata', this.ruleForm)
      this.$store
        .dispatch('refund/orgEdit', this.ruleForm)
        .then((res) => {
          this.showLoading = false
          console.log('添加成功', res)
          this.$message({
            message: '添加成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.$emit('on-submit')
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    edit() {
      this.showLoading = true
      this.dialogType = ''
      this.$store
        .dispatch('refund/orgEdit', this.ruleForm)
        .then((res) => {
          console.log('修改成功', res)
          // this.dialogOriginData = this.formData
          this.$message({
            message: '修改成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.showLoading = false
          this.$emit('on-submit')
        })
        .catch((err) => {
          //错误后继续回填数据
          this.ruleForm = this.dialogOriginData
          this.showLoading = false
        })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
// .el-form-item__label {
//   text-align: center;
//   white-space: nowrap;
// }
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
.el-date-editor.el-input,
.el-date-editor.el-input__inner,
.el-input__inne {
  width: 100%;
}
</style>
