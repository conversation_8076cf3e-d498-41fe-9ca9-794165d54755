<template>
  <div class="organ-refund">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="银行名称：" prop="carNoColor">
            <el-select v-model="search.bankId" filterable placeholder="请选择">
              <el-option
                v-for="item in orgList"
                :key="item.index"
                :label="item.fieldNameDisplay"
                :value="item.fieldValue"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onReSetHandle()">重置</el-button>
              <el-button size="mini" type="primary" @click="add()"
                >新增</el-button
              >
              <el-button size="mini" type="warning" @click="add('edit')"
                >修改</el-button
              >
              <el-button size="mini" type="danger" @click="del()"
                >删除</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="600"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column label="选择" width="50" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change="getCurrentRow(scope.$index, scope.row.id)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column
          prop="bindingOrgId"
          align="center"
          label="银行名称"
          min-width="150"
        >
          <template slot-scope="scope">
            {{ getOrgView(scope.row.bindingOrgId) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bindingOrgId"
          align="center"
          label="简称"
          min-width="120"
        />
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="退费渠道"
        >
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.refundChannel, 'getRefundChannel') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="refundAddress"
          align="center"
          min-width="120"
          label="渠道地址"
        />
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          min-width="160"
        />
        <el-table-column prop="creatorName" align="center" label="创建人" />
        <el-table-column
          prop="updateTime"
          align="center"
          min-width="160"
          label="更新时间"
        />
        <el-table-column prop="updaterName" align="center" label="更新人" />
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[10, 20, 50]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <add-and-edit
      :visible.sync="dialogAddVisible"
      :type.sync="dialogType"
      :originData="originData"
      :orgList="orgList"
      :channelType="channelType"
      @on-submit="onUpdateList"
    ></add-and-edit>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import addAndEdit from './addAndEdit'
import { licenseColorOption } from '@/common/const/optionsData'
import { typeAdapter } from '@/common/method/formatOptions'

export default {
  components: {
    dartSearch,
    dartSearchItem,
    addAndEdit,
  },
  data() {
    return {
      licenseColorOption,
      dialogAddVisible: false,
      loading: false,
      center: 'center',
      dialogType: 'add',
      total: '',
      id: '',
      radio: [],
      search: {
        bankId: '',
        page: 1,
        pageSize: 20,
      },
      tableData: [],
      orgList: [],
      channelType: [],
      originData: {},
    }
  },
  created() {
    this.getOrgViewList()
    this.getOrgList()
    // this.getChannelType()
  },
  methods: {
    typeAdapter,
    getChannelType() {
      //渠道字典
      this.$store
        .dispatch('refund/getChannelType')
        .then((res) => {
          console.log('渠道字典', res)
          this.channelType = res
        })
        .catch((err) => {})
    },
    getOrgView(value) {
      for (let i = 0; i < this.orgList.length; i++) {
        if (this.orgList[i].fieldValue == value) {
          return this.orgList[i].fieldNameDisplay
        }
      }
      return ''
    },
    getOrgViewList() {
      this.$store.dispatch('refund/getOrgView').then((res) => {
        console.log('银行数据', res.orgIds)
        let list = res.orgIds
        //过滤数据
        list.forEach((item) => {
          let org = {
            fieldNameDisplay: item.fieldNameDisplay,
            fieldValue: item.fieldValue,
          }
          this.orgList.push(org)
        })
      })
    },
    getOrgList() {
      this.loading = true
      console.log('this.search', this.search)
      let params = this.search
      // if (!params.bankId) {
      //   delete params.bankId
      // }
      this.$store
        .dispatch('refund/searchOrg', params)
        .then((res) => {
          console.log('list', res)
          this.loading = false
          this.tableData = res.data
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //获取选中
    getCurrentRow(index, id) {
      this.originData = this.tableData[index]
      console.log(
        '数据回填~~~~~~~~~~~~',
        index,
        this.tableData[index],
        this.originData
      )
      this.id = id
    },
    //操作按钮打开对话框
    add(dialogType = 'add') {
      console.log('this.radio', this.radio)
      if (dialogType === 'edit' && this.radio.length == 0) {
        this.message('请先选中一条记录！', 'warning')
        return
      }
      this.dialogType = dialogType
      this.dialogAddVisible = true
    },
    del() {
      this.confirmDilog('确定要删除该条数据吗？', '删除操作', 'danger')
        .then(() => {
          this.$store
            .dispatch('refund/orgDelete', { id: this.id })
            .then((res) => {
              this.message('删除成功', 'success')
              this.onUpdateList()
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.radio = []
      this.search.page = 1
      this.search.pageSize = 20
    },
    onSearchHandle() {
      this.radio = []
      this.search.page = 1
      this.getOrgList()
    },
    changePage(page) {
      this.radio = []
      this.search.page = page
      this.getOrgList()
    },
    handleSizeChange(pageSize) {
      this.radio = []
      this.search.pageSize = pageSize
      this.getOrgList()
    },
    onUpdateList() {
      this.radio = []
      this.getOrgList()
    },
    confirmDilog(msg, title, type) {
      return new Promise((resolve, reject) => {
        // if (useType === 'moreConfirm') {
        if (this.radio.length === 0) {
          reject({ msg: '请先选中一条记录！', type: 'warning' })
          return
        }
        // }
        this.$confirm(msg, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject({ msg: '取消确认', type: 'info' })
          })
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.organ-refund {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
  }
  .btn-wrapper {
    margin-left: 20px;
    margin-top: 10px;
  }
}
</style>
