<template>
  <div>
    <el-dialog width="70%"
               title="客账模式绑定车辆"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :show-close="true"
               :before-close="handleCloseIcon">
      <div>
        <el-table ref="multipleTable"
                  :data="tableData"
                  align="center"
                  height="300px"
                  header-align="center"
                  style="width: 100%;"
                  :row-key="getRowKeys"
                  @selection-change="handleSelectionChange">
          <el-table-column prop="carNo"
                           align="center"
                           label="车牌号"
                           width="170">
            <template slot-scope="scope">
              {{scope.row.carNo}} 【{{getVehicleColor(scope.row.carColor)}}】
            </template>
          </el-table-column>
          <el-table-column prop="carType"
                           align="center"
                           label="车辆类型"
                           width="120">
            <template slot-scope="scope">
              {{getCarType(scope.row.carType)}}
            </template>
          </el-table-column>
          <el-table-column prop="cardNo"
                           align="center"
                           label="ETC卡号"
                           min-width="150" />
          <el-table-column prop="cardType_str"
                           align="center"
                           label="ETC卡类型"
                           min-width="150">
          </el-table-column>
          <el-table-column prop="cardStatus_str"
                           align="center"
                           label="ETC卡状态"
                           min-width="120" />
          <el-table-column prop="isBind"
                           align="center"
                           label="绑定状态">
            <template slot-scope="scope">
              {{scope.row.isBind?'已绑定':'未绑定'}}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination g-flex g-flex-end">
        <el-pagination @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page="formData.pageNum"
                       :page-size="formData.pageSize"
                       layout="total, prev, pager, next, jumper"
                       :total="total">
        </el-pagination>
      </div>
      <!-- <div class="bindTips">
        温馨提示：
        <div class="bindTips-item">1.卡片状态异常不允许绑定客账，请排除异常状态后重试；</div>
        <div class="bindTips-item">2.绑定客账成功后，原卡账余额将转移至客账，其他客账绑定车辆共享客账余额；</div>
        <div class="bindTips-item">3.暂不支持客账模式转卡账模式，默认全选可</div>
      </div>
      <div class="foot"
           v-if="tableData && tableData.length">
        <el-button @click="bindHandle"
                   type="primary">批量绑定</el-button>
      </div> -->

      <!-- <el-dialog width="30%"
                 title="确认绑定"
                 :visible.sync="dialogconfirmVisible"
                 :close-on-click-modal="false"
                 append-to-body
                 :show-close="true">
        <div style="font-size:18px">
          目前只支持卡账模式转客账模式，转换后无法转回卡账模式；请确认是否继续绑定？
        </div>
        <span slot="footer"
              class="dialog-footer">
          <el-button @click="dialogconfirmVisible = false">取 消</el-button>
          <el-button type="primary"
                     @click="confimBindHandle">确 定</el-button>
        </span>
      </el-dialog> -->

    </el-dialog>
    <signature :visible.sync="dialogSignVisible"
               :itemData='itemData'
               :signatureData='signatureData'
               @on-success='onSignSuccessHandle'></signature>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getallGxCardType,
  getCarType,
} from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import signature from './components/signature.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    itemData: {
      type: Object,
      default: {},
    },
  },
  components: { signature },
  data() {
    return {
      dialogVisible: false,
      dialogconfirmVisible: false,
      tableData: [],
      formData: {
        pageNum: 1,
        pageSize: 10,
        customerId: '',
      },
      total: 0,
      bindVehicleData: {
        accountId: '',
        vehicleList: [],
      },
      signatureData: {
        customerId: '',
        vehicles: [],
        conType: '5',
        specType: '1',
      },
      dialogSignVisible: false,
      isRequest: false,
    }
  },

  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val && this.itemData && this.itemData.customerId) {
        this.initData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    itemData(val) {
      if (this.visible && val && val.customerId) {
        this.initData()
      }
    },
  },
  created() {
    if (this.visible && this.itemData.customerId) {
      this.initData()
    }
  },
  methods: {
    getVehicleColor,
    getallGxCardType,
    getCarType,
    initData() {
      this.formData.customerId = this.itemData.customerId
      this.formData.pageNum = 1
      this.guestAccountVehicle()
    },
    handleSizeChange(val) {
      this.formData.pageSize = val
      this.guestAccountVehicle()
    },
    handleCurrentChange(val) {
      this.formData.pageNum = val
      this.guestAccountVehicle()
    },
    //记录选中哪一项，确保翻页后保留选中
    getRowKeys(row) {
      return row.carNo
    },
    guestAccountVehicle() {
      console.log(new Date())
      if (this.isRequest) return
      this.isRequest = true
      this.$request({
        url: this.$interfaces.getVehicleAccountList,
        method: 'post',
        data: this.formData,
      })
        .then((res) => {
          this.isRequest = false
          if (res.code == 200) {
            this.total = res.data.total
            let records = res.data.records
            this.tableData = records
          }
        })
        .catch((e) => {
          this.isRequest = false
        })
    },
    //列表多选事件
    handleSelectionChange(val) {
      this.signatureData.vehicles = val.map((item) => {
        return {
          vehicleCode: item.carNo,
          vehicleColor: item.carColor,
        }
      })
    },
    getVehicleData() {
      this.signatureData.vehicles = []
      this.signatureData.vehicles = this.tableData.map((item) => {
        return {
          vehicleCode: item.carNo,
          vehicleColor: item.carColor,
        }
      })
    },
    //二次确认弹框
    bindHandle() {
      this.getVehicleData()
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '目前只支持卡账模式转客账模式，转换后无法转回卡账模式；请确认是否继续绑定？'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.confimBindHandle()
          }
        },
      })
    },
    // 用户完成协议签署
    onSignSuccessHandle() {
      this.confimBindHandle()
    },
    // 2022.7.29 绑定前校验签约
    confimBindHandle() {
      this.signatureData.customerId = this.formData.customerId
      let params = {
        customerId: this.formData.customerId,
      }
      request({
        url: api.checkContracts,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.dialogconfirmVisible = false
          if (res.code == 200) {
            console.log(res, 'sss')
            if (res.data.isCompleted == '0') {
              let _this = this
              const h = _this.$createElement
              _this.$msgbox({
                title: '提示',
                message: h('div', null, [
                  h(
                    'p',
                    {
                      style:
                        'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
                    },
                    '查询出该用户还未签署相关协议，请点击"签署"去签署'
                  ),
                ]),
                showCancelButton: true,
                confirmButtonText: '签署',
                cancelButtonText: '取消',
                showClose: false,
                callback(action) {
                  if (action == 'confirm') {
                    _this.dialogSignVisible = true
                  }
                },
              })
            } else {
              this.confimBind()
            }
          } else {
            // this.$message({
            //   type: 'error',
            //   message: res.msg,
            // })
          }
        })
        .catch((err) => {})
    },
    //批量绑定
    confimBind() {
      this.$request({
        url: this.$interfaces.batchConvertCust,
        method: 'post',
        data: {
          customerId: this.itemData.customerId,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.resetTable()
            this.dialogVisible = false
            this.dialogconfirmVisible = false
            this.$msgbox({
              title: '提示',
              showClose: true,
              type: 'success',
              customClass: 'my_msgBox singelBtn',
              dangerouslyUseHTMLString: true,
              message: '批量绑定成功',
            })
          } else {
            this.dialogconfirmVisible = false
          }
        })
        .catch((err) => {
          this.dialogconfirmVisible = false
        })
    },
    resetTable() {
      this.formData.pageNum = 1
      this.formData.pageSize = 10
      this.guestAccountVehicle()
    },
    //关闭弹窗
    handleCloseIcon() {
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.foot {
  margin-top: 20px;
  text-align: center;
}
.bindTips {
  margin-top: 10px;
  .bindTips-item {
    margin: 4px;
  }
}
</style>