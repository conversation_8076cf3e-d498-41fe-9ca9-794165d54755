<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:车辆消费记录
  * @author:zhang<PERSON>
  * @date:2023/03/29 14:50:10
-->
<template>
  <div>
    <el-dialog width="80%"
               title="车辆消费详情"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :show-close="true"
               :append-to-body="true">
      <div>
        <div>
          <el-form label-width="120px">
            <div class="g-flex g-flex-align-center ">
              <el-form-item label="通行时间:">
                <el-date-picker v-model="time"
                                type="daterange"
                                clearable
                                value-format="yyyyMMdd"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期">
                </el-date-picker>
              </el-form-item>
              <el-button type="primary"
                         style="margin-left:10px"
                         @click="getRecord"> 搜索</el-button>
            </div>
          </el-form>
        </div>
        <el-table :data="tableData"
                  height="500px"
                  style="width:100%;margin-top: 10px;">
          <el-table-column align="center"
                           prop="pay_time"
                           label="通行时间"
                           width="170">
            <template slot-scope="scope">
              <span>{{scope.row.pay_time }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           prop="customer_name"
                           label="用户名称"
                           width="220">
          </el-table-column>
          <el-table-column align="center"
                           prop="vehicle_code"
                           label="车辆号码"
                           width="120">
          </el-table-column>
          <el-table-column align="center"
                           prop="cpu_card_id"
                           label="ETC通行卡号"
                           width="190">
          </el-table-column>
          <el-table-column align="center"
                           prop="pay_way"
                           label="付费方式"
                           width="150">
            <template slot-scope="scope">
              <span>{{ scope.row.pay_way == '1' ? (scope.row.pay_way == '2' ?'出口ETC刷卡通行':'ETC交易-停用'):'出口ETC通行' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="通行金额(元)"
                           width="120">
            <template slot-scope="scope">
              <span>{{ (scope.row.pay_toll / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="服务费(元)"
                           width="120">
            <template slot-scope="scope">
              <span>{{ !scope.row.serviceAmount||scope.row.serviceAmount==0?'--':(scope.row.serviceAmount / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="滞纳金(元)"
                           width="120">
            <template slot-scope="scope">
              <span>{{!scope.row.overdueAmount||scope.row.overdueAmount==0?'--':(scope.row.overdueAmount / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="滞纳天数(天)"
                           width="120">
            <template slot-scope="scope">
              <span>{{!scope.row.overdueDay||scope.row.overdueDay=='0'?'--':scope.row.overdueDay }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="通行后卡面余额(元)"
                           width="150">
            <template slot-scope="scope">
              <span>{{ (scope.row.after_wallet / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="代收费(元)"
                           width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.pay_agency">{{ (scope.row.pay_agency / 100).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center"
                           prop="en_station"
                           label="入口站点"
                           width="160">
          </el-table-column>
          <el-table-column align="center"
                           prop="ex_station"
                           label="出口站点"
                           width="160">
          </el-table-column>
        </el-table>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
var moment = require('moment')
export default {
  name: '',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    accountInfo: {
      type: Object,
      default: {},
    },
    vehicleInfo: {
      type: Object,
      default: {},
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      search: {
        customer_id: '',
        cpu_card_id: '',
        pay_start_date: '',
        pay_end_date: '',
        vehicle_code: '',
        vehicle_color: '',
      },
      time: '',
    }
  },
  computed: {},
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    vehicleInfo(val) {
      if (val) {
        this.search.customer_id = this.accountInfo.customerId
        this.search.cpu_card_id = this.vehicleInfo.cardNo
        this.search.vehicle_code = this.vehicleInfo.carNo
        this.search.vehicle_color = this.vehicleInfo.carColor
        this.getRecord()
      }
    },
  },
  created() {
    this.search.pay_start_date = moment().startOf('day').format('YYYYMMDD')
    this.search.pay_end_date = moment().endOf('day').format('YYYYMMDD')
  },
  methods: {
    getRecord() {
      if (this.time) {
        this.search.pay_start_date = this.time[0]
        this.search.pay_end_date = this.time[1]
      } else {
        this.search.pay_start_date = moment().startOf('day').format('YYYYMMDD')
        this.search.pay_end_date = moment().endOf('day').format('YYYYMMDD')
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.tollRecord,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.tableData = res.data
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang='scss' scoped>
.el-form-item {
  margin-bottom: 0px;
}
</style>