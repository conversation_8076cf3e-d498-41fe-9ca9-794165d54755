<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:调整充值提醒线
  * @author:dwz
  * @date:2022/11/2 14:56:29
!-->
<template>
  <div>
    <el-dialog
      width="40%"
      title="充值提醒线调整"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
      <div class="sign-from">
        <el-form
          :model="formData"
          ref="formData"
          class="nat-form nat-form-list"
          :rules="rules"
          label-width="140px"
        >
          <el-form-item label="原提醒线(元)：" prop="oldBlackLine">
            <el-input
              disabled
              :value="formData.oldBlackLine | moneyFilter"
            ></el-input>
          </el-form-item>
          <el-form-item label="新提醒线(元)：" prop="newBlackLine">
            <el-input
              v-model="formData.newBlackLine"
              placeholder="请输入新提醒线"
            ></el-input>
          </el-form-item>
          <el-form-item prop="amount">
            <el-button type="primary" @click="confirm">确定</el-button>
            <el-button @click="handleCloseIcon">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
export default {
  name: '',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    itemData: {
      type: Object,
      default: {},
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      formData: {
        accountId: '',
        oldBlackLine: '',
        newBlackLine: '',
      },
      rules: {
        newBlackLine: [
          {
            required: true,
            message: '请输入新提醒线',
            trigger: 'blur',
          },
          {
            pattern: /(^(([1-9]([0-9]+)?)|(0))(\.[0-9]{2})?$)/,
            message: '请输入正确的金额格式',
          },
        ],
      },
    }
  },
  computed: {},
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.formData.accountId = this.itemData.accountId
        this.formData.oldBlackLine = this.itemData.warnLineAmount
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  created() {
    // console.log(this.itemData, this.signatureData, '-----=-==--------')
  },
  methods: {
    confirm() {
      // let params = JSON.parse(JSON.stringify(this.formData))
      let params = {
        accountId: this.formData.accountId,
        lineAmount: float.mul(this.formData.newBlackLine, 100),
      }

      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.startLoading()
          request({
            url: api.updateAccountLine,
            method: 'post',
            data: params,
          })
            .then((res) => {
              this.endLoading()
              console.log(res, '提醒线调整')
              if (res.code == 200) {
                this.$emit('on-success')
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch((err) => {
              this.endLoading()
            })
        }
      })
    },
    handleCloseIcon() {
      this.dialogVisible = false
      this.formData.newBlackLine = ''
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang='scss' scoped>
</style>