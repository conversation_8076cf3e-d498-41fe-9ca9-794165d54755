<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:转客账签署协议
  * @author:zhang<PERSON>
  * @date:2022/07/29 15:22:29
!-->
<template>
  <div>
    <el-dialog width="40%"
               title="电子协议签署"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :show-close="true"
               :before-close="handleCloseIcon">

      <div class="qrBox g-flex"
           ref="qrBox"
           style="margin-top: 20px"
           v-if="signUrl">
        <div style="width: 50%; padding-left: 5%">
          <p>请用户扫码签署用户协议</p>
          <p style="color:#ec3642">确认用户已完成签署后，请点击已签署按钮
          </p>
          <p style="margin-top:50px;">
            <el-button @click="searchContractStatus"
                       type="primary"
                       size="small"
                       style="font-size:18px">已签署</el-button>
            <!-- <el-button @click="resetContractStatus"
                       size="small"
                       style="font-size:18px">重新生成协议</el-button> -->
          </p>

        </div>
        <div id="qrCode"
             style=" width: 40%; margin-left: 3%"
             ref="qrCodeBox"></div>
      </div>
      <!-- <div style='width:100%;margin: 10px 0px'
           class="g-flex g-flex-center ">
      </div> -->
      <div class="sign-from"
           v-else>
        <el-form :model="formData"
                 ref="formData"
                 class="nat-form nat-form-list"
                 :rules="rules"
                 label-width="140px">

          <el-form-item label="签约人姓名："
                        prop="signBy">
            <el-input v-model="formData.signBy"
                      placeholder="请输入签约人姓名"></el-input>
          </el-form-item>
          <el-form-item label="签约人手机号："
                        prop="signContact">
            <el-input v-model="formData.signContact"
                      placeholder="请输入签约人手机号"></el-input>
          </el-form-item>
          <el-form-item label="签约人身份证号："
                        prop="signIdNo">
            <el-input v-model="formData.signIdNo"
                      placeholder="请输入签约人身份证号"></el-input>
          </el-form-item>
          <el-form-item prop="amount">
            <el-button type="primary"
                       @click="signatureHandle">生成电子协议</el-button>
            <el-button @click="dialogVisible=false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>

    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import QRCode from 'qrcodejs2'
export default {
  name: '',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    itemData: {
      type: Object,
      default: {},
    },
    signatureData: {
      type: Object,
      default: {},
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      formData: {
        customerId: '',
        signIdNo: '',
        signBy: '',
        signContact: '',
        custType: '1',
        saleAmount: '16500',
        redirectUrl: '',
        redirectParam: '',
        otherId: '',
        conType: '9',
        vehicles: [],
      },
      rules: {
        signBy: [
          {
            required: true,
            message: '请选择输入签署人姓名',
            trigger: 'blur',
          },
        ],
        signContact: [
          {
            required: true,
            message: '请输入正确手机号',
            trigger: 'blur',
            pattern: '^1[0-9]{10}$',
          },
        ],
        signIdNo: [
          { required: true, message: '请输入签署人证件号', trigger: 'blur' },
          {
            min: 10,
            max: 30,
            message: '长度在 10 到 30 个字符',
            trigger: 'change',
          },
        ],
      },
      signUrl: '',
      signId: '',
    }
  },
  computed: {},
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        Object.assign(this.signatureData, this.itemData)
        this.initData(this.signatureData)
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  created() {
    // console.log(this.itemData, this.signatureData, '-----=-==--------')
  },
  methods: {
    initData(val) {
      if (val.userType == '0') {
        this.formData.signBy = val.userName
        this.formData.signIdNo = val.idNo
        this.formData.signContact = val.mobile
      }
      this.formData.vehicles = [{}]
      this.formData.customerId = val.customerId
    },
    signatureHandle() {
      let params = JSON.parse(JSON.stringify(this.formData))
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          this.startLoading()
          request({
            url: api.batchSignUrl,
            method: 'post',
            data: params,
          })
            .then((res) => {
              console.log(res, '生成电子协议')
              if (res.code == 200) {
                this.endLoading()
                this.signUrl = res.data.signUrl
                this.signId = res.data.conId
                this.showQrcode(res.data.signUrl)
              } else {
                this.endLoading()
                this.$message.error(res.msg)
              }
            })
            .catch((err) => {
              this.endLoading()
            })
        }
      })
    },
    //显示二维码
    showQrcode(url) {
      this.isShowQrCode = true
      this.$nextTick(() => {
        document.getElementById('qrCode').innerHTML = ''
        // const h = this.$createElement;
        let qrBox = this.$refs.qrCodeBox
        new QRCode(qrBox, {
          text: url,
          width: 200,
          height: 200,
          colorDark: '#333333', //二维码颜色
          colorLight: '#ffffff', //二维码背景色
          correctLevel: QRCode.CorrectLevel.L, //容错率，L/M/H
        })
      })
    },
    //查询签署状态
    searchContractStatus() {
      let params = {
        customer_id: this.formData.customerId,
        con_id: this.signId,
      }
      this.startLoading()
      request({
        url: api.contractStatus,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log(res, '查询签署状态')
          if (res.code == 200) {
            if (res.data.conStatus == '已完成') {
              this.$message({
                type: 'success',
                message: '协议签署成功!',
              })
              this.signUrl = ''
              this.dialogVisible = false
              this.$emit('on-success')
            } else {
              this.$message({
                type: 'error',
                message: `《${res.data.conTypeDesc}协议》${res.data.conStatus},请签署完成再试`,
              })
            }
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {})
    },
    handleCloseIcon() {
      this.signUrl = ''
      this.dialogVisible = false
      this.formData.signBy = ''
      this.formData.signIdNo = ''
      this.formData.signContact = ''
    },
    resetContractStatus() {
      this.signUrl = ''
    },
  },
}
</script>

<style lang='scss' scoped>
</style>