<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:转客账签署协议
  * @author:zhang<PERSON>
  * @date:2022/07/29 15:22:29
!-->
<template>
  <div>
    <el-dialog
      width="40%"
      title="电子协议签署"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
      <div
        class="qrBox g-flex"
        ref="qrBox"
        style="margin-top: 20px"
        v-if="codeUrl"
      >
        <div style="width: 50%; padding-left: 5%">
          <p>请用户扫码签署用户协议</p>
          <p style="color: #ec3642">确认用户已完成签署后，请点击已签署按钮</p>
          <p style="margin-top: 50px">
            <el-button
              @click="searchContractStatus"
              type="primary"
              size="small"
              style="font-size: 18px"
              >已签署</el-button
            >
            <!-- <el-button @click="resetContractStatus"
                       size="small"
                       style="font-size:18px">重新生成协议</el-button> -->
          </p>
        </div>
        <div id="qrCode" ref="qrCodeBox">
          <img style="width: 50%; margin-left: 20%" :src="codeUrl" alt="" />
        </div>
      </div>
      <!-- <div style='width:100%;margin: 10px 0px'
           class="g-flex g-flex-center ">
      </div> -->
      <div class="sign-from" v-else>
        <el-form
          :model="formData"
          ref="formData"
          class="nat-form nat-form-list"
          :rules="rules"
          label-width="140px"
        >
          <el-form-item label="签约人姓名：" prop="signBy">
            <el-input
              v-model="formData.signName"
              placeholder="请输入签约人姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="签约人手机号：" prop="signPhone">
            <el-input
              v-model="formData.signPhone"
              placeholder="请输入签约人手机号"
            ></el-input>
          </el-form-item>
          <el-form-item label="签约人身份证号：" prop="signIdNo">
            <el-input
              v-model="formData.signIdNo"
              placeholder="请输入签约人身份证号"
            ></el-input>
          </el-form-item>
          <el-form-item prop="amount">
            <el-button type="primary" @click="signatureHandle"
              >生成电子协议</el-button
            >
            <el-button @click="dialogVisible = false">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import QRCode from 'qrcodejs2'
export default {
  name: '',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    itemData: {
      type: Object,
      default: {},
    },
    signatureData: {
      type: Object,
      default: {},
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      formData: {
        source: '1', //1线下2线上，B端默认线下
        customerId: '',
        signIdNo: '',
        signName: '',
        signPhone: '',
        businessType: '8', //客账转换默认8
        productType: '9', //客账默认9
        // custType: '1',
        // saleAmount: '16500',
        // redirectUrl: '',
        // redirectParam: '',
        // otherId: '',
        // conType: '9',
        marketId: '',
        vehicles: [],
      },
      rules: {
        signName: [
          {
            required: true,
            message: '请选择输入签署人姓名',
            trigger: 'blur',
          },
        ],
        signPhone: [
          {
            required: true,
            message: '请输入正确手机号',
            trigger: 'blur',
            pattern: '^1[0-9]{10}$',
          },
        ],
        signIdNo: [
          { required: true, message: '请输入签署人证件号', trigger: 'blur' },
          {
            min: 10,
            max: 30,
            message: '长度在 10 到 30 个字符',
            trigger: 'change',
          },
        ],
      },
      // signUrl: '',
      // signId: '',
      codeUrl: '', //二维码地址
    }
  },
  computed: {},
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        Object.assign(this.signatureData, this.itemData)
        this.initData(this.signatureData)
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  created() {
    // console.log(this.itemData, this.signatureData, '-----=-==--------')
  },
  methods: {
    initData(val) {
      console.log('签章信息', val)
      if (val.userType == '0') {
        this.formData.signName = val.userName
        this.formData.signIdNo = val.idNo
        this.formData.signPhone = val.mobile
      }
      this.formData.vehicles = [{}]
      this.formData.customerId = val.customerId
    },
    signatureHandle() {
      // let params = JSON.parse(JSON.stringify(this.formData))
      this.$refs['formData'].validate((valid) => {
        if (valid) {
          // console.log('生成签章原信息', params, this.signatureData)
          this.getMarket()
          // this.startLoading()
          // request({
          //   url: api.batchSignUrl,
          //   method: 'post',
          //   data: params,
          // })
          //   .then((res) => {
          //     console.log(res, '生成电子协议')
          //     if (res.code == 200) {
          //       this.endLoading()
          //       this.signUrl = res.data.signUrl
          //       this.signId = res.data.conId
          //       this.showQrcode(res.data.signUrl)
          //     } else {
          //       this.endLoading()
          //       this.$message.error(res.msg)
          //     }
          //   })
          //   .catch((err) => {
          //     this.endLoading()
          //   })
        }
      })
    },
    getSignPreview() {
      let params = JSON.parse(JSON.stringify(this.formData))
      console.log('预览签章信息', params)
      this.startLoading()
      request({
        url: api.batchSignUrlV2,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log(res, '营销活动查询')
          if (res.code == 200) {
            let signKey = res.data.signKey
            this.getSignInfo(signKey)
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getSignInfo(signKey) {
      this.startLoading()
      request({
        url: api.getSignUrl,
        method: 'post',
        data: { signKey: signKey },
      })
        .then((res) => {
          this.endLoading()
          console.log(res, '营销活动查询')
          if (res.code == 200) {
            let pdfInfo = res.data.data
            this.getSignQrcode(signKey, pdfInfo)
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getSignQrcode(signKey, pdfInfo) {
      let signUrl =
        'https://portal.gxetc.com.cn/new-agreement?type=sign&signInfo=' +
        encodeURIComponent(JSON.stringify(pdfInfo))
      let params = {
        businessType: '8', //默认8
        envVersion: 'trial',
        name: '电子协议签约',
        page: 'pages/transfers/transfers',
        sceneValue:
          '/pagesB/signWebview/signWebview?signKey=' +
          signKey +
          '&ownPath=' +
          encodeURIComponent(JSON.stringify(signUrl)),
        scene: signKey, //使用signKey作为key
      }
      this.startLoading()
      request({
        url: api.getSignQrcode,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log('二维码生成', res)
          if (res.code == 200) {
            this.codeUrl = res.data.imageUrl
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //获取营销活动，签章用
    getMarket() {
      let vehicles = this.signatureData.vehicles[0]
      let params = {
        customer_id: this.formData.customerId,
        vehicleColor: vehicles.vehicleColor || '',
        vehicleCode: vehicles.vehicleCode || '',
      }
      console.log('生成签章原信息', params)
      this.startLoading()
      request({
        url: api.signMarket,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log(res, '营销活动查询')
          if (res.code == 200) {
            if (res.data) {
              this.formData.marketId = res.data[0].marketActiveMastId
              this.getSignPreview()
            } else {
              this.$message({
                type: 'error',
                message: '查询不到营销活动！',
              })
            }
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //显示二维码
    showQrcode(url) {
      this.isShowQrCode = true
      // this.$nextTick(() => {
      //   document.getElementById('qrCode').innerHTML = ''
      //   // const h = this.$createElement;
      //   let qrBox = this.$refs.qrCodeBox
      //   new QRCode(qrBox, {
      //     text: url,
      //     width: 200,
      //     height: 200,
      //     colorDark: '#333333', //二维码颜色
      //     colorLight: '#ffffff', //二维码背景色
      //     correctLevel: QRCode.CorrectLevel.L, //容错率，L/M/H
      //   })
      // })
    },
    //查询签署状态
    searchContractStatus() {
      let params = {
        customer_id: this.formData.customerId,
        // con_id: this.signId,
      }
      this.startLoading()
      request({
        url: api.checkContracts,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log(res, '查询签署状态')
          if (res.code == 200) {
            if (res.data.isCompleted != '0') {
              this.$message({
                type: 'success',
                message: '协议签署成功!',
              })
              this.codeUrl = ''
              this.dialogVisible = false
              this.$emit('on-success')
            } else {
              this.$message({
                type: 'error',
                message: `协议还在签署中,请稍后再试`,
              })
            }
          } else {
            this.$message({
              type: 'error',
              message: res.msg,
            })
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    handleCloseIcon() {
      this.signUrl = ''
      this.dialogVisible = false
      this.formData.signName = ''
      this.formData.signIdNo = ''
      this.formData.signPhone = ''
    },
    resetContractStatus() {
      this.signUrl = ''
    },
  },
}
</script>

<style lang='scss' scoped>
</style>