<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:绑定车辆信息
  * @author:zhangys
  * @date:2023/03/29 14:50:10
-->
<template>
  <div>
    <div style="padding: 10px">
      <el-table
        ref="multipleTable"
        :data="tableData"
        align="center"
        height="350px"
        header-align="center"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          prop="vehicleNo"
          align="center"
          label="车牌号"
          min-width="150"
        />
        <el-table-column
          prop="vehicleNo"
          align="center"
          label="车牌颜色"
          width="170"
        >
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.vehicleColor) }}色
          </template>
        </el-table-column>
        <el-table-column
          prop="isTrunk"
          align="center"
          label="车辆类型"
          width="120"
        >
          <template slot-scope="scope">
            {{ getVehicleType(scope.row.isTrunk) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardNo"
          align="center"
          label="ETC卡号"
          min-width="180"
        >
        </el-table-column>
        <el-table-column
          prop="productType"
          align="center"
          label="ETC卡类型"
          min-width="150"
        >
          <template slot-scope="scope">
            {{ getallGxCardType(scope.row.productType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardStatus"
          align="center"
          label="ETC卡状态"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ getCpuStatus(scope.row.cardStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="blackAmount"
          align="center"
          label="最低预存线"
          min-width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.blackAmount | moneyFilter }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="" align="center" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="searchTollList(scope.row)"
              >查看消费记录</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <!-- <div class="pagination g-flex g-flex-end">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="formData.pageNum"
        :page-size="formData.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div> -->
    <!-- <tollRecord
      :visible.sync="tollListVisible"
      :accountInfo="accountInfo"
      :vehicleInfo="vehicleInfo"
    ></tollRecord> -->
  </div>
</template>

<script>
import {
  getVehicleColor,
  getallGxCardType,
  getCpuStatus,
  getVehicleType
} from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import tollRecord from './tollRecord'
export default {
  props: {
    accountId: {
      type: String,
    },
  },
  components: { tollRecord },
  data() {
    return {
      tableData: [],
    }
  },

  watch: {},
  created() {
    // this.formData.customerId = this.accountInfo.customerId
    // this.guestAccountVehicle()
    this.getBindCarList()
  },

  methods: {
    getVehicleColor,
    getallGxCardType,
    getCpuStatus,
    getVehicleType,
    getBindCarList() {
      let params = {
        accountId: this.accountId,
      }
      this.$request({
        url: this.$interfaces.bindingAccountVehicleList,
        method: 'post',
        data: params,
      }).then((res) => {
        console.log(res)
        if (res.code == 200) {
          // this.b2bData = res.data
          this.tableData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    handleSelectionChange(val) {
      let idArr = []
      val.forEach((item) => {
        idArr.push(item.vehicleId)
      })
      this.$emit('getVehicleIds', idArr)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.foot {
  margin-top: 20px;
  text-align: center;
}
.bindTips {
  margin-top: 10px;
  .bindTips-item {
    margin: 4px;
  }
}
</style>