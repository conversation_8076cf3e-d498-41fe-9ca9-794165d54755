<template>
  <div class="down">
    <div class="thetable">
      <div class="title">
        <p>退款信息填写</p>
        <el-button size="mini"
                   type="primary"
                   @click="toprint()">业务申请单打印</el-button>
      </div>
    </div>
    <div class="downnav">
      <el-form :model="formData"
               ref="searchForm"
               class="nat-form nat-form-list"
               :rules="rules"
               label-width="180px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="退费类型："
                          prop="type">
              <el-select v-model="formData.type"
                         placeholder="请选择"
                         @change="selectChange">
                <el-option v-for="item in typeList"
                           :key="item.index"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="退款金额(元)："
                          prop="amount">
              <el-input v-model="formData.amount"
                        placeholder="请输入退款金额"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24"
                v-if="formData.type=='2003'">
          <el-col :span="8">
            <el-form-item class="my_form_label"
                          label="开户行："
                          prop="bankName">
              <el-autocomplete class="my_input"
                               style="width: 100%"
                               clearable
                               :fetch-suggestions="queryBankName"
                               @select="selectBankName"
                               v-model="formData.bankName"
                               placeholder="银行账户开户行"></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行卡号："
                          prop="bankNo">
              <div>
                <el-input v-model="formData.bankNo"
                          placeholder="请输入银行卡号"></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="联系电话："
                          prop="mobile">
              <div>
                <el-input v-model="formData.mobile"
                          placeholder="请输入联系电话"
                          disabled></el-input>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="图形验证码：">
              <div>
                <el-input v-model="formData.captchaCode"
                          placeholder="请输入图形验证码"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <img :src="captchaUrl"
               class="captcha"
               @click="getCaptcha" />
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8"
                  v-if="formData.type=='2003'">
            <el-form-item label="开户名："
                          prop="bankAccount">
              <div>
                <el-input v-model="formData.bankAccount"
                          placeholder="请输入开户名"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="短信验证码："
                          prop="mobileCode">
              <div>
                <el-input v-model="formData.mobileCode"
                          placeholder="请输入短信验证码"></el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-button type="primary"
                     @click="sendSmsHandle"
                     class="sendSMSBtn">{{ smsName }}</el-button>
        </el-row>
      </el-form>
    </div>
    <div class="pdf-view"
         v-show="false"
         style="z-index: 100">
      <iframe id="previewPdf"
              :src="fileUrl"
              height="99%"
              width="100%">
      </iframe>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import { bankName } from '@/common/const/optionsData.js'
import float from '@/common/method/float.js'
export default {
  props: {
    userinfo: {
      type: Object,
      default: {},
    },
    refundType: {
      type: String,
      default: '2',
    },
    fileUUID: {
      type: String,
      default: '',
    },
  },
  data() {
    const validateamount = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入退款金额'))
      } else if (float.mul(value, 100) > this.userinfo.amount) {
        callback(new Error('退款金额不能大于余额'))
      } else {
        callback()
      }
    }
    return {
      bankName,
      fileUrl: '', //pdf链接
      captchaUrl: '',
      smsName: '发送验证码',
      formData: {
        captchaId: '',
        mobile: '',
        captchaCode: '',
        bankName: '',
        mobileCode: '',
        bankNo: '',
        bankAccount: '',
        amount: '',
        type: '2003',
      },
      input: '',
      rules: {
        amount: [
          { required: true, validator: validateamount, trigger: 'change' },
        ],
        bankName: [
          { required: true, message: '请选择开户行' },
          {
            min: 2,
            max: 50,
            message: '长度在 2 到 50 个字符',
            trigger: 'blur',
          },
        ],
        bankNo: [
          { required: true, message: '请输入银行卡号' },
          {
            min: 8,
            max: 30,
            message: '账号长度应在 8 到 30 个位',
            trigger: 'blur',
          },
        ],
        mobile: [
          {
            required: true,
            message: '请输入正确手机号',
            trigger: 'change',
            pattern: '^1[0-9]{10}$',
          },
        ],
        mobileCode: [
          { required: true, message: '请输入短信验证码', trigger: 'change' },
        ],
        bankAccount: [
          { required: true, message: '请输入开户名' },
          {
            min: 2,
            max: 50,
            message: '长度在 2 到 50 个字符',
            trigger: 'blur',
          },
        ],
      },
      //退费类型
      typeList: [
        {
          value: '2003',
          label: '退款到银行卡',
        },
        {
          value: '2004',
          label: '退到互联网账户',
        },
      ],
      accountData: {},
    }
  },
  created() {
    this.getCaptcha()
    this.formData.mobile = this.userinfo.mobile
    // this.formData.amount = this.userinfo.amount
  },
  methods: {
    torefund() {
      console.log(this.$refs['searchForm'])
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.startrefund()
        } else {
          return false
        }
      })
    },
    startrefund() {
      this.$confirm('是否申请客账退费?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.startLoading()
          let params = JSON.parse(JSON.stringify(this.formData))
          params.amount = float.mul(params.amount, 100)
          params.fileUUID = this.fileUUID
          params.accountId = this.userinfo.accountId
          if (params.type == '2004') {
            params.userName = this.accountData.userName
            params.userNo = this.accountData.userNo
          }
          request({
            url: api.guestAccountRefund,
            method: 'post',
            data: {
              ...params,
            },
          })
            .then((res) => {
              this.endLoading()
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '账户退费申请成功!',
                })
                this.$store.dispatch('tagsView/delView', this.$route)
                this.$router.push({
                  path: '../../../refund/logoutRefund',
                })
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch((error) => {
              this.endLoading()
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消申请账户退费',
          })
        })
    },
    getCaptcha() {
      request({
        url: api.getCaptcha,
        method: 'post',
      }).then((res) => {
        if (res.code == 200) {
          this.formData.captchaId = res.data.captchaId
          this.captchaUrl = res.data.image
        }
      })
    },
    sendSmsHandle() {
      if (!this.formData.mobile) {
        this.$message({
          message: '请输入联系电话',
          type: 'warning',
        })
        return
      }
      if (!this.formData.captchaCode) {
        this.$message({
          message: '请输入图形验证码',
          type: 'warning',
        })
        return
      }
      if (this.time) return
      this.startLoading()
      let params = {
        captchaId: this.formData.captchaId,
        mobileCode: this.formData.captchaCode,
        mobile: this.formData.mobile,
        userNo: this.$route.query.userNo,
      }
      let countdown = 60
      request({
        url: api.sendSms,
        method: 'post',
        data: {
          ...params,
        },
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.time = setInterval(() => {
              countdown = countdown - 1
              this.smsName = countdown + '秒后重新发送'
              if (countdown === 0) {
                clearInterval(this.time)
                this.time = null
                this.smsName = '重新发送'
              }
            }, 1000)
          } else {
            this.getCaptcha()
          }
        })
        .catch((error) => {
          this.endLoading()
          this.getCaptcha()
        })
    },
    //查询银行开户名
    queryBankName(queryString, cb) {
      let bankName = this.bankName.map((item) => {
        return {
          value: item.label,
          address: item.value,
        }
      })
      let results = queryString
        ? bankName.filter(this.createFilter(queryString))
        : bankName
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        )
      }
    },
    selectBankName(item) {
      this.formData.bankName = item.value
    },
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
    toprint() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.startprint()
        } else {
          return false
        }
      })
    },
    startprint() {
      this.startLoading()
      let param = {
        amount: this.formData.amount,
        bankAccountName: this.formData.bankAccount,
        bankDepositName: this.formData.bankName,
        bankNum: this.formData.bankNo,
        id: this.userinfo.accountId,
        phone: this.formData.mobile,
        refundType: this.refundType,
        type: this.formData.type,
        // fileUUID:this.fileUUID
      }
      if (param.type == '2004') {
        param.bankDepositName = this.accountData.userName
        param.bankAccountName = this.accountData.userName
        param.bankNum = this.accountData.userNo
      }
      request({
        url: api.guestRefundPrint,
        method: 'post',
        data: param,

        responseType: 'blob',
      })
        .then((response) => {
          this.blobToObj(response)
            .then((res) => {
              if (res.code != 200) {
                this.$message.error(res.msg)
              }
              this.endLoading()
            })
            .catch((error) => {})
          this.endLoading()
          let binaryData = []
          binaryData.push(response)

          this.fileUrl = window.URL.createObjectURL(
            new Blob(binaryData, { type: 'application/pdf' })
          )
          document.getElementById('previewPdf').onload = () => {
            //等待iframe加载完成后再执行doPrint.每次iframe设置src之后都会重新执行这部分代码。
            if (this.fileUrl) {
              // this.endLoading()
              document.getElementById('previewPdf').contentWindow.print()
              this.$emit('change')
            }
          }
        })
        .catch((error) => {})
    },
    blobToObj(blobData) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader() // 创建读取文件对象
        reader.readAsText(blobData, 'utf-8')
        reader.onload = function (result) {
          try {
            let result = JSON.parse(reader.result)
            resolve(result)
          } catch (e) {
            reject(e)
          }
        }
      })
    },
    //选择退款到哪里 2003 银行卡 2004 互联网账户
    selectChange(val) {
      if (
        (val == '2004' && Object.keys(this.accountData).length == 0) ||
        this.accountData.userNo == null
      ) {
        this.getAccountView()
      }
    },
    // 获取互联网账户信息
    getAccountView() {
      request({
        url: api.getAccountList,
        method: 'post',
        data: {
          custMastId: this.userinfo.customerId,
        },
      })
        .then((res) => {
          console.log(res, '<<---------res')
          if (res.code == 200) {
            if (res.data.length > 1) {
              this.$msgbox({
                message: '查询出多条互联网账户信息，请联系开发人员维护',
                title: '提示',
                dangerouslyUseHTMLString: true,
                customClass: 'my_msgBox singelBtn',
                confirmButtonText: '确定',
                type: 'error',
              })
              this.formData.type = '2003'
              return
            }
            this.accountData = res.data.length != 0 ? res.data[0] : {}
            if (
              (Object.keys(this.accountData).length != 0 &&
                !this.accountData.userNo) ||
              res.data.length == 0
            ) {
              this.$msgbox({
                message: '未查询出互联网账户信息，请退款至银行卡',
                title: '提示',
                dangerouslyUseHTMLString: true,
                customClass: 'my_msgBox singelBtn',
                confirmButtonText: '确定',
                type: 'error',
              })
              this.formData.type = '2003'
              return
            }
          }
        })
        .catch((error) => {
          this.$msgbox({
            message: error.message,
            title: '提示',
            dangerouslyUseHTMLString: true,
            customClass: 'my_msgBox singelBtn',
            confirmButtonText: '确定',
            type: 'error',
          })
        })
    },
  },
  watch: {
    userAmount(val) {
      this.formData.amount = this.moneyFilter(val)
    },
    'formData.type': {
      handler(val) {
        this.$nextTick(() => {
          this.$bus.$emit('refundSource', val)
        })
      },
      immediate: true,
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  line-height: 28px;
  margin: 0 0 20px;
  padding: 11px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  p {
    font-weight: 600;
    margin: 0;
  }
}
img {
  width: 140px;
  height: 40px;
}
// .sendSMS {
//   display: inline-block;
//   width: 140px;
//   color: #409eff;
//   margin-left: 10px;
//   line-height: 40px;
// }
</style>