<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:记录详情
  * @author:zhangys
  * @date:2023/03/29 14:50:10
-->
<template>
  <div class="detail-box">
    <div class="search">
      <dart-search
        ref="searchForm1"
        class="search"
        :formSpan="24"
        label-position="right"
        :searchOperation="false"
        :model="search"
        :fontWidth="1"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item
            :label="flowType == '1' ? '充值订单申请时间' : '消费订单申请时间'"
            prop=""
          >
            <el-date-picker
              v-model="time"
              type="datetimerange"
              clearable
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item
            label="支付方式:"
            prop="company"
            v-if="flowType == '1'"
          >
            <el-select
              clearable
              v-model="search.businessType"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in accountPayType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            label="第三方订单号:"
            prop="company"
            v-if="flowType == '1'"
          >
            <el-input
              v-model="search.bankOrderNo"
              clearable
              placeholder="请输入三方订单号"
            ></el-input>
          </dart-search-item>
          <dart-search-item
            label="车牌号:"
            prop="company"
            v-if="flowType == '2'"
          >
            <el-input
              v-model="search.carNo"
              clearable
              placeholder="请输入车牌号"
            ></el-input>
          </dart-search-item>
          <dart-search-item
            label="消费类型:"
            prop="company"
            v-if="flowType == '2'"
          >
            <el-select
              clearable
              v-model="search.businessType"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in accountConsumeType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true" :colElementNum="1">
            <div class="g-flex g-flex-end">
              <el-button @click="reset">重置</el-button>
              <el-button type="primary" @click="getAccountPayList"
                >查询</el-button
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <el-table
      v-loading="loading"
      :data="tableData"
      :align="center"
      :header-align="center"
      height="300px"
      border
      style="width: 100%; margin-bottom: 20px"
      row-key="id"
    >
      <el-table-column
        prop="createTime"
        align="center"
        min-width="160"
        label="操作时间"
      >
      </el-table-column>
      <el-table-column
        prop="id"
        align="center"
        :label="flowType == '1' ? '充值订单号' : '消费订单号'"
        min-width="180"
      />
      <el-table-column
        prop="beforeAmount"
        align="center"
        label="车牌号"
        min-width="180"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.otherCarNo">
            {{ scope.row.otherCarNo }}【{{
              getVehicleColor(scope.row.otherCarColor)
            }}】</span
          >
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="beforeAmount"
        align="center"
        :label="flowType == '1' ? '充值前金额' : '消费前金额'"
        min-width="100"
      >
        <template slot-scope="scope">
          ￥{{ scope.row.beforeAmount | moneyFilter }}
        </template>
      </el-table-column>
      <el-table-column
        prop="amount"
        align="center"
        :label="flowType == '1' ? '充值金额' : '消费金额'"
      >
        <template slot-scope="scope">
          ￥{{ scope.row.amount | moneyFilter }}
        </template>
      </el-table-column>
      <el-table-column
        prop="afterAmount"
        align="center"
        :label="flowType == '1' ? '充值后金额' : '消费后金额'"
        min-width="100"
      >
        <template slot-scope="scope">
          ￥{{ scope.row.afterAmount | moneyFilter }}
        </template>
      </el-table-column>
      <el-table-column
        prop="businessName"
        align="center"
        :label="flowType == '1' ? '充值方式' : '消费方式'"
        :show-overflow-tooltip="true"
        min-width="180"
      >
      </el-table-column>
      <el-table-column
        prop="flowType"
        align="center"
        label="消费类型"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ scope.row.flowType == '1' ? '充值' : '消费' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="flowType"
        align="center"
        label="支付类型"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ scope.row.payType == '1' ? '支付' : '退款' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="status"
        align="center"
        label="订单状态"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          {{ getClientAccountPayStatus(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column prop="operatorName" align="center" label="操作员">
      </el-table-column>
      <el-table-column
        prop="branchName"
        align="center"
        min-width="180"
        label="操作网点"
      >
      </el-table-column>

      <el-table-column
        v-if="flowType == '1'"
        prop="bankOrderNo"
        align="center"
        min-width="160"
        label="三方订单号"
      >
      </el-table-column>
    </el-table>
    <div v-if="total > search.pageSize" class="pagination">
      <el-pagination
        background
        :current-page="search.pageNo"
        :page-size="search.pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>
  </div>
</template>
<script>
import {
  getVehicleColor,
  getClientAccountPayStatus,
} from '@/common/method/formatOptions'
import {
  accountPayType,
  accountConsumeType,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
export default {
  components: { dartSearch, dartSearchItem },
  props: {
    accountId: {
      type: String,
      default: '',
    },
    flowType: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      loading: false,
      center: 'center',
      total: '',
      search: {
        accountId: '',
        pageNum: 1,
        pageSize: 10,
        flowType: '1',
        startTime: '',
        endTime: '',
        bankOrderNo: '',
        businessType: '',
        carNo: '',
      },

      tableData: [],
      time: '',
      accountPayType,
      accountConsumeType,
    }
  },
  created() {
    this.search.accountId = this.accountId
    this.search.flowType = this.flowType
    this.getAccountPayList()
  },
  methods: {
    getVehicleColor,
    getClientAccountPayStatus,

    reset() {
      for (let key in this.search) {
        this.search[key] = ''
      }
      this.time = ''
      this.search.accountId = this.accountId
      this.search.flowType = this.flowType
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    getAccountPayList() {
      let params = JSON.parse(JSON.stringify(this.search))
      console.log(this.time, '<<---------time')
      if (this.time) {
        params.startTime = this.time[0]
        params.endTime = this.time[1]
      } else {
        params.startTime = ''
        params.endTime = ''
      }

      this.startLoading()
      this.$request({
        url: this.$interfaces.consumptionList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.tableData = []
          this.endLoading()
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getAccountPayList()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.detail-box {
  padding: 0 15px;
}
</style>