<template>
  <div class="down">
    <div class="thetable">
      <div class="title">客账用户详情</div>
    </div>
    <div class="downnav">
      <el-form class="nat-form nat-form-list">
        <el-row>
          <el-col :span="
               8">
            <el-form-item label="客账编号：">
              <div>{{userinfo.accountId}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户用户类型：">
              <div>{{userinfo.userType == '1' ? '单位' : '个人'}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户余额(元)：">
              <div>{{ userinfo.amount | moneyFilter }}元</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="证件号:">
              <div>{{userinfo.idNo}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人手机号：">
              <div>{{userinfo.mobile}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间：">
              <div>{{userinfo.createTime}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="客账用户名称：">
              <div>{{userinfo.userName}}</div>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  created() {},
  props: {
    userinfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {}
  },
  methods: {},
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  line-height: 28px;
  margin: 0 0 20px;
  padding: 11px 24px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
}
.downnav {
  margin-left: 100px;
}
</style>