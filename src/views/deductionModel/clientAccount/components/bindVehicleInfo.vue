<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:绑定车辆信息
  * @author:zhang<PERSON>
  * @date:2023/03/29 14:50:10
-->
<template>
  <div>

    <div style="padding:10px">
      <el-table ref="multipleTable"
                :data="tableData"
                align="center"
                height="350px"
                header-align="center"
                style="width: 100%;">
        <el-table-column prop="cardNo"
                         align="center"
                         label="ETC卡号"
                         min-width="150" />
        <el-table-column prop="carNo"
                         align="center"
                         label="车牌号"
                         width="170">
          <template slot-scope="scope">
            {{scope.row.carNo}} 【{{getVehicleColor(scope.row.carColor)}}】
          </template>
        </el-table-column>
        <el-table-column prop="carType"
                         align="center"
                         label="车辆类型"
                         width="120">
          <template slot-scope="scope">
            {{getCarType(scope.row.carType)}}
          </template>
        </el-table-column>

        <el-table-column prop="cardType_str"
                         align="center"
                         label="广西卡类型"
                         min-width="150">
        </el-table-column>
        <el-table-column prop="cardStatus_str"
                         align="center"
                         label="ETC卡状态"
                         min-width="120" />
        <el-table-column prop="isBind"
                         align="center"
                         label="绑定状态">
          <template slot-scope="scope">
            {{scope.row.isBind?'已绑定':'未绑定'}}
          </template>
        </el-table-column>
        <el-table-column prop="obuNo"
                         align="center"
                         label="OBU号"
                         min-width="120" />
        <el-table-column prop=""
                         align="center"
                         label="操作">
          <template slot-scope="scope">
            <el-button type="text"
                       @click="searchTollList(scope.row)">查看消费记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page="formData.pageNum"
                     :page-size="formData.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <tollRecord :visible.sync="tollListVisible"
                :accountInfo="accountInfo"
                :vehicleInfo="vehicleInfo"></tollRecord>

  </div>
</template>

<script>
import {
  getVehicleColor,
  getallGxCardType,
  getCarType,
} from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import tollRecord from './tollRecord'
export default {
  props: {
    accountInfo: {
      type: Object,
      default: {},
    },
  },
  components: { tollRecord },
  data() {
    return {
      tableData: [],
      formData: {
        pageNum: 1,
        pageSize: 10,
        customerId: '',
      },
      total: 0,
      tollListVisible: false,
      vehicleInfo: {},
    }
  },

  watch: {},
  created() {
    this.formData.customerId = this.accountInfo.customerId
    this.guestAccountVehicle()
  },

  methods: {
    getVehicleColor,
    getallGxCardType,
    getCarType,

    handleSizeChange(val) {
      this.formData.pageSize = val
      this.guestAccountVehicle()
    },
    handleCurrentChange(val) {
      this.formData.pageNum = val
      this.guestAccountVehicle()
    },

    guestAccountVehicle() {
      this.$request({
        url: this.$interfaces.getVehicleAccountList,
        method: 'post',
        data: this.formData,
      })
        .then((res) => {
          if (res.code == 200) {
            this.total = res.data.total
            let records = res.data.records
            this.tableData = records
          }
        })
        .catch((e) => {})
    },

    resetTable() {
      this.formData.pageNum = 1
      this.formData.pageSize = 10
      this.guestAccountVehicle()
    },

    searchTollList(val) {
      this.tollListVisible = true
      this.vehicleInfo = val
    },
  },
}
</script>

<style lang="scss" scoped>
.foot {
  margin-top: 20px;
  text-align: center;
}
.bindTips {
  margin-top: 10px;
  .bindTips-item {
    margin: 4px;
  }
}
</style>