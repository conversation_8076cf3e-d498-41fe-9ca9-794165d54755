<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:预存线调整
  * @author:dwz
  * @date:2023/11/3 9:24:25
-->
<template>
  <div class="detail-wrap">
    <div class="orderItem">
      <div class="title">账户信息</div>
      <el-form label-width="120px" size="small">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="账户名称:">
              {{ accountInfo.userName }}
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="客户证件类型:">
              <span v-if="accountInfo.userType == '0'">
                {{ typeAdapter(accountInfo.idType, 'getPersonalOCRType') }}
              </span>
              <span v-else>
                {{ typeAdapter(accountInfo.idType, 'getenterpriseOCRType') }}
              </span>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="手机号:">
              {{ accountInfo.mobile }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间:">
              {{ accountInfo.createTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="日日通客账状态:">
              {{ accountInfo.accountStatus_str }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客账绑定车辆数:">
              <!-- {{ accountInfo.dept }} -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户类型:">
              {{ accountInfo.userType == '1' ? '单位' : '个人' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="客账可用余额:">
              {{ accountInfo.availableAmount | moneyFilter }}元
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客账最低保证金:">
              {{ accountInfo.blackAmount | moneyFilter }}元
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客账提醒线:">
              {{ accountInfo.warnLineAmount | moneyFilter }}元
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="联系地址:">
              {{ accountInfo.adress }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="orderItem">
      <div class="title">设置最低预存线</div>
      <div class="black-wrapper">
        <div>设置客账内选中车辆的</div>
        <!-- <div style="margin-left: 4px; color: red">*</div> -->
        <el-form
          :model="formData"
          ref="formData"
          class="nat-form nat-form-list"
          :rules="rules"
          label-width="140px"
        >
          <el-form-item label="最低预存线(元)" prop="blackAmount">
            <el-input
              v-model="formData.blackAmount"
              placeholder="请输入最低预存线金额"
            ></el-input>
          </el-form-item>
        </el-form>
        <div>设置成功后，客账最低预存线为：客账内所有车辆最低预存线的总和</div>
      </div>
    </div>
    <div class="orderItem">
      <div class="title">车辆信息</div>
      <blackVehicleInfo
        :accountId="accountInfo.accountId"
        @getVehicleIds="getVehicleIds"
      ></blackVehicleInfo>
    </div>
    <div class="footer">
      <el-button @click="confirm" type="primary">确定修改</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import blackVehicleInfo from './components/blackVehicleInfo'
import { typeAdapter } from '@/common/method/formatOptions.js'
import float from '@/common/method/float.js'
// import {
//   getVehicleClassType,
//   getVehicleColor,
//   getCpuStatus,
// } from '@/common/method/optionsFilter'
export default {
  components: { blackVehicleInfo },
  props: {
    accountInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {},
  data() {
    return {
      activeName: 'recharge',
      formData: {
        blackAmount: '',
      },
      rules: {
        blackAmount: [
          {
            required: true,
            message: '请输入最低预存线金额',
            trigger: 'blur',
          },
          {
            pattern: /(^(([1-9]([0-9]+)?)|(0))(\.[0-9]{2})?$)/,
            message: '请输入正确的金额格式',
          },
        ],
      },
      tableData: [],
      vehicleIds: [], //选中的车辆列表
    }
  },
  methods: {
    typeAdapter,
    getVehicleIds(vehicleIds) {
      console.log('vehicleIds', vehicleIds)
      this.vehicleIds = vehicleIds
    },
    confirm() {
      // let params = JSON.parse(JSON.stringify(this.formData))
      let params = {
        accountId: this.accountInfo.accountId,
        amount: float.mul(this.formData.blackAmount, 100),
        vehicleIds: this.vehicleIds,
      }

      this.$refs['formData'].validate((valid) => {
        if (valid) {
          if (this.vehicleIds.length == 0) {
            this.$message.warning('请先选择需要调整的车辆')
            return
          }
          this.startLoading()
          request({
            url: api.updateAccountBlackLine,
            method: 'post',
            data: params,
          })
            .then((res) => {
              this.endLoading()
              console.log(res, '预存线调整')
              if (res.code == 200) {
                this.$emit('on-success')
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch((err) => {
              this.endLoading()
            })
        }
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-bottom: 20px;
}

.orderItem {
  .black-wrapper {
    display: flex;
    align-items: center;
    padding: 10px;
    font-size: 14px;
    .nat-form {
      margin: 0 10px;
    }
  }
}
.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>


<style lang='scss' scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}

::v-deep.black-wrapper {
  .el-form-item {
    margin-bottom: 0px;
    .el-form-item__content {
      margin-left: 120px !important;
    }
    .el-form-item__label {
      width: 120px !important;
      text-align: left;
      padding-right: 0 !important;
    }
  }
}
.title {
  font-weight: 550;
  padding: 10px 20px;
  border-bottom: 1px solid #f5f7fa;
}
.footer {
  margin: 30px 0;
  text-align: center;
}
</style>