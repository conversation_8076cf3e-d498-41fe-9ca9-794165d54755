<template>
  <div class="account-list">
    <div class="search">
      <dart-search
        ref="searchForm1"
        :formSpan="24"
        label-position="right"
        :searchOperation="false"
        :model="search"
        :fontWidth="1"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="用户名称：" prop="userName">
            <el-input
              v-model="search.userName"
              placeholder="请输入用户名"
            ></el-input>
          </dart-search-item>
          <dart-search-item label="手机号：" prop="mobile">
            <el-input
              v-model="search.mobile"
              placeholder="请输入手机号"
            ></el-input>
          </dart-search-item>
          <dart-search-item label="车牌号：" prop="mobile">
            <el-input
              v-model="search.carNo"
              placeholder="请输入车牌号"
            ></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                >查询</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table-box">
      <el-table
        :data="tableData"
        align="center"
        height="100%"
        header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column prop="isOpen" align="center" label="开通状态">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.isOpen == '0'">
              <span>{{ scope.row.isOpen_str }}</span>
            </el-tag>
            <el-tag type="info" v-else>
              <span>{{ scope.row.isOpen_str }}</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="userName"
          align="center"
          label="用户名称"
          min-width="180"
        />
        <el-table-column
          prop="idNo"
          align="center"
          label="证件号"
          min-width="170"
        />
        <el-table-column
          prop="mobile"
          align="center"
          label="手机号"
          min-width="120"
        />
        <el-table-column
          prop="accountStatus_str"
          align="center"
          label="客账状态"
        >
          <template slot-scope="scope">
            {{
              scope.row.accountStatus_str
                ? scope.row.accountStatus_str
                : '未开通'
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="amount"
          align="center"
          min-width="120"
          label="客账最低预存线"
        >
          <template slot-scope="scope">
            ￥{{ scope.row.blackAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="amount"
          align="center"
          min-width="120"
          label="账户可用余额"
        >
          <template slot-scope="scope">
            ￥{{ scope.row.availableAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          label="创建时间"
          min-width="150"
        />
        <el-table-column prop="createBy" align="center" label="操作人" />
        <el-table-column
          prop="createBranch"
          align="center"
          label="操作网点"
          min-width="140"
        />
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          min-width="230"
        >
          <template slot-scope="scope">
            <!-- <el-button v-if="scope.row.isOpen"
                       type="text"
                       size="mini"
                       @click="bindVehicleHandle(scope.row)">绑定车辆</el-button> -->
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.isOpen == '0'"
              @click="detail(scope.row)"
              >详情</el-button
            >
            <el-button
              v-if="scope.row.isOpen == '0' || scope.row.isOpen == '9'"
              type="text"
              size="mini"
              @click="getVehicleList(scope.row)"
              >绑定车辆</el-button
            >
            <!-- <el-button v-if="scope.row.isOpen=='0'"
                       size="mini"
                       type="text"
                       @click="recordDetail(scope.row)">记录详情</el-button> -->

            <el-button
              v-if="scope.row.isOpen != '0' && scope.row.isOpen != '9'"
              type="text"
              size="mini"
              @click="guestAccount(scope.row)"
              >开通客账</el-button
            >
            <el-popover trigger="hover" :append-to-body="false" placement style="margin-left: 10px">
              <!-- <el-button v-if="scope.row.isOpen=='0'||scope.row.isOpen=='9'"
                         type="text"
                         size="mini"
                         @click="showBindVehicle(scope.row)">查看车辆</el-button> -->
              <el-button
                v-if="scope.row.isOpen == '0' && scope.row.amount > 0"
                size="mini"
                type="text"
                @click="refund(scope.row)"
                >客账退款</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="prestoreHandle(scope.row)"
                v-permisaction="['clientAccount:updateBlackLine']"
                >调整最低预存线</el-button
              >
              <el-button
                size="mini"
                type="text"
                @click="blackLineHandle(scope.row)"
                v-permisaction="['clientAccount:updateLine']"
                >调整充值提醒线</el-button
              >
              <el-button type="text" size="mini" slot="reference"
                >更多操作 <i class="el-icon-arrow-down"></i
              ></el-button>
            </el-popover>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        :page-sizes="[10, 20, 50]"
        layout="total,sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 记录详情页面 -->
    <!-- <dart-slide :visible.sync="drawer"
                title="记录详情"
                width='80%'
                @close='drawer=false'>
      <recordDetail v-if="drawer"
                    :accountId='accountId'></recordDetail>
    </dart-slide> -->
    <bindVehicle
      :visible.sync="dialogBindVisible"
      :itemData="itemData"
    ></bindVehicle>

    <!-- 
        2022.11.8变更流程
        开通客账->确认信息->开通完成->签约->签约完成->自动绑定车辆
     -->
    <signature
      :visible.sync="dialogSignVisible"
      :itemData="itemData"
      :signatureData="signatureData"
      @on-success="confimBind"
    ></signature>

    <!-- detailVisible -->
    <dart-slide
      :visible.sync="detailVisible"
      title="记录详情"
      width="90%"
      v-transfer-dom
      :maskClosable="true"
      @close="detailVisible = false"
    >
      <detail v-if="detailVisible" :accountInfo="accountInfo"></detail>
    </dart-slide>
    <!-- 调整最低预存线 -->
    <dart-slide
      :visible.sync="preDetailVisible"
      title="客账最低预存线调整"
      width="90%"
      v-transfer-dom
      :maskClosable="true"
      @close="preDetailVisible = false"
    >
      <prestoreDetail
        v-if="preDetailVisible"
        :accountInfo="accountInfo"
        @on-success="confirmPrestore"
      ></prestoreDetail>
    </dart-slide>
    <!-- 调整充值提醒线 -->
    <black-line
      :visible.sync="dialogBlackVisible"
      :itemData="itemData"
      @on-success="confirmBlack"
    ></black-line>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import bindVehicle from './bindVehicle'
import { _ignoreNull, _ignoreEmpty } from '@/utils/utils'
import dartSlide from '@/components/dart/Slide/index.vue'
// import recordDetail from './recordDetail.vue'
import signature from './components/signature.vue'
import detail from './detail'
import prestoreDetail from './prestoreDetail'
import blackLine from './components/blackLine.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    bindVehicle,
    dartSlide,
    // recordDetail,
    signature,
    detail,
    blackLine,
    prestoreDetail,
  },
  created() {
    this.guestAccountList()
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      dialogVisible: false,
      dialogBindVisible: false,
      dialogBlackVisible: false,
      search: {
        userName: '',
        mobile: '',
        carNo: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,

      itemData: {},
      tableData: [],
      lineData: {},
      drawer: false,
      accountId: '',
      vehicleTableData: [], //用户绑定车辆列表
      dialogSignVisible: false,
      signatureData: {
        //签约接口入参
        customerId: '',
        vehicles: [],
        conType: '5',
        specType: '1',
      },
      detailVisible: false,
      preDetailVisible: false,
      accountInfo: {},
    }
  },
  methods: {
    detail(val) {
      this.accountInfo = val
      this.detailVisible = true
    },
    prestoreHandle(val) {
      this.accountInfo = val
      this.preDetailVisible = true
    },
    handleSizeChange(val) {
      this.search.pageSize = val
      this.guestAccountList()
    },
    handleCurrentChange(val) {
      this.search.pageNum = val
      this.guestAccountList()
    },
    //获取用户列表
    guestAccountList(flag) {
      this.loading = true
      let params = _ignoreEmpty(JSON.parse(JSON.stringify(this.search)))
      params.pageNum = this.search.pageNum
      params.pageSize = this.search.pageSize
      this.$request({
        url: this.$interfaces.guestAccountList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.search.pageNum = res.data.current
            this.search.pageSize = res.data.size
            if (flag) {
              this.$msgbox({
                title: '提示',
                showClose: true,
                type: 'success',
                customClass: 'my_msgBox singelBtn',
                dangerouslyUseHTMLString: true,
                message: '车辆绑定成功',
              })
            }
          }
        })
        .catch((error) => {
          this.loading = false
        })
    },

    //查看用户绑定车辆
    showBindVehicle(val) {
      this.itemData = val
      this.dialogBindVisible = true
    },
    // 查询用户绑定车辆列表
    getVehicleList(val) {
      this.itemData = val
      let params = {
        pageNum: 1,
        pageSize: 200,
        customerId: val.customerId,
      }
      this.$request({
        url: this.$interfaces.getVehicleAccountList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            let records = res.data.records
            this.vehicleTableData = records
            this.signatureData.vehicles = []
            this.signatureData.vehicles = this.vehicleTableData.map((item) => {
              return {
                vehicleCode: item.carNo,
                vehicleColor: item.carColor,
              }
            })
            this.confimBindHandle(val)
          }
        })
        .catch((e) => {})
    },
    // 2022.11.8 绑定前校验签约
    confimBindHandle(val) {
      this.signatureData.customerId = val.customerId
      let params = {
        customerId: val.customerId,
      }
      request({
        url: api.checkContracts,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, 'sss')

          if (res.code == 200) {
            if (res.data.isCompleted == '0') {
              let _this = this
              const h = _this.$createElement
              _this.$msgbox({
                title: '提示',
                message: h('div', null, [
                  h(
                    'p',
                    {
                      style:
                        'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
                    },
                    '查询出该用户还未签署相关协议，请点击"签署"去签署'
                  ),
                ]),
                showCancelButton: true,
                confirmButtonText: '签署',
                cancelButtonText: '取消',
                showClose: false,
                callback(action) {
                  if (action == 'confirm') {
                    _this.dialogSignVisible = true
                  }
                },
              })
            } else {
              this.confimBind()
            }
          } else {
            // this.$message({
            //   type: 'error',
            //   message: res.msg,
            // })
          }
        })
        .catch((err) => {})
    },
    //批量绑定
    confimBind() {
      this.$request({
        url: this.$interfaces.batchConvertCust,
        method: 'post',
        data: {
          customerId: this.itemData.customerId,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            let flag = true
            this.guestAccountList(flag)
          } else {
          }
        })
        .catch((err) => {})
    },
    confirmBlack() {
      this.$message.success('调整提醒线成功')
      this.dialogBlackVisible = false
      this.onSearchHandle()
    },
    confirmPrestore(){
      this.$message.success('调整预存线成功')
      this.preDetailVisible = false
      this.onSearchHandle()
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.guestAccountList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10
    },

    close() {
      this.$emit('update:visible', false)
    },
    //开通客账
    ConfirmHandle(item) {
      let data = {
        customerId: item.customerId,
      }
      this.$request({
        url: this.$interfaces.openGuestAccount,
        method: 'post',
        data,
      })
        .then((res) => {
          console.log(res, '开通客账')
          if (res.code == 200) {
            this.$message({
              message: '客账扣款模式开通成功!',
              type: 'success',
            })
            if (res.data.isBindVehicle) {
              this.getVehicleList(item)
            }
            this.guestAccountList()
          }
        })
        .catch((err) => {})
    },
    //开通客账确认弹框
    guestAccount(item) {
      let _this = this
      const h = _this.$createElement
      this.lineData = item
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style:
                'color: #F56C6C;font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '确认开通客账扣款模式用户信息'
          ),
          h('p', null, '用户名：' + item.userName),
          h('p', null, '手机号：' + item.mobile),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.ConfirmHandle(item)
          }
        },
      })
    },
    //绑定车辆
    bindVehicleHandle(item) {
      this.dialogBindVisible = true
      this.itemData = item
    },
    //调整充值提醒线
    blackLineHandle(item) {
      this.dialogBlackVisible = true
      this.itemData = item
    },
    //记录详情
    recordDetail(item) {
      this.drawer = true
      this.accountId = item.accountId
    },

    //客账退费
    refund(item) {
      //跳转退费申请页面
      this.$router.push({
        path: './refund',
        query: {
          clientAccountInfo: item,
        },
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
.dialogClass {
  height: 30px;
  font-size: 18px;
}


.account-list ::v-deep .el-table__body-wrapper {
  // overflow: visible !important;
  padding-bottom: 40px;
}

.account-list ::v-deep .el-table__fixed-body-wrapper {
  // overflow: visible !important;
  padding-bottom: 40px;
}
</style>
