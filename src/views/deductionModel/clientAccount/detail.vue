<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:客账详情
  * @author:zhang<PERSON>
  * @date:2023/03/29 10:32:25
-->
<template>
  <div class="detail-wrap">
    <div class="orderItem">
      <div class="title">日日通客账详情</div>
      <el-form label-width="120px" size="small">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="客户名称:">
              {{ accountInfo.userName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户证件类型:">
              <span v-if="accountInfo.userType == '0'">
                {{ typeAdapter(accountInfo.idType, 'getPersonalOCRType') }}
              </span>
              <span v-else>
                {{ typeAdapter(accountInfo.idType, 'getenterpriseOCRType') }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户证件号码:">
              {{ accountInfo.idNo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="客户类型:">
              {{ accountInfo.userType == '1' ? '单位用户' : '个人用户' }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="部门类型:">
              {{ accountInfo.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户联系地址:">
              {{ accountInfo.adress }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="客户联系人:">
              {{ accountInfo.linkName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="移动电话:">
              {{ accountInfo.mobile }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电子邮箱:">
              {{ accountInfo.email }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="开户操作人:">
              {{ accountInfo.createBy }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户网点:">
              {{ accountInfo.createBranch }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户时间:">
              {{ accountInfo.createTime }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24" v-if="accountInfo.userType == '1'">
          <el-col :span="8">
            <el-form-item label="对公账户名称:">
              {{ b2bData.accountName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="对公账户账号:">
              {{ accountInfo.subAccountNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="单位开户行:">
              {{ accountInfo.openBank }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="客账最低预存线:">
              ￥{{ accountInfo.blackAmount | moneyFilter }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客账可用余额:">
              ￥{{ accountInfo.availableAmount | moneyFilter }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="orderItem">
      <div class="title">账户交易详情</div>
      <div style="padding: 10px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="充值记录" name="recharge">
            <recordDetail
              :accountId="accountInfo.accountId"
              flowType="1"
              v-if="activeName == 'recharge'"
            ></recordDetail>
          </el-tab-pane>

          <el-tab-pane label="消费记录" name="sale">
            <recordDetail
              :accountId="accountInfo.accountId"
              flowType="2"
              v-if="activeName == 'sale'"
            ></recordDetail>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div class="orderItem">
      <div class="title">绑定车辆信息</div>
      <bindVehicleInfo :accountInfo="accountInfo"></bindVehicleInfo>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import recordDetail from './components/recordDetail'
import bindVehicleInfo from './components/bindVehicleInfo'
import { typeAdapter } from '@/common/method/formatOptions.js'
export default {
  components: { recordDetail, bindVehicleInfo },
  props: {
    accountInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {},
  data() {
    return {
      activeName: 'recharge',
      b2bData: {},
    }
  },
  created() {
    this.b2bView()
  },
  methods: {
    typeAdapter,
    b2bView() {
      if (this.accountInfo.userType == '0') return
      let params = {
        custMastId: this.accountInfo.customerId,
      }
      this.$request({
        url: this.$interfaces.b2bView,
        method: 'post',
        data: params,
      }).then((res) => {
        console.log(res)
        if (res.code == 200) {
          this.b2bData = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-bottom: 20px;
}

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>


<style lang='scss' scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}
.title {
  font-weight: 550;
  padding: 10px 20px;
  border-bottom: 1px solid #f5f7fa;
}
</style>