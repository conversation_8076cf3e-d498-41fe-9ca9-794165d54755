/**
 * 交易类型
 */
export const vcTradeTypesMap = {
  1: '高速',
  2: '停车',
  3: '加油站',
  4: '服务区',
  5: '路政'
}

/**
 * 车型组合
 */
export const vcVehicleTypes = {
  1: '一型客车',
  2: '二型客车',
  3: '三型客车',
  4: '四型客车',
  11: '一型货车',
  12: '二型货车',
  13: '三型货车',
  14: '四型货车',
  15: '五型货车',
  16: '六型货车',
  21: '一型专项作业车',
  22: '二型专项作业车',
  23: '三型专项作业车',
  24: '四型专项作业车',
  25: '五型专项作业车',
  26: '六型专项作业车',
}

/**
 * 字符串数字 转换为中文函数 例如 1，2，3 =>  中文，英文，杨文
 * @param {*} dataString // 字符串集合
 * @param {*} mapping  // map集合
 * @returns 
 */

export const convertDataToNames = (dataString, mapping) => {
  const dataList = dataString.split(',').map(Number);
  return dataList.map(item => mapping[item]).join(', ');
}