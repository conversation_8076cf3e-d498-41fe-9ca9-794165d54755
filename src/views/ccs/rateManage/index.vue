<template>
  <div class="user">
    <div class="tab-item">
      <el-tabs @tab-click="handleClick" type="border-card">
        <el-tab-pane
          :label="item.label"
          v-for="(item, index) in typeOptions"
          :key="index"
        ></el-tab-pane>
        <div>
          <serviceRate v-if="selectItem == '1'"></serviceRate>
          <channelRate v-if="selectItem == '2'"></channelRate>
          <overdueRate v-if="selectItem == '3'"></overdueRate>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import serviceRate from './serviceRate'
import channelRate from './channelRate'
import overdueRate from './overdueRate'

export default {
  components: { serviceRate, channelRate, overdueRate },

  data() {
    return {
      typeOptions: [
        { label: '服务费', value: '1' },
        { label: '通道费', value: '2' },
        { label: '逾期服务费', value: '3' }
      ],
      selectItem: '1'
    }
  },
  methods: {
    handleClick(val) {
      this.selectItem = this.typeOptions[val.index].value
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
  ::v-deep .table {
    .status-txt {
      font-weight: 500;
      &.green {
        color: #67c23a;
      }
      &.gray {
        color: #909399;
      }
    }
  }
}
.tab-item {
  .el-tabs--border-card {
    box-shadow: none !important;
  }
  .el-tabs__content {
    padding: 15px !important;
  }
  .el-tabs--border-card {
    border-bottom: none !important;
  }
}
</style>