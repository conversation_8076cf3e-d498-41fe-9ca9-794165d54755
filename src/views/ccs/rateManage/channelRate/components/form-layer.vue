<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="新增通道费率"
    width="45%"
    center
  >
    <el-form
      ref="form"
      :model="formData"
      label-width="120px"
      v-if="dialogVisible"
      :rules="rules"
    >
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="请款开始日期：" prop="nStartDate">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="formData.nStartDate"
              value-format="yyyyMMdd"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="请款结束日期：" prop="nEndDate">
            <el-date-picker
              type="date"
              placeholder="选择日期"
              v-model="formData.nEndDate"
              value-format="yyyyMMdd"
              style="width: 100%;"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="费率：" prop="dRate">
        <el-input
          v-model="formData.dRate"
          placeholder="请输入小数，例如费率是6‰，填写0.006"
        ></el-input>
        <div class="tip-item">
          <i style="color: red;">*</i>
          请输入小数，例如费率是6‰，填写0.006。
        </div>
      </el-form-item>
      <el-form-item label="合作方：" prop="vcBankId">
        <el-select
          v-model="formData.vcBankId"
          placeholder="请选择"
          clearable
          style="width: 100%;"
          @change="vcBankIdChange"
        >
          <el-option
            v-for="item in vcBankIdArr"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item
        label="商户号："
        v-if="
          formData.vcBankId == 'S_WECHAT' || formData.vcBankId == 'C_WECHAT'
        "
        prop="vcMchId"
      >
        <el-select
          v-model="formData.vcMchId"
          placeholder="请选择"
          clearable
          style="width: 100%;"
        >
          <el-option
            v-for="item in vcMchIdOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="tip">
      <div class="tip-title">注意项：</div>
      <div class="tip-item">
        <i>*</i>
        至少提前一天配置，当日配置隔日生效。否则影响费率使用。
      </div>
      <div class="tip-item">
        <i>*</i>
        调整原有费率，须先失效旧费率再新增，同时新费率须包含旧费率。
      </div>
      <div class="tip-item"><i>*</i> 当日配置费率，须当日完成，不可跨日。</div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { convertFormat } from '@/utils'
import { vcMchIdOptions } from '@/common/const/optionsData'

var validateNumer = (rule, value, callback) => {
  var reg = /^\d+(?=\.{0,1}\d+$|$)/
  if (!reg.test(value)) {
    callback(new Error('请输入数字'))
  } else {
    callback()
  }
}
export default {
  data() {
    var validateDate = (rule, value, callback) => {
      console.log(value)
      if (this.formData.nStartDate > this.formData.nEndDate) {
        console.log
        callback(new Error('请输入正确的签约日期'))
      } else {
        callback()
      }
    }
        let validVcMhId = (rule, value, callback) => {
      let { vcBankId } = this.formData
      if ((vcBankId == 'S_WECHAT' || vcBankId == 'C_WECHAT') && !value) {
        console.log(vcBankId, value)
        callback(new Error('请选择商户号'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      formData: {},
      rules: {
        nStartDate: [
          { required: true, message: '请款开始时间不能为空', trigger: 'blur' },
          { validator: validateDate, trigger: 'blur' }
        ],
        nEndDate: [
          { required: true, message: '请款结束时间不能为空', trigger: 'blur' },
          { validator: validateDate, trigger: 'blur' }
        ],
        dRate: [
          { required: true, message: '费率不能为空', trigger: 'blur' },
          { validator: validateNumer, trigger: 'blur' }
        ],
        vcBankId: [
          { required: true, message: '合作方不能为空', trigger: 'change' }
        ],
        vcMchId: [
          { required: true, message: '商户号不能为空', trigger: 'change' },
          { validator: validVcMhId, trigger: 'blur' }
          ]

      },
      vcBankIdArr: [
        {
          label: '银联',
          value: 'CUP'
        },
        {
          label: '微信车主平台',
          value: 'S_WECHAT'
        },
        {
          label: '微信指定卡',
          value: 'C_WECHAT'
        }
      ],
      vcMchIdOptions:convertFormat(vcMchIdOptions),
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          this.$emit('submit', this.formData)
          // this.dialogVisible = false
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    reset() {
      this.formData = {}
    },
    vcBankIdChange(val){
      if(val == 'CUP' && this.formData.vcMchId){
        this.formData.vcMchId = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tip {
  .tip-title {
    font-weight: 500;
  }
  .tip-item {
    margin-top: 8px;
    margin-left: 15px;
    i {
      color: red;
    }
  }
}
</style>
