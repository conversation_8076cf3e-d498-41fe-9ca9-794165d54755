<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button type="primary" slot="btn" size="mini" @click="add">新增</el-button>
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            size="mini"
            type="danger"
            v-if="scope.cFlag == 1"
            @click="handelRow(scope, 'del')"
          >失效</el-button>
        </template>
      </my-table>
    </div>
    <!-- 表单弹框 -->
    <formLayer ref="formLayer" @submit="addSubmit"></formLayer>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import {
  passageRateQuery,
  passageRateApply,
  passageRateDelete
} from '@/api/ccsRate'
import formLayer from './components/form-layer.vue'

export default {
  components: {
    MyTable,
    SearchForm,
    formLayer
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: passageRateQuery,
      pageSizeKey: 'pageSize',
      pageNumKey: 'page',
      selectedImage: '',
      dialogVisible: false
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 打开新增弹框
    add() {
      this.$refs.formLayer.reset()
      this.$refs.formLayer.dialogVisible = true
    },
    // 新增
    async addSubmit(params) {
      let res = await passageRateApply(params)
      if (res.code == 200) {
        this.$message.success('添加成功')
        this.$refs.formLayer.dialogVisible = false
        this.getTableData()
      }
    },
    // 操作
    handelRow(row, type) {
      if (type == 'del') {
        let params = {
          id: row.id
        }
        this.deleteCode(params)
      }
    },
    // 删除接口
    deleteCode(params) {
      this.$confirm('请确认是否将该费率失效', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await passageRateDelete(params)
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  ::v-deep .fontWidth {
    display: flex;
    label{
      width: auto!important;
    }
    .el-col-24{
      padding: 5px!important;
    }
    div {
      width: auto;
    }
    .el-form-item{
      margin-bottom: 0;
    }
  }
}
</style>