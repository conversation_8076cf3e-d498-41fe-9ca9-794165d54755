

import { vcMchIdOptions } from '@/common/const/optionsData'
//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'vcBankId',
      label: '合作方',
      formatter: (row) => {
        return row == 'CUP' ? '银联' : row == 'S_WECHAT'? '微信车主平台' : '微信指定卡'
      }
    },
    {
      prop: 'vcMchId',
      width: 120,
      label: '商户号',
      formatter: (row) => {
        return vcMchIdOptions[row]
      }
    },
    {
      prop: 'dRate',
      label: '费率',
    },
    {
      prop: 'nStartDateStr',
      label: '请款开始时间',
    },
    {
      prop: 'nEndDateStr',
      label: '请款结束时间',
    },
    {
      prop: 'cFlag',
      width: 100,
      label: '生效状态',
      // formatter: (row) => {
      //   return row == 1 ? '有效' : '失效'
      // }
      filterHtml: (val) => {
        return val == 1 ? '<span class="status-txt green">有效</span>' : `<span class="status-txt gray">失效</span>`
      }
    },
    {
      prop: 'dtUpdateTime',
      label: '操作时间',
      formatter: (val,row) => {
        return row.dtUpdateTime || row.dtInsertTime
      }
    },
    {
      prop: 'vcInsertOperator',
      label: '操作人',
      formatter: (val,row) => {
        return row.vcUpdateOperator || row.vcInsertOperator
      }
    },
    {
      prop: 'action',
      width: 120,
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'datePicker',
      field: 'nStartDate',
      label: '请款开始日期',
      placeholder: '请款开始日期',
      valueFormat: 'yyyyMMdd',
      default: ''
    },
    {
      type: 'datePicker',
      field: 'nEndDate',
      label: '请款结束日期',
      placeholder: '请款结束日期',
      valueFormat: 'yyyyMMdd',
      default: ''
    },
    {
      type: 'select',
      field: 'vcBankId',
      label: '合作方',
      placeholder: '合作方',
      default: '',
      options: [
        {
          label: '银联',
          value: 'CUP'
        }, 
        {
          label: '微信车主平台',
          value: 'S_WECHAT'
        },
        {
          label: '微信指定卡',
          value: 'C_WECHAT'
        },
      ]
    }
  ]
}
