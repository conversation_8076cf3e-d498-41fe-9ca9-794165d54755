
import { convertDataToNames, vcTradeTypesMap,vcVehicleTypes } from '../utils'
import { vcMchIdOptions } from '@/common/const/optionsData'

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'vcBankId',
      width: 140,
      label: '合作方',
      formatter: (row) => {
        return row == 'CUP' ? '银联' : row == 'S_WECHAT'? '微信车主平台' : '微信指定卡'
      }
    },
    {
      prop: 'vcMchId',
      width: 120,
      label: '商户号',
      formatter: (row) => {
         return vcMchIdOptions[row]
      }
    },
    {
      prop: 'vcTradeTypes',
      label: '交易类型',
      width: 200,
      formatter: (row) => {
        return convertDataToNames(row, vcTradeTypesMap)
      }
    },
    {
      prop: 'vcCarUserTypes',
      label: '用户类型',
      width: 120,
      formatter: (val) => {
        return val?convertDataToNames(val, { 0: '个人', 1: '企业' }):''
      }
    },
    {
      prop: 'personCar',
      label: '客车',
      width: 200,
      wordWrap: true,
      formatter: (val, row) => {
        let str = filterNumbersInRange(row.vcVehicleTypes, 0, 10)
        return convertDataToNames(str, vcVehicleTypes)
      }
    },
    {
      prop: 'goodsCar',
      label: '货车',
      width: 200,
      wordWrap: true,
      formatter: (val, row) => {
        let str = filterNumbersInRange(row.vcVehicleTypes, 10, 20)
        return convertDataToNames(str, vcVehicleTypes)
      }
    },
    {
      prop: 'specialCar',
      label: '专项车',
      width: 200,
      wordWrap: true,
      formatter: (val, row) => {
        let str = filterNumbersInRange(row.vcVehicleTypes, 20, 30)
        return convertDataToNames(str, vcVehicleTypes)
      }
    },
    {
      prop: 'dRate',
      label: '费率',
    },
    {
      prop: 'nStartDateStr',
      width: 140,
      label: '请款开始时间',
    },
    {
      prop: 'nEndDateStr',
      width: 140,
      label: '请款结束时间',
    },
    {
      prop: 'cFlag',
      width: 100,
      label: '生效状态',
      // formatter: (row) => {
      //   return row == 0 ? '失效' : '有效'
      // }
      filterHtml: (val) => {
        return val == 0 ? '<span class="status-txt gray">失效</span>' : `<span class="status-txt green">有效</span> `
      }
    },
    {
      prop: 'dtUpdateTime',
      label: '操作时间',
      width: 160,
      formatter: (val,row) => {
        return row.dtUpdateTime || row.dtInsertTime
      }
    },
    {
      prop: 'vcInsertOperator',
      label: '操作人',
      formatter: (val,row) => {
        return row.vcUpdateOperator || row.vcInsertOperator
      }
    },
    {
      prop: 'action',
      width: 120,
      fixed: 'right',
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'datePicker',
      field: 'nStartDate',
      label: '请款开始日期',
      placeholder: '请款开始日期',
      valueFormat: 'yyyyMMdd',
      default: ''
    },
    {
      type: 'datePicker',
      field: 'nEndDate',
      label: '请款结束日期',
      placeholder: '请款结束日期',
      valueFormat: 'yyyyMMdd',
      default: ''
    },
    {
      type: 'select',
      field: 'vcBankId',
      label: '合作方',
      placeholder: '合作方',
      default: '',
      options: [
        {
          label: '银联',
          value: 'CUP'
        }, 
        {
          label: '微信车主平台',
          value: 'S_WECHAT'
        },
        {
          label: '微信指定卡',
          value: 'C_WECHAT'
        },
      ]
    },
  ]
}

// 转换函数
function filterNumbersInRange (dataString, min, max) {
  if(!dataString){
    return ''
  }
  const dataList = dataString.split(',')
    .map(Number)
    .filter(item => item >= min && item <= max);
  return dataList.join(',');
}