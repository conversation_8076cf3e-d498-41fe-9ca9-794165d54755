<template>
  <div class="user">
		<dart-search ref="searchForm1"
								label-position="right"
								:model="search"
								:rules="rules">
			<template slot="search-form">
				<dart-search-item label="统计日期" prop="value1">
					<el-date-picker
						v-model="search.value1"
						type="daterange"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						:picker-options="pickerOptions"
					>
					</el-date-picker>
				</dart-search-item>
				<dart-search-item label="渠道方" prop="vcchannelid">
					<el-select v-model="search.vcchannelid"
										placeholder="请选择"
										collapse-tags>
						<el-option v-for="item in optiondata"
											:key="item.channel_id"
											:label="item.channel_name"
											:value="item.channel_id" />
					</el-select>
				</dart-search-item>
				<dart-search-item :is-button="true"
													:span="8">
					<el-button type="primary"
										size="mini"
										native-type="submit"
										@click="onSearchHandle">搜索</el-button>
					<el-button size="mini"
										@click="onResultHandle">重置</el-button>
				</dart-search-item>
			</template>
		</dart-search>
		<div class="table" :style="`height:${tableHeight}px`">
			<img src="@/image/bg-left.png" />
		</div>
	</div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
export default {
	components:{
		dartSearch,
		dartSearchItem
	},
	data() {
		return{
			search:{
        value1:[],
			  vcchannelid:''
			},
			optiondata: [
				
			],
			tableHeight:0,
			rules:{
				value1: [
				  { required: true, message: '请选择统计日期', trigger: 'change' }
				],
				vcchannelid: [
					{ required: true, message: '请选择渠道方', trigger: 'change' }
				]
			},
			pickerOptions: {
				// 设置时间选择器的禁用时间
				disabledDate(time) {
					const b = time.getTime() > Date.now(); // 限制不能超过今天
					return b;
				},
			}
		}
	},
	created(){
		this.getchannels()
	},
	mounted() {
    this.$nextTick(() => {
      this.fetTableHeight();
    });
  },
	methods:{
		fetTableHeight() {
      //获取屏幕高度
      let screenHeight = document.body.clientHeight;
      let tableHeight = screenHeight - 245;
      this.tableHeight = tableHeight;
      console.log(screenHeight);
    },	
		onSearchHandle(){
			this.$refs["searchForm1"].$children[0].validate((valid) => {
				if (valid) {
					this.clearStatement()
				} else {
					
					return false;
				}
			});
		},
		onResultHandle(){
      this.$refs["searchForm1"].$children[0].resetFields();
		},
		gettime(data) {
      const value =
        data.getFullYear().toString() +
        this.checkTime(data.getMonth() + 1).toString() +
        this.checkTime(data.getDate()).toString();
      return value;
    },
		checkTime(i) {
      if (i < 10) {
        i = "0" + i;
      }
      return i;
    },
		getchannels(){
      this.$store
        .dispatch('report/channels')
        .then(res => {
					this.optiondata = res
          console.log(res)
				})
				.catch(() => { })
		},
		clearStatement() {
			let params = {
			  name: "clearStatement",
			  VC_CHANNEL_ID: this.search.vcchannelid,
				CLEAR_DATE_START: this.gettime(this.search.value1[0]),
				CLEAR_DATE_END: this.gettime(this.search.value1[1])
			}
      this.$store
        .dispatch('report/report',params)
        .then(res => {
          let url = res
          let decodeUrl = decode(url)
					// console.log(decodeUrl,'地址')
					let clientWidth = document.documentElement.clientWidth;
          let clientHeight = document.documentElement.clientHeight;
					window.open(
            decodeUrl,
            "_blank",
            "width=" +
              clientWidth +
              ",height=" +
              clientHeight +
              ",left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,"
          );
        })
        .catch(() => { })
    },
	}

}
</script>

<style lang="scss" scoped>
	.user{
		padding: 20px;
		.table{
			width: 100%;
			background-color: #FFFFFF;
			text-align: center;
			img{
				width: 50%;
			}
		}
	}
</style>
