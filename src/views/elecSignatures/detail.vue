<template>
  <div class="detail" v-loading.fullscreen.lock="showLoading">
    <div class="info">
      <div class="btn-title">
        <div class="title">签约详情</div>
      </div>

      <el-form label-width="100px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="签约人：">
              <div>{{ detail.signName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名：">
              <div>{{ detail.custName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合同编号：">
              <div>{{ detail.contractId }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="手机号：">
              <div>{{ detail.signContact }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌颜色：">
              <div>{{ getType(typeList.carColors, detail.carColor) }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号：">
              <div>{{ detail.carNo }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="产品类型：">
              <div>{{ detail.contractTypeStr }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最低保证金(元)：">
              <div>{{ detail.blackLine }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保证金余额(元)：">
              <div>{{ detail.amount }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="状态：">
              <div>{{ detail.cardStatusStr }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="办理时间：">
              <div>{{ detail.createTime }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="info" style="margin-top: 20px">
      <div class="title">档案资料</div>
      <!-- <div
        class="archives-box"
        v-if="detail.orderStatus == 3 || detail.orderStatus == 1"
        style="padding: 30px"
      >
        <photograph
          :urlList.sync="imgList"
          :customerId="detail.custMastId"
          :vehicle_code="detail.carNo"
          :vehicle_color="detail.carColor"
          :other_code="detail.cardNo"
          @getDetailImgs="getDetailImgs"
        />
      </div> -->
      <div class="archives-box" style="padding: 30px">
        <div v-for="(item, index) in imgList" :key="index">
          <div class="archives-item" v-if="item.file_url">
            <el-image
              :preview-src-list="previewImgList"
              :src="item.file_url"
            ></el-image>
            <span class="demonstration">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="info" style="margin-top: 20px" v-if="detail && detail.version === 'V2'">
      <div class="title">协议明细记录</div>
      <div class="http-detail">
        <el-table
            :data="detail.docDetails || []"
            :align="center"
            :header-align="center"
            border
            :max-height="550"
            style="width: 100%;"
            :row-style="{ height: '40px' }"
            :cell-style="{ padding: '0px' }"
            :header-row-style="{ height: '40px' }"
            :header-cell-style="{ padding: '0px' }"
        >
        <el-table-column
            prop="cid"
            align="center"
            width="160"
            label="编号"
            show-overflow-tooltip
        />
        <el-table-column
            prop="contractId"
            align="center"
            width="160"
            label="合同编号"
            show-overflow-tooltip
        ></el-table-column>
        <el-table-column
            prop="docId"
            align="center"
            width="200"
            show-overflow-tooltip
            label="文档编号"
        />
        <el-table-column
            prop="createTime"
            align="center"
            width="180"
            label="办理日期"
        />
        <el-table-column
            prop="delFlag"
            align="center"
            width="120"
            label="协议状态"
        >   
        <template slot-scope="scope">
              {{ scope.row.delFlag == 0 ? '有效' : '失效' }}
            </template>
        </el-table-column>
        <el-table-column
            prop="signStatus"
            align="center"
            width="120"
            label="签章状态"
        >
        <template slot-scope="scope">
              {{ scope.row.signStatus == 0 ? '创建' : '签章完成' }}
            </template>
        </el-table-column>
        <el-table-column
            prop="templateId"
            align="center"
            width="120"
            label="模板文件Id"
        />
        <!-- <el-table-column
            prop="templateType"
            align="center"
            width="120"
            label="模板文件类型"
        /> -->
        <el-table-column
            prop="templateVersion"
            align="center"
            width="120"
            label="模板文件版本"
        />
        <el-table-column
            prop="remark"
            align="center"
            width="200"
            label="备注"
            show-overflow-tooltip
        />
        <el-table-column
            fixed="right"
            label="操作"
            align="center"
            header-align="center"
          >
          <template slot-scope="scope">
            <el-button
                size="mini"
                type="primary"
                @click="previewSign(scope.row)"
                >预览</el-button
              >
          </template>
        </el-table-column>
        </el-table>
      </div>
    </div>
    
    <div class="foot">
      <el-button type="normal" style="margin: 0 20px" @click="back"
        >返回</el-button
      >
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'

export default {
  data() {
    return {
      loading: false,
      showLoading: false,
      center: 'center',
      id: '',
      search: '',
      typeList: {
        carColors: [], //车牌颜色字典
        cardTypes: [], //卡类型字典
        payOrgIds: [], //机构字典
        refundChannels: [], //渠道字典
        refundStatus: [], //退费状态字典
      },
      previewImgList: [],
      imgdom: [
        {
          lable: '身份证(人像面)',
          photo_code: '22',
          file_url: '',
          file_serial: '',
          // isShow: true,
        },
        {
          lable: '身份证(国徽面)',
          photo_code: '23',
          file_url: '',
          file_serial: '',
          // isShow: true,
        },
        {
          lable: '代办人身份证(人像面)',
          photo_code: '26',
          file_url: '',
          file_serial: '',
          // isShow: false,
        },
        {
          lable: '代办人身份证(国徽面)',
          photo_code: '27',
          file_url: '',
          file_serial: '',
          // isShow: false,
        },
        {
          lable: '单位有效证件',
          photo_code: '2',
          file_url: '',
          file_serial: '',
          // isShow: false,
        },
      ],
      imgList: [
        {
          photo_code: '1',
          label: '身份证人像面',
          file_url: '',
          file_serial: '',
        },
        {
          photo_code: '11',
          label: '身份证国徽面',
          file_url: '',
          file_serial: '',
        },
        // {
        //   photo_code: '2',
        //   label: '单位营业执照副本',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   photo_code: '25',
        //   label: '委托书',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   photo_code: '17',
        //   label: '联系人身份证人像面',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   photo_code: '18',
        //   label: '联系人身份证国徽面',
        //   file_url: '',
        //   file_serial: '',
        // },
        {
          photo_code: '3',
          label: '行驶证正页',
          file_url: '',
          file_serial: '',
        },
        {
          photo_code: '12',
          label: '行驶证副页',
          file_url: '',
          file_serial: '',
        },
        // {
        //   photo_code: '3',
        //   label: '行驶证',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   photo_code: '12',
        //   label: '行驶证副页',
        //   file_url: '',
        //   file_serial: '',
        // },
        {
          photo_code: '6',
          label: '车辆照片',
          file_url: '',
          file_serial: '',
        },
        // {
        //   photo_code: '20',
        //   label: '车辆道路运输证',
        //   file_url: '',
        //   file_serial: '',
        // },
        {
          photo_code: '15',
          label: '车头照片',
          file_url: '',
          file_serial: '',
        },
        // {
        //   photo_code: '16',
        //   label: '车身照片',
        //   file_url: '',
        //   file_serial: '',
        // },
        {
          photo_code: '75',
          label: '代办人身份证人像面',
          file_url: '',
          file_serial: '',
          isOther: true, //签约人证件信息
        },
        {
          photo_code: '76',
          label: '代办人身份证国徽面',
          file_url: '',
          file_serial: '',
          isOther: true, //签约人证件信息
        },
        {
          photo_code: '5',
          label: '委托书',
          file_url: '',
          file_serial: '',
        },
        // {
        //   label: '车辆变更单',
        //   photo_code: '8',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   label: '车辆其他',
        //   photo_code: '10',
        //   file_url: '',
        //   file_serial: '',
        // },
        // {
        //   photo_code: '1',
        //   label: '签约人身份证人像面',
        //   file_url: '',
        //   file_serial: '',
        //   isOther: true, //签约人证件信息
        // },
        // {
        //   photo_code: '11',
        //   label: '签约人身份证国徽面',
        //   file_url: '',
        //   file_serial: '',
        //   isOther: true, //签约人证件信息
        // },
      ],
      handleTypeList: [
        {
          value: 0,
          label: '未处理',
        },
        {
          value: 1,
          label: '通过',
        },
        {
          value: 2,
          label: '退回',
        },
        {
          value: 3,
          label: '修改',
        },
      ],
      //订单状态：0-用户申请退款1-业务员审核2-清算中3-网点审核4-运营部复核5-制表6-财务打款9-已归档
      orderStatusList: [
        {
          value: 0,
          label: '用户申请退款',
        },
        {
          value: 1,
          label: '业务员审核',
        },
        {
          value: 2,
          label: '清算中',
        },
        {
          value: 3,
          label: '网点审核',
        },
        {
          value: 4,
          label: '运营部复核',
        },
        {
          value: 5,
          label: '制表',
        },
        {
          value: 6,
          label: '财务打款',
        },
        {
          value: 9,
          label: '已归档',
        },
      ],
      sourceList: [
        {
          value: 1,
          label: '网点',
        },
        {
          value: 2,
          label: 'C端',
        },
      ],
      // //制表状态：0-未制表；1—已制表
      // tabFlagList: [
      //   {
      //     value: 0,
      //     label: '未制表',
      //   },
      //   {
      //     value: 1,
      //     label: '已制表',
      //   },
      // ],
      //0-未比对；1-比对成功；2-比对失败
      transferMatchList: [
        {
          value: 0,
          label: '未比对',
        },
        {
          value: 1,
          label: '比对成功',
        },
        {
          value: 2,
          label: '比对失败',
        },
      ],
      detail: {},
      tableData: [],
      count: 0,
      imgSenceList: ['1', '2', '13'],
    }
  },
  created() {
    this.id = this.$route.query.id
    this.search = this.$route.query.search
    this.getTypeList()
    this.getDetail()
  },
  methods: {
    previewSign(val) {
        let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            val.filePath,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    //获取列表
    getDetail() {
      this.showLoading = true
      let params = { cid: this.id }
      request({
        url: api.getSignDetai,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.showLoading = false
          this.detail = res.data
          // this.tableData = res.data.opLogList
          this.getDetailImgs(res.data, this.imgSenceList[this.count])
        })
        .catch((err) => {
          this.showLoading = false
          console.log(err)
        })
      // this.$store
      //   .dispatch('refund/getLogoutDetail', params)
      //   .then((res) => {
      //     this.showLoading = false
      //     this.detail = res
      //     this.tableData = res.opLogList
      //     this.getDetailImgs(res)
      //   })
      //   .catch((err) => {
      //     this.showLoading = false
      //   })
    },
    //获取档案
    getDetailImgs(detail, type) {
      let params = {
        customer_id: detail.custMastId,
        vehicle_code: '',
        vehicle_color: '',
        scene: type,
        // other_code: detail.cardNo,
      }
      if (type == '2') {
        //获取车辆档案
        params.vehicle_code = detail.carNo
        params.vehicle_color = detail.carColor
      } else {
        delete params.vehicle_code
        delete params.vehicle_color
      }
      this.$store
        .dispatch('refund/getDetailImgs', params)
        .then((res) => {
          this.changeImgList(res, type)
        })
        .catch((err) => {
          this.loading = false
        })
    },
    //筛选图片的数据
    changeImgList(arrList, type) {
      //没数据时跳出循环
      if (arrList.length == 0) return
      if (type == '6') {
        //初始化计数器
        this.count = 0
      }

      for (let i = 0; i < arrList.length; i++) {
        let arrItem = arrList[i]
        for (let j = 0; j < this.imgList.length; j++) {
          let imgItem = this.imgList[j]
          if (type != '13') {
            //不是签约人信息，直接赋值
            if (arrItem.photo_code == imgItem.photo_code) {
              imgItem.file_url = arrItem.file_url
              imgItem.file_serial = arrItem.file_serial
            }
          } else {
            //签约人信息，判断isOther
            if (arrItem.photo_code == imgItem.photo_code && imgItem.isOther) {
              imgItem.file_url = arrItem.file_url
              imgItem.file_serial = arrItem.file_serial
            }
          }
        }
        if (i == arrList.length - 1 && this.count < 2) {
          console.log('执行了吗', this.count)
          //最后一个了，循环继续获取档案
          this.count++
          this.getDetailImgs(this.detail, this.imgSenceList[this.count])
        }
      }
      let previewImgArr = this.imgList.filter((item) => item.file_url)
      previewImgArr.forEach((item) => {
        this.previewImgList.push(item.file_url)
      })
      // console.log('imgList===>>>>>>>>>>>>>>>>', this.imgList)
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getTypeList() {
      this.$store
        .dispatch('refund/getTypeList')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            // console.log('keykeykey', key)
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
            // console.log('typeList[key]', key, this.$data[key])
          })
        })
        .catch((err) => {})
    },
    //返回前一页并关闭当前页面包屑
    back() {
      //关闭当前页面包屑
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(
          (item) => item.path === this.$route.path
        ),
        1
      )
      console.log('当前路由', this.$route.path)
      this.$router.push({
        path: './elecSignatures',
        replace: true,
        query: {
          search: this.search,
        },
      })
    },
  },
  // filters: {
  //   moneyFilter(val) {
  //     if (val) {
  //       return (val / 100).toFixed(2)
  //     } else {
  //       return '0.00'
  //     }
  //   },
  // },
}
</script>

<style scoped lang="scss">
.detail {
  padding: 20px;
  .info {
    background-color: #fff;
    .btn-title {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f0f0f0;
      align-items: center;
      padding-right: 20px;
      .title {
        border: 0;
        margin: 0;
      }
    }
    .textarea-wrapper {
      padding: 0 20px 30px 20px;
    }
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  .nat-form.nat-form-list .el-form-item {
    margin-bottom: 0px;
  }
  ::v-deep .el-form-item__label {
    min-width: 180px !important;
  }
  .http-detail {
        padding: 0 20px 20px;
        margin-bottom: 20px;
    }

  .archives-box {
    display: flex;
    flex-wrap: wrap;
    -moz-box-pack: start;
    -ms-box-pack: start;
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -moz-justify-content: flex-start;
    justify-content: flex-start;
  }
  .archives-box .archives-item {
    width: 240px;
    margin-right: 20px;
    margin-bottom: 20px;
  }
  .archives-box .archives-item .demonstration {
    display: block;
    color: #8492a6;
    width: 100%;
    text-align: center;
    font-size: 15px;
    margin-top: 10px;
  }
  .archives-box .archives-item .el-image {
    width: 240px;
    height: 180px;
  }

  .foot {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    background-color: #fff;
    padding: 20px;
    text-align: center;
  }
}
</style>