<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="用户名：" prop="customerName">
            <el-input v-model="search.customerName" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="经办人手机号：" prop="signContact">
            <el-input v-model="search.signContact" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="产品类型：" prop="contractType">
            <el-select
              v-model="search.contractType"
              clearable
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="item in contractTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="办理时间：">
            <el-date-picker
              v-model="dateTime"
              unlink-panels
              :picker-options="pickerOptions"
              type="daterange"
              placeholder="选择日期时间"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              range-separator="至"
              value-format="yyyy-MM-dd"
              @blur="dateTimeBlur"
              @change="dateTimeChange"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="vehicleColor">
            <el-select
              v-model="search.vehicleColor"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌：" prop="vehicleCode">
            <el-input v-model="search.vehicleCode" placeholder=""></el-input>
          </dart-search-item>
          <template v-if="isCollapse">
            <dart-search-item label="合同编号：" prop="contractId">
              <el-input v-model="search.contractId" placeholder=""></el-input>
            </dart-search-item>
            <!-- <dart-search-item label="办理时间起始：" prop="staHicTime">
              <el-date-picker
                v-model="search.staHicTime"
                type="date"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="办理时间截止：" prop="endHicTime">
              <el-date-picker
                v-model="search.endHicTime"
                type="date"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </dart-search-item> -->
            <dart-search-item label="协议状态：" prop="contractStatus">
              <el-select
                v-model="search.contractStatus"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in contractStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="担保状态：" prop="guaranteeStatus">
              <el-select
                v-model="search.guaranteeStatus"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in guaranteeStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="解约时间：">
              <el-date-picker
                v-model="dateTimeArr"
                unlink-panels
                :picker-options="pickerOptions1"
                type="daterange"
                placeholder="选择日期时间"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                range-separator="至"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="解约人手机号：" prop="terminationPhone">
              <el-input
                v-model="search.terminationPhone"
                placeholder=""
              ></el-input>
            </dart-search-item>
          </template>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px"
            :span="24"
          >
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="onExportHandle"
                ><i class="el-icon-download"></i> 导出</el-button
              >
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
            </div>
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          :max-height="550"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '40px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '40px' }"
          :header-cell-style="{ padding: '0px' }"
        >
          <el-table-column
            prop="contractId"
            align="center"
            min-width="150"
            label="协议编号"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.cid }}
                </div>
                <span>{{ scope.row.cid }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="custName"
            align="center"
            min-width="150"
            label="用户名"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.custName }}
                </div>
                <span>{{ scope.row.custName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="signName"
            align="center"
            width="120"
            label="签约人"
          />
          <el-table-column
            prop="signContact"
            align="center"
            width="120"
            label="经办人手机号"
          />
          <el-table-column
            prop="carNo"
            align="center"
            min-width="100"
            label="车牌号"
          />
          <el-table-column
            prop="carColor"
            align="center"
            min-width="120"
            label="车牌颜色"
          >
            <template slot-scope="scope">
              {{ getVehicleColor(scope.row.carColor) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="contractTypeStr"
            align="center"
            width="200"
            label="产品类型"
          />
          <!-- <el-table-column
            prop="blackLine"
            align="center"
            width="80"
            label="最低保证金(元)"
          />
          <el-table-column
            prop="amount"
            align="center"
            width="80"
            label="保证金余额(元)"
          /> -->
          <el-table-column
            prop="delFlag"
            align="center"
            width="80"
            label="协议状态"
          >
            <template slot-scope="scope">
              {{ scope.row.delFlag == 0 ? '有效' : '失效' }}
            </template>
          </el-table-column>
          <el-table-column
            prop="effectiveEndTime"
            align="center"
            min-width="160"
            label="失效时间"
          />
          <el-table-column
            prop="createTime"
            align="center"
            min-width="160"
            label="办理时间"
          />
          <el-table-column
            prop="guaranteeStatus"
            align="center"
            min-width="80"
            label="担保状态"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.guaranteeStatus == '1'">未确认</span>
              <span v-else-if="scope.row.guaranteeStatus == '2'">已解约</span>
              <span v-else>无担保</span>
            </template></el-table-column
          >
          <el-table-column
            prop="terminationTime"
            align="center"
            min-width="160"
            label="解约时间"
          />
          <el-table-column
            prop="terminationPhone"
            align="center"
            min-width="120"
            label="解约人手机号"
          />
          <el-table-column
            prop="terminationNo"
            align="center"
            min-width="180"
            label="互联网账户编号"
            ><template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.terminationNo }}
                </div>
                <span>{{ scope.row.terminationNo }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            header-align="center"
            min-width="240"
            align="left"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="downLoadSign(scope.row)"
                >下载合同</el-button
              >
              <el-button
                v-if="scope.row.version !== 'V2'"
                size="mini"
                type="primary"
                @click="previewSign(scope.row)"
                >预览</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="toDetail(scope.row.cid)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="changePage"
            :current-page="search.pageNum"
            :page-sizes="[10, 20, 50]"
            :page-size="search.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import {
  licenseColorOption,
  gxCardTypeOptionsIssue,
} from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { getDict } from '@/api/dict'

var moment = require('moment')
// import { decode } from 'js-base64'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      gxCardTypeOptionsIssue,
      licenseColorOption,
      loading: false,
      dialogHandleDetail: false,
      dialogTransactionDetail: false,
      isCollapse: false,
      center: 'center',
      dateTime: [], //时间范围
      dateTimeArr: [], //解约时间范围
      pickerMinDate: null,
      pickerMaxDate: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          this.pickerMaxDate = maxDate
          this.pickerMinDate = minDate
        },
        disabledDate: (time) => {
          //当前时间
          let now = moment()

          if (!this.pickerMinDate && !this.pickerMaxDate) {
            //不选时间，默认只能到当前日期
            return time.getTime() > Date.now()
          } else if (this.pickerMinDate && !this.pickerMaxDate) {
            //选了开始日期后，判断结束日期
            let minStart = this.pickerMinDate
            let minEnd
            //加3个月90天
            let start90 = moment(minStart).add(90, 'days')
            //判断加上90天后时间是否在当前时间之前
            let flag = moment(start90).isSameOrBefore(now)
            if (flag) {
              minEnd = start90
            } else {
              minEnd = now
            }
            return time.getTime() > minEnd || time.getTime() < minStart
          } else {
            return time.getTime() > Date.now()
          }
        },
      },
      pickerOptions1: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
      search: {
        vehicleCode: '', //车牌号
        vehicleColor: '', //车牌颜色// 0-蓝色 // 1-黄色，2-黑色，3-白色，4-渐变绿色，5-黄绿双拼色， 6-蓝白渐变色，
        customerName: '', //用户名
        contractType: '', //产品类型
        staHicTime: '', //时间起始
        endHicTime: '', //时间结束
        contractStatus: '',
        contractId: '',
        signContact: '',
        guaranteeStatus: '', //担保状态
        terminationTimeStart: '', //解约时间起始
        terminationTimeEnd: '', //解约时间截止
        terminationPhone: '', //解约人手机号
        pageNum: 1,
        pageSize: 10,
      },
      contractTypeList: [], // 产品类型数据
      contractStatusList: [
        // { value: '', label: '全部' },
        { value: '0', label: '有效' },
        { value: '1', label: '失效' },
      ],
      guaranteeStatusList: [
        // { value: '', label: '全部' },
        { value: '1', label: '未确认' },
        { value: '2', label: '已解约' },
      ],

      total: 0,
      tableData: [],
    }
  },
  created() {
    // this.getSignList()
    // this.getDefaultTime()
    this.getContractTypeList()
  },
  methods: {
    // 获取产品类型数据
    getContractTypeList() {
      let params = {
        businessType: 'CARD_TYPE',
        ruleType: 'ALL',
        isDefault: '1',
      }
      getDict(params).then(({ code, data }) => {
        if (code == 200) {
          this.contractTypeList = data.map((ele) => ({
            label: ele.dictName,
            value: ele.dictCode,
          }))
        }
      })
    },
    getVehicleColor,
    // 设置默认的开始与结束时间
    getDefaultTime() {
      this.dateTime[1] = moment().format('YYYY-MM-DD')
      this.dateTime[0] = moment().subtract(30, 'days').format('YYYY-MM-DD')
      console.log('this.dateTime', this.dateTime)
    },
    dateTimeChange(e) {
      // console.log('e', e)
      if (!e) {
        this.pickerMaxDate = null
        this.pickerMinDate = null
      }
      console.log('dateTime', this.dateTime)
    },
    dateTimeBlur() {
      if (!this.pickerMaxDate || !this.pickerMinDate) {
        //没选完就结束的话全部清空
        this.pickerMinDate = null
        this.pickerMaxDate = null
      }
    },
    getSignList() {
      console.log('saerch', this.dateTimeArr)
      if (
        !this.search.vehicleCode &&
        !this.search.contractId &&
        !this.dateTime
      ) {
        this.$message({
          type: 'warning',
          message: '不选办理时间时，查询条件至少要选择车牌或者合同编号',
        })
        return
      }

      this.loading = true
      let params = { ...this.search }

      params.staHicTime = this.dateTime ? this.dateTime[0] : ''
      params.endHicTime = this.dateTime ? this.dateTime[1] : ''

      params.terminationTimeStart = this.dateTimeArr ? this.dateTimeArr[0] : ''
      params.terminationTimeEnd = this.dateTimeArr ? this.dateTimeArr[1] : ''

      console.log('prams入参', params)
      request({
        url: api.getSignList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          console.log('返回的合同', res)
          this.tableData = res.data.records
            ? res.data.records.map((ele) => ({
                ...ele,
                effectiveEndTime: ele.effectiveEndTime
                  ? moment(ele.effectiveEndTime).format('YYYY-MM-DD HH:mm:ss')
                  : '',
              }))
            : []
          this.total = res.data.total
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    },
    previewSign(data) {
      let params = {
        conId: data.contractId,
        vehicleCode: data.carNo,
        vehicleColor: data.carColor,
      }
      request({
        url: api.previewSign,
        method: 'post',
        data: params,
      })
        .then((res) => {
          // this.loading = false
          console.log('返回的合同', res)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            res.data.viewUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch((err) => {
          // this.loading = false
          console.log(err)
        })
    },
    downLoadSign(data) {
      let params = {
        vehicleCode: data.carNo,
        vehicleColor: data.carColor,
      }
      data.version == 'V2'
        ? (params['cid'] = data.cid)
        : (params['conId'] = data.contractId)
      request({
        url: api.downSign,
        method: 'post',
        data: params,
        responseType: 'blob',
      })
        .then((res) => {
          // this.loading = false
          //   console.log('返回的合同', res)
          this.getBlob(res, 'application/zip', '电子合同')
        })
        .catch((err) => {
          // this.loading = false
          console.log(err)
        })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getSignList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getSignList()
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getSignList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10

      this.getSignList()
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    onExportHandle() {
      if (
        !this.search.vehicleCode &&
        !this.search.contractId &&
        !this.dateTime
      ) {
        this.$message({
          type: 'warning',
          message: '不选办理时间时，导出条件至少要选择车牌或者合同编号',
        })
        return
      }
      this.startLoading()

      let data = {
        fileName: '电子合同导出列表',
        name: 'hsIssueContracReport',
        param: {
          ...this.search,
        },
      }

      data.param.staHicTime = this.dateTime ? this.dateTime[0] : ''
      data.param.endHicTime = this.dateTime ? this.dateTime[1] : ''

      delete data.param.pageNum
      delete data.param.pageSize
      console.log('导出入參', data)
      request({
        url: api.downLoadExport,
        method: 'post',
        data: data,
        responseType: 'blob',
      })
        .then((res) => {
          this.endLoading()
          // this.loading = false
          console.log('返回的合同', res)
          this.getBlob(res, 'application/vnd.ms-excel', '电子合同列表')
        })
        .catch((err) => {
          this.endLoading()
          // this.loading = false
          console.log(err)
        })
    },
    toDetail(id) {
      //跳转审核页面
      this.$router.push({
        path: './signDetail',
        query: {
          id: id,
          search: JSON.stringify(this.search),
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.refund {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }

  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .btn-wrapper {
    margin-left: 40px;
    // margin-top: 10px;
  }

  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
}
</style>
