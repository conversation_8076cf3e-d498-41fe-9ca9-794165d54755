<template>
  <div>
    <ePage>
      <!-- 搜索栏 -->
      <dart-search
        slot="report-search"
        ref="searchForm"
        :formSpan="24"
        :searchOperation="false"
        :fontWidth="1"
        label-position="right"
        :model="search"
        :rules="rules"
      >
        <template slot="search-form">
          <div class="search-title">预付款划拨统计表</div>
          <template v-for="item in formProperties" >
            <dart-search-item
              :label="item.fieldLabel"
              :prop="item.fieldKey"
            >
              <template v-if="item.element != 'custom'">
                <searchField
                  :fieldProps="item.fieldProps"
                  :fieldOptions="item"
                  :ref="item.ref"
                  v-model="search[item.fieldKey]"
                ></searchField>
              </template>
            </dart-search-item>
          </template>

          <dart-search-item label="操作员" prop="businessTotalCustomerIdName">
            <el-input clearable class="searchInput" v-model="search.businessTotalCustomerIdName" disabled>
              <el-button slot="append" @click="chooseOperator()" icon="el-icon-search"></el-button>
              <el-button 
                v-if="search.businessTotalCustomerId" 
                slot="append" 
                @click="clearOperator()" 
                icon="el-icon-delete">
              </el-button>
            </el-input>
          </dart-search-item>
          <dart-search-item :is-button="true" :colElementNum="3">
            <div class="g-flex g-flex-end">
              <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
              <el-button size="mini" @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>

    </ePage>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  selectSchema,
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '@/views/reportstatistics/components/service'
import ePage from '@/views/reportstatistics/components/ePage.vue'

import { datePickerOptions } from '@/components/schemaQuery/tool'
var moment = require('moment')
let branchTypeOptions = [
  { value: '01', label: '自营' },
  { value: '02', label: '运营' },
  { value: '03', label: '一站式' },
  { value: '04', label: '合作' },
  { value: '05', label: '银行' },
  { value: '06', label: '线上' },
  { value: '99', label: '其他' },
]

let branchIdOptions = [
  { value: '1', label: '仅当前部门' },
  { value: '2', label: '当前部门及下属' }
]

export default {
  data() {
    return {
      loading: false,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
        deptID: [
          { 
            required: true, 
            validator: this.validateDeptOrOperator,
            trigger: 'change' 
          }
        ],
        radius: [
          { 
            required: true,
            validator: this.validateDeptOrOperator,
            trigger: 'change' 
          }
        ],
        businessTotalCustomerId: [
          {
            validator: this.validateDeptOrOperator,
            trigger: 'change'
          }
        ]
      },
      search: {
        name: 'advancePublicFlowReport', // 报表名称
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '', //部门id
        branchDeptNo: '', //部门编号
        branchLength: '', //部门编号长度
        deptID: '',
        businessTotalCustomerId: '', // 新增: 操作员ID
        businessTotalCustomerIdName: '', // 新增: 操作员名称
      },
      formProperties: {
        startTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'startTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
        endTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
            defaultTime:"23:59:59"
          },
        },
        deptID: {
          ...cascaderSchema,
          ref: 'cascaderNodes',
          placeholder: '请选择',
          fieldLabel: '部门名称',
          fieldKey: 'deptID',
        },
        radius: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: branchIdOptions
          },
          fieldLabel: '查询范围',
          fieldKey: 'radius'
        }
      },
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg((data) => {
      _self.formProperties.deptID.fieldProps.options = data || []
    })
  },
  methods: {
    validateDeptOrOperator(rule, value, callback) {
      const hasDept = this.search.deptID;
      const hasRadius = this.search.radius;
      const hasOperator = this.search.businessTotalCustomerId;
      
      // 检查部门和查询范围是否配对
      if ((hasDept && !hasRadius) || (!hasDept && hasRadius)) {
        callback(new Error('部门和查询范围必须同时选择'));
        return;
      }

      // 检查是否至少选择了一组有效的查询条件
      if (!hasOperator && !hasDept && !hasRadius) {
        callback(new Error('请至少选择部门和查询范围，或选择操作员'));
        return;
      }

      callback();
    },
    onValid() {
      let { startTime, endTime, radius, businessTotalCustomerId } = this.search
      if (moment(startTime).isAfter(endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }

      // 如果是选择操作员，不进行时间范围验证
      if (businessTotalCustomerId) {
        return true
      }

      let typeObj = {
        1: {
          months: 11,
          text: '查询仅当前部门统计日期时间段不能不能大于一年'
        },
        2: {
          months: 0,
          text: '查询当前部门及下属统计日期时间段大于一个月'
        }
      }

      if (
        moment(endTime).diff(moment(startTime), 'months') >
        typeObj[radius].months
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: typeObj[radius].text
        })
        return
      }
      return true
    },
    getDeptNo(item) {
      let deptNum = item.deptNum
      let branchDeptNo = ''
      switch (item.deptLevel) {
        case 1:
          branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.branchDeptNo = branchDeptNo
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    formatParams() {
      let cascaderData = {}
      let { radius, businessTotalCustomerId } = this.search

      // 如果选择了部门
      if (this.search.deptID) {
        try {
          cascaderData =
            this.$refs['cascaderNodes'][0].$children[0].getCheckedNodes()[0].data
          this.getDeptNo(cascaderData)
        } catch (e) {
          console.log(e)
        }
      }

      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      delete params.businessTotalCustomerIdName // 删除显示用的名称字段

      // 根据选择的查询范围处理参数
      if (radius == 1) {
        params.branchId = params.OPERATOR_DEPT
        delete params.branchDeptNo
        delete params.branchLength
        delete params.radius
      } else {
        delete params.radius
      }

      return params
    },
    onSearchHandle() {
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          if (!this.onValid()) return
          let params = this.formatParams()
          // console.log(params, 'paramsparamsparams')
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
        this.search.businessTotalCustomerId = ''
        this.search.businessTotalCustomerIdName = null
        this.$forceUpdate()
      })
    },
    chooseOperator() {
      this.$openPage(
        '@/views/equipment/cardRechargeRecord/components/operatorList',
        '选择操作员',
        {
          multiple: 10,
          callBack: res => {
            let ids = res.map(item => item.etcUserId).join(',')
            let names = res.map(item => item.realName).join(',')
            
            // 移除清空部门相关字段的逻辑，允许同时选择
            this.$set(this.search, 'businessTotalCustomerId', ids)
            this.$set(this.search, 'businessTotalCustomerIdName', names)
            this.$forceUpdate()
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    },
    clearOperator() {
      this.search.businessTotalCustomerId = ''
      this.search.businessTotalCustomerIdName = ''
      this.$forceUpdate()
    },
  },
}
</script>

<style lang="scss">
.search-title {
  margin: 4px 0 10px 20px;
}
.searchInput {
  .el-input__inner {
    background-color: #fff !important;
    color: #606266 !important;
  }
}
</style>
