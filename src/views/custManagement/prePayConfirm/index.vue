<template>
  <div class="pay-confirm">
    <dart-search :formSpan="24"
                 :gutter="20"
                 ref="searchForm1"
                 label-position="right"
                 :model="search"
                 :fontWidth="2">
      <template slot="search-form"
                style="padding-left: 10px">
        <div class="collapse-wrapper"
             v-show="!isCollapse">
          <dart-search-item label="付款方名称："
                            prop="">
            <el-input v-model="search.payerName"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="付款方账号："
                            prop="carNo">
            <el-input v-model="search.payerAccount"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="收款方开户名："
                            prop="carNo">
            <el-input v-model="search.beneficiaryName"
                      placeholder=""
                      clearable></el-input>
          </dart-search-item>
          <dart-search-item label="收款方账号："
                            prop="carNo">
            <el-input v-model="search.beneficiaryAccount"
                      placeholder=""
                      clearable></el-input>
          </dart-search-item>
          <dart-search-item label="交易日期："
                            prop="carNo">
            <dart-date-range v-model="search.transDateRange"
                             type="datetime"
                             value-format="timestamp"
                             :default-time="defaultTime"></dart-date-range>
          </dart-search-item>
          <dart-search-item label="导入时间："
                            prop="carNo">
            <dart-date-range v-model="search.importDateRange"
                             type="datetime"
                             value-format="timestamp"
                             :default-time="defaultTime"></dart-date-range>
          </dart-search-item>
          <dart-search-item label="状态："
                            prop="status">
            <el-select v-model="search.status"
                       clearable
                       placeholder="请选择">
              <el-option v-for="item in statusList"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
        </div>
        <dart-search-item :is-button="true"
                          :span="16">
          <div class="btn-wrapper">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle"><i class="el-icon-search"></i> 搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button size="mini"
                       type="warning"
                       v-permisaction="['transfer:confirm']"
                       @click="moreConfirm">批量确认</el-button>
            <el-button size="mini"
                       @click="toHandInput"
                       type="warning">手工录入</el-button>
            <el-button size="mini"
                       type="primary"
                       @click="uploadDialogVisible = true"><i class="el-icon-upload"></i> 导入</el-button>
            <span class="collapse"
                  v-if="!isCollapse"
                  @click="isCollapse = true">收起</span>
            <span class="collapse"
                  v-else
                  @click="isCollapse = false">展开</span>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table ref="multipleTable"
                v-loading="loading"
                :data="tableData"
                :align="center"
                :header-align="center"
                border
                height="300px"
                style="width: 100%"
                :row-style="{ height: '35px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '35px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id"
                @selection-change="handleSelectionChange">
        <el-table-column :selectable="selectable"
                         type="selection"
                         width="40"></el-table-column>
        <el-table-column prop="beneficiaryAccount"
                         align="center"
                         label="收款方账号"
                         min-width="180"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="beneficiaryName"
                         align="center"
                         label="收款方开户名"
                         min-width="180"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="incomeAmount"
                         align="center"
                         min-width="120"
                         label="金额"
                         :show-overflow-tooltip="true">
          <template slot-scope="scope">
            ￥{{ scope.row.incomeAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column prop="availableAmount"
                         align="center"
                         label="可用金额"
                         min-width="120"
                         :show-overflow-tooltip="true"><template slot-scope="scope">
            ￥{{ scope.row.availableAmount | moneyFilter }}
          </template></el-table-column>
        <el-table-column prop="payerAccount"
                         align="center"
                         label="付款方账号"
                         min-width="180"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="payerName"
                         align="center"
                         min-width="250"
                         label="付款方名称"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="transactionNo"
                         align="center"
                         min-width="100"
                         label="交易流水号" />
        <el-table-column prop="transactionDate"
                         align="center"
                         label="交易日期"
                         min-width="180" />
        <el-table-column prop="createdAt"
                         align="center"
                         min-width="180"
                         label="导入时间" />
        <el-table-column prop="abstractText"
                         align="center"
                         label="摘要"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="remark"
                         align="center"
                         min-width="100"
                         label="备注"
                         :show-overflow-tooltip="true" />
        <el-table-column prop="status"
                         align="center"
                         label="状态">
          <template slot-scope="scope">
            {{ getStatus(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right"
                         label="操作"
                         align="center"
                         width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.status == '0'">
              <el-button @click="oneConfirm(scope.row.id)"
                         v-permisaction="['transfer:confirm']"
                         type="text"
                         size="small">确认</el-button>
              <el-button v-permisaction="['transfer:delete']"
                         type="text"
                         size="small"
                         @click="handleDelete(scope.row.id)">删除</el-button>
            </div>
            <div v-if="scope.row.status == '1'">
              <el-button v-if="scope.row.availableAmount === scope.row.incomeAmount"
                         @click="handleCancel(scope.row.id)"
                         type="text"
                         size="small">取消确认</el-button>
              <el-button type="text"
                         @click="handleConcat(scope.row)"
                         size="small"
                         :disabled="scope.row.availableAmount == '0'"
                         v-if="scope.row.src != '9'">关联客户</el-button>
              <el-button @click="toPayOrder(scope.row.id)"
                         type="text"
                         size="small">查看</el-button>
            </div>
            <div v-permisaction="['transfer:revoke']">
              <el-button v-if="scope.row.status == '2'"
                         type="text"
                         size="small"
                         @click="revoke(scope.row)">撤回</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="total-price">
        未使用订单金额合计：￥{{ totalPrice | moneyFilter }}
      </div>
    </div>
    <div class="pagination"
         v-if="total > search.pageSize">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.pageNum"
                     :page-sizes="[20, 50, 100, 200, 300]"
                     :page-size="search.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <!-- 互联网账户充值弹框组件 -->
    <account-dialog :orderId="id"
                    :visible.sync="accountDialogVisible"
                    @updateList="updateList"></account-dialog>
    <upload-dialog :visible.sync="uploadDialogVisible"
                   @uploadSuccess="uploadSuccess">
    </upload-dialog>

    <!-- 客账扣款模式账户充值弹框组件 -->
    <guestAccount :orderId="id"
                  :visible.sync="guestAccountDialogVisible"
                  @updateList="updateList"></guestAccount>

    <el-dialog title="请选择要充值的账户类型"
               :visible.sync="selectDialogVisible"
               :close-on-click-modal="false"
               width="30%">
      <div class="g-flex g-flex-column g-flex-horizontal-vertical">
        <el-button style="width:270px;margin-bottom:20px"
                   type="primary"
                   @click="netAccountHandle">【互联网账户】充值</el-button>
        <el-button style="width:270px;margin-left:0"
                   type="primary"
                   @click="guestAccountHandle">【客账扣款模式账户】充值</el-button>
      </div>

    </el-dialog>
  </div>
</template>
<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import dartDateRange from '@/components/DateRange/date-range'
import accountDialog from './accountDialog'
import uploadDialog from './uploadDialog'
import guestAccount from './guestAccount'
import dartSlide from '@/components/Slide/index'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    dartDateRange,
    accountDialog,
    uploadDialog,
    guestAccount,
  },
  data() {
    return {
      loading: false,
      accountDialogVisible: false,
      guestAccountDialogVisible: false,
      uploadDialogVisible: false,
      isCollapse: false,
      center: 'center',
      total: 0,
      id: '',
      tableHeight: 0,
      totalPrice: '',
      defaultTime: ['00:00:00', '23:59:59'],
      search: {
        status: '',
        beneficiaryAccount: '',
        beneficiaryName: '',
        payerAccount: '',
        payerName: '',
        transactionStartDate: '',
        transactionEndDate: '',
        importStartDate: '',
        importEndDate: '',
        pageNum: 1,
        pageSize: 20,
        transDateRange: [],
        importDateRange: [],
      },
      tableData: [],
      multipleSelection: [],
      statusList: [
        { value: '0', label: '无效' },
        { value: '1', label: '正常' },
        { value: '2', label: '取消' },
      ],
      selectDialogVisible: false, //充值的账户类型弹框
    }
  },
  created() {
    this.getprePayList()
  },
  methods: {
    getStatus(status) {
      if (status == '0') {
        return '无效'
      } else if (status == '1') {
        return '正常'
      } else if (status == '2') {
        return '已取消'
      } else {
        return ''
      }
    },
    getTotalPrice() {
      this.$store
        .dispatch('custManagement/getSumAvailableAmount')
        .then((res) => {
          this.totalPrice = res
        })
        .catch((err) => {
          console.log('err', err)
        })
    },
    getprePayList() {
      this.loading = true
      let formatStr = 'YYYY-MM-DD HH:mm:ss'
      let params = JSON.parse(JSON.stringify(this.search))
      if (!!params.transDateRange[0] && !!params.transDateRange[1]) {
        params.transactionStartDate = !!params.transDateRange[0]
          ? moment(params.transDateRange[0]).format(formatStr)
          : ''
        params.transactionEndDate = !!params.transDateRange[1]
          ? moment(params.transDateRange[1]).format(formatStr)
          : ''
      }
      if (!!params.importDateRange[0] && !!params.importDateRange[1]) {
        params.importStartDate = !!params.importDateRange[0]
          ? moment(params.importDateRange[0]).format(formatStr)
          : ''
        params.importEndDate = !!params.importDateRange[1]
          ? moment(params.importDateRange[1]).format(formatStr)
          : ''
      }
      if (!params.status) {
        delete params.status
      }

      delete params.transDateRange
      delete params.importDateRange

      console.log('入参params', params)
      this.$store
        .dispatch('custManagement/advancePayment', params)
        .then((res) => {
          this.loading = false
          this.tableData = res.records
          this.total = res.total
          //获取总金额
          this.getTotalPrice()
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getprePayList()
    },
    onResultHandle() {
      // this.search.pageNum = 1
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
    },
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.multipleSelection = []
      selection.forEach((item) => {
        if (!this.multipleSelection.includes(item.id)) {
          this.multipleSelection.push(item.id)
        }
      })
    },
    selectable(row) {
      return row.status != '1' && row.status != '2'
    },
    handleClick() {},
    handleDelete(id) {
      this.confirmDilog('是否确认删除', 'info')
        .then(() => {
          let params = {
            orderId: id,
          }
          console.log('删除业务', params)
          this.$store
            .dispatch('custManagement/transfeDelete', params)
            .then((res) => {
              this.loading = false
              this.getprePayList()
              this.message('删除成功', 'success')
            })
            .catch((err) => {
              this.loading = false
              // this.message(err.msg, 'error')
            })
        })
        .catch(() => {
          this.message('取消删除', 'info')
        })
    },
    handleCancel(id) {
      this.confirmDilog('是否确认取消确认', 'info')
        .then(() => {
          let params = {
            orderId: id,
          }
          console.log('确认业务', params)
          this.$store
            .dispatch('custManagement/transfeCancel', params)
            .then((res) => {
              this.loading = false
              this.getprePayList()
              this.message('取消确认成功', 'success')
            })
            .catch((err) => {
              this.loading = false
              // this.message(err.msg, 'error')
            })
        })
        .catch(() => {
          this.message('取消取消确认', 'info')
        })
    },
    //确认业务
    oneConfirm(id) {
      this.confirmDilog('是否确认到账', 'info')
        .then(() => {
          let params = {
            orderIds: [id],
          }
          console.log('确认业务', params)
          this.$store
            .dispatch('custManagement/transfeConfirm', params)
            .then((res) => {
              // console.log('成功了', res)
              this.loading = false
              this.getprePayList()
              this.message('确认成功', 'success')
            })
            .catch((err) => {
              this.loading = false
              // this.message(err.msg, 'error')
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    //选择账户类型
    handleConcat(val) {
      console.log(val)
      this.id = val.id
      this.selectDialogVisible = true
    },
    //互联网账户充值
    netAccountHandle() {
      this.accountDialogVisible = true
      this.selectDialogVisible = false
    },
    //客账扣款模式账户充值
    guestAccountHandle() {
      this.guestAccountDialogVisible = true
      this.selectDialogVisible = false
    },
    //充值成功回调
    updateList() {
      this.guestAccountDialogVisible = false
      this.accountDialogVisible = false
      this.getprePayList()
    },
    //跳转充值订单页面
    toPayOrder(id) {
      this.$router.push({
        path: './payOrder',
        query: {
          id,
        },
      })
    },
    //导入文件成功回调
    uploadSuccess() {
      this.uploadDialogVisible = false
      this.message('导入文件成功', 'success')
      this.getprePayList()
    },
    toHandInput() {
      this.$router.push({
        path: './handInput',
      })
    },
    moreConfirm() {
      this.confirmDilog('是否确认到账', 'info', 'moreConfirm')
        .then(() => {
          let params = {
            orderIds: this.multipleSelection,
          }
          console.log('批量确认业务', params)
          this.$store
            .dispatch('custManagement/transfeConfirm', params)
            .then((res) => {
              this.loading = false
              this.multipleSelection = []
              this.$refs.multipleTable.clearSelection()
              this.getprePayList()
              this.message(res.msg, 'info')
            })
            .catch((err) => {
              this.loading = false
              this.multipleSelection = []
              this.$refs.multipleTable.clearSelection()
              // this.message(err, 'error')
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getprePayList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getprePayList()
    },
    confirmDilog(msg, type, useType) {
      return new Promise((resolve, reject) => {
        if (useType === 'moreConfirm') {
          if (this.multipleSelection.length === 0) {
            reject({ msg: '请先选择1条记录', type: 'warning' })
            return
          }
        }
        this.$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject({ msg: '取消确认', type: 'info' })
          })
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
    //撤销删除
    revoke(val) {
      this.confirmDilog('是否确认撤回', 'info')
        .then(() => {
          let params = {
            orderId: val.id,
          }
          this.$store
            .dispatch('custManagement/transferRevoke', params)
            .then((res) => {
              this.loading = false
              this.message('撤回成功', 'success')
              this.getprePayList()
            })
            .catch((err) => {
              this.loading = false
              // this.message(err.msg, 'error')
            })
        })
        .catch(() => {
          this.message('取消撤回', 'info')
        })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
// .pay-confirm {
//   padding: 20px;
//   .table {
//     margin: 0px 0 10px 0;
//   }
//   .total-price {
//     color: red;
//   }
// }
.btn-wrapper {
  margin-left: 28px;
  // margin-top: 13px;
  // margin-bottom: 0;
}
.pay-confirm {
  //   height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    margin-top: 10px;
    color: red;
  }
  .el-button.is-disabled {
    color: #c0c4cc !important;
  }
}

.collapse {
  cursor: pointer;
  color: #409eff;
  margin-left: 10px;
  font-size: 14px;
}
</style>