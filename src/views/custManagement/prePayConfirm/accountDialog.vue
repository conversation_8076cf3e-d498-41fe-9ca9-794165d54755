<template>
  <div class="account-dialog"
       v-loading.fullscreen.lock="showLoading"
       :key="orderId">
    <el-dialog :visible.sync="visible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :fullscreen="isFullscreen"
               :show-close="false"
               width="80%">
      <template slot="title">
        <div class="btn-wrapper">
          <i @click="isFullscreen = true"
             v-if="!isFullscreen"
             class="el-icon-full-screen"></i>
          <i @click="isFullscreen = false"
             v-else
             class="el-icon-copy-document"></i>
          <i @click="close()"
             class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 原始交易信息详情 </span>
        </div>
      </template>
      <div class="search-list">
        <dart-search :formSpan="24"
                     :gutter="20"
                     ref="searchForm1"
                     :searchOperation='false'
                     label-position="right"
                     :model="search"
                     :fontWidth="2">
          <template slot="search-form"
                    style="padding-left: 10px">
            <dart-search-item label="用户名："
                              prop="company"
                              v-if="search.userType=='2'">
              <el-input v-model="search.company"
                        clearable
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="手机号码："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        clearable
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="登录名："
                              prop="loginName">
              <el-input v-model="search.loginName"
                        clearable
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="用户类型："
                              prop="userType">
              <el-select v-model="search.userType"
                         clearable
                         placeholder="请选择"
                         @change="userTypeChange">
                <el-option v-for="item in userTypeOption"
                           :key="item.index"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item>
            <dart-search-item :is-button="true"
                              :span="24"
                              :push="1"
                              style="margin-top: 10px">
              <el-button type="primary"
                         size="mini"
                         native-type="submit"
                         @click="onSearchHandle">查询</el-button>
              <el-button size="mini"
                         @click="onResultHandle">重置</el-button>
              <el-button @click="HandleRecharge()"
                         type="primary"
                         size="mini">
                充值
              </el-button>
            </dart-search-item>
          </template>
        </dart-search>
      </div>
      <div class="table">
        <el-table ref="multipleTable"
                  v-loading="loading"
                  :data="tableData"
                  :align="center"
                  :header-align="center"
                  border
                  :max-height="400"
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '54px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '54px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           width="55"></el-table-column>
          <el-table-column prop="netUserId"
                           align="center"
                           label="用户编号"
                           min-width="180" />
          <el-table-column prop="netLoginName"
                           align="center"
                           label="登录名"
                           min-width="180" />
          <el-table-column prop="incomeAmount"
                           align="center"
                           min-width="120"
                           label="账户余额">
            <template slot-scope="scope">
              ￥{{ scope.row.amout | moneyFilter }}
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="availableAmount"
            align="center"
            label="可用金额"
            min-width="120"
            ><template slot-scope="scope">
              ￥{{ scope.row.availableAmount | moneyFilter }}
            </template></el-table-column
          > -->
          <el-table-column prop="companyName"
                           align="center"
                           label="用户名称"
                           min-width="180"
                           v-if="search.userType=='2'" />
          <el-table-column prop="netMobile"
                           align="center"
                           min-width="120"
                           label="联系电话" />
          <el-table-column prop="createdTime"
                           align="center"
                           min-width="180"
                           label="创建时间" />
        </el-table>
      </div>
      <div v-if="total > search.pageSize"
           class="pagination g-flex g-flex-center">
        <el-pagination background
                       :current-page="search.pageNo"
                       :page-size="search.pageSize"
                       layout="prev, pager, next, jumper"
                       :total="total"
                       @current-change="changePage" />
      </div>
      <template slot="footer">

        <el-button @click="close()"
                   size="mini">关闭</el-button>
      </template>
    </el-dialog>
    <authorize-dialog :visible.sync="authorizeDialogVisible"
                      @handleConfirm="handleConfirm">
    </authorize-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import authorizeDialog from './authorizeDialog'
import { getSecuCode } from '@/utils/dialogUtils'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    authorizeDialog,
  },
  watch: {
    orderId(val) {
      if (val && this.visible) {
        this.search.pageNum = 1
        this.getAccountList()
      }
    },
    visible(val) {
      if (val && this.orderId) {
        this.search.pageNum = 1
        this.getAccountList()
      }
    }
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      authorizeDialogVisible: false,
      //   dialogFormVisible: false,
      center: 'center',
      search: {
        company: '',
        mobile: '',
        loginName: '',
        pageNum: 1,
        pageSize: 10,
        userType: '2',
      },
      total: '',
      userNoList: [],
      tableData: [],
      userTypeOption: [
        { value: '1', label: '个人' },
        { value: '2', label: '单位' },
      ],
    }
  },
  methods: {
    userTypeChange(val) {
      if (val == '1') {
        this.search.company = ''
      }
    },
    getAccountList() {
      console.log('刷新了吗', this.orderId)
      this.loading = true
      this.$store
        .dispatch('custManagement/getNetAccountList', this.search)
        .then((res) => {
          this.loading = false
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    reCharge(formData) {
      this.showLoading = true
      let params = {
        orderId: this.orderId,
        userNoList: this.userNoList,
        ...formData,
      }
      console.log('充值入参', params)
      this.$store
        .dispatch('custManagement/transferRecharge', params)
        .then((res) => {
          this.showLoading = false
          this.$confirm(
            '订单号[' + res.orderId + ']' + res.payMessage,
            '提示',
            {
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              showClose: false,
              confirmButtonText: '确定',
              type: 'success',
            }
          ).then(() => {
            this.userNoList = []
            this.$refs.multipleTable.clearSelection()
            this.showLoading = true
            setTimeout(() => {
              this.$router.push({
                path: './payOrder',
                query: {
                  id: this.orderId,
                },
              })
              this.showLoading = false
            }, 2000)
          })
        })
        .catch((err) => {
          this.userNoList = []
          this.$refs.multipleTable.clearSelection()
          this.showLoading = false
          console.log('err', err)
        })
    },
    HandleRecharge() {
      if (this.userNoList.length === 0) {
        this.$message({
          type: 'warning',
          message: '请先选中一条记录！',
        })
        return
      }
      this.authorizeDialogVisible = true
    },
    handleConfirm(formData) {
      //处理金额和密码
      formData.money *= 100
      formData.password = getSecuCode(formData.password)
      this.reCharge(formData)
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getAccountList()
    },
    onResultHandle() {
      // this.search.pageNum = 1
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
      this.search.userType = '2'
      this.getAccountList()
    },
    handleSelectionChange(selection) {
      this.userNoList = []
      selection.forEach((item) => {
        if (!this.userNoList.includes(item.netUserNo)) {
          this.userNoList.push(item.netUserNo)
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getAccountList()
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}
</style>
