<template>
  <div class="hand-input">
    <div class="input-box">
      <div class="title-warpper">
        <div class="title">预付款流水录入</div>
      </div>
      <div class="form-wrapper">
        <div class="title">基本信息</div>
        <el-form
          :inline="true"
          ref="ruleForm"
          :rules="rules"
          :model="ruleForm"
          class="form"
        >
          <el-row :xs="24" :sm="24">
            <el-col :span="6">
              <el-form-item
                label="收款方账号："
                prop="beneficiaryAccount"
                required
              >
                <el-input
                  v-model="ruleForm.beneficiaryAccount"
                  placeholder="请输入收款方账号"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="收款方开户名："
                prop="beneficiaryName"
                required
              >
                <el-input
                  v-model="ruleForm.beneficiaryName"
                  placeholder="请输入收款方开户名"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位收入金额：" prop="incomeAmount">
                <el-input
                  v-model="ruleForm.incomeAmount"
                  placeholder="请输入单位收入金额"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="付款方账号：" prop="payerAccount">
                <el-input
                  v-model="ruleForm.payerAccount"
                  placeholder="请输入付款方账号"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24" style="margin-top: 10px">
            <el-col :span="6">
              <el-form-item
                class="form-item"
                label="付款方名称："
                prop="payerName"
                required
              >
                <el-input
                  v-model="ruleForm.payerName"
                  placeholder="请输入付款方名称"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="付款方开户行：" prop="payerBank">
                <el-input
                  v-model="ruleForm.payerBank"
                  placeholder="请输入付款方开户行"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="交易流水号：" prop="transactionNo">
                <el-input
                  v-model="ruleForm.transactionNo"
                  placeholder="请输入交易流水号"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="交易日期：" prop="transactionDate">
                <el-date-picker
                  v-model="ruleForm.transactionDate"
                  type="date"
                  placeholder="选择交易日期"
                  clearable
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24" style="margin-top: 10px">
            <el-col :span="6">
              <el-form-item label="摘要：">
                <el-input
                  v-model="ruleForm.abstractText"
                  placeholder="请输入摘要"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24" style="margin-top: 10px">
            <el-col :span="16">
              <el-form-item label="备注：">
                <el-input
                  rows="5"
                  type="textarea"
                  v-model="ruleForm.remark"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="bottom-btn">
        <el-button type="primary" @click="submitForm('ruleForm')"
          >立即创建</el-button
        >
        <el-button @click="back()">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { param } from '@/utils'
var moment = require('moment')
export default {
  data() {
    return {
      ruleForm: {
        beneficiaryAccount: '',
        beneficiaryName: '',
        payerAccount: '',
        payerName: '',
        incomeAmount: '',
        payerBank: '',
        transactionNo: '',
        transactionDate: '',
        abstractText: '',
        remark: '',
      },
      rules: {
        beneficiaryAccount: [
          { required: true, message: '请输入收款方账号', trigger: 'blur' },
        ],
        beneficiaryName: [
          { required: true, message: '请输入收款方开户名', trigger: 'blur' },
        ],
        payerName: [
          { required: true, message: '请输入付款方名称', trigger: 'blur' },
        ],
        payerAccount: [
          { required: true, message: '请输入付款方账号', trigger: 'blur' },
        ],
        incomeAmount: [
          { required: true, message: '请输入单位收入金额', trigger: 'blur' },
        ],
        payerBank: [
          { required: true, message: '请输入付款方开户行', trigger: 'blur' },
        ],
        transactionNo: [
          { required: true, message: '请输入交易流水号', trigger: 'blur' },
        ],
        transactionDate: [
          {
            type: 'date',
            required: true,
            message: '请选择交易日期',
            trigger: 'change',
          },
        ],
      },
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.submit()
        } else {
          //   console.log('error submit!!')
          return false
        }
      })
    },
    submit() {
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      params.incomeAmount = this.accMul(params.incomeAmount, 100)
      params.transactionDate = params.transactionDate
        ? moment(params.transactionDate).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('入参', params)
      this.$store
        .dispatch('custManagement/saveAdvance', params)
        .then((res) => {
          this.$message({
            type: 'success',
            message: '创建成功',
          })
          this.back()
        })
        .catch((err) => {})
    },
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString()
      try {
        m += s1.split('.')[1].length
      } catch (e) {}
      try {
        m += s2.split('.')[1].length
      } catch (e) {}
      return (
        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
        Math.pow(10, m)
      )
    },
    back() {
      //关闭当前页面包屑
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(
          (item) => item.path === this.$route.path
        ),
        1
      )
      console.log('当前路由', this.$route.path)
      this.$router.push({
        path: './prePayConfirm',
        replace: true,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.hand-input {
  height: 100%;
  position: relative;
  flex-flow: column;
  display: flex;
  .input-box {
    padding: 20px;
    margin: 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .title-warpper {
    .title {
      font-size: 16px;
      border-left: 4px solid #09aff7;
      padding-left: 5px;
    }
  }
  .form-wrapper ::v-deep {
    margin-top: 20px;
    padding-left: 20px;
    .el-form-item__label {
      font-size: 12px;
      width: 105px;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .el-input__inner {
      width: 200px;
      height: 40px;
    }
    .el-textarea__inner {
      width: 700px;
    }
    .title {
      margin-bottom: 20px;
      font-size: 14px;
      border-left: 4px solid #ff6e00;
      padding-left: 5px;
    }
  }
  .bottom-btn {
    margin-top: 30px;
    margin-left: 30px;
  }
}
</style>
