<template>
  <div class="account-dialog"
       v-loading.fullscreen.lock="showLoading"
       :key="orderId">
    <el-dialog :visible.sync="visible"
               :close-on-click-modal="false"
               :center="true"
               class="guest-dialog form_dialog"
               :fullscreen="isFullscreen"
               :show-close="false"
               width="80%">
      <template slot="title">
        <div class="btn-wrapper">
          <i @click="isFullscreen = true"
             v-if="!isFullscreen"
             class="el-icon-full-screen"></i>
          <i @click="isFullscreen = false"
             v-else
             class="el-icon-copy-document"></i>
          <i @click="close()"
             class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 请选择充值客账 </span>
        </div>
      </template>
      <div class="guest-account-list">
        <div class="search-list search">
          <dart-search :formSpan="24"
                       :gutter="20"
                       ref="searchForm1"
                       :searchOperation='false'
                       label-position="right"
                       :model="search"
                       :fontWidth="2">
            <template slot="search-form"
                      style="padding-left: 10px">
              <dart-search-item label="用户名称:"
                                prop="userName">
                <el-input v-model="search.userName"
                          placeholder=""></el-input>
              </dart-search-item>
              <dart-search-item label="手机号码："
                                prop="mobile">
                <el-input v-model="search.mobile"
                          placeholder=""></el-input>
              </dart-search-item>
              <dart-search-item label="车牌号："
                                prop="mobile">
                <el-input v-model="search.carNo"
                          placeholder="请输入车牌号"></el-input>
              </dart-search-item>
              <dart-search-item :is-button="true"
                                :span="24"
                                :push="1"
                                style="margin-top: 10px">
                <el-button type="primary"
                           size="mini"
                           native-type="submit"
                           @click="onSearchHandle">查询</el-button>
                <el-button size="mini"
                           @click="onResultHandle">重置</el-button>
                <el-button @click="HandleRecharge()"
                           type="primary"
                           size="mini">
                  充值
                </el-button>
              </dart-search-item>
            </template>
          </dart-search>
        </div>
        <div class="table table-box">
          <el-table ref="multipleTable"
                    v-loading="loading"
                    :data="tableData"
                    :align="center"
                    :header-align="center"
                    border
                    :max-height="400"
                    style="width: 100%; margin-bottom: 20px"
                    :row-style="{ height: '54px' }"
                    :cell-style="{ padding: '0px' }"
                    :header-row-style="{ height: '54px' }"
                    :header-cell-style="{ padding: '0px' }"
                    row-key="id"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection"
                             width="55"></el-table-column>
            <el-table-column prop="userName"
                             align="center"
                             label="用户名"
                             min-width="180" />
            <el-table-column prop="idNo"
                             align="center"
                             label="证件号"
                             min-width="180" />
            <el-table-column prop="mobile"
                             align="center"
                             label="手机号"
                             min-width="180" />
            <el-table-column prop="amount"
                             align="center"
                             min-width="120"
                             label="账户余额">
              <template slot-scope="scope">
                ￥{{ scope.row.amount | moneyFilter }}
              </template>
            </el-table-column>
            <el-table-column prop="createTime"
                             align="center"
                             min-width="180"
                             label="创建时间" />
            <el-table-column prop="accountStatus_str"
                             align="center"
                             label="账户状态">
            </el-table-column>
          </el-table>
        </div>
        <div v-if="total > search.pageSize"
             class="pagination g-flex g-flex-center">
          <el-pagination background
                         :current-page="search.pageNum"
                         :page-size="search.pageSize"
                         layout="prev, pager, next, jumper"
                         :total="total"
                         @current-change="changePage" />
        </div>
      </div>

      <template slot="footer">
        <el-button @click="close()">关闭</el-button>
      </template>
    </el-dialog>
    <authorize-dialog :visible.sync="authorizeDialogVisible"
                      @handleConfirm="handleConfirm">
    </authorize-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import authorizeDialog from './authorizeDialog'
import { getSecuCode } from '@/utils/dialogUtils'
import { _ignoreNull, _ignoreEmpty } from '@/utils/utils'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    authorizeDialog,
  },
  watch: {
    orderId(val) {
      if (val && this.visible) {
        this.search.pageNum = 1
        this.getOpenAccountList()
      }
    },
    visible(val) {
      if (this.orderId && val) {
        this.search.pageNum = 1
        this.getOpenAccountList()
      }
    },
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      authorizeDialogVisible: false,
      //   dialogFormVisible: false,
      center: 'center',
      search: {
        userName: '',
        mobile: '',
        carNo: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: '',
      accountList: [],
      tableData: [],
    }
  },
  methods: {
    //查询开通客账用户列表
    getOpenAccountList() {
      this.loading = true
      //   let params = _ignoreEmpty(JSON.parse(JSON.stringify(this.search)))
      let params = JSON.parse(JSON.stringify(this.search))

      params.pageNum = this.search.pageNum
      params.pageSize = this.search.pageSize
      this.$request({
        url: this.$interfaces.openedAccountList,
        method: 'post',
        data: params,
      }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    reCharge(formData) {
      this.showLoading = true
      let params = {
        orderId: this.orderId,
        accountList: this.accountList,
        ...formData,
      }
      console.log('充值入参', params)
      this.$request({
        url: this.$interfaces.rechargeGuestAccount,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.showLoading = false
          this.$confirm(
            '订单号[' + res.data.orderId + ']' + res.data.payMessage,
            '提示',
            {
              showCancelButton: false,
              closeOnClickModal: false,
              closeOnPressEscape: false,
              showClose: false,
              confirmButtonText: '确定',
              type: 'success',
            }
          ).then(() => {
            this.accountList = []
            this.$refs.multipleTable.clearSelection()
            this.showLoading = true
            setTimeout(() => {
              this.$router.push({
                path: './payOrder',
                query: {
                  id: this.orderId,
                },
              })
              this.showLoading = false
            }, 2000)
          })
        })
        .catch((err) => {
          this.accountList = []
          this.$refs.multipleTable.clearSelection()
          this.showLoading = false
          console.log('err', err)
        })
    },
    HandleRecharge() {
      if (this.accountList.length === 0) {
        this.$message({
          type: 'warning',
          message: '请先选中一条记录！',
        })
        return
      }
      this.authorizeDialogVisible = true
    },
    handleConfirm(formData) {
      //处理金额和密码
      formData.money *= 100
      formData.password = getSecuCode(formData.password)
      this.reCharge(formData)
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getOpenAccountList()
    },
    onResultHandle() {
      // this.search.pageNum = 1
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
    },
    handleSelectionChange(val) {
      this.accountList = []
      this.accountList = val.map((item) => {
        return item.accountId
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getOpenAccountList()
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss"scoped >
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}
</style>
