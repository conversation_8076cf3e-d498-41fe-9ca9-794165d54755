<template>
  <div class="authorizeDialog">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :before-close="handleCloseIcon"
      :show-close="true"
      width="30%"
      title="充值"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="ruleForm"
      >
        <el-form-item label="充值金额(元)：" prop="money">
          <el-input
            type="number"
            v-model="ruleForm.money"
            placeholder="请输入充值金额"
            oninput="if(value.length>11)value=value.slice(0,11)"
          />
        </el-form-item>
        <el-form-item label="网点主管账户：" prop="userName">
          <el-input
            v-model="ruleForm.userName"
            placeholder="请输入网点主管账户"
          />
        </el-form-item>
        <el-form-item label="网点主管密码：" prop="password">
          <el-input
            type="password"
            v-model="ruleForm.password"
            auto-complete="new-password"
            placeholder="请输入网点主管密码"
          />
        </el-form-item>
        <!-- <template slot="footer"> -->
        <div class="bottom-btn g-flex g-flex-center">
          <el-button
            type="primary"
            @click="handleConfirm('ruleForm')"
            size="mini"
          >
            确认
          </el-button>
          <el-button @click="handleCloseIcon()" size="mini">关闭</el-button>
        </div>

        <!-- </template> -->
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      ruleForm: {
        money: '',
        userName: '',
        password: '',
      },
      rules: {
        money: [
          {
            required: true,
            message: '请输入充值金额',
            trigger: 'blur',
          },
          {
            pattern: /(^(([1-9]([0-9]+)?)|(0))(\.[0-9]{2})?$)/,
            message: '请输入正确的金额格式',
          },
        ],
        userName: [
          { required: true, message: '请输入网点主管账户', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入网点主管密码', trigger: 'blur' },
        ],
      },
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    handleConfirm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('handleConfirm', this.ruleForm)
          for (const key in this.ruleForm) {
            this.ruleForm[key] = ''
          }
          this.dialogFormVisible = false
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.form_dialog {
  margin-top: 50px;
}
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
</style>
