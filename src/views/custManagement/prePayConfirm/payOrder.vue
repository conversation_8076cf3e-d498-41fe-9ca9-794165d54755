<template>
  <div class="pay-confirm">
    <div class="search">
      <dart-search :formSpan="24"
                   :gutter="20"
                   ref="searchForm1"
                   label-position="right"
                   :model="search"
                   :fontWidth="2">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="用户编号："
                            prop="userNo">
            <el-input v-model="search.userNo"
                      placeholder="输入编号"></el-input>
          </dart-search-item>
          <dart-search-item label="支付状态："
                            prop="payStatus">
            <el-select v-model="search.payStatus"
                       placeholder="请选择">
              <el-option v-for="item in payStatus"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <!-- <dart-search-item label="退款状态：" prop="refoundStatus">
            <el-select v-model="search.carNoColor" placeholder="请选择">
              <el-option
                v-for="item in refoundStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item> -->
          <dart-search-item label="客户名称："
                            prop="userName">
            <el-input v-model="search.userName"
                      placeholder="输入名称"></el-input>
          </dart-search-item>
          <dart-search-item label="联系电话："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder="输入电话"></el-input>
          </dart-search-item>
          <dart-search-item label="公司名称："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder="输入名称"></el-input>
          </dart-search-item>
          <dart-search-item label="操作员："
                            prop="operatorName">
            <el-input v-model="search.operatorName"
                      placeholder="输入姓名"></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :span="22"
                            :push="1">
            <div class="btn-wrapper">
              <el-button type="primary"
                         size="mini"
                         native-type="submit"
                         @click="onSearchHandle"><i class="el-icon-search"></i> 搜索</el-button>
              <el-button size="mini"
                         @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table">
        <el-table v-loading="loading"
                  :data="tableData"
                  :align="center"
                  :header-align="center"
                  border
                  :max-height="550"
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '54px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '54px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id">
          <el-table-column prop="batchId"
                           align="center"
                           label="订单号" />
          <el-table-column prop="userNo"
                           align="center"
                           label="用户编号"
                           min-width="180" />
          <el-table-column prop="beforeAmount"
                           align="center"
                           min-width="120"
                           label="充值前金额">
            <template slot-scope="scope">
              ￥{{ scope.row.beforeAmount | moneyFilter }}
            </template>
          </el-table-column>
          <el-table-column prop="amount"
                           align="center"
                           min-width="120"
                           label="订单金额">
            <template slot-scope="scope">
              ￥{{ scope.row.amount | moneyFilter }}
            </template>
          </el-table-column>
          <el-table-column prop="afterAmount"
                           align="center"
                           min-width="120"
                           label="充值后金额">
            <template slot-scope="scope">
              ￥{{ scope.row.afterAmount | moneyFilter }}
            </template>
          </el-table-column>
          <el-table-column prop="payerAccount"
                           align="center"
                           label="付款方账号"
                           min-width="180"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="payerName"
                           align="center"
                           min-width="250"
                           label="付款方名称"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="beneficiaryAccount"
                           align="center"
                           label="收款方账号"
                           min-width="180"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="beneficiaryName"
                           align="center"
                           label="收款方开户名"
                           min-width="180"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="businessName"
                           align="center"
                           label="支付方式"
                           min-width="180"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="payStatus"
                           align="center"
                           label="支付状态">
            <template slot-scope="scope">
              <font v-if="scope.row.payStatus == '1'||scope.row.payStatus == '0'"
                    style="color: blue">{{
                getPayStatus(scope.row.payStatus)
              }}</font>
              <font v-if="scope.row.payStatus == '2'"
                    style="color: green">{{
                getPayStatus(scope.row.payStatus)
              }}</font>
              <font v-if="scope.row.payStatus == '3'"
                    style="color: red">{{
                getPayStatus(scope.row.payStatus)
              }}</font>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="operatorName" align="center" label="退款状态">
            <template slot-scope="scope">
              {{ getRefoundStatus(scope.row.refoundStatus) }}
            </template></el-table-column
          > -->
          <el-table-column prop="operatorName"
                           align="center"
                           label="操作员" />
          <el-table-column prop="companyName"
                           align="center"
                           min-width="180"
                           label="公司名称"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="userName"
                           align="center"
                           min-width="180"
                           label="客户名称"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="mobile"
                           align="center"
                           min-width="120"
                           label="联系电话" />
          <el-table-column prop="userLoginName"
                           align="center"
                           label="登录名"
                           :show-overflow-tooltip="true" />
          <el-table-column prop="createTime"
                           align="center"
                           min-width="160"
                           label="创建时间">
            <template slot-scope="scope">
              {{ scope.row.createTime }}
            </template>
          </el-table-column>
          <el-table-column prop="refoundStatus"
                           align="center"
                           min-width="160"
                           label="退款状态">
            <template slot-scope="scope">
              {{ scope.row.refoundStatus=='4'?'已冲正':'未冲正' }}
            </template>
          </el-table-column>
          <el-table-column fixed="right"
                           label="操作"
                           align="center">
            <template slot-scope="scope">
              <div>
                <el-button v-permisaction="['transfer:public/refound']"
                           v-if="scope.row.businessCode=='1000'&&scope.row.refoundStatus=='0'&&scope.row.payStatus=='2'"
                           @click="refundConfirm(scope.row.id)"
                           type="text"
                           size="small">冲正</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="total > search.pageSize"
             class="pagination">
          <el-pagination background
                         :current-page="search.pageNo"
                         :page-size="search.pageSize"
                         layout="prev, pager, next, jumper"
                         :total="total"
                         @current-change="changePage" />
        </div>
      </div>
      <div class="bottom-btn">
        <el-button size="mini"
                   @click="back">返回</el-button>
      </div>
    </div>
    <el-dialog title="授权"
               width="30%"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :show-close="true">
      <el-form ref="authorizationData"
               :rules="rules"
               :model="authorizationData"
               label-width="120px"
               class="demo-ruleForm">
        <!-- <el-row :gutter="24">
          <el-col :span="24"> -->
        <el-form-item class="my_form_label"
                      label="授权人账号"
                      prop="account">
          <el-input placeholder="请输入授权人账号"
                    v-model="authorizationData.account">
          </el-input>
        </el-form-item>
        <el-form-item class="my_form_label"
                      label="授权人密码"
                      prop="password">
          <el-input placeholder="请输入授权人密码"
                    type="password"
                    v-model="authorizationData.password">
          </el-input>
        </el-form-item>
        <div class="bottom-btn g-flex g-flex-center">
          <el-button @click="dialogVisible=false">关闭</el-button>

          <el-button type="primary"
                     @click="submitForm">
            确认
          </el-button>
        </div>
        <!-- </el-col>
        </el-row> -->
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      loading: false,
      center: 'center',
      total: '',
      search: {
        orderId: '',
        payStatus: '', //0订单创建 1：支付中 2：支付成功 3：支付失败
        refoundStatus: '', //退款状态 4：退款成功 5：退款失败
        userName: '', //用户名字
        mobile: '', //手机号
        userNo: '', //用户编号
        company: '', //公司名字
        operatorName: '', //操作员名字
        pageNum: 1,
        pageSize: 20,
      },
      payStatus: [
        { value: '0', label: '新建' },
        { value: '1', label: '支付中' },
        { value: '2', label: '支付成功' },
        { value: '3', label: '支付失败' },
      ],
      refoundStatus: [
        { value: '4', label: '新建' },
        { value: '5', label: '支付中' },
      ],
      tableData: [],
      authorizationData: {
        password: '',
        account: '',
      },
      dialogVisible: false,
      rules: {
        account: [
          { required: true, message: '请输入授权人账号', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入授权人密码', trigger: 'blur' },
        ],
      },
      id: '',
    }
  },
  created() {
    console.log('获取id', this.$route.query.id)
    this.search.orderId = this.$route.query.id
    this.getpayOrderList()
  },
  methods: {
    getPayStatus(value) {
      let payStatus = this.payStatus
      for (let i = 0; i < payStatus.length; i++) {
        if (payStatus[i].value == value) {
          return payStatus[i].label
        }
      }
      return ''
    },
    getRefoundStatus(value) {
      let refoundStatus = this.refoundStatus
      for (let i = 0; i < refoundStatus.length; i++) {
        if (refoundStatus[i].value == value) {
          return refoundStatus[i].label
        }
      }
      return ''
    },
    getpayOrderList() {
      this.loading = true
      console.log('入参params', this.search)
      this.$store
        .dispatch('custManagement/searchRechargeList', this.search)
        .then((res) => {
          this.loading = false
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getpayOrderList()
    },
    onResultHandle() {
      // this.search.pageNum = 1
      for (const key in this.search) {
        if (key !== 'orderId') this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
    },
    changePage(page) {
      this.search.pageNum = page
      this.getpayOrderList()
    },
    back() {
      //关闭当前页面包屑
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(
          (item) => item.path === this.$route.path
        ),
        1
      )
      console.log('当前路由', this.$route.path)
      this.$router.push({
        path: './prePayConfirm',
        replace: true,
      })
    },
    confirmDilog(msg, type) {
      return new Promise((resolve, reject) => {
        this.$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    //冲正前弹框确认
    refundConfirm(id) {
      this.id = id
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '冲正前需主管授权，请确认是否冲正?'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.dialogVisible = true
            // _this.HandleRefound(id)
          }
        },
      })
    },
    //冲正授权弹框
    submitForm() {
      this.$refs['authorizationData'].validate((valid) => {
        if (valid) {
          this.authDate()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    authDate() {
      let data = JSON.parse(JSON.stringify(this.authorizationData))
      request({
        url: api.transferRefoundValidate,
        method: 'post',
        data: data,
      }).then((res) => {
        if (res && res.code == 200) {
          this.HandleRefound()
        }
      })
    },
    //退款
    HandleRefound() {
      this.loading = true
      let params = {
        orderId: this.id,
      }
      request({
        url: api.transferRefound,
        method: 'post',
        data: params,
      }).then((res) => {
        this.loading = false
        if (res.code == 200) {
          this.dialogVisible = false
          this.authorizationData.account = ''
          this.authorizationData.password = ''
          this.getpayOrderList()
          if (res.data.status == '4') {
            this.$message({
              message: '冲正成功',
              type: 'success',
            })
          } else {
            this.$message({
              message: '冲正失败',
              type: 'error',
            })
          }
        }
        console.log(res)
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.pay-confirm {
  padding: 20px;
  .table {
    margin: 0px 0 10px 0;
    // height: 655px;
  }
}
</style>