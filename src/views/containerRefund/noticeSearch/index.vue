<template>
  <div class="notice-search" v-loading.fullscreen.lock="fullscreenLoading">
    <div class="search">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="推送编号：" prop="containerRefundNoticeId">
            <el-input
              v-model="search.containerRefundNoticeId"
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="推送方向：" prop="handleParty">
            <el-select v-model="search.handleParty" placeholder="请选择">
              <el-option
                v-for="item in noticeType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="推送状态：" prop="result">
            <el-select v-model="search.result" placeholder="请选择">
              <el-option
                v-for="item in noticeResult"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="卡号：" prop="cardNo">
            <el-input v-model="search.cardNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="车牌：" prop="carNo">
            <el-input v-model="search.carNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="carNoColor">
            <el-select v-model="search.carNoColor" placeholder="请选择">
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            label="退费申请单编号："
            prop="containerRefundMastId"
            class="nowrap"
          >
            <el-input
              v-model="search.containerRefundMastId"
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="24">
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        :max-height="560"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column prop="id" align="center" label="编号" />
        <el-table-column
          prop="containerRefundMastId"
          align="center"
          label="退费申请单号"
          min-width="105"
        />
        <el-table-column
          prop="type"
          align="center"
          min-width="125"
          label="推送方向"
        >
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.type, 'getNoticeType') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="handleMode"
          align="center"
          min-width="105"
          label="已处理方式"
        >
          <template slot-scope="scope">
            {{
              typeAdapter(scope.row.handleMode, 'getContainerRefundHandleMode')
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="handleParty"
          align="center"
          min-width="105"
          label="已处理机构"
        >
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.handleParty, 'getHandlePartyType') }}
          </template>
        </el-table-column>
        <el-table-column prop="custName" align="center" label="客户名称" />
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="180"
          label="卡号"
        />
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column prop="carColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.carColor, 'getVehicleColor') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="operateTime"
          align="center"
          min-width="180"
          label="操作时间"
        />
        <el-table-column
          prop="createTime"
          align="center"
          min-width="180"
          label="创建时间"
        />
        <el-table-column
          prop="feeStr"
          align="center"
          min-width="180"
          label="末次推送时间"
        />
        <el-table-column
          prop="count"
          align="center"
          min-width="105"
          label="已推送次数"
        />
        <el-table-column prop="result" align="center" label="推送状态">
          <template slot-scope="scope">
            {{ typeAdapter(scope.row.result, 'getNoticeResult') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          align="center"
          min-width="300"
          label="推送结果明细"
          :show-overflow-tooltip="true"
        />
        <el-table-column prop="operatorName" align="center" label="推送人" />
        <el-table-column fixed="right" label="推送按钮" width="100">
          <template slot-scope="scope">
            <el-button
              @click.native.prevent="rePushHandle(scope.row.id)"
              type="text"
              size="small"
            >
              重新推送
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div v-if="total > search.pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="search.pageNo"
          :page-sizes="[10, 20, 50]"
          :page-size="search.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  licenseColorOption,
  noticeType,
  noticeResult,
} from '@/common/const/optionsData'
import { typeAdapter } from '@/common/method/formatOptions'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      fullscreenLoading: false,
      loading: false,
      licenseColorOption,
      noticeType,
      noticeResult,
      center: 'center',
      total: '',
      search: {
        carColor: '',
        carNo: '',
        cardNo: '',
        containerRefundMastId: '',
        containerRefundNoticeId: '',
        handleParty: '',
        pageNo: 1,
        pageSize: 10,
        result: '',
      },
      tableData: [],
    }
  },
  created() {
    this.getNoticeList()
  },
  methods: {
    typeAdapter,
    getNoticeList() {
      this.loading = true
      // console.log('prams入参', params)
      this.$store
        .dispatch('containerRefund/containerRefundNoticeSearch', this.search)
        .then((res) => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    changePage(page) {
      this.search.pageNo = page
      this.getNoticeList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getNoticeList()
    },
    onSearchHandle() {
      this.search.pageNo = 1
      console.log('当前页面notice', this.search.pageNo)
      this.getNoticeList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNo = 1
      this.search.pageSize = 10
    },
    rePushHandle(id) {
      this.fullscreenLoading = true
      this.$store
        .dispatch('containerRefund/reSendNotice', { id: id })
        .then((res) => {
          this.fullscreenLoading = false
          this.$message({
            message: '重新推送成功！',
            type: 'success',
          })
          this.getNoticeList()
        })
        .catch((err) => {
          this.fullscreenLoading = false
          console.log('err', err)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.notice-search {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
  }
  .nowrap {
    white-space: nowrap;
  }
  .btn-wrapper {
    margin-left: 40px;
    margin-top: 10px;
  }
}
</style>