<template>
  <div class="handle-refund" v-loading.fullscreen.lock="showLoading">
    <input
      type="file"
      id="myInput"
      ref="myInput"
      class="imagebase"
      v-show="false"
      :accept="imgListType"
      @change="getImgBase"
    />
    <div class="section-1 margin-top">
      <el-descriptions
        :title="'申请单号[' + id + ']>>>退款账户信息'"
        :column="2"
        border
        class="desc-wrapper"
      >
        <el-descriptions-item label="卡主姓名：">{{
          containerRefundMast.bankCustName
        }}</el-descriptions-item>
        <el-descriptions-item label="开户行名称：">{{
          containerRefundMast.bankName
        }}</el-descriptions-item>
        <el-descriptions-item label="银行卡号：">{{
          containerRefundMast.bankCardNo
        }}</el-descriptions-item>
        <el-descriptions-item label="是否已现场减半：">{{
          getHalfFlag(containerRefundMast.halfFlag)
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="section-2 margin-top">
      <el-descriptions
        class="margin-top desc-wrapper"
        :title="'客户信息'"
        :column="2"
        border
      >
        <el-descriptions-item label="客户编号：">{{
          custMast.custCode
        }}</el-descriptions-item>
        <el-descriptions-item label="客户名称：">{{
          custMast.custName
        }}</el-descriptions-item>
        <el-descriptions-item label="证件类型：">{{
          typeAdapter(custMast.custIdTypeCode, 'getPersonalOCRType')
        }}</el-descriptions-item>
        <el-descriptions-item>
          <template slot="label"> 证件号码： </template>
          {{ custMast.custIdNo }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="section-3 margin-top">
      <el-descriptions
        class="margin-top desc-wrapper"
        :title="'车辆信息'"
        :column="2"
        border
      >
        <el-descriptions-item label="卡号：">{{
          containerRefundMast.cardNo
        }}</el-descriptions-item>
        <el-descriptions-item label="OBU合同序列号：">{{
          ObuNo
        }}</el-descriptions-item>
        <el-descriptions-item label="车牌号码：">{{
          containerRefundMast.carNo
        }}</el-descriptions-item>
        <el-descriptions-item label="车牌颜色：">{{
          typeAdapter(containerRefundMast.carNoColor, 'getVehicleColor')
        }}</el-descriptions-item>
        <el-descriptions-item label="客货标志：">
          {{ typeAdapter(cardMast.isTrunk, 'getVehicleType') }}
        </el-descriptions-item>
        <el-descriptions-item label="车型：">
          {{ typeAdapter(cardMast.carType, 'getCarType') }}
        </el-descriptions-item>
        <el-descriptions-item label="卡片类型：">{{
          typeAdapter(containerRefundMast.cardType, 'getCpuCardType')
        }}</el-descriptions-item>
        <el-descriptions-item label="区内卡类型：">{{
          typeAdapter(containerRefundMast.gxCarType, 'getGxCardType')
        }}</el-descriptions-item>
        <el-descriptions-item label="车辆种类：">{{
          carStyleStr
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="section-4 margin-top">
      <el-descriptions class="margin-top desc-wrapper" :column="2" border>
        <template slot="title">
          交易明细
          <el-button @click="showTransactionDialog" type="primary" size="small"
            >明细</el-button
          >
        </template>
        <el-descriptions-item label="申请单号：">{{ id }}</el-descriptions-item>
        <el-descriptions-item label="交易编号：">{{
          containerRefundMast.transactionId
        }}</el-descriptions-item>
        <el-descriptions-item label="入口时间：">{{
          containerRefundMast.enTime
        }}</el-descriptions-item>
        <el-descriptions-item label="入口站：">{{
          containerRefundMast.enStation
        }}</el-descriptions-item>
        <el-descriptions-item label="出口时间：">{{
          containerRefundMast.exTime
        }}</el-descriptions-item>
        <el-descriptions-item label="出口站：">{{
          containerRefundMast.exStation
        }}</el-descriptions-item>
        <el-descriptions-item label="交易时间：">{{
          containerRefundMast.time
        }}</el-descriptions-item>
        <el-descriptions-item label="交易金额（元）：">{{
          containerRefundMast.feeStr
        }}</el-descriptions-item>
        <el-descriptions-item label="申请时间：">{{
          containerRefundMast.applyTime
        }}</el-descriptions-item>
        <el-descriptions-item label="预约时间：">{{
          containerRefundMast.appointTime
        }}</el-descriptions-item>
        <el-descriptions-item label="记账时间：">{{
          containerRefundMast.accountingTime
        }}</el-descriptions-item>
        <el-descriptions-item label="退费交易编号：">{{
          containerRefundMast.refundDetailId
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="section-4 margin-top">
      <el-descriptions class="margin-top desc-wrapper" :column="1" border>
        <template slot="title"> 证明资料 </template>
        <el-descriptions-item
          v-for="index in 5"
          :key="index"
          :label="'证明资料[' + index + ']：'"
        >
          <el-image
            v-if="index - 1 == 0 && imgUpdateList['filePathData']"
            style="width: 200px; height: 200px"
            :src="imgUpdateList['filePathData']"
            :preview-src-list="srcList"
            :fit="'cover'"
          >
          </el-image>
          <el-button
            @click="delImg(index - 1)"
            style="margin-left: 10px; margin-bottom: 6px"
            v-if="index - 1 == 0 && imgUpdateList['filePathData']"
            type="danger"
            size="mini"
            plain
            >删除</el-button
          >
          <el-button
            @click="openFDialog(index - 1)"
            style="margin: 10px"
            v-if="index - 1 == 0 && !imgUpdateList['filePathData']"
            size="medium"
            plain
            >上传图片</el-button
          >
          <el-image
            v-if="
              imgUpdateList[
                'filePathData' + String.fromCharCode(65 + (index - 1))
              ]
            "
            style="width: 200px; height: 200px"
            :src="
              imgUpdateList[
                'filePathData' + String.fromCharCode(65 + (index - 1))
              ]
            "
            :preview-src-list="srcList"
            :fit="'cover'"
          >
          </el-image
          ><el-button
            @click="delImg(index - 1)"
            style="margin-left: 10px; margin-bottom: 6px"
            v-if="
              imgUpdateList[
                'filePathData' + String.fromCharCode(65 + (index - 1))
              ]
            "
            type="danger"
            size="mini"
            plain
            >删除</el-button
          >
          <el-button
            @click="openFDialog(index - 1)"
            style="margin: 10px"
            v-if="
              index - 1 != 0 &&
              !imgUpdateList[
                'filePathData' + String.fromCharCode(65 + (index - 1))
              ]
            "
            size="medium"
            plain
            >上传图片</el-button
          >
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :labelStyle="{ width: '201px' }" :column="1" border>
        <el-descriptions-item label="备注">{{
          containerRefundMast.remark
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="section-4 margin-top">
      <el-descriptions class="margin-top desc-wrapper" :column="2" border>
        <template slot="title">处理进度</template>
        <el-descriptions-item label="处理机构：">
          {{
            typeAdapter(containerRefundMast.handleParty, 'getHandlePartyType')
          }}</el-descriptions-item
        >
        <el-descriptions-item label="申请状态：">
          {{
            typeAdapter(
              containerRefundMast.handleMode,
              'getContainerRefundHandleMode'
            )
          }}</el-descriptions-item
        >
        <el-descriptions-item label="处理意见：">
          <textarea
            v-model="remark"
            class="textarea"
            name="suggestion"
            id=""
            cols="100"
            rows="10"
            placeholder="请输入处理意见"
          ></textarea>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="btn-wrapper">
      <template v-if="type == 'update'">
        <el-button
          v-permisaction="['containerRefund:containerRefundAuditUpdatePhotos']"
          class="btn"
          @click="pushHandle"
          type="primary"
          >保存并推送至路方审核</el-button
        >
      </template>
      <el-button
        class="btn"
        v-if="dataList.pass == 1"
        @click="passHandle"
        type="primary"
        >通过</el-button
      >
      <el-button
        class="btn"
        v-if="dataList.refuse == 1"
        contextmenu=""
        @click="refuseHandle"
        type="danger"
        >驳回</el-button
      >
      <el-button class="btn" @click="cancelHandle">返回</el-button>
    </div>
    <transactionDetail
      ref="transaction"
      v-if="dataList"
      :dialogFormVisible.sync="dialogFormVisible"
      :transactionId="transactionId"
      :cardNo="cardNo"
    ></transactionDetail>
  </div>
</template>
<script>
import { typeAdapter, getHalfFlag } from '@/common/method/formatOptions'
import transactionDetail from './transactionDetail'
import api from '@/api/index'
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  // props: {
  //   id: {
  //     type: Number,
  //   },
  // },
  components: {
    transactionDetail,
  },
  data() {
    return {
      showLoading: false,
      dialogFormVisible: false,
      id: '',
      srcList: [],
      dataList: {},
      cardMast: {},
      containerRefundMast: {},
      custMast: {},
      transactionId: '',
      cardNo: '',
      ObuNo: '',
      carStyleStr: '',
      remark: '',
      imgUpdateList: {
        issueApplyId: '',
        filePathData: '',
        filePathDataB: '',
        filePathDataC: '',
        filePathDataD: '',
        filePathDataE: '',
        code1: '',
        code2: '',
        code3: '',
        code4: '',
        code5: '',
      },
      imgListType: [
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/png',
        'image/bmp',
      ],
      index: '', //图片索引
      type: '', //路由操作类型
    }
  },
  created() {
    console.log('获取id', this.$route.query.id)
    this.id = this.$route.query.id
    this.type = this.$route.query.type
    this.getContainerRefundAuditDetail()
  },
  methods: {
    typeAdapter,
    getHalfFlag,
    getContainerRefundAuditDetail() {
      this.showLoading = true
      let params = {
        handleId: this.id,
      }
      console.log('入参', params)
      this.$store
        .dispatch('containerRefund/getContainerRefundAuditDetailList', params)
        .then((res) => {
          this.showLoading = false
          console.log('list列表', res)
          this.dataList = res
          this.containerRefundMast = res.containerRefundMast
          this.custMast = res.custMast
          this.cardMast = res.cardMast
          this.cardNo = res.containerRefundMast.cardNo
          this.ObuNo = res.ObuNo
          this.carStyleStr = res.carStyleStr
          //更新图片赋值id
          this.imgUpdateList.issueApplyId = this.id
          //处理预览图片
          for (let i = 0; i < 5; i++) {
            let headStr = 'filePathData'
            let lastStr = String.fromCharCode(65 + i)
            let joinStr = headStr + lastStr
            if (i === 0) {
              res[headStr] &&
                this.srcList.push(res.containerRefundMast[headStr])
              this.imgUpdateList[headStr] =
                res.containerRefundMast[headStr] || ''
            } else {
              res[joinStr] &&
                this.srcList.push(res.containerRefundMast[joinStr])
              this.imgUpdateList[joinStr] =
                res.containerRefundMast[joinStr] || ''
            }
            // if (i === 0) {
            //   this.srcList[i] = res.containerRefundMast[headStr] || ''
            // } else {
            //   this.srcList[i] = res.containerRefundMast[joinStr] || ''
            // }

            console.log('imgUpdateList', this.imgUpdateList)
          }
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    openFDialog(index) {
      this.index = index
      let _self = this
      this.$nextTick(() => {
        console.log('_self.$refs', this.$refs.myInput)
        _self.$refs.myInput.value = ''
        _self.$refs.myInput.click()
      })
    },
    delImg(index) {
      this.$confirm('此操作将删除该图片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          //删除操作
          let imgList = JSON.parse(JSON.stringify(this.imgUpdateList))
          if (index == 0) {
            imgList['filePathData'] = ''
            imgList['code1'] = ''
          } else {
            //B-E
            imgList['filePathData' + String.fromCharCode(65 + index)] = ''
            imgList['code' + (index + 1)] = ''
          }
          this.srcList[index] = ''
          this.imgUpdateList = imgList
          console.log('imgUpdateList', this.srcList)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    //打开交易明细并加载数据。
    showTransactionDialog() {
      this.transactionId = this.containerRefundMast.transactionId
      this.dialogFormVisible = true
    },
    validata() {
      console.log('remarkkkkk', this.remark)
      if (!this.remark) {
        console.log('请先填写处理意见！', this.remark)
        this.$confirm('请先填写处理意见！', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning',
        })
        return false
      }
      return true
    },
    //保存图片推送路方
    pushHandle() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.containerRefundAuditUpdatePhotos,
        method: 'post',
        data: this.imgUpdateList,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('修改图片推送成功')
            this.getContainerRefundAuditDetail()
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //保存图片推送路方
    saveImg() {
      this.$request({
        url: this.$interfaces.containerRefundAuditUpdatePhotos,
        method: 'post',
        data: this.imgUpdateList,
      })
        .then((res) => {})
        .catch((err) => {})
    },
    //通过操作
    passHandle() {
      if (this.validata()) {
        this.showLoading = true
        let params = { id: this.id, remark: this.remark }
        this.$store
          .dispatch('containerRefund/containerRefundPass', params)
          .then((res) => {
            this.showLoading = false
            //处理退费通过操作
            this.$confirm('操作通过成功！', '成功提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'success',
            }).then(() => {
              //返回首页
              this.back()
            })
            this.saveImg()
          })
          .catch((err) => {
            this.showLoading = false
          })
      }
    },
    //驳回操作
    refuseHandle() {
      if (this.validata()) {
        this.showLoading = true
        let params = { id: this.id, remark: this.remark }
        this.$store
          .dispatch('containerRefund/containerRefundRefuse', params)
          .then((res) => {
            this.showLoading = false
            //处理退费通过操作
            this.$confirm('操作驳回成功！', '成功提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'success',
            }).then(() => {
              //返回首页
              this.back()
            })
            this.saveImg()
          })
          .catch((err) => {
            this.showLoading = false
          })
      }
    },
    upload(obj) {
      let _this = this
      let param = {
        image: obj.imgBase64,
        photoCode: obj.photo_code,
      }
      let url = process.env.VUE_APP_BASE_API + '/issue-web' + api.cacheImgUplaod
      let config = {
        headers: {
          Authorization: getToken(),
        },
      } //添加请求头
      axios
        .post(url, param, config)
        .then((response) => {
          console.log(response)
          if (response.data.code == 200) {
            // let result = response.data.data
            // this.changelist(result.photoCode, result)
            console.log('response', response.data)
            let imgList = JSON.parse(JSON.stringify(this.imgUpdateList))
            this.srcList[this.index] = response.data.data.fileUrl
            if (this.index == 0) {
              imgList['filePathData'] = response.data.data.fileUrl
              imgList['code1'] = response.data.data.code
            } else {
              //B-E
              imgList['filePathData' + String.fromCharCode(65 + this.index)] =
                response.data.data.fileUrl
              imgList['code' + (this.index + 1)] = response.data.data.code
            }
            this.imgUpdateList = imgList
            console.log('this.imgUpdateList', this.imgUpdateList)
          } else {
            this.$msgbox({
              title: '提示',
              message: response.data.msg,
              customClass: 'my_msgBox singelBtn',
              confirmButtonText: '确定',
              type: 'error',
            })
          }
        })
        .catch((err) => {
          _this.$alert(err.message, '提示', {
            dangerouslyUseHTMLString: true,
            showClose: false,
            confirmButtonText: '确定',
          })
        })
    },
    getImgBase() {
      let _this = this
      let event = event || window.event
      let file = event.target.files[0]
      if (parseInt(file.size / 1024 / 1024) >= 20) {
        // 超过10M，提示无法上传
        this.$msgbox({
          title: '温馨提示',
          message: '上传图片大小不得超过20M',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      if (this.imgListType.indexOf(file.type) === -1) {
        this.$msgbox({
          title: '温馨提示',
          message: '请上传图片格式的文件',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      var maxSize = 400 * 1024
      this.changeImage(file, maxSize, this.getFileBase64)
      return
    },
    changeImage(file, size, callback, type) {
      let _self = this
      if (file.size > size) {
        // 文件大于size 则进行压缩处理
        var reader = new FileReader()
        reader.readAsDataURL(file)
        var img = new Image()
        reader.onload = function (e) {
          console.log('reader', e)
          img.src = e.target.result
          img.onload = function () {
            var data = _self.compress(img)
            console.log('压缩后', data)
            var text = window.atob(data.split(',')[1])
            var buffer = new Uint8Array(text.length)
            for (var i = 0; i < text.length; i++) {
              buffer[i] = text.charCodeAt(i)
            }
            console.log('文件类型', file.type)
            var rev = _self.getBlob([buffer], file.type)
            console.log('压缩转码完成', rev)
            var rfile = new File([rev], file.name)
            callback(rfile, type)
          }
        }
      } else {
        callback(file, type)
      }
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function () {
          imgResult = reader.result
        }
        reader.onerror = function (error) {
          reject(error)
        }
        reader.onloadend = function () {
          resolve(imgResult)
        }
      })
    },
    getFileBase64(file) {
      let _this = this
      this.getBase64(file).then((res) => {
        const param = {
          imgBase64: res,
          scene: this.scene,
          photo_code: this.photo_code,
        }
        _this.upload(param)
      })
    },
    compress(img) {
      //    用于压缩图片的canvas
      var canvas = document.createElement('canvas')
      var ctx = canvas.getContext('2d')
      //    瓦片canvas
      var tCanvas = document.createElement('canvas')
      var tctx = tCanvas.getContext('2d')
      var initSize = img.src.length
      var width = img.width
      var height = img.height
      // 如果图片大于四百万像素，计算压缩比并将大小压至400万以下
      var ratio
      if ((ratio = (width * height) / 4000000) > 1) {
        ratio = Math.sqrt(ratio)
        width /= ratio
        height /= ratio
      } else {
        ratio = 1
      }
      canvas.width = width
      canvas.height = height
      //        铺底色
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      // 如果图片像素大于100万则使用瓦片绘制
      var count
      if ((count = (width * height) / 1000000) > 1) {
        count = ~~(Math.sqrt(count) + 1) // 计算要分成多少块瓦片
        //            计算每块瓦片的宽和高
        var nw = ~~(width / count)
        var nh = ~~(height / count)
        tCanvas.width = nw
        tCanvas.height = nh
        for (var i = 0; i < count; i++) {
          for (var j = 0; j < count; j++) {
            tctx.drawImage(
              img,
              i * nw * ratio,
              j * nh * ratio,
              nw * ratio,
              nh * ratio,
              0,
              0,
              nw,
              nh
            )
            ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh)
          }
        }
      } else {
        ctx.drawImage(img, 0, 0, width, height)
      }
      // 进行最小压缩
      var ndata = canvas.toDataURL('image/jpeg', 0.5)
      console.log('压缩前：' + initSize)
      console.log('压缩后：' + ndata.length)
      console.log(
        '压缩率：' + ~~((100 * (initSize - ndata.length)) / initSize) + '%'
      )
      tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0
      return ndata
    },
    getBlob(buffer, format) {
      try {
        return new Blob(buffer, { type: format })
      } catch (e) {
        var bb = new (window.BlobBuilder ||
          window.WebKitBlobBuilder ||
          window.MSBlobBuilder)()
        buffer.forEach(function (buf) {
          bb.append(buf)
        })
        return bb.getBlob(format)
      }
    },
    dataUrlToBlob(dataURI) {
      let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0] // mime类型
      let byteString = atob(dataURI.split(',')[1]) //base64 解码
      let arrayBuffer = new ArrayBuffer(byteString.length) //创建缓冲数组
      let intArray = new Uint8Array(arrayBuffer) //创建视图
      for (let i = 0; i < byteString.length; i++) {
        intArray[i] = byteString.charCodeAt(i)
      }
      return new Blob([intArray], { type: mimeString })
    },
    //返回前一页并关闭当前页面包屑
    back() {
      //关闭当前页面包屑
      this.$store.state.tagsView.visitedViews.splice(
        this.$store.state.tagsView.visitedViews.findIndex(
          (item) => item.path === this.$route.path
        ),
        1
      )
      console.log('当前路由', this.$route.path)
      this.$router.push({
        path: './auditSearch',
        replace: true,
      })
    },
    cancelHandle() {
      this.back()
    },
  },
}
</script>
<style lang="scss" scoped>
.margin-top {
  margin-top: 20px;
}
.btn-wrapper {
  margin: 30px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  .btn {
    margin-right: 15px;
  }
}
.textarea {
  border-color: #e8e8e8;
  padding: 10px;
}

.desc-wrapper ::v-deep {
  .el-descriptions__body {
    .el-descriptions__table {
      border-collapse: inherit;
      .el-descriptions-row {
        .el-descriptions-item__content {
          white-space: nowrap;
          height: 30px;
          padding: 0 10px;
        }
        .el-descriptions-item__label {
          width: 200px;
          height: 30px;
          padding: 0 10px;
        }
      }
    }
  }
}
</style>
