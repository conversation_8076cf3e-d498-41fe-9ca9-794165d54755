<template>
  <!-- <div
    class="account-dialog"
    v-loading.fullscreen.lock="showLoading"
    :key="orderId"
  > -->
  <div class="account-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 集装箱退费操作记录 </span>
        </div>
      </template>
      <div class="search-list">
        <dart-search
          :formSpan="24"
          :gutter="20"
          ref="searchForm1"
          label-position="right"
          :model="search"
          :fontWidth="2"
        >
          <template slot="search-form" style="padding-left: 10px">
            <dart-search-item label="申请单号：" prop="id">
              <el-input v-model="search.id" placeholder="" clearable></el-input>
            </dart-search-item>
            <dart-search-item label="车牌颜色：" prop="carNoColor">
              <el-select
                v-model="search.carNoColor"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in licenseColorOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="车牌：" prop="carNo">
              <el-input
                v-model="search.carNo"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <template v-if="isCollapse">
              <dart-search-item label="处理机构：" prop="handleParty">
                <el-select
                  v-model="search.handleParty"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in handlePartyList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    clearable
                  />
                </el-select>
              </dart-search-item>
              <dart-search-item label="申请状态：" prop="handleMode">
                <el-select
                  v-model="search.handleMode"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in containerRefundHandleMode"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </dart-search-item>
              <dart-search-item label="操作人：" prop="carNo">
                <el-input
                  v-model="search.operator"
                  placeholder=""
                  clearable
                ></el-input>
              </dart-search-item>
              <dart-search-item label="操作网点：" prop="carNo">
                <el-input
                  v-model="search.deptName"
                  placeholder=""
                  clearable
                ></el-input>
              </dart-search-item>
              <dart-search-item label="操作时间：" prop="value1">
                <el-date-picker
                  v-model="timeArr"
                  type="daterange"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </dart-search-item>
            </template>
            <dart-search-item
              :is-button="true"
              :span="24"
              :push="1"
              style="margin-top: 10px"
            >
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                >查询</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
              <el-button size="mini" type="primary" @click="onExportHandle"
                ><i class="el-icon-download"></i> 导出</el-button
              >
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
            </dart-search-item>
          </template>
        </dart-search>
      </div>
      <div class="table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          style="width: 100%; margin-bottom: 20px"
          :max-height="600"
          :row-style="{ height: '40px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '40px' }"
          :header-cell-style="{ padding: '0px' }"
        >
          <!-- <el-table-column label="选择" width="50" align="center">
            <template slot-scope="scope">
              <el-radio
                v-model="radio"
                :label="scope.$index"
                @change="getCurrentRow(scope.row.id, scope.row.transactionId)"
              >
                <span></span>
              </el-radio>
            </template>
          </el-table-column> -->
          <!-- <el-table-column prop="id" align="center" label="申请单号"
            ><template slot-scope="scope">
              <span @click="showHandleDetail(scope.row.id)" class="text">{{
                scope.row.id
              }}</span>
            </template></el-table-column
          > -->
          <el-table-column prop="id" align="center" label="申请单号" />
          <el-table-column
            prop="halfFlag"
            align="center"
            min-width="80"
            label="是否现场减半"
          >
            <template slot-scope="scope">
              {{ getHalfFlag(scope.row.halfFlag) }}
            </template>
          </el-table-column>
          <el-table-column prop="custCode" align="center" label="客户编号" />
          <el-table-column
            prop="sern"
            align="center"
            min-width="100"
            label="客户名称"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.custName }}
                </div>
                <span> {{ scope.row.custName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="cardNo"
            align="center"
            min-width="180"
            label="卡号"
          />
          <el-table-column
            prop="carNo"
            align="center"
            min-width="100"
            label="车牌号"
          />
          <el-table-column prop="carNoColor" align="center" label="车牌颜色">
            <template slot-scope="scope">
              {{ getVehicleColor(scope.row.carNoColor) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enStation"
            align="center"
            min-width="100"
            label="入口站"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.enStation }}
                </div>
                <span> {{ scope.row.enStation }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="enTime"
            align="center"
            min-width="160"
            label="入口时间"
            sortable
          />
          <el-table-column
            prop="exStation"
            align="center"
            min-width="100"
            label="出口站"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.exStation }}
                </div>
                <span> {{ scope.row.exStation }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="exTime"
            align="center"
            min-width="160"
            label="出口时间"
            sortable
          />
          <el-table-column
            prop="feeStr"
            align="center"
            min-width="105"
            label="交易金额(元)"
          />
          <el-table-column
            prop="time"
            align="center"
            min-width="160"
            label="交易时间"
            sortable
          />
          <el-table-column
            prop="accountingTime"
            align="center"
            label="记账时间"
            min-width="160"
            sortable
          />
          <el-table-column
            prop="applyTime"
            align="center"
            min-width="160"
            label="申请时间"
            sortable
          />
          <el-table-column prop="handleParty" align="center" label="处理机构">
            <template slot-scope="scope">
              {{ getHandlePartyType(scope.row.handleParty + '') }}
            </template>
          </el-table-column>
          <el-table-column prop="handleMode" align="center" label="申请状态">
            <template slot-scope="scope">
              {{ getContainerRefundHandleMode(scope.row.handleMode + '') }}
            </template>
          </el-table-column>
          <el-table-column
            prop="transactionId"
            align="center"
            min-width="150"
            label="交易编号"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.transactionId }}
                </div>
                <span> {{ scope.row.transactionId }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column
            prop="appointTime"
            align="center"
            min-width="180"
            label="预约时间"
          /> -->
          <el-table-column
            prop="operateTime"
            align="center"
            min-width="180"
            label="操作时间"
            sortable
          />
          <el-table-column
            prop="operator"
            align="center"
            min-width="120"
            label="操作人"
          />
          <el-table-column
            prop="deptName"
            align="center"
            min-width="150"
            label="操作网点"
          />
          <el-table-column
            prop="sern"
            align="center"
            min-width="150"
            label="处理意见"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.remark }}
                </div>
                <span> {{ scope.row.remark }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="appointTime"
            align="center"
            min-width="180"
            label="预约时间"
            sortable
          />
          <el-table-column
            prop="refundDetailId"
            align="center"
            label="退费明细编号"
            min-width="110"
          />
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="changePage"
            :current-page="search.pageNo"
            :page-sizes="[10, 20, 50]"
            :page-size="search.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <template slot="footer">
        <!-- <el-button @click="HandleRecharge()" type="primary" size="mini">
          充值
        </el-button> -->
        <el-button @click="close()" size="mini">关闭</el-button></template
      >
    </el-dialog>
    <!-- <authorize-dialog
      :visible.sync="authorizeDialogVisible"
      @handleConfirm="handleConfirm"
    >
    </authorize-dialog> -->
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
// import authorizeDialog from './authorizeDialog'
// import { getSecuCode } from '@/utils/dialogUtils'
import {
  licenseColorOption,
  // containerRefundHandleMode,
  halfFlagList,
  handlePartyList,
} from '@/common/const/optionsData'
import {
  getHandlePartyType,
  getHalfFlag,
  getVehicleColor,
  getContainerRefundHandleMode,
} from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    // orderId: {
    //   type: String,
    // },
  },
  components: {
    dartSearch,
    dartSearchItem,
    // authorizeDialog,
  },
  watch: {
    // orderId(val) {
    //   if (val) {
    //     this.search.pageNum = 1
    //     this.getAccountList()
    //   }
    // },
  },
  data() {
    return {
      licenseColorOption,
      containerRefundHandleMode: [
        { value: '', label: '全部' },
        { value: '0', label: '待申请' },
        { value: '1', label: '审核中' },
        { value: '2', label: '已通过' },
        { value: '3', label: '已驳回' },
        { value: '4', label: '退费(处理)中' },
        { value: '5', label: '退费(退款)中' },
        { value: '6', label: '已退费' },
        { value: '7', label: '退费失败' },
        { value: '8', label: '用户放弃' },
      ],
      halfFlagList,
      handlePartyList,
      loading: false,
      isFullscreen: false,
      showLoading: false,
      isCollapse: false,
      // authorizeDialogVisible: false,
      //   dialogFormVisible: false,
      center: 'center',
      timeArr: [],
      search: {
        id: '', //申请单号，
        carNoColor: '',
        carNo: '',
        handleMode: '',
        handleParty: '',
        deptName: '',
        operator: '',
        timeStart: '', //交易时间起始
        timeEnd: '', //交易时间结束
        pageNo: 1,
        pageSize: 10,
      },
      total: 0,
      // userNoList: [],
      tableData: [],
    }
  },
  methods: {
    getHandlePartyType,
    getHalfFlag,
    getVehicleColor,
    getContainerRefundHandleMode,
    getContainerRefundList() {
      this.loading = true
      let params = { ...this.search }
      params.timeStart =
        this.timeArr && this.timeArr[0]
          ? moment(this.timeArr[0]).format('YYYY-MM-DD')
          : ''
      params.timeEnd =
        this.timeArr && this.timeArr[1]
          ? moment(this.timeArr[1]).format('YYYY-MM-DD')
          : ''
      console.log('prams入参', params)

      request({
        url: api.containerRefundAuditResultSearch,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
      // this.$store
      //   .dispatch('containerRefund/getContainerRefundList', params)
      //   .then((res) => {
      //     this.loading = false
      //     console.log('返回的申请列表', res)
      //     this.tableData = res.records
      //     this.total = res.total
      //   })
      //   .catch((err) => {
      //     this.loading = false
      //     console.log('err', err)
      //   })
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getContainerRefundList()
    },
    onSearchHandle() {
      this.search.pageNo = 1
      this.getContainerRefundList()
    },
    onResultHandle() {
      // this.search.pageNum = 1
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.timeArr = []
      this.search.pageNo = 1
      this.search.pageSize = 20
    },
    // handleSelectionChange(selection) {
    //   this.userNoList = []
    //   selection.forEach((item) => {
    //     if (!this.userNoList.includes(item.netUserNo)) {
    //       this.userNoList.push(item.netUserNo)
    //     }
    //   })
    // },
    changePage(page) {
      this.search.pageNo = page
      this.getContainerRefundList()
    },
    onExportHandle() {
      let params = {
        name: '集装箱退费操作报表',
        ...this.search,
        // ...this.searchTimeStr,
      }
      params.timeStart =
        this.timeArr && this.timeArr[0]
          ? moment(this.timeArr[0]).format('YYYY-MM-DD')
          : ''
      params.timeEnd =
        this.timeArr && this.timeArr[1]
          ? moment(this.timeArr[1]).format('YYYY-MM-DD')
          : ''
      delete params.pageNo
      delete params.pageSize
      console.log('导出入參', params)
      request({
        url: api.refundReport,
        method: 'post',
        data: params,
      })
        .then((res) => {
          let url = res.data
          let decodeUrl = decode(url)
          // window.open(decodeUrl)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch((err) => {
          // this.loading = false
          // console.log('err', err)
        })
      // this.$store
      //   .dispatch('containerRefund/refundReport', params)
      //   .then((res) => {
      //     let url = res
      //     let decodeUrl = decode(url)
      //     window.open(decodeUrl)
      //   })
      //   .catch((err) => {})
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}

.account-dialog {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
