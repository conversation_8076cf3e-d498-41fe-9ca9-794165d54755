<template>
  <div class="handle-refund" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogHandleDetail"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title">操作详情</span>
        </div>
      </template>
      <template>
        <div class="section-1 margin-top">
          <el-descriptions
            :title="'申请单号[' + id + ']>>>退款账户信息'"
            :column="2"
            border
            class="desc-wrapper"
          >
            <el-descriptions-item label="卡主姓名：">{{
              containerRefundMast.bankCustName
            }}</el-descriptions-item>
            <el-descriptions-item label="开户行名称：">{{
              containerRefundMast.bankName
            }}</el-descriptions-item>
            <el-descriptions-item label="银行卡号：">{{
              containerRefundMast.bankCardNo
            }}</el-descriptions-item>
            <el-descriptions-item label="是否已现场减半：">{{
              getHalfFlag(containerRefundMast.halfFlag)
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="section-3 margin-top">
          <el-descriptions
            class="margin-top desc-wrapper"
            :title="'车辆信息'"
            :column="2"
            border
          >
            <el-descriptions-item label="卡号：">{{
              containerRefundMast.cardNo
            }}</el-descriptions-item>
            <el-descriptions-item label="OBU合同序列号：">{{
              ObuNo
            }}</el-descriptions-item>
            <el-descriptions-item label="车牌号码：">{{
              containerRefundMast.carNo
            }}</el-descriptions-item>
            <el-descriptions-item label="车牌颜色：">{{
              typeAdapter(containerRefundMast.carNoColor, 'getVehicleColor')
            }}</el-descriptions-item>
            <el-descriptions-item label="客货标志：">
              {{ typeAdapter(cardMast.isTrunk, 'getVehicleType') }}
            </el-descriptions-item>
            <el-descriptions-item label="车型：">
              {{ typeAdapter(cardMast.carType, 'getCarType') }}
            </el-descriptions-item>
            <el-descriptions-item label="卡片类型：">{{
              typeAdapter(containerRefundMast.cardType, 'getCpuCardType')
            }}</el-descriptions-item>
            <el-descriptions-item label="区内卡类型：">{{
              typeAdapter(containerRefundMast.gxCarType, 'getGxCardType')
            }}</el-descriptions-item>
            <el-descriptions-item label="车辆种类：">{{
              carStyleStr
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="section-4 margin-top">
          <el-descriptions class="margin-top desc-wrapper" :column="2" border>
            <template slot="title">
              交易明细
              <el-button
                @click="showTransactionDialog"
                type="primary"
                size="small"
                >明细</el-button
              >
            </template>
            <el-descriptions-item label="申请单号：">{{
              id
            }}</el-descriptions-item>
            <el-descriptions-item label="交易编号：">{{
              containerRefundMast.transactionId
            }}</el-descriptions-item>
            <el-descriptions-item label="入口时间：">{{
              containerRefundMast.enTime
            }}</el-descriptions-item>
            <el-descriptions-item label="入口站：">{{
              containerRefundMast.enStation
            }}</el-descriptions-item>
            <el-descriptions-item label="出口时间：">{{
              containerRefundMast.exTime
            }}</el-descriptions-item>
            <el-descriptions-item label="出口站：">{{
              containerRefundMast.exStation
            }}</el-descriptions-item>
            <el-descriptions-item label="交易时间：">{{
              containerRefundMast.time
            }}</el-descriptions-item>
            <el-descriptions-item label="交易金额（元）：">{{
              containerRefundMast.feeStr
            }}</el-descriptions-item>
            <el-descriptions-item label="申请时间：">{{
              containerRefundMast.applyTime
            }}</el-descriptions-item>
            <el-descriptions-item label="预约时间：">{{
              containerRefundMast.appointTime
            }}</el-descriptions-item>
            <el-descriptions-item label="记账时间：">{{
              containerRefundMast.accountingTime
            }}</el-descriptions-item>
            <el-descriptions-item label="退费交易编号：">{{
              containerRefundMast.refundDetailId
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="table">
          <span class="title">操作记录</span>
          <el-table
            :data="tableData"
            :align="center"
            :header-align="center"
            border
            style="width: 100%; margin-bottom: 20px"
            :row-style="{ height: '54px' }"
            :cell-style="{ padding: '0px' }"
            :header-row-style="{ height: '54px' }"
            :header-cell-style="{ padding: '0px' }"
            row-key="id"
          >
            <el-table-column
              prop="createTime"
              align="center"
              label="创建时间"
              min-width="180"
            />
            <el-table-column prop="handleParty" align="center" label="处理机构">
              <template slot-scope="scope">
                {{ typeAdapter(scope.row.handleParty, 'getHandlePartyType') }}
              </template>
            </el-table-column>
            <el-table-column prop="handleMode" align="center" label="处理方式">
              <template slot-scope="scope">
                {{
                  typeAdapter(
                    scope.row.handleMode,
                    'getContainerRefundHandleMode'
                  )
                }}
              </template>
            </el-table-column>
            <el-table-column prop="deptName" align="center" label="操作网点" />
            <el-table-column prop="operator" align="center" label="操作人" />
            <el-table-column
              prop="operateTime"
              align="center"
              min-width="180"
              label="名义操作时间"
            />
            <el-table-column
              prop="remark"
              align="center"
              min-width="250"
              label="处理意见"
            />
          </el-table>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { typeAdapter, getHalfFlag } from '@/common/method/formatOptions'
export default {
  props: {
    dialogHandleDetail: {
      type: Boolean,
      default: false,
    },
    // id: {
    //   type: Number,
    // },
  },
  components: {},
  data() {
    return {
      isFullscreen: false,
      showLoading: false,
      center: 'center',
      id: '',
      cardMast: {},
      containerRefundMast: {},
      transactionId: '',
      cardNo: '',
      ObuNo: '',
      carStyleStr: '',
      tableData: [],
    }
  },
  // watch: {
  //   id(val) {
  //     if (this.dialogHandleDetail) {
  //       this.getContainerRefundAuditDetail(val)
  //     }
  //   },
  // },
  methods: {
    typeAdapter,
    getHalfFlag,
    getContainerRefundAuditDetail(id) {
      this.id = id
      this.showLoading = true
      console.log('id', id)
      let params = {
        issueApplyId: id,
      }
      console.log('入参', params)
      this.$store
        .dispatch('containerRefund/containerRefundOpratorDetail', params)
        .then((res) => {
          this.showLoading = false
          console.log('弹窗的列表', res)
          this.containerRefundMast = res.containerRefundMast
          this.cardMast = res.cardMast
          this.tableData = res.containerRefundOperateList
          this.transactionId = res.containerRefundMast.transactionId
          this.cardNo = res.containerRefundMast.cardNo
          this.ObuNo = res.ObuNo
          this.carStyleStr = res.carStyleStr
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    showTransactionDialog() {
      this.$emit('showTransactionDetail', this.transactionId, this.cardNo)
    },
    close() {
      console.log('执行关闭')
      this.$emit('update:dialogHandleDetail', false)
    },
  },
}
</script>
<style lang="scss" scoped>
.margin-top {
  margin-top: 20px;
}
.table {
  padding: 0;
  .title {
    display: block;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 700;
    color: #303133;
  }
}
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
.desc-wrapper ::v-deep {
  .el-descriptions__body {
    .el-descriptions__table {
      border-collapse: inherit;
      .el-descriptions-row {
        .el-descriptions-item__content {
          white-space: nowrap;
          height: 30px;
          padding: 0 10px;
        }
        .el-descriptions-item__label {
          width: 200px;
          height: 30px;
          padding: 0 10px;
          white-space: nowrap;
        }
      }
    }
  }
}

.form_dialog ::v-deep .el-dialog__body {
  overflow-x: auto;
}
</style>
