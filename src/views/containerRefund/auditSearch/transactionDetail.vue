<template>
  <div class="transaction-detail">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 原始交易信息详情 </span>
        </div>
      </template>
      <div class="desc-item">
        <el-descriptions class="margin-top" :column="4" border>
          <el-descriptions-item label="公路收费方：">{{
            detail.serviceprovideridStr
          }}</el-descriptions-item>
          <el-descriptions-item label="发行方：">{{
            detail.issueridStr
          }}</el-descriptions-item>
          <el-descriptions-item label="消息包编号：">{{
            detail.messageid
          }}</el-descriptions-item>
          <el-descriptions-item label="记录编号：">{{
            detail.transid
          }}</el-descriptions-item>
          <el-descriptions-item label="交易时间：">{{
            detail.time
          }}</el-descriptions-item>
          <el-descriptions-item label="收费类型：">{{
            detail.chargeTypeStr
          }}</el-descriptions-item>
          <el-descriptions-item label="服务类型：">{{
            typeAdapter(detail.servicetype, 'getServiceType')
          }}</el-descriptions-item>
          <!-- 交易的服务类型，取值见基础信息维护 1-公路电子收费2-停车场消费 3-加油站消费 4-服务区消费 5-市政拓展 -->
          <el-descriptions-item label="描述：">{{
            detail.description
          }}</el-descriptions-item>
          <el-descriptions-item label="入口车道号：">{{
            detail.enlane
          }}</el-descriptions-item>
          <el-descriptions-item label="入口站：">{{
            detail.enstation
          }}</el-descriptions-item>
          <el-descriptions-item label="入口时间：">{{
            detail.entime
          }}</el-descriptions-item>
          <el-descriptions-item label="入口重量(kg)：">{{
            detail.enWeight
          }}</el-descriptions-item>
          <!-- 入口重量，单位：KG 0：非计重收费车型； 大于0：货车重量，车货总重 -->
          <el-descriptions-item label="出口车道号：">{{
            detail.exlane
          }}</el-descriptions-item>
          <el-descriptions-item label="出口站：">{{
            detail.exstation
          }}</el-descriptions-item>
          <el-descriptions-item label="出口时间：">{{
            detail.extime
          }}</el-descriptions-item>
          <el-descriptions-item label="出口重量(kg)：">{{
            detail.exWeight
          }}</el-descriptions-item>
          <!-- 入口重量，单位：KG 0：非计重收费车型； 大于0：货车重量，车货总重 -->
          <el-descriptions-item label="卡号：">{{
            cardNo
          }}</el-descriptions-item>
          <el-descriptions-item label="车牌号：">{{
            detail.cardlicense
          }}</el-descriptions-item>
          <el-descriptions-item label="交易序号：">{{
            detail.transno
          }}</el-descriptions-item>
          <el-descriptions-item label="交易前余额：">{{
            detail.prebalance | moneyFilter
          }}</el-descriptions-item>
          <!-- 交易前余额，表以分为单位，消息包以元为单位 -->
          <el-descriptions-item label="交易后余额：">{{
            detail.postbalance | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="TAC：">{{
            detail.tac
          }}</el-descriptions-item>
          <el-descriptions-item label="交易标识：">{{
            detail.transtype
          }}</el-descriptions-item>
          <!-- 2位16进制数，PBOC定义，如06为传统交易，09为复合交易 -->
          <el-descriptions-item label="交易序号：">{{
            detail.terminalno
          }}</el-descriptions-item>
          <el-descriptions-item label="PSAM交易序号：">{{
            detail.terminaltransno
          }}</el-descriptions-item>
          <el-descriptions-item label="网络编码：">{{
            detail.cardnetid
          }}</el-descriptions-item>
          <el-descriptions-item label="OBU序列号：">{{
            detail.obusn
          }}</el-descriptions-item>
          <el-descriptions-item label="OBU中记录的车牌号：">{{
            detail.obulicense
          }}</el-descriptions-item>
          <el-descriptions-item label="交易编号：">{{
            detail.transactionId
          }}</el-descriptions-item>
          <el-descriptions-item label="车型：">
            {{ detail.CarTypeStr }}
          </el-descriptions-item>
          <el-descriptions-item label="卡类型：">{{
            typeAdapter(detail.cardtype, 'getCpuCardType')
          }}</el-descriptions-item>
          <!-- 卡类型，22为储值卡，23记帐卡 -->
          <el-descriptions-item label="OBU单/双片标识：">{{
            typeAdapter(detail.obuSign, 'getObuSign')
          }}</el-descriptions-item>
          <!-- 	OBU单/双片标识 1位数字 1-单片式obu 2-双片式obu -->
          <el-descriptions-item label="交易金额 (元)：">{{
            detail.fee | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="卡面扣费金额 (元)：">{{
            detail.transFee | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="应收金额(元)：">{{
            detail.payFee | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="优惠金额(元)：">{{
            detail.discountFee | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="收费路段编号：">{{
            detail.sectionId
          }}</el-descriptions-item>
          <el-descriptions-item label="ETC门架编号：">{{
            detail.tollGrantryId
          }}</el-descriptions-item>
          <el-descriptions-item label="收费单元编号：">{{
            detail.tollIntervalId
          }}</el-descriptions-item>
          <el-descriptions-item label="收费单元名称：">{{
            detail.tollIntervalName
          }}</el-descriptions-item>
          <el-descriptions-item label="通行编号：">{{
            detail.passId
          }}</el-descriptions-item>
          <el-descriptions-item label="车辆识别标识：">{{
            typeAdapter(detail.vehicleSign, 'getVehicleSign')
          }}</el-descriptions-item>
          <!-- 本字段由收费车道或 ETC门架填写 0x00-大件运输 0x01-非优惠车 0x02-绿通车 0x03-联合收割机车 0x04-集装箱车 0x05-0xfe 预留 0xff 为默认值 -->
          <el-descriptions-item label="省中心优惠类型：">{{
            typeAdapter(detail.discountType, 'getDiscountType')
          }}</el-descriptions-item>
          <!-- 省中心优惠类 型 省中心确认是否优惠，及优 惠类型 0- 无优惠 1- 绿通 2- 联合收割机 3- 集装箱 -->
          <el-descriptions-item label="省中心优惠金额(元)：">{{
            detail.provinceDiscountFee | moneyFilter
          }}</el-descriptions-item>
          <el-descriptions-item label="记账时间：">{{
            detail.accountingtime
          }}</el-descriptions-item>
          <!-- 记账状态 0-初始状态，1-应收/付，2-确认应收/付，3-争议，4-一级实收/付，5-坏账，6-争议支付，7-争议拒付 -->
          <el-descriptions-item label="交易状态："
            >{{ typeAdapter(detail.transactionstate, 'getTransactionState')
            }}{{ detail.transactionstate }}</el-descriptions-item
          >
          <!-- 是否多省交易 0-单省 1-多省 -->
          <el-descriptions-item label="通行省数量(个)：">{{
            detail.multiProvince
          }}</el-descriptions-item>
          <el-descriptions-item label="清分日：">{{
            detail.cleardate
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { typeAdapter } from '@/common/method/formatOptions'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    transactionId: {
      type: String,
    },
    cardNo: {
      type: String,
    },
  },
  data() {
    return {
      isFullscreen: false,
      detail: [],
    }
  },
  watch: {
    transactionId(val) {
      if (this.dialogFormVisible) {
        this.getPassRecordDetail(val)
      }
    },
  },
  methods: {
    typeAdapter,
    getPassRecordDetail(transactionId) {
      let params = { transactionId: transactionId }
      console.log('交易明细入参', params)
      this.$store
        .dispatch('containerRefund/getPassRecordDetail', params)
        .then((res) => {
          console.log('交易明细详情列表', res)
          this.detail = res
        })
        .catch((err) => {})
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      width: 200px;
      white-space: nowrap;
    }
  }
}

// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
