<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="申请单号：" prop="id">
            <el-input v-model="search.id" placeholder="" clearable></el-input>
          </dart-search-item>
          <dart-search-item label="交易编号：" prop="transactionId">
            <el-input
              v-model="search.transactionId"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="退费明细编号：" prop="refundDetailId">
            <el-input
              v-model="search.refundDetailId"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="carNoColor">
            <el-select
              v-model="search.carNoColor"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌：" prop="carNo">
            <el-input
              v-model="search.carNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="卡号：" prop="cardNo">
            <el-input
              v-model="search.cardNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <template v-if="isCollapse">
            <dart-search-item label="处理机构：" prop="handleParty">
              <el-select
                v-model="search.handleParty"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in handlePartyList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="记账时间起始：" prop="accountingTimeStart">
              <el-date-picker
                v-model="search.accountingTimeStart"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="记账时间截止：" prop="accountingTimeEnd">
              <el-date-picker
                v-model="search.accountingTimeEnd"
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="申请状态：" prop="handleMode">
              <el-select
                v-model="search.handleMode"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in containerRefundHandleMode"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="交易时间起始：" prop="timeStart">
              <el-date-picker
                v-model="search.timeStart"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="交易时间截止：" prop="timeEnd">
              <el-date-picker
                v-model="search.timeEnd"
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item
              label="是否已现场减半："
              prop="halfFlag"
              class="nowrap"
            >
              <el-select
                v-model="search.halfFlag"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in halfFlagList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="申请时间起始：" prop="applyTimeStart">
              <el-date-picker
                v-model="search.applyTimeStart"
                type="datetime"
                placeholder="选择日期时间"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="申请时间截止：" prop="applyTimeEnd">
              <el-date-picker
                v-model="search.applyTimeEnd"
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
              >
              </el-date-picker>
            </dart-search-item>
          </template>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px"
            :span="24"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              ><i class="el-icon-search"></i> 搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <!-- </dart-search-item> -->
            <!-- <dart-search-item :is-button="true" style="margin-top: 10px"> -->
            <el-button type="warning" size="mini" @click="onApplyHandle"
              >审核/驳回</el-button
            >
            <el-button size="mini" type="primary" @click="onExportHandle"
              ><i class="el-icon-download"></i> 导出</el-button
            >
            <el-button size="mini" type="primary" @click="getOpRecord"
              >操作记录查询</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="onApplyHandle('update')"
              >修改证明材料</el-button
            >
            <span class="collapse" v-if="!isCollapse" @click="isCollapse = true"
              >展开</span
            >
            <span class="collapse" v-else @click="isCollapse = false"
              >收起</span
            >
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          :max-height="isCollapse ? 450 : 550"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '40px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '40px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column label="选择" width="50" align="center">
            <template slot-scope="scope">
              <el-radio
                v-model="radio"
                :label="scope.$index"
                @change="getCurrentRow(scope.row.id, scope.row.transactionId)"
              >
                <span></span>
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="id" align="center" label="申请单号"
            ><template slot-scope="scope">
              <span @click="showHandleDetail(scope.row.id)" class="text">{{
                scope.row.id
              }}</span>
            </template></el-table-column
          >
          <el-table-column
            prop="halfFlag"
            align="center"
            min-width="80"
            label="是否现场减半"
          >
            <template slot-scope="scope">
              {{ getHalfFlag(scope.row.halfFlag) }}
            </template>
          </el-table-column>
          <el-table-column prop="custCode" align="center" label="客户编号" />
          <el-table-column
            prop="sern"
            align="center"
            min-width="100"
            label="客户名称"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.custName }}
                </div>
                <span> {{ scope.row.custName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="cardNo"
            align="center"
            min-width="180"
            label="卡号"
          />
          <el-table-column
            prop="carNo"
            align="center"
            min-width="100"
            label="车牌号"
          />
          <el-table-column prop="carNoColor" align="center" label="车牌颜色">
            <template slot-scope="scope">
              {{ getVehicleColor(scope.row.carNoColor) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="enStation"
            align="center"
            min-width="100"
            label="入口站"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.enStation }}
                </div>
                <span> {{ scope.row.enStation }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="enTime"
            align="center"
            min-width="160"
            sortable
            label="入口时间"
          />
          <el-table-column
            prop="exStation"
            align="center"
            min-width="100"
            label="出口站"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.exStation }}
                </div>
                <span> {{ scope.row.exStation }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="exTime"
            align="center"
            min-width="160"
            sortable
            label="出口时间"
          />
          <el-table-column
            prop="feeStr"
            align="center"
            min-width="105"
            label="交易金额(元)"
          />
          <el-table-column
            prop="time"
            align="center"
            min-width="160"
            sortable
            label="交易时间"
          />
          <el-table-column
            prop="accountingTime"
            align="center"
            label="记账时间"
            sortable
            min-width="160"
          />
          <el-table-column
            prop="applyTime"
            align="center"
            min-width="160"
            label="申请时间"
            sortable
          />
          <el-table-column prop="handleParty" align="center" label="处理机构">
            <template slot-scope="scope">
              {{ getHandlePartyType(scope.row.handleParty + '') }}
            </template>
          </el-table-column>
          <el-table-column prop="handleMode" align="center" label="申请状态">
            <template slot-scope="scope">
              {{ getContainerRefundHandleMode(scope.row.handleMode + '') }}
            </template>
          </el-table-column>
          <el-table-column
            prop="transactionId"
            align="center"
            min-width="150"
            label="交易编号"
          >
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item" effect="dark" placement="top">
                <div slot="content">
                  {{ scope.row.transactionId }}
                </div>
                <span> {{ scope.row.transactionId }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="refundDetailId"
            align="center"
            label="退费明细编号"
            min-width="110"
          />
          <el-table-column
            prop="appointTime"
            align="center"
            min-width="180"
            label="预约时间"
            sortable
          />
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="changePage"
            :current-page="search.pageNo"
            :page-sizes="[10, 20, 50]"
            :page-size="search.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- <handleRefund
      v-if="isShowHandle"
      @cancelHandle="cancelHandle"
      @updateList="updateList"
      :id="handleId"
    ></handleRefund> -->
    <handleDetail
      ref="handleDetail"
      :dialogHandleDetail.sync="dialogHandleDetail"
      :id="handleId"
      @showTransactionDetail="showTransactionDetail"
    ></handleDetail>
    <transactionDetail
      ref="transactionDetail"
      :dialogFormVisible.sync="dialogTransactionDetail"
      :transactionId="transactionId"
      :cardNo="cardNo"
    ></transactionDetail>
    <operationRecord
      ref="operationRecord"
      :visible.sync="dialogOperationRecord"
    ></operationRecord>
  </div>
</template>

<script>
import {
  getHandlePartyType,
  getHalfFlag,
  getVehicleColor,
  getContainerRefundHandleMode,
} from '@/common/method/formatOptions'
import {
  licenseColorOption,
  containerRefundHandleMode,
  halfFlagList,
  handlePartyList,
} from '@/common/const/optionsData'
import handleRefund from './handleRefund.vue'
import handleDetail from './handleDetail'
import transactionDetail from './transactionDetail'
import operationRecord from './operationRecord'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')
import { decode } from 'js-base64'
import { mapGetters } from 'vuex'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    handleRefund,
    handleDetail,
    transactionDetail,
    operationRecord,
  },
  data() {
    return {
      licenseColorOption: licenseColorOption,
      containerRefundHandleMode: containerRefundHandleMode,
      handlePartyList: handlePartyList,
      halfFlagList: halfFlagList,
      loading: false,
      // isShowHandle: false,
      dialogHandleDetail: false,
      dialogTransactionDetail: false,
      dialogOperationRecord: false,
      isCollapse: false,
      center: 'center',
      search: {
        cardNo: '', //卡号
        carNo: '', //车牌号
        carNoColor: '', //车牌颜色// 0-蓝色 // 1-黄色，2-黑色，3-白色，4-渐变绿色，5-黄绿双拼色， 6-蓝白渐变色，
        handleMode: '', //申请状态 0-待申请 1-审核中 2-已通过 3-已驳回 4-退费(处理)中 5-退费(退款)中 6-已退费 7-退费失败
        id: '', //申请单号
        handleParty: '', //处理机构，1是发行方，2经营管理方
        refundDetailId: '', //退费明细编号
        transactionId: '', //交易编号
        halfFlag: '', //是否已现场减半
        applyTimeStart: '', //申请时间起始
        applyTimeEnd: '', //申请时间截止
        timeStart: '', //交易时间起始
        timeEnd: '', //交易时间结束
        accountingTimeStart: '', //记账时间起始
        accountingTimeEnd: '', //记账时间结束
        pageNo: 1,
        pageSize: 10,
      },
      cardNo: '',
      total: 0,
      radio: {},
      handleId: null,
      transactionId: '',
      tableData: [],
    }
  },
  computed: {
    ...mapGetters(['refundSearch']),
  },
  created() {
    console.log('refundRearch', this.refundSearch)
    if (Object.keys(this.refundSearch).length > 0) {
      this.search = this.refundSearch
    }
    this.getContainerRefundList()
  },
  methods: {
    getHandlePartyType,
    getHalfFlag,
    getVehicleColor,
    getContainerRefundHandleMode,
    getContainerRefundList() {
      this.loading = true
      let params = { ...this.search }
      params.applyTimeStart = params.applyTimeStart
        ? moment(params.applyTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.applyTimeEnd = params.applyTimeEnd
        ? moment(params.applyTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.timeStart = params.timeStart
        ? moment(params.timeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.timeEnd = params.timeEnd
        ? moment(params.timeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.accountingTimeStart = params.accountingTimeStart
        ? moment(params.accountingTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.accountingTimeEnd = params.accountingTimeEnd
        ? moment(params.accountingTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('prams入参', params)
      this.$store
        .dispatch('containerRefund/getContainerRefundList', params)
        .then((res) => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.records
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    changePage(page) {
      this.radio = {}
      this.search.pageNo = page
      this.getContainerRefundList()
    },
    handleSizeChange(pageSize) {
      this.radio = {}
      this.search.pageSize = pageSize
      this.getContainerRefundList()
    },
    onSearchHandle() {
      this.radio = {}
      this.search.pageNo = 1
      console.log('当前页面audit', this.search.pageNo)
      //缓存搜索参数
      this.$store
        .dispatch('containerRefund/setRefundSearch', this.search)
        .then((res) => {
          console.log('缓存过后的search', res)
        })
      this.getContainerRefundList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.radio = []
      this.search.pageNo = 1
      this.search.pageSize = 20
      //清除缓存
      this.$store
        .dispatch('containerRefund/removeRefundSearch')
        .then((res) => {})
    },
    onApplyHandle(type = 'normal') {
      if (!this.handleId) {
        this.$confirm('请选中一条记录！', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning',
        })
        return
      } else {
        let type = typeof this.radio
        let length = Object.keys(this.radio).length
        if (type === 'object' && length === 0) {
          this.$confirm('请选中一条记录！', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning',
          })
          return
        }
      }
      //跳转审核页面
      this.$router.push({
        path: './handleRefund',
        query: {
          id: this.handleId,
          type: type,
        },
      })
    },
    //打开交易明细,并加载数据
    showTransactionDetail(transactionId, cardNo) {
      this.dialogTransactionDetail = true
      this.transactionId = transactionId
      this.cardNo = cardNo
    },
    //打开操作详情dialog,并加载数据
    showHandleDetail(id) {
      // this.radio = {}
      console.log('低得多', id, this.handleId)
      //id相等时手动更新。
      this.$refs.handleDetail.getContainerRefundAuditDetail(id)
      this.dialogHandleDetail = true
    },
    onExportHandle() {
      let params = {
        name: '集装箱退费报表',
        ...this.search,
        // ...this.searchTimeStr,
      }
      params.applyTimeStart = params.applyTimeStart
        ? moment(params.applyTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.applyTimeEnd = params.applyTimeEnd
        ? moment(params.applyTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.timeStart = params.timeStart
        ? moment(params.timeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.timeEnd = params.timeEnd
        ? moment(params.timeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.accountingTimeStart = params.accountingTimeStart
        ? moment(params.accountingTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.accountingTimeEnd = params.accountingTimeEnd
        ? moment(params.accountingTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      delete params.pageNo
      delete params.pageSize
      console.log('导出入參', params)
      this.$store
        .dispatch('containerRefund/refundReport', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          // window.open(decodeUrl)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch((err) => {})
    },
    getCurrentRow(id) {
      this.handleId = id
    },
    getOpRecord() {
      //跳转审核页面
      this.dialogOperationRecord = true
    },
  },
}
</script>

<style lang="scss" scoped>
.refund {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
