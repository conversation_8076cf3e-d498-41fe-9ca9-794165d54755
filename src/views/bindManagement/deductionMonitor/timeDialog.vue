<template>
  <div class="edit-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="50%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 请款交易时间选择 </span>
        </div>
      </template>
      <div class="time-select">
        <dart-date-range
          v-model="transDateRange"
          type="datetime"
          value-format="timestamp"
          :default-time="defaultTime"
        ></dart-date-range>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="update()">确认导出</el-button>
        <el-button @click="close()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// import { typeAdapter } from '@/common/method/formatOptions'
import dartDateRange from '@/components/DateRange/date-range'

var moment = require('moment')
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    dartDateRange,
  },
  data() {
    return {
      isFullscreen: false,
      showLoading: false,
      defaultTime: ['00:00:00', '23:59:59'],
      transDateRange: [],
      transactionStartDate: '',
      transactionEndDate: '',
    }
  },
  methods: {
    update() {
      let formatStr = 'YYYY-MM-DD HH:mm:ss'
      // let params = JSON.parse(JSON.stringify(this.search))
      if (!!this.transDateRange[0] && !!this.transDateRange[1]) {
        this.transactionStartDate = !!this.transDateRange[0]
          ? moment(this.transDateRange[0]).format(formatStr)
          : ''
        this.transactionEndDate = !!this.transDateRange[1]
          ? moment(this.transDateRange[1]).format(formatStr)
          : ''
      }
      if (!this.transactionStartDate || !this.transactionEndDate) {
        this.$message({
          message: '请先选择请款交易时间！',
          type: 'warning',
        })
        return
      }
      this.$emit('on-submit', {
        transDateSt: this.transactionStartDate,
        transDateEn: this.transactionEndDate,
      })
      // this.showLoading = true
      // let params = {
      //   sern: this.detail.sern,
      //   reason: this.reason,
      // }
      // this.$store
      //   .dispatch('bindManagement/singleTransfer', params)
      //   .then((res) => {
      //     //修改成功
      //     this.showLoading = false
      //     this.$emit('on-submit')
      //     this.$message({
      //       message: '确认转补缴成功',
      //       type: 'success',
      //     })
      //   })
      //   .catch((err) => {
      //     this.showLoading = false
      //   })
      console.log(
        'this.transDateRange',
        this.transactionStartDate,
        this.transactionEndDate
      )
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      width: 200px;
      white-space: nowrap;
    }
  }
}

// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
