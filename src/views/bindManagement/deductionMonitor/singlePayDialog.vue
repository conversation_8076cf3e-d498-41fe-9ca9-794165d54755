<template>
  <div class="edit-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 转补缴详情 </span>
        </div>
      </template>
      <div class="desc-item">
        <el-descriptions class="margin-top" :column="2" border>
          <el-descriptions-item label="卡号：">
            {{ detail.cardNo }}
          </el-descriptions-item>
          <el-descriptions-item label="车牌：">
            {{ detail.carNo }}
          </el-descriptions-item>
          <el-descriptions-item label="系统计算补缴金额：">
            {{ detail.sysAmount }}(元)
          </el-descriptions-item>
          <el-descriptions-item label="扣款流水号：">
            {{ detail.sern }}
          </el-descriptions-item>
          <el-descriptions-item label="通行金额：">
            {{ detail.transAmountStr }}(元)
          </el-descriptions-item>
          <el-descriptions-item label="退费金额：">
            {{ detail.refundAmount }}(元)
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red">*</span> 转补缴原因：
            </template>
            <el-input v-model="reason" placeholder="转补缴原因"></el-input>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template slot="footer">
        <el-button type="primary" @click="update()">确认转补缴</el-button>
        <el-button @click="close()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// import { typeAdapter } from '@/common/method/formatOptions'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    // refundOrderId: {
    //   type: Number,
    // },
    // typeList: {
    //   type: Object,
    //   default: {},
    // },
    detail: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      isFullscreen: false,
      showLoading: false,
      reason: '',
      // refundOrder: {},
      // refundStatus: [
      //   { value: '7', label: '不可退款' },
      //   { value: '0', label: '待退款' },
      //   { value: '3', label: '退款失败' },
      // ],
      // refundType: [
      //   { value: '1', label: '交易编号' },
      //   { value: '2', label: '行程编号' },
      // ],
      // updateForm: {
      //   refundStatus: '',
      //   suspendReason: '',
      // },
    }
  },
  // watch: {
  //   dialogFormVisible(val) {
  //     if (val) {
  //       // console.log(this.refundOrderId)
  //       // this.getOrderDetail()
  //     }
  //   },
  // },
  methods: {
    update() {
      this.showLoading = true
      let params = {
        sern: this.detail.sern,
        reason: this.reason,
      }
      this.$store
        .dispatch('bindManagement/singleTransfer', params)
        .then((res) => {
          //修改成功
          this.showLoading = false
          this.$emit('on-submit')
          this.$message({
            message: '确认转补缴成功',
            type: 'success',
          })
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.desc-item ::v-deep {
  overflow-x: auto;
  padding-bottom: 20px;
  .el-descriptions-row {
    .el-descriptions-item__content {
      white-space: nowrap;
    }
    .el-descriptions-item__label {
      width: 200px;
      white-space: nowrap;
    }
  }
}

// .form_dialog ::v-deep .el-dialog__body {
//   overflow-x: auto;
// }
</style>
