<template>
  <div class="refund" v-loading.fullscreen.lock="fullscreenLoading">
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="客户代码：" prop="carNo">
            <el-input v-model="search.custCode" placeholder></el-input>
          </dart-search-item>
          <dart-search-item label="客户名称：" prop="carNo">
            <el-input v-model="search.custName" placeholder></el-input>
          </dart-search-item>
          <dart-search-item label="车牌：" prop="carNo">
            <el-input v-model="search.carNo" placeholder></el-input>
          </dart-search-item>
          <dart-search-item label="卡号：" prop="carNo">
            <el-input v-model="search.cardNo" placeholder></el-input>
          </dart-search-item>
          <dart-search-item label="卡片类型：" prop="listType">
            <el-select v-model="search.cardType" placeholder="请选择">
              <el-option
                v-for="item in typeList.cardTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="证件号码：" prop="carNo">
            <el-input v-model="search.custIdNo" placeholder></el-input>
          </dart-search-item>
          <div class="collapse-wrapper" v-show="isCollapse">
            <dart-search-item label="扣款流水号：" prop="carNo">
              <el-input v-model="search.sern" placeholder></el-input>
            </dart-search-item>
            <dart-search-item label="银行名称：" prop="listType">
              <el-select v-model="search.bankId" filterable placeholder="请选择">
                <el-option
                  v-for="(item, index) in typeList.orgIds"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <!-- <el-autocomplete
                class="inline-input"
                v-model="state"
                :fetch-suggestions="querySearch"
                placeholder="请选择"
                @select="handleSelect"
                clearable
              >
              </el-autocomplete>-->
            </dart-search-item>
            <dart-search-item label="银行卡号：" prop="carNo">
              <el-input v-model="search.bankCardNo" placeholder></el-input>
            </dart-search-item>
            <dart-search-item label="扣款类型：" prop="listType">
              <el-select v-model="search.deductionType" placeholder="请选择">
                <el-option
                  v-for="item in typeList.dedutionTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="扣款发起从：" prop="deductionSt">
              <el-date-picker type="datetime" placeholder="选择日期时间" v-model="search.deductionSt"></el-date-picker>
            </dart-search-item>
            <dart-search-item label="扣款发起到：" prop="deductionEn">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.deductionEn"
              ></el-date-picker>
            </dart-search-item>

            <dart-search-item label="是否入账：" prop="enterAccountFlag">
              <el-select v-model="search.enterAccountFlag" placeholder="请选择">
                <el-option
                  v-for="item in typeList.enterAccountFlags"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="入账日期从：" prop="enterAccountDateSt">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.enterAccountDateSt"
              ></el-date-picker>
            </dart-search-item>
            <dart-search-item label="入账日期到：" prop="enterAccountDateEn">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.enterAccountDateEn"
              ></el-date-picker>
            </dart-search-item>

            <dart-search-item label="代扣机构：" prop="listType">
              <el-select v-model="search.payOrgId" filterable placeholder="请选择">
                <el-option
                  v-for="(item, index) in typeList.orgIds"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <!-- <el-autocomplete
                class="inline-input"
                v-model="state1"
                :fetch-suggestions="querySearch"
                placeholder="请选择"
                @select="handleSelect1"
                clearable
              >
              </el-autocomplete>-->
            </dart-search-item>
            <dart-search-item label="捷通应收日从：" prop="bankAccountDateSt">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.bankAccountDateSt"
              ></el-date-picker>
            </dart-search-item>
            <dart-search-item label="捷通应收日到：" prop="bankAccountDateEn">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.bankAccountDateEn"
              ></el-date-picker>
            </dart-search-item>
            <dart-search-item label="处理状态：" prop="source">
              <el-select v-model="search.handleStatus" placeholder="请选择">
                <el-option
                  v-for="item in typeList.handleStatusDic"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="入账类型：" prop="enterType">
              <el-select v-model="search.enterType" placeholder="请选择">
                <el-option
                  v-for="item in typeList.enterTypeDic"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="对账标识：" prop="recMark">
              <el-select v-model="search.recMark" placeholder="请选择">
                <el-option
                  v-for="item in typeList.recMarkList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="补缴处理状态：" prop="auditStatus">
              <el-select v-model="search.auditStatus" placeholder="请选择">
                <el-option
                  v-for="item in typeList.auditStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
          </div>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px; margin-left: 35px"
            :span="24"
          >
            <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">
              <i class="el-icon-search"></i> 搜索
            </el-button>
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button type="primary" size="mini" @click="setType('record')">
              <i class="el-icon-download"></i> 导出
            </el-button>
            <el-button size="mini" type="primary" @click="toAfterPay">转补缴</el-button>
            <el-button size="mini" type="warning" @click="reset">作废重发</el-button>
            <el-button
              size="mini"
              type="warning"
              @click="confirmOpen"
              v-permisaction="['deductions:deductionCancel']"
            >批量冲正</el-button>
            <el-button size="mini" type="warning" @click="setType('order')">
              <i class="el-icon-download"></i> 请款订单导出
            </el-button>
            <!-- <el-button size="mini" type="primary" @click="onExportHandle"
              ><i class="el-icon-upload"></i> 批量导入</el-button
            >
            <el-button size="mini" type="primary" @click="onExportHandle"
              ><i class="el-icon-upload"></i> 批量导入审核</el-button
            >-->
            <span class="collapse" v-if="!isCollapse" @click="isCollapse = true">展开</span>
            <span class="collapse" v-else @click="isCollapse = false">收起</span>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column align="center" width="80" label="全选">
          <template slot="header">
            <div>
              补缴全选
              <el-checkbox v-model="checkAll" @change="handleCheckAll"></el-checkbox>
            </div>
          </template>
          <template
            slot-scope="scope"
            v-if="
              scope.row.handleStatus != '待补缴' &&
              scope.row.handleStatus != '已补缴'
            "
          >
            <el-checkbox v-model="scope.row.checked" @change="handleCheckOne"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column align="center" width="80" label="全选">
          <template slot="header">
            <div>
              重发全选
              <el-checkbox v-model="checkAll1" @change="handleCheckAll1"></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope" v-if="getShow(scope.row)">
            <el-checkbox v-model="scope.row.checked1" @change="handleCheckOne1"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column align="center" width="80" label="全选">
          <template slot="header">
            <div>
              冲正全选
              <el-checkbox v-model="checkAll2" @change="handleCheckAll2"></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope" v-if="!(scope.row.status > 7 && scope.row.status < 10)">
            <el-checkbox v-model="scope.row.checked2" @change="handleCheckOne2"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column prop="sern" align="center" min-width="200" label="扣款流水号">
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.sern }}</div>
              <span>{{ scope.row.sern }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="deductionType" align="center" label="扣款类型" min-width="200">
          <!-- <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.status }}
              </div>
              <span> {{ scope.row.status }}</span>
            </el-tooltip>
          </template>-->
          <template slot-scope="scope">{{ scope.row.deductionTypeValue }}</template>
        </el-table-column>
        <el-table-column prop="cardNo" align="center" min-width="200" label="卡号">
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.cardNo }}</div>
              <span>{{ scope.row.cardNo }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop align="center" label="扣款金额(元)" min-width="120">
          <template slot-scope="scope">
            <span>
              {{
              divNumbers(
              addNumbers(
              scope.row.amount,
              addNumbers(scope.row.serviceAmount, scope.row.overdueAmount)
              )
              )
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="amount" align="center" label="通行费金额(元)" min-width="120">
          <template slot-scope="scope">
            <span>{{ divNumbers(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="服务费(元)" min-width="120">
          <template slot-scope="scope">
            <span>
              {{
              !scope.row.serviceAmount || scope.row.serviceAmount == 0
              ? '--'
              : divNumbers(scope.row.serviceAmount)
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳金(元)" min-width="120">
          <template slot-scope="scope">
            <span>
              {{
              !scope.row.overdueAmount || scope.row.overdueAmount == 0
              ? '--'
              : divNumbers(scope.row.overdueAmount)
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳天数(天)" min-width="120">
          <template slot-scope="scope">
            <span>
              {{
              !scope.row.overdueDay || scope.row.overdueDay == '0'
              ? '--'
              : scope.row.overdueDay
              }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="bankId" align="center" min-width="130" label="银行名称">
          <template slot-scope="scope">{{ getType(typeList.orgIds, scope.row.bankId) }}</template>
        </el-table-column>
        <el-table-column prop="carNo" align="center" min-width="130" label="车牌" />
        <el-table-column prop="carColor" align="center" min-width="140" label="车牌颜色">
          <template slot-scope="scope">{{ getType(typeList.carColors, scope.row.carColor) }}</template>
        </el-table-column>
        <el-table-column prop="statusValue" align="center" min-width="140" label="扣款状态"></el-table-column>
        <el-table-column prop="carColor" align="center" min-width="140" label="补缴状态">
          <template slot-scope="scope">
            <!-- {{ getType(typeList.auditStatusList, scope.row.handleStatus) }} -->
            {{ scope.row.handleStatus }}
          </template>
        </el-table-column>
        <el-table-column prop="firstSendTime" align="center" min-width="160" label="扣款发起时间" />
        <el-table-column prop="deductionAccountDate" align="center" min-width="160" label="扣款完成时间" />
        <el-table-column prop="enterAccountFlagValue" align="center" min-width="160" label="是否入账" />
        <el-table-column prop="enterAccountDate" align="center" min-width="160" label="入账时间" />
        <el-table-column prop="enterAccountDate" align="center" min-width="160" label="捷通应收时间" />
        <el-table-column prop="refundAmount" align="center" label="已退费">
          <template slot-scope="scope">
            {{ divNumbers(scope.row.refundAmount) || '' }}
          </template>
        </el-table-column>
        <el-table-column prop="remark" align="center" min-width="100" label="备注">
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.remark }}</div>
              <span>{{ scope.row.remark }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="resendOriSern" align="center" min-width="180" label="作废重发原扣款流水号">
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">{{ scope.row.resendOriSern }}</div>
              <span>{{ scope.row.resendOriSern }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="cancelFlag" align="center" label="冲正作废">
          <template slot-scope="scope">{{ scope.row.cancelFlag === 0 ? '否' : '是' }}</template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="300"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              :disabled="!(scope.row.cStatus == 3 && scope.row.cPayFailType == 2)"
              type="primary"
              size="mini"
              @click="supplementary(scope.row.sern)"
            >补扣</el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="isDisabledBtn(scope.row)"
              @click="manualLateFeeHandle(scope.row)"
            >调差</el-button>
            <el-button type="primary" size="mini" @click="toRecord(scope.row.deductionId)">通行记录</el-button>
            <!-- <el-button
              v-if="scope.row.status == 7"
              type="warning"
              size="mini"
              @click="confirmOpen(scope.row.deductionId)"
              >冲正</el-button
            >-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageIndex"
        :page-sizes="[10, 20, 50,100,500,1000,5000]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
    <single-pay-dialog
      :dialogFormVisible.sync="showSingleDialog"
      :detail="paymentDetail"
      @on-submit="onUpdateList"
    ></single-pay-dialog>
    <record :visible.sync="dialogRecord" :id="id"></record>
    <time-dialog :dialogFormVisible.sync="dialogTime" @on-submit="onExportHandle"></time-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import singlePayDialog from './singlePayDialog'
import timeDialog from './timeDialog'
import record from './record'
import { licenseColorOption } from '@/common/const/optionsData'
import { manualLateFeeApi } from '@/api/ccsRate'
import { getToken } from '@/utils/auth'
import float from '@/common/method/float.js'

var moment = require('moment')
// import { decode } from 'js-base64'
// import refund from '@/store/modules/refund'
// import { mapGetters } from 'vuex'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    singlePayDialog,
    record,
    timeDialog
  },
  data() {
    return {
      loading: false,
      fullscreenLoading: false,
      dialogDetail: false,
      dialogRecord: false,
      dialogTime: false,
      isCollapse: false,
      checkAll: false,
      checkAll1: false,
      checkAll2: false,
      showSingleDialog: false,
      center: 'center',
      exportType: '',
      // state: '', //银行占位
      // state1: '', //代扣机构组件占位
      search: {
        custCode: '', //客户代码
        custName: '', //客户名称
        carNo: '', //车牌
        cardNo: '', //卡号
        cardType: '',
        custIdNo: '', //证件号码
        sern: '',
        bankId: '',
        bankCardNo: '',
        deductionType: '',
        deductionSt: '',
        deductionEn: '',
        enterAccountFlag: '',
        enterAccountDateSt: '',
        enterAccountDateEn: '',
        payOrgId: '',
        bankAccountDateSt: '',
        bankAccountDateEn: '',
        handleStatus: '',
        enterType: '',
        recMark: '',
        auditStatus: '',
        pageIndex: 1,
        pageSize: 20
      },
      total: 0,
      totalPrice: '',
      radio: {},
      id: null,
      handleId: null,
      typeList: {
        orgIds: [], //代扣机构
        cardTypes: [], //卡片类型
        dedutionTypes: [], //扣款类型
        carColors: licenseColorOption,
        enterAccountFlags: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '0',
            label: '未入账'
          },
          {
            value: '1',
            label: '已入账'
          },
          {
            value: '2',
            label: '入账时异常处理'
          }
        ], //是否入账
        enterTypeDic: [], //入账类型
        handleStatusDic: [], //处理状态
        auditStatusList: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '1',
            label: '待补缴'
          },
          {
            value: '2',
            label: '已补缴'
          }
        ],
        recMarkList: [
          {
            value: '',
            label: '全部'
          },
          {
            value: '1',
            label: '需要对账'
          },
          {
            value: '2',
            label: '挂起'
          }
        ] //对账标识
      },
      // multipleSelection: [],
      tableData: [],
      paymentDetail: {}
    }
  },
  // computed: {
  //   ...mapGetters(['refundSearch']),
  // },
  created() {
    // console.log('refundRearch', this.refundSearch)
    // if (Object.keys(this.refundSearch).length > 0) {
    //   this.search = this.refundSearch
    // }
    // this.getAuditBlackSearch()
    this.getDictionaries()
  },
  methods: {
    // querySearch(queryString, cb) {
    //   let arr = []
    //   let arrList = this.typeList.orgIds
    //   arrList.forEach((item) => {
    //     // console.log('item', item)
    //     let lvObj = {
    //       value: item.label,
    //       label: item.value,
    //     }
    //     arr.push(lvObj)
    //   })

    //   var restaurants = arr
    //   console.log('restaurants', restaurants)
    //   let results = queryString
    //     ? restaurants.filter(this.createFilter(queryString))
    //     : restaurants
    //   // 调用 callback 返回建议列表的数据
    //   cb(results)
    // },
    // createFilter(queryString) {
    //   return (restaurant) => {
    //     return (
    //       restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
    //       0
    //     )
    //   }
    // },
    //银行
    // handleSelect(item) {
    //   this.search.bankId = item.label
    //   console.log(item, this.search.bankId)
    // },
    // //代扣机构
    // handleSelect1(item) {
    //   this.search.payOrgId = item.label
    //   console.log(item, this.search.payOrgId)
    // },
    getDictionaries() {
      this.$store
        .dispatch('bindManagement/deductionsInit')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          let originList = res
          Object.keys(typeList).forEach(key => {
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
          })
        })
        .catch(err => {})
    },
    getShow(item) {
      if (
        item.isTransferSupplement == 2 ||
        item.status != 7 ||
        (item.enterAccountFlag == 1 && item.needAccountFlag != 1)
      ) {
        return false
      }
      return true
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr, lvTypeList)
      if (lvTypeList.length === 0) {
        lvTypeList.push({
          label: '全部',
          value: ''
        })
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getAuditBlackSearch() {
      this.loading = true
      let params = { ...this.search }
      params.deductionSt = params.deductionSt
        ? moment(params.deductionSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.deductionEn = params.deductionEn
        ? moment(params.deductionEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.enterAccountDateSt = params.enterAccountDateSt
        ? moment(params.enterAccountDateSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.enterAccountDateEn = params.enterAccountDateEn
        ? moment(params.enterAccountDateEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.bankAccountDateSt = params.bankAccountDateSt
        ? moment(params.bankAccountDateSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.bankAccountDateEn = params.bankAccountDateEn
        ? moment(params.bankAccountDateEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('prams入参', params)
      this.$store
        .dispatch('bindManagement/getDeductionsList', params)
        .then(res => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.records

          for (let i = 0; i < this.tableData.length; i++) {
            this.$set(this.tableData[i], 'checked', false)
            this.$set(this.tableData[i], 'checked1', false)
            this.$set(this.tableData[i], 'checked2', false)
          }
          this.total = res.total
        })
        .catch(err => {
          this.loading = false
          console.log('err', err)
        })
    },
    // getPaymentDetail(id) {
    //   let params = { auditBlackListId: id }
    //   console.log('交易明细入参', params)
    //   this.$store
    //     .dispatch('bindManagement/auditBlackDetailInfo', params)
    //     .then((res) => {
    //       console.log('交易明细详情列表', res)
    //       this.paymentDetail = res
    //       this.dialogDetail = true
    //     })
    //     .catch((err) => {})
    // },

    //补缴全选
    handleCheckAll(val) {
      console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        if (
          this.tableData[i].handleStatus != '待补缴' &&
          this.tableData[i].handleStatus != '已补缴'
        ) {
          this.$set(this.tableData[i], 'checked', val)
        }
      }
    },
    //重发全选
    handleCheckAll1(val) {
      console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        if (
          !(
            this.tableData[i].isTransferSupplement == 2 ||
            this.tableData[i].status != 7 ||
            (this.tableData[i].enterAccountFlag == 1 &&
              this.tableData[i].needAccountFlag != 1)
          )
        ) {
          this.$set(this.tableData[i], 'checked1', val)
        }
      }
    },
    //冲正多选
    handleCheckAll2(val) {
      console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        if (!(this.tableData[i].status > 7 && this.tableData[i].status < 10)) {
          this.$set(this.tableData[i], 'checked2', val)
        }
      }
    },
    handleCheckOne(val) {
      console.info('check one change is ', val)
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量
      this.tableData.forEach(item => {
        if (item.checked) {
          someStatusCount++
        }
      })
      this.checkAll = totalCount === someStatusCount ? true : false
    },
    handleCheckOne1(val) {
      console.info('check1 one change is ', val)
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量
      this.tableData.forEach(item => {
        if (item.checked1) {
          someStatusCount++
        }
      })
      this.checkAll1 = totalCount === someStatusCount ? true : false
    },
    handleCheckOne2(val) {
      console.info('check2 one change is ', val)
      let totalCount = this.tableData.length
      let someStatusCount = 0 //选到的数量
      this.tableData.forEach(item => {
        if (item.checked2) {
          someStatusCount++
        }
      })
      this.checkAll2 = totalCount === someStatusCount ? true : false
    },
    changePage(page) {
      this.search.pageIndex = page
      this.getAuditBlackSearch()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getAuditBlackSearch()
    },
    onSearchHandle() {
      this.search.pageIndex = 1
      console.log('当前页面audit', this.search.pageIndex)
      // //缓存搜索参数
      // this.$store
      //   .dispatch('containerRefund/setRefundSearch', this.search)
      //   .then((res) => {
      //     console.log('缓存过后的search', res)
      //   })
      this.getAuditBlackSearch()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.tableData.forEach(item => {
        item.checked = false
        item.checked1 = false
        item.checked2 = false
      })
      this.checkAll = false
      this.checkAll1 = false
      this.checkAll2 = false
      // this.search.isJet = '1'
      this.search.pageIndex = 1
      this.search.pageSize = 20
      // //清除缓存
      // this.$store
      //   .dispatch('containerRefund/removeRefundSearch')
      //   .then((res) => {})
    },
    //转补缴
    toAfterPay() {
      let checkedArr = this.tableData.filter(item => item.checked)
      console.log('checkNum', checkedArr)

      if (checkedArr.length == 0) {
        this.$message({
          message: '请至少选择一条转补缴的扣款记录！',
          type: 'warning'
        })
        return
      }
      //单个转补缴
      if (checkedArr.length == 1) {
        this.AfterPayHandle('single', checkedArr)
      }
      //批量转补缴
      if (checkedArr.length > 1) {
        this.AfterPayHandle('more', checkedArr)
      }
    },
    //转补缴操作
    AfterPayHandle(type, selection) {
      if (type == 'single') {
        let params = {
          deductId: selection[0].deductionId
        }
        this.$store
          .dispatch('bindManagement/transferDetail', params)
          .then(res => {
            console.log('单补缴详情', res)
            this.paymentDetail = res
            this.showSingleDialog = true
          })
          .catch(err => {})
      } else if (type == 'more') {
        this.$confirm('是否确认转补缴操作？', '转补缴操作', {
          distinguishCancelAndClose: true,
          confirmButtonText: '转补缴',
          cancelButtonText: '取消'
        }).then(() => {
          let sernArr = selection.map(item => {
            return item.sern
          })
          let params = { datas: sernArr }
          this.$store
            .dispatch('bindManagement/batchTransfer', params)
            .then(res => {
              this.$message({
                message: res,
                type: 'success'
              })
              this.getAuditBlackSearch()
            })
            .catch(err => {})
        })
      }
    },
    //次次顺补扣操作
    doSupplementary(sern) {
      this.fullscreenLoading = true
      let params = { sern: sern }
      console.log(params)
      this.$request({
        url: this.$interfaces.supplementary,
        method: 'post',
        data: params
      })
        .then(res => {
          console.log(res)
          this.fullscreenLoading = false
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg
            })
            this.getAuditBlackSearch()
          }
        })
        .catch(error => {
          this.fullscreenLoading = false
          console.log('err', error)
        })
    },
    supplementary(sern) {
      this.$confirm('将进行补扣操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.doSupplementary(sern)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消操作'
          })
        })
    },
    /**
     * 是否禁用调差按钮
     */
    isDisabledBtn(row) {
      console.log(!(row.cStatus == 3 && row.cPayFailType == 2))
      let disabled = false
      if (
        ['1', '2', '9'].includes(row.cStatus) ||
        (row.cStatus == '3' && ['0', '1'].includes(row.cPayFailType))
      ) {
        disabled = true
      }
      return disabled
    },
    /**
     * 调差方法
     */
    manualLateFeeHandle(row) {
      this.$prompt('请输入调差金额', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+(?=\.{0,1}\d+$|$)/,
        inputErrorMessage: '请输入正确调差金额（数字）'
      })
        .then(async ({ value }) => {
          let params = {
            amount: Number(value),
            sern: row.sern
          }
          let res = await manualLateFeeApi(params)
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.getAuditBlackSearch()
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })
    },
    reset() {
      let checkedArr = this.tableData.filter(item => item.checked1)
      console.log('checkedArr', checkedArr)

      if (checkedArr.length == 0) {
        this.$message({
          message: '请至少选择一条作废重发的扣款记录！',
          type: 'warning'
        })
        return
      }

      //单个作废重发
      if (checkedArr.length == 1) {
        this.resetHandle('single', checkedArr)
      }
      //批量作废重发
      if (checkedArr.length > 1) {
        this.resetHandle('more', checkedArr)
      }
    },
    resetHandle(type, selection) {
      if (type == 'single') {
        this.fullscreenLoading = true
        let params = {
          deductId: selection[0].deductionId
        }
        this.$store
          .dispatch('bindManagement/singleReSend', params)
          .then(res => {
            this.fullscreenLoading = false
            console.log('单笔作废信息返回', res)
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.getAuditBlackSearch()
          })
          .catch(err => {
            this.fullscreenLoading = false
          })
      } else if (type == 'more') {
        this.fullscreenLoading = true
        let idsArr = selection.map(item => {
          return item.deductionId
        })
        let params = { deductIds: idsArr }
        this.$store
          .dispatch('bindManagement/batchReSend', params)
          .then(res => {
            this.fullscreenLoading = false
            this.$message({
              message: res,
              type: 'success'
            })
            this.getAuditBlackSearch()
          })
          .catch(err => {
            this.fullscreenLoading = false
          })
      }
    },
    setType(type) {
      this.exportType = type
      this.dialogTime = true
    },
    confirmOpen() {
      let checkedArr = this.tableData.filter(item => item.checked2)
      console.log('checkedArr', checkedArr)

      if (checkedArr.length == 0) {
        this.$message({
          message: '请至少选择一条需要冲正的扣款记录！',
          type: 'warning'
        })
        return
      }

      this.$prompt('填写备注后,将批量进行冲正操作, 是否继续?', '提示', {
        confirmButtonText: '确定'
      })
        .then(({ value }) => {
          console.log('value', value)
          if (!value) {
            this.$message.error('请先填写备注')
            return
          }
          this.payReset(checkedArr, value)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })

      // this.$confirm('将批量进行冲正操作, 是否继续?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning',
      // })
      //   .then(() => {
      //     this.payReset(checkedArr)
      //   })
      //   .catch(() => {
      //     this.$message({
      //       type: 'info',
      //       message: '取消操作',
      //     })
      //   })
    },
    payReset(checkedArr, remark) {
      this.fullscreenLoading = true
      let idsArr = checkedArr.map(item => {
        return item.deductionId
      })
      let params = { deductIds: idsArr, remark: remark }
      console.log(params)
      this.$request({
        url: this.$interfaces.deductionCancel,
        method: 'post',
        data: params
      })
        .then(res => {
          console.log(res)
          this.fullscreenLoading = false
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.data
            })
            this.getAuditBlackSearch()
          }
        })
        .catch(error => {
          this.fullscreenLoading = false
          console.log('err', error)
        })
    },
    onExportHandle(transDateObj) {
      this.dialogTime = false
      let url = ''
      //扣款记录导出和请款记录导出
      if (this.exportType == 'record') {
        url =
          process.env.VUE_APP_BASE_API +
          '/issue-web/deductions/export?Authorization=Bearer ' +
          getToken()
      } else if (this.exportType == 'order') {
        url =
          process.env.VUE_APP_BASE_API +
          '/issue-web/deductions/deductionExport?Authorization=Bearer ' +
          getToken()
      }
      // this.loading = true
      let params = { ...this.search, ...transDateObj }
      params.deductionSt = params.deductionSt
        ? moment(params.deductionSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.deductionEn = params.deductionEn
        ? moment(params.deductionEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.enterAccountDateSt = params.enterAccountDateSt
        ? moment(params.enterAccountDateSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.enterAccountDateEn = params.enterAccountDateEn
        ? moment(params.enterAccountDateEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.bankAccountDateSt = params.bankAccountDateSt
        ? moment(params.bankAccountDateSt).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.bankAccountDateEn = params.bankAccountDateEn
        ? moment(params.bankAccountDateEn).format('YYYY-MM-DD HH:mm:ss')
        : ''
      delete params.pageSize
      delete params.pageIndex
      console.log('prams入参', params)
      for (let i in params) {
        if (params[i] != '') {
          url += '&' + i + '=' + params[i]
        }
      }
      console.log('url', url)
      window.open(url)
    },
    onUpdateList() {
      this.showSingleDialog = false
      this.getAuditBlackSearch()
    },
    toRecord(id) {
      this.id = id
      this.dialogRecord = true
    },
    addNumbers(a, b) {
      if (!a) {
        a = 0
      }
      if (!b) {
        b = 0
      }
      return float.add(a, b).toFixed(2)
    },
    divNumbers(c) {
      return float.div(c, 100).toFixed(2)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  display: flex;
}
// ::v-deep.dart-search-wrapper
//   .dart-search-container
//   .collapse-wrapper
//   .el-col-24:nth-child(5)
//   .el-form-item__label {
//   width: 200px !important;
// }
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  flex: 1;
}
.refund {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>