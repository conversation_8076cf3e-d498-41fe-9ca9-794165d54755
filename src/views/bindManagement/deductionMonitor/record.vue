<template>
  <div class="account-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 通行列表 </span>
        </div>
      </template>
      <div class="table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          :height="300"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '35px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '35px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column
            prop="description"
            align="center"
            min-width="180"
            label="通行路段"
          />
          <el-table-column
            prop="fee"
            align="center"
            min-width="115"
            label="通行费金额(元)"
          >
            <template slot-scope="scope">
              {{ scope.row.fee | moneyFilter }}
            </template>
          </el-table-column>
          <el-table-column
            prop="serviceProviderIdValue"
            align="center"
            min-width="180"
            label="公路收费方"
          />
          <!-- <el-table-column
            prop="handleStatus"
            align="center"
            min-width="130"
            label="处理状态"
          >
            <template slot-scope="scope">
              {{ getType(typeList.handleStatusss, scope.row.handleStatus) }}
            </template>
          </el-table-column> -->
          <el-table-column
            prop="extime"
            align="center"
            min-width="180"
            label="交易时间"
          />
          <el-table-column
            fixed="right"
            label="操作"
            header-align="center"
            min-width="180"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                @click="toRecordDetail(scope.row)"
                >通行明细</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-if="total > pageSize" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="pageIndex"
          :page-sizes="[10, 20, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>

      <div class="title-wrapper">
        <span class="title" style="font-size: 16px; font-weight: 500">
          退费交易消息
        </span>
      </div>
      <div class="table">
        <el-table
          v-loading="loading2"
          :data="tableData2"
          :align="center"
          :header-align="center"
          border
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '35px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '35px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column
            prop="refundDetailId"
            align="center"
            min-width="180"
            label="退费明细编号"
          />
          <el-table-column
            prop="refundid"
            align="center"
            min-width="180"
            label="退费文件编号"
          />
          <el-table-column
            prop="serviceproviderid"
            align="center"
            min-width="180"
            label="收费方简称"
          />
          <el-table-column
            prop="cardNo"
            align="center"
            min-width="180"
            label="卡号"
          />
          <!-- <el-table-column
            prop="carNo"
            align="center"
            min-width="130"
            label="车牌颜色"
          >
            <template slot-scope="scope">
              {{ getType(licenseColorOption, scope.row.carNo) }}
            </template>
          </el-table-column> -->
          <el-table-column
            prop="carNo"
            align="center"
            min-width="100"
            label="车牌号"
          />
          <el-table-column
            prop="transactionId"
            align="center"
            min-width="180"
            label="交易编号passId"
          />
          <el-table-column
            prop="refundamount"
            align="center"
            min-width="150"
            label="退费金额（元）"
          />
          <el-table-column
            prop="originalAmount"
            align="center"
            min-width="150"
            label="原扣款金额（元）"
          />
          <el-table-column
            prop="refundTime"
            align="center"
            min-width="150"
            label="路方退费日期"
          />
          <el-table-column
            prop="time"
            align="center"
            min-width="180"
            label="交易时间"
          />
        </el-table>
      </div>
    </el-dialog>
    <transaction-detail
      ref="transaction"
      :dialogFormVisible.sync="dialogFormVisible"
      :detail="transDetail"
    >
    </transaction-detail>
  </div>
</template>

<script>
// import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import transactionDetail from './transactionDetail'
import { licenseColorOption } from '@/common/const/optionsData'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: Number,
    },
  },
  components: {
    // dartSearch,
    dartSearchItem,
    transactionDetail,
  },
  watch: {
    visible(val) {
      console.log('进来页面了', val)
      if (val) {
        // this.search.pageNum = 1
        this.getRecordList()
        this.getRefundList()
      }
    },
  },
  data() {
    return {
      licenseColorOption,
      loading: false,
      loading2: false,
      isFullscreen: false,
      showLoading: false,
      dialogFormVisible: false,
      flag: false,
      center: 'center',
      pageIndex: 1,
      pageSize: 20,
      total: '',
      tableData: [],
      tableData2: [],
      transDetail: {},
    }
  },
  methods: {
    toRecordDetail(record) {
      let params = {}
      // let detail = JSON.parse(JSON.stringify(record))
      if (this.flag) {
        params = {
          issuerId: record.issuerid,
          messageId: record.messageid,
          transId: record.transid,
          serviceProviderId: record.serviceProviderId,
        }
      } else {
        params = {
          flag: false,
          transactionId: record.transactionId,
        }
      }

      console.log('入参', params)
      this.$store
        .dispatch('bindManagement/getPassDetail', params)
        .then((res) => {
          this.transDetail = res
          this.dialogFormVisible = true
        })
        .catch((err) => {})
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    onSearchHandle() {
      this.pageIndex = 1
      this.getRecordList()
    },
    getRecordList() {
      let params = {
        deductionId: this.id,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
      }
      this.$store
        .dispatch('bindManagement/getDeductionsPass', params)
        .then((res) => {
          console.log('getRecordList', res)
          this.tableData = res.records
          this.total = res.total
          this.flag = res.flag
        })
        .catch((err) => {})
    },
    getRefundList() {
      this.loading2 = true
      let params = {
        deductionId: this.id,
      }
      console.log(params)
      this.$request({
        url: this.$interfaces.refundTrans,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.loading2 = false
          if (res.code == 200) {
            this.tableData2 = res.data
          }
        })
        .catch((error) => {
          this.loading2 = false
          console.log('err', error)
        })
    },
    changePage(page) {
      this.pageIndex = page
      this.getRecordList()
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.getRecordList()
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  // .el-row {
  //   display: flex;
  // }
  // .el-col {
  //   margin-top: 0 !important;
  // }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}

.total-price {
  display: flex;
  align-items: center;
  margin-top: 10px;
  color: red;
  font-size: 14px;
}
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.title-wrapper {
  display: flex;
  justify-content: center;
}
</style>
