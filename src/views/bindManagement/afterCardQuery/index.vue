<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌号：" prop="carNo">
            <el-input
              v-model="search.carNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" placeholder="请选择" clearable>
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="卡号：" prop="cardNo">
            <el-input
              v-model="search.cardNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="网点名称：" prop="deptName">
            <el-input
              v-model="search.deptName"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="补缴开始时间：" prop="staTime">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.staTime"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="补缴结束时间：" prop="endTime">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.endTime"
            >
            </el-date-picker>
          </dart-search-item>
          <!-- <dart-search-item label="注销时间：" prop="stopDate">
            <el-date-picker
              type="date"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd"
              v-model="search.stopDate"
              clearable
            >
            </el-date-picker>
          </dart-search-item> -->
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px; margin-left: 35px"
            :span="24"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              ><i class="el-icon-search"></i> 搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <!-- <span class="collapse" v-if="!isCollapse" @click="isCollapse = true"
              >展开</span
            >
            <span class="collapse" v-else @click="isCollapse = false"
              >收起</span
            > -->
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column
          prop="orderId"
          align="center"
          min-width="180"
          label="订单编号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.orderId }}
              </div>
              <span> {{ scope.row.orderId }}</span>
            </el-tooltip>
          </template></el-table-column
        >
        <el-table-column
          prop="amount"
          align="center"
          min-width="120"
          label="补缴金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.amount | moneyFilter }}
          </template></el-table-column
        >
        <el-table-column
          prop="payStatus"
          align="center"
          min-width="120"
          label="订单状态"
        />
        <el-table-column
          prop="bankOrderNo"
          align="center"
          min-width="180"
          label="第三方支付订单编号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.bankOrderNo }}
              </div>
              <span> {{ scope.row.bankOrderNo }}</span>
            </el-tooltip>
          </template></el-table-column
        >
        <el-table-column
          prop="carNo"
          align="center"
          min-width="120"
          label="车牌号"
        />
        <el-table-column
          prop="carColor"
          align="center"
          min-width="120"
          label="车牌颜色"
        >
          <template slot-scope="scope">
            {{ getType(licenseColorOption, scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="190"
          label="卡号"
        />

        <el-table-column
          prop="rechargeType"
          align="center"
          min-width="120"
          label="补缴充值方式"
        />
        <el-table-column
          prop="createTime"
          align="center"
          min-width="160"
          label="补缴时间"
        />
        <el-table-column
          prop="deptName"
          align="center"
          min-width="120"
          label="操作网点"
        />
        <el-table-column
          prop="realName"
          align="center"
          min-width="120"
          label="操作员"
        />
        <!-- <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="toDetail(scope.row.cardMastId)"
              >详情</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')
// import { mapGetters } from 'vuex'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      licenseColorOption,
      loading: false,
      isCollapse: false,
      center: 'center',
      search: {
        carColor: '',
        carNo: '',
        cardNo: '',
        deptName: '', //网点
        endTime: '',
        staTime: '',
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
    }
  },
  // computed: {
  //   ...mapGetters(['refundSearch']),
  // },
  created() {
    // console.log('refundRearch', this.refundSearch)
    // if (Object.keys(this.refundSearch).length > 0) {
    //   this.search = this.refundSearch
    // }
    // this.getCardPayList()
  },
  methods: {
    // 查询版本信息
    getCardPayList() {
      console.log(this.search)
      this.loading = true
      let params = { ...this.search }
      params.staTime = params.staTime
        ? moment(params.staTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.endTime = params.endTime
        ? moment(params.endTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      this.$request({
        url: this.$interfaces.getAfterCardPay,
        method: 'post',
        data: params,
        timeout: 1 * 60 * 1000, //设置超时时间1分钟
      })
        .then((res) => {
          this.loading = false
          // console.log(res, 'res')
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
        .catch((error) => {})
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getAuditBlackSearch() {
      this.loading = true
      let params = { ...this.search }
      params.createTimeFrom = params.createTimeFrom
        ? moment(params.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeTo = params.createTimeTo
        ? moment(params.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeFrom = params.payTimeFrom
        ? moment(params.payTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeTo = params.payTimeTo
        ? moment(params.payTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('prams入参', params)
      this.$store
        .dispatch('bindManagement/auditBlackSearch', params)
        .then((res) => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.iPage.records
          this.total = res.iPage.total
          this.totalPrice = res.oweFee
          this.radio = []
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    // toDetail(id) {
    //   let params = { cardMastId: id }
    //   this.$request({
    //     url: this.$interfaces.getCarInfoDetail,
    //     method: 'post',
    //     data: params,
    //   })
    //     .then((res) => {
    //       console.log(res, 'res')
    //       if (res.code == 200) {
    //         this.cardDTO = res.data.cardDTO
    //         this.carListDTO = res.data.carListDTO
    //         this.obuMastList = res.data.obuMastList
    //         this.cancelCustDTO = res.data.cancelCustDTO
    //         this.dialogDetail = true
    //       }
    //     })
    //     .catch((error) => {})
    // },
    changePage(page) {
      this.search.pageNum = page
      this.getCardPayList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getCardPayList()
    },
    onSearchHandle() {
      this.search.pageNum = 1
      // //缓存搜索参数
      // this.$store
      //   .dispatch('containerRefund/setRefundSearch', this.search)
      //   .then((res) => {
      //     console.log('缓存过后的search', res)
      //   })
      this.getCardPayList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
      // //清除缓存
      // this.$store
      //   .dispatch('containerRefund/removeRefundSearch')
      //   .then((res) => {})
    },
    updateList() {
      this.search.pageNum = 1
      this.search.pageSize = 20
      //   this.dialogDetail = false
      this.getCardPayList()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  display: flex;
}
::v-deep.dart-search-wrapper
  .dart-search-container
  .collapse-wrapper
  .el-col-24:nth-child(5)
  .el-form-item__label {
  width: 200px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  flex: 1;
}
.refund {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
