<template>
  <div class="import-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      title="卡号不存在的争议流水导入文件"
      :before-close="handleCloseIcon"
      width="45%"
    >
      <div class="selector g-flex g-flex-start g-flex-align-center">
        <div class="label">选择执行业务：</div>
        <el-radio-group v-model="formData.dealType" size="mini">
          <el-radio label="0">扣款</el-radio>
          <!-- <el-radio label="1">扣款转补缴</el-radio> -->
          <el-radio label="1">直接生成补缴单</el-radio>
        </el-radio-group>
      </div>
      <fieldset class="fieldset">
        <legend class="fieldset-legend">附件上传</legend>

        <div slot="tip" class="el-upload__tip">1、仅支持xls格式的Excel文件</div>
        <div slot="tip" class="el-upload__tip">
          2、导入字段包含：[ 交易卡号 | 交易编号 ｜ 金额（元）]
        </div>
        <div slot="tip" class="el-upload__tip">
          <a
            style="font-size: 14px;cursor:pointer;color:#409EFF"
            @click="exportTemp"
            >导入文件模板下载</a
          >
        </div>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
import { paymentSourceOptions } from '@/common/const/optionsData.js'
import { preDownloadTemplate } from '@/api/equipment'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFormVisible: false,

      showLoading: false,
      formData: {
        file: '',
        dealType: '0'
      },
      fileList: [],
      paymentSourceOptions,
      gridData: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fileList = []
        this.formData.file = ''
        this.formData.dealType = '0'
      }
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    moneyFilter(val) {
      let value = val
      if (!value) return value
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
    onResponseSubmit() {
      this.$emit('uploadSuccess')
    },
    submitUpload() {
      if (!this.formData.dealType) {
        this.$message({
          type: 'error',
          message: '请选择执行业务'
        })
        return
      }
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件'
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }

      this.upload()
    },
    upload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('dealType', this.formData.dealType)
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/preDedDisputeManual/importCardNotExitJour'
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken()
          }
        })
        .then(res => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = ''

          if (res.data.code == 200) {
            this.$message({
              type: 'success',
              message: '导入成功'
            })
            this.dialogFormVisible = false
            this.$emit('uploadSuccess')
          } else {
            console.log(res,11);
            this.$message.error(res.data.msg)
          }
        })
        .catch(err => {
          this.showLoading = false
          // this.$refs.upload.clearFiles()
          // this.formData.file = ''
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
    exportTemp() {
      let params = {}
      let fileObj = {
        fileName: '卡号不存在的争议流水导入文件模版.xlsx'
      }
      this.exportFile(params, preDownloadTemplate, fileObj)
    },
    /**
     * 下载导出
     * @param {*} data 接口入参
     * @param {*} apiMethod 导出的接口方法
     * @param {*} fileObj 如果是流blob的形式，则传文件对象
     * @returns
     */
    async exportFile(data, apiMethod, fileObj) {
      this.loading = true
      if (!apiMethod) {
        console.error('缺少导出地址apiMethod！')
        return
      }
      let res = await apiMethod(data)
      this.loading = false
      if (fileObj) {
        // 如果是流blob的形式
        const link = document.createElement('a')
        let blob = new Blob([res]) //构造一个blob对象来处理数据
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = `${fileObj.fileName}` //下载的文件名
        document.body.appendChild(link)
        link.click() // 执行下载
        document.body.removeChild(link) // 释放标签
        return
      }
      // 如果是链接的形式
      if (res.code == 200) {
        let url = res.data
        let decodeUrl = decode(url)
        // console.log(decodeUrl,'地址')
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.fieldset-legend {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  width: 80px;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>