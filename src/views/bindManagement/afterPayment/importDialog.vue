<template>
  <div class="import-dialog"
       v-loading.fullscreen.lock="showLoading">
    <el-dialog :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :show-close="true"
               title="补缴记录导入"
               :before-close="handleCloseIcon"
               width="45%">
      <div class="selector g-flex g-flex-start g-flex-align-center">
        <div class="label">生成方式：</div>
        <el-select v-model="formData.source"
                   placeholder="请选择"
                   clearable>
          <el-option v-for="item in paymentSourceOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </div>
      <fieldset class="fieldset">
        <legend class="fieldset-legend">附件上传</legend>

        <div slot="tip"
             class="el-upload__tip">1、仅支持xls格式的Excel文件</div>
        <div slot="tip"
             class="el-upload__tip">
          2、导入字段包含：[ 扣款流水号 | 补缴金额（元） ]
        </div>
        <div slot="tip"
             class="el-upload__tip"><a style="font-size: 14px;cursor:pointer;color:#409EFF"
             href="https://portal.gxetc.com.cn/public-static/file/补缴批量导入模板.xlsx">补缴批量导入模板</a></div>
        <el-upload class="upload"
                   ref="upload"
                   :on-remove="handleRemove"
                   :auto-upload="false"
                   action="action"
                   accept=".xls,.xlsx"
                   :file-list="fileList"
                   :multiple="false"
                   :on-change="onChange">
          <el-button slot="trigger"
                     size="small"
                     type="primary">选取文件</el-button>
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload"
                   type="primary"
                   size="mini">确定</el-button>
        <el-button size="mini"
                   @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="导入结果"
               :visible.sync="responseDialogVisible"
               width="55%"
               center>
      <el-table :data="gridData"
                border
                height="280px">
        <el-table-column property="sern"
                         label="扣款流水号"
                         width="250"></el-table-column>
        <el-table-column property="amount"
                         label="补缴金额（元）"
                         width="150">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.amount) }}
          </template>
        </el-table-column>
        <el-table-column property="msg"
                         label="导入结果"></el-table-column>
      </el-table>
      <span slot="footer"
            class="dialog-footer">
        <el-button type="primary"
                   @click="onResponseSubmit">知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
import { paymentSourceOptions } from '@/common/const/optionsData.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,

      showLoading: false,
      formData: {
        file: '',
        source: '',
      },
      fileList: [],
      paymentSourceOptions,
      responseDialogVisible: false,
      gridData: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fileList = [];
        this.formData.file = '';
        this.formData.source = '';
      }
      this.responseDialogVisible = false;
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    moneyFilter(val) {
      let value = val
      if (!value) return value
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
    onResponseSubmit() {
      this.responseDialogVisible = false;
      this.$emit('uploadSuccess')
    },
    submitUpload() {
      if (!this.formData.source) {
        this.$message({
          type: 'error',
          message: '请选择生成方式',
        })
        return
      }
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      this.upload()
    },
    upload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('source', this.formData.source)
      this.$store
        .dispatch('bindManagement/imports', formData)
        .then((res) => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = '';

          if (res.code == 200 && res.data && res.data.length) {
            this.gridData = res.data;
            this.responseDialogVisible = true;
          } else {
            this.$message({
              type: 'success',
              message: '导入成功',
            })
            this.$emit('uploadSuccess')
          }

        })
        .catch((err) => {
          this.showLoading = false
          // this.$refs.upload.clearFiles()
          // this.formData.file = ''
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.fieldset-legend {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  width: 80px;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>