<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="补缴类型：" prop="listType">
            <el-select v-model="search.listType" placeholder="请选择">
              <el-option
                v-for="item in typeList.listTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌：" prop="carNo">
            <el-input v-model="search.carNo" placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="carNoColor">
            <el-select v-model="search.carNoColor" placeholder="请选择">
              <el-option
                v-for="item in typeList.carColors"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="处理方式：" prop="handleStatus">
            <el-select v-model="search.handleStatus" placeholder="请选择">
              <el-option
                v-for="item in typeList.handleStatusss"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="生成时间从：" prop="createTimeFrom">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.createTimeFrom"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="生成时间到：" prop="createTimeTo">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.createTimeTo"
            >
            </el-date-picker>
          </dart-search-item>
          <div class="collapse-wrapper" v-show="isCollapse">
            <dart-search-item label="生成方式：" prop="source">
              <el-select v-model="search.source" placeholder="请选择">
                <el-option
                  v-for="item in typeList.source"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="支付时间从：" prop="payTimeFrom">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                v-model="search.payTimeFrom"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="支付时间到：" prop="payTimeTo">
              <el-date-picker
                type="datetime"
                placeholder="选择日期时间"
                default-time="23:59:59"
                v-model="search.payTimeTo"
              >
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="序号：" prop="id">
              <el-input v-model="search.id" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="[部中心下载]是否本方车辆：" prop="isJet">
              <el-select v-model="search.isJet" placeholder="请选择">
                <el-option
                  v-for="item in isSelfCar"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="撤销申请标识：" prop="cancelApplyFlag">
              <el-select v-model="search.cancelApplyFlag" placeholder="请选择">
                <el-option
                  v-for="item in typeList.cancelApplyFlags"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="扣款流水号：" prop="requestId">
              <el-input v-model="search.requestId" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="审核版本：" prop="version">
              <el-select
                v-model="search.version"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="(item, index) in versionList"
                  :key="index"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="补缴订单状态：" prop="delFlag">
              <el-select
                v-model="search.delFlag"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in orderStatusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="ETC卡号：" prop="cardNo">
              <el-input v-model="search.cardNo" placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="银行名称：" prop="bankId">
              <el-select
                v-model="search.bankId"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in typeList.orgIds"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="代扣机构：" prop="payOrgId">
              <el-select
                v-model="search.payOrgId"
                filterable
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in typeList.orgIds"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="产品类型：" prop="productType">
              <el-select v-model="search.productType" placeholder="请选择">
                <el-option
                  v-for="item in gxCardTypeAllOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
          </div>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px; margin-left: 35px"
            :span="24"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              ><i class="el-icon-search"></i> 搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <!-- </dart-search-item> -->
            <!-- <dart-search-item :is-button="true" style="margin-top: 10px"> -->
            <el-button
              type="primary"
              size="mini"
              @click="onApplyHandle('audit')"
              >审核</el-button
            >
            <el-button size="mini" type="primary" @click="onExportHandle"
              ><i class="el-icon-download"></i> 导出</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="onApplyHandle('cancelApply')"
              >撤销申请</el-button
            >
            <el-button
              size="mini"
              type="warning"
              @click="onApplyHandle('cancelApplyAudit')"
              >撤销申请审核</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="importDialogVisible = true"
              ><i class="el-icon-upload"></i> 批量导入</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="versionDialogVisible = true"
              ><i class="el-icon-upload"></i> 批量导入审核</el-button
            >
            <el-button
              size="mini"
              type="primary"
              v-permisaction="['preDedDisputeManual:importCardNotExitJour']"
              @click="disputeDialogVisible = true"
              ><i class="el-icon-upload"></i> 卡号不存在争议导入</el-button
            >
            <span class="collapse" v-if="!isCollapse" @click="isCollapse = true"
              >展开</span
            >
            <span class="collapse" v-else @click="isCollapse = false"
              >收起</span
            >
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column label="选择" width="50" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change="getCurrentRow(scope.row.id, scope.row.transactionId)"
            >
              <span></span>
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="id" align="center" label="序号"
          ><template slot-scope="scope">
            <span
              @click="onApplyHandle('scanDetail', scope.row.id)"
              class="text"
              >{{ scope.row.id }}</span
            >
          </template></el-table-column
        >
        <el-table-column
          prop="requestId"
          align="center"
          label="扣款流水号"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.requestId }}
              </div>
              <span> {{ scope.row.requestId }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="listType"
          align="center"
          min-width="140"
          label="补缴类型"
        >
          <template slot-scope="scope">
            {{ getType(typeList.listTypes, scope.row.listType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="source"
          align="center"
          label="生成方式"
          min-width="150"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ getType(typeList.source, scope.row.source) }}
              </div>
              <span> {{ getType(typeList.source, scope.row.source) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="custName"
          align="center"
          min-width="140"
          label="用户名"
        />
        <el-table-column
          prop="productType"
          align="center"
          min-width="140"
          label="产品类型"
        >
          <template slot-scope="scope">
            {{ getType(gxCardTypeAllOptions, scope.row.productType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="relateCardId"
          align="center"
          min-width="180"
          label="卡号"
        />
        <el-table-column
          prop="relateObu"
          align="center"
          min-width="180"
          label="OBU号"
        />
        <el-table-column
          prop="carNo"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column prop="carNoColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.carNoColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="vehicleTypeName"
          align="center"
          min-width="120"
          label="客货类型"
        />
        <el-table-column
          prop="cancelUpdateDate"
          align="center"
          min-width="180"
          label="注销时间"
        />
        <el-table-column
          prop="deptName"
          align="center"
          min-width="160"
          label="注销办理网点"
        />
        <el-table-column
          prop="oweFeeStr"
          align="center"
          min-width="115"
          label="欠费金额(元)"
        />
        <el-table-column align="center" label="服务费(元)" min-width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.serviceAmount || scope.row.serviceAmount == 0
                ? '--'
                : divNumbers(scope.row.serviceAmount)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳金(元)" min-width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.overdueAmount || scope.row.overdueAmount == 0
                ? '--'
                : divNumbers(scope.row.overdueAmount)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳天数(天)" min-width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.overdueDay || scope.row.overdueDay == '0'
                ? '--'
                : scope.row.overdueDay
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          align="center"
          min-width="180"
          label="补缴生成时间"
        />
        <el-table-column
          prop="auditTime"
          align="center"
          min-width="180"
          label="补缴通过审核时间"
        />
        <el-table-column prop="roleType" align="center" label="权限类型">
          <template slot-scope="scope" v-if="scope.row.roleType == 1">
            全类型
          </template>
          <template slot-scope="scope" v-else-if="scope.row.roleType == 2">
            挂起
          </template>
          <template v-else> </template>
        </el-table-column>
        <el-table-column prop="bankIdStr" align="center" label="银行名称" />
        <el-table-column prop="payOrgIdStr" align="center" label="代扣机构" />
        <el-table-column
          prop="createName"
          align="center"
          min-width="100"
          label="创建人"
        />
        <el-table-column
          prop="handleStatus"
          align="center"
          min-width="130"
          label="处理状态"
        >
          <template slot-scope="scope">
            <el-tag
              style="width: 96px"
              :type="getHandleStatusType(scope.row.handleStatus)"
              >{{
                getType(typeList.handleStatusss, scope.row.handleStatus)
              }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column prop="auditName" align="center" label="审核人" />
        <el-table-column
          prop="reason"
          align="center"
          label="预追缴原因"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.reason || '' }}
              </div>
              <span> {{ scope.row.reason || '' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="creationTime"
          align="center"
          min-width="180"
          label="预追缴名单生成时间"
        />
        <el-table-column
          prop="transactionId"
          align="center"
          min-width="210"
          label="是否本方车辆|版本号"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.isJet == '1' ? '本方|' : ''
                }}{{ scope.row.version }}
              </div>
              <span>
                {{ scope.row.isJet == '1' ? '本方|' : ''
                }}{{ scope.row.version }}</span
              >
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="cancelApplyFlag"
          align="center"
          label="撤销申请标识"
          min-width="110"
        >
          <template slot-scope="scope">
            {{ getType(typeList.cancelApplyFlags, scope.row.cancelApplyFlag) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cancelApplyName"
          align="center"
          label="撤销申请人"
          min-width="100"
        />
        <el-table-column
          prop="cancelApplyTime"
          align="center"
          min-width="180"
          label="撤销申请时间"
        />
        <el-table-column
          prop="cancelAuditName"
          align="center"
          label="撤销申请审核人"
        />
        <el-table-column
          prop="cancelAuditTime"
          align="center"
          min-width="180"
          label="撤销申请审核时间"
        />
        <el-table-column
          prop="cancelRemark"
          align="center"
          min-width="180"
          label="撤销原因"
        />
        <el-table-column
          prop="delFlag"
          min-width="120"
          align="center"
          label="补缴订单状态"
        >
          <template slot-scope="scope">
            {{ getType(orderStatusList, scope.row.delFlag) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="total-price">
        <div style="margin-right: 30px">交易条数合计：[ {{ total }} ] 笔</div>
        <div>欠费金额合计：[ {{ totalPrice | moneyFilter }} ] 元</div>
      </div>
    </div>
    <div v-if="total > search.pageSize" class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <paymentDetail
      ref="paymentDetail"
      :dialogFormVisible.sync="dialogDetail"
      :handleId="handleId"
      :paymentDetail="paymentDetail"
      :disputedetail="disputedetail"
      :typeList="typeList"
      :type="type"
      @updateList="updateList"
    ></paymentDetail>
    <import-dialog
      :visible.sync="importDialogVisible"
      @uploadSuccess="updateList"
    >
    </import-dialog>
    <version-dialog
      :visible.sync="versionDialogVisible"
      :typeList="typeList"
      @updateList="updateList"
    >
    </version-dialog>
    <dispute-dialog
      :visible.sync="disputeDialogVisible"
      :typeList="typeList"
      @updateList="updateList"
    >
    </dispute-dialog>
  </div>
</template>

<script>
// import handleRefund from './handleRefund.vue'
import importDialog from './importDialog'
import paymentDetail from './paymentDetail'
import versionDialog from './versionDialog'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { getToken } from '@/utils/auth'
import float from '@/common/method/float.js'
import { gxCardTypeAllOptions } from '@/common/const/optionsData'
import disputeDialog from './dispute-import.vue'

var moment = require('moment')
// import { decode } from 'js-base64'
// import { mapGetters } from 'vuex'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    paymentDetail,
    importDialog,
    versionDialog,
    disputeDialog
  },
  data() {
    return {
      loading: false,
      dialogDetail: false,
      importDialogVisible: false,
      versionDialogVisible: false,
      disputeDialogVisible:false,
      isCollapse: false,
      center: 'center',
      search: {
        requestId: '', //扣款流水
        cancelApplyFlag: '',
        carNo: '',
        carNoColor: '',
        createTimeFrom: '',
        createTimeTo: '',
        handleStatus: '',
        bankId: '',
        payOrgId: '',
        id: '',
        isJet: '',
        listType: '',
        payTimeFrom: '',
        payTimeTo: '',
        source: '',
        pageNum: 1,
        pageSize: 20,
        version: '',
        delFlag: '0', //补缴订单状态，0 正常  1删除
        productType: ''
      },
      total: '',
      totalPrice: '',
      radio: {},
      handleId: null,
      typeList: {
        orgIds: [], //代扣机构
        bindingBankType: [], //绑定机构
        cancelApplyFlags: [], //撤销申请标识
        carColors: [], //车牌颜色
        handleStatusss: [], //处理状态
        listTypes: [], //补缴类型
        source: [], //生成方式
        statuss: [], //
        deductionType: [] //扣款类型
      },
      isSelfCar: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '1',
          label: '是'
        }
      ],
      orderStatusList: [
        {
          value: '0',
          label: '正常'
        },
        {
          value: '1',
          label: '删除'
        }
      ],
      tableData: [],
      paymentDetail: {},
      type: '', //详情类型
      disputedetail: {},
      versionList: [],
      gxCardTypeAllOptions: [
        { label: '全部', value: '' },
        ...gxCardTypeAllOptions
      ]
    }
  },
  // computed: {
  //   ...mapGetters(['refundSearch']),
  // },
  created() {
    // console.log('refundRearch', this.refundSearch)
    // if (Object.keys(this.refundSearch).length > 0) {
    //   this.search = this.refundSearch
    // }
    // this.getAuditBlackSearch()
    this.getDictionaries()
    this.getDictionariesNew()
    this.getAuditVersions()
  },
  methods: {
    // 查询版本信息
    getAuditVersions() {
      this.$request({
        url: this.$interfaces.listAuditVersions,
        method: 'post'
      })
        .then(res => {
          console.log('this.$interfaces.listAuditVersions', res.data)
          this.versionList = res.data
        })
        .catch(error => {})
    },
    getHandleStatusType(val) {
      // 0-初始化 1-审核通过 2-已补缴 3-审核拒绝 4-已撤销（退费） 5-已撤销（人工）
      let type = {
        0: 'info',
        1: 'info',
        2: 'success',
        3: 'danger',
        4: 'warning',
        5: 'warning'
      }
      return type && type[val] ? type[val] : ''
    },
    getDictionaries() {
      this.$store
        .dispatch('bindManagement/dictionaries')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach(key => {
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
          })
        })
        .catch(err => {})
    },
    getDictionariesNew() {
      this.$store
        .dispatch('bindManagement/deductionsInit')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          let originList = res
          Object.keys(typeList).forEach(key => {
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
          })
        })
        .catch(err => {})
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        lvTypeList.push({
          label: '全部',
          value: ''
        })
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    getAuditBlackSearch() {
      this.loading = true
      let params = { ...this.search }
      params.createTimeFrom = params.createTimeFrom
        ? moment(params.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeTo = params.createTimeTo
        ? moment(params.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeFrom = params.payTimeFrom
        ? moment(params.payTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeTo = params.payTimeTo
        ? moment(params.payTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('prams入参', params)
      this.$store
        .dispatch('bindManagement/auditBlackSearch', params)
        .then(res => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.iPage.records
          this.total = res.iPage.total
          this.totalPrice = res.oweFee
          this.radio = []
        })
        .catch(err => {
          this.loading = false
          console.log('err', err)
        })
    },
    getPaymentDetail(id, type) {
      console.log('id', id)
      let params = { auditBlackListId: id }
      console.log('交易明细入参', params)
      this.$store
        .dispatch('bindManagement/auditBlackDetailInfo', params)
        .then(res => {
          console.log('交易明细详情列表', res)
          this.paymentDetail = res
          this.type = type
          this.dialogDetail = true
        })
        .catch(err => {})
      let data = { auditBlackListId: id }
      this.$store
        .dispatch('bindManagement/hsAuditDisputedetail', data)
        .then(res => {
          this.disputedetail = res
          console.log('11', res)
        })
        .catch(err => {})
    },
    changePage(page) {
      this.radio = {}
      this.search.pageNum = page
      this.getAuditBlackSearch()
    },
    handleSizeChange(pageSize) {
      this.radio = {}
      this.search.pageSize = pageSize
      this.getAuditBlackSearch()
    },
    onSearchHandle() {
      this.radio = {}
      this.search.pageNum = 1
      console.log('当前页面audit', this.search.pageNum)
      // //缓存搜索参数
      // this.$store
      //   .dispatch('containerRefund/setRefundSearch', this.search)
      //   .then((res) => {
      //     console.log('缓存过后的search', res)
      //   })
      this.getAuditBlackSearch()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.radio = []
      // this.search.isJet = '1'
      this.search.pageNum = 1
      this.search.pageSize = 20
      this.search.delFlag = '0'
      // //清除缓存
      // this.$store
      //   .dispatch('containerRefund/removeRefundSearch')
      //   .then((res) => {})
    },
    updateList() {
      this.radio = []
      this.search.pageNum = 1
      this.search.pageSize = 20
      this.dialogDetail = false
      this.importDialogVisible = false
      this.versionDialogVisible = false
      this.disputeDialogVisible = false
      this.getAuditBlackSearch()
    },
    onApplyHandle(type, id) {
      console.log('onApplyHandle', id)
      if (!id) {
        if (!this.handleId) {
          this.$confirm('请选中一条记录！', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        } else {
          let type = typeof this.radio
          let length = Object.keys(this.radio).length
          if (type === 'object' && length === 0) {
            this.$confirm('请选中一条记录！', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
            return
          }
        }
        this.checkAuditStatusHandle(this.handleId, type, res => {
          if (res) {
            this.getPaymentDetail(this.handleId, type)
          }
        })
      } else {
        this.getPaymentDetail(id, type)
      }
    },
    checkAuditStatusHandle(id, type, callback) {
      let url = ''
      if (type == 'audit') {
        url = 'bindManagement/checkAuditHandleState'
      } else if (type == 'cancelApply') {
        url = 'bindManagement/checkCancelAuditHandleState'
      } else if (type == 'cancelApplyAudit') {
        url = 'bindManagement/checkAuditCancelAuditDetailInfoApply'
      }
      let params = { auditBlackListId: id }
      this.$store
        .dispatch(url, params)
        .then(res => {
          callback(true)
        })
        .catch(err => {
          callback(false)
        })
    },
    onExportHandle() {
      // this.loading = true
      let params = { ...this.search }
      params.createTimeFrom = params.createTimeFrom
        ? moment(params.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeTo = params.createTimeTo
        ? moment(params.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeFrom = params.payTimeFrom
        ? moment(params.payTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeTo = params.payTimeTo
        ? moment(params.payTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''

      // www.baidu.com?a=1&b=2&c=3
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/business/audit/blackList/exportSearch?Authorization=Bearer ' +
        getToken()
      for (let i in params) {
        if (params[i] != '') {
          url += '&' + i + '=' + params[i]
        }
      }
      console.log('url', url)
      window.open(url)
    },
    getCurrentRow(id) {
      this.handleId = id
    },
    divNumbers(c) {
      return float.div(c, 100).toFixed(2)
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  display: flex;
}
::v-deep.dart-search-wrapper
  .dart-search-container
  .collapse-wrapper
  .el-col-24:nth-child(5)
  .el-form-item__label {
  width: 200px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  flex: 1;
}
.refund {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
