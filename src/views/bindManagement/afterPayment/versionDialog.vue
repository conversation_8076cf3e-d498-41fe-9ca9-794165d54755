<template>
  <div class="account-dialog"
       v-loading.fullscreen.lock="showLoading">
    <el-dialog :visible.sync="visible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :fullscreen="isFullscreen"
               :show-close="false"
               width="80%">
      <template slot="title">
        <div class="btn-wrapper">
          <i @click="isFullscreen = true"
             v-if="!isFullscreen"
             class="el-icon-full-screen"></i>
          <i @click="isFullscreen = false"
             v-else
             class="el-icon-copy-document"></i>
          <i @click="close()"
             class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 补缴记录导入审核 </span>
        </div>
      </template>
      <div class="search-list">
        <dart-search :formSpan="24"
                     :gutter="20"
                     :searchOperation='false'
                     ref="searchForm1"
                     label-position="right"
                     :fontWidth="2">
          <template slot="search-form"
                    style="padding-left: 10px">
            <dart-search-item label="生成方式："
                              prop="source">
              <el-select v-model="source"
                         @change='onSourceChange'
                         placeholder="请选择">
                <el-option v-for="item in paymentSourceOptions"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </dart-search-item>
            <dart-search-item label="待审核版本："
                              prop="company">
              <el-select v-model="version"
                         placeholder="请选择">
                <el-option v-for="(item, index) in versionList"
                           :key="index"
                           :label="item"
                           :value="item" />
              </el-select>
            </dart-search-item>

          </template>
        </dart-search>
      </div>
      <div class="table">
        <el-table v-loading="loading"
                  :data="tableData"
                  :align="center"
                  :header-align="center"
                  border
                  :height="300"
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '35px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '35px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id">
          <el-table-column prop="id"
                           align="center"
                           label="序号"><template slot-scope="scope">
              <span @click="onApplyHandle('scanDetail', scope.row.id)"
                    class="text">{{ scope.row.id }}</span>
            </template></el-table-column>
          <el-table-column prop="requestId"
                           align="center"
                           label="待补缴请款交易ID"
                           min-width="200">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ scope.row.requestId }}
                </div>
                <span> {{ scope.row.requestId }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="listType"
                           align="center"
                           min-width="140"
                           label="补缴类型">
            <template slot-scope="scope">
              {{ getType(typeList.listTypes, scope.row.listType) }}
            </template>
          </el-table-column>
          <el-table-column prop="source"
                           align="center"
                           label="生成方式"
                           min-width="150">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ getType(typeList.source, scope.row.source) }}
                </div>
                <span> {{ getType(typeList.source, scope.row.source) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="relateCardId"
                           align="center"
                           min-width="180"
                           label="卡号" />
          <el-table-column prop="relateObu"
                           align="center"
                           min-width="180"
                           label="OBU号" />
          <el-table-column prop="carNo"
                           align="center"
                           min-width="100"
                           label="车牌号" />
          <el-table-column prop="carNoColor"
                           align="center"
                           label="车牌颜色">
            <template slot-scope="scope">
              {{ getType(typeList.carColors, scope.row.carNoColor) }}
            </template>
          </el-table-column>
          <el-table-column prop="oweFee"
                           align="center"
                           min-width="115"
                           label="欠费金额(元)">
            <template slot-scope="scope">
              {{ scope.row.oweFee | moneyFilter }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime"
                           align="center"
                           min-width="180"
                           label="补缴生成时间" />
          <el-table-column prop="roleType"
                           align="center"
                           label="权限类型">
            <template slot-scope="scope"
                      v-if="scope.row.roleType == 1">
              全类型
            </template>
            <template slot-scope="scope"
                      v-else-if="scope.row.roleType == 2">
              挂起
            </template>
            <template v-else> </template>
          </el-table-column>
          <el-table-column prop="createName"
                           align="center"
                           min-width="100"
                           label="创建人" />
          <el-table-column prop="handleStatus"
                           align="center"
                           min-width="130"
                           label="处理状态">
            <template slot-scope="scope">
              {{ getType(typeList.handleStatusss, scope.row.handleStatus) }}
            </template>
          </el-table-column>
           <el-table-column prop="bankIdStr"
                         align="center"
                         label="银行名称" />
        <el-table-column prop="payOrgIdStr"
                         align="center"
                         label="代扣机构" />
          <el-table-column prop="auditName"
                           align="center"
                           label="审核人" />
          <el-table-column prop="reason"
                           align="center"
                           label="预追缴原因"
                           min-width="100">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ scope.row.reason }}
                </div>
                <span> {{ scope.row.reason }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="creationTime"
                           align="center"
                           min-width="180"
                           label="预追缴名单生成时间" />
          <el-table-column prop="transactionId"
                           align="center"
                           min-width="200"
                           label="是否本方车辆|版本号" />
          <el-table-column prop="cancelApplyFlag"
                           align="center"
                           label="撤销申请标识"
                           min-width="110">
            <template slot-scope="scope">
              {{
                getType(typeList.cancelApplyFlags, scope.row.cancelApplyFlag)
              }}
            </template>
          </el-table-column>
          <el-table-column prop="cancelApplyName"
                           align="center"
                           label="撤销申请人"
                           min-width="100" />
          <el-table-column prop="cancelApplyTime"
                           align="center"
                           min-width="180"
                           label="撤销申请时间" />
          <el-table-column prop="cancelAuditName"
                           align="center"
                           label="撤销申请审核人" />
          <el-table-column prop="cancelAuditTime"
                           align="center"
                           min-width="180"
                           label="撤销申请审核时间" />
        </el-table>
        <div class="total-price">
          <div style="margin-right: 30px">
            交易条数合计：[ {{ tableData.length }} ] 笔
          </div>
          <div style="margin-right: 30px">当前审核版本：[ {{ version }} ]</div>
          <div>欠费金额合计：[ {{ totalPrice }} ] 元</div>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="afterPayImportAudit()"
                   type="primary"
                   size="mini">
          通过
        </el-button>
        <el-button @click="afterPayImportReject()"
                   type="danger"
                   size="mini">
          驳回
        </el-button>
        <el-button @click="close()"
                   size="mini">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { paymentSourceOptions } from '@/common/const/optionsData.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    typeList: {
      type: Object,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
  },
  watch: {
    visible(val) {
      if (val) {
        this.getVersionList()
        this.source = '1'
        this.versionList = [];
        this.version = [];
      }
    },
    version(val) {
      if (val) {
        this.onSearchHandle()
      }
    },
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      center: 'center',
      version: '',
      total: '',
      totalPrice: '',
      versionList: [],
      tableData: [],
      source: '1',
      paymentSourceOptions
    }
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    onSearchHandle() {
      this.loading = true
      this.$store
        .dispatch('bindManagement/importsAuditSearch', {
          version: this.version,
          source: this.source
        })
        .then((res) => {
          this.loading = false
          console.log('版本数据', res)
          this.tableData = res.list
          this.totalPrice = res.totalOweFee
        })
        .catch((err) => {
          this.loading = false
        })
    },
    onSourceChange() {
      this.getVersionList();
    },
    getVersionList() {
      this.$store
        .dispatch('bindManagement/importAuditVersions', {
          source: this.source,
        })
        .then((res) => {
          console.log('versionLst', res)
          this.versionList = res
          this.version = ''
          this.tableData = []
        })
        .catch((err) => { })
    },
    afterPayImportAudit() {
      this.showLoading = true
      let params = {
        version: this.version,
        source: this.source
      }
      this.$store
        .dispatch('bindManagement/importAuditPass', params)
        .then((res) => {
          this.$message({ type: 'success', message: '审核成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    afterPayImportReject() {
      let params = {
        version: this.version,
        source: this.source,
      }
      this.$store
        .dispatch('bindManagement/importAuditRefuse', params)
        .then((res) => {
          this.$message({ type: 'success', message: '驳回成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  // .el-row {
  //   display: flex;
  // }
  // .el-col {
  //   margin-top: 0 !important;
  // }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}

.total-price {
  display: flex;
  align-items: center;
  margin-top: 10px;
  color: red;
  font-size: 14px;
}
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
