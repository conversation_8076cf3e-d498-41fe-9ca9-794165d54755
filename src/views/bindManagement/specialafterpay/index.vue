<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="统计开始日期"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="date"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="统计结束日期"
                          prop="endTime">
          <el-date-picker v-model="search.endTime"
                          type="date"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <!-- <dart-search-item label="短信类型"
                          prop="mType">
            <el-select v-model="search.mType" placeholder="请选择">
              <el-option
                v-for="item in mTypeoption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
         </dart-search-item> -->
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {
    this.search.startTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.search.endTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  },
  data() {
    return {
      departmenttype,
      search: {
        startTime: '',
        endTime: '',
        mType: '0',
        name: '',
      },
      tableHeight: 0,
      rules: {
        startTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择统计结束日期', trigger: 'change' },
        ],
      },
      mTypeoption: [
        { value: '0', label: '补缴通知' },
        { value: '1', label: '补缴拉黑' },
      ],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不能大于三个月',
        })
        return
      }
      this.sendReportRequest()
    },
    sendReportRequest() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let params = {
            name: 'specialStatistics',
            stTime: moment(this.search.startTime).format('YYYY-MM-DD HH:mm:ss'),
            edTime: moment(this.search.endTime).format('YYYY-MM-DD HH:mm:ss'),
            // mType:this.search.mType
          }
          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
    },
    gettime(data) {
      const value = moment(data).startOf('month').format('YYYY-MM-DD HH:mm:ss')
      return value
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>