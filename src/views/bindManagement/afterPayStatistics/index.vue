<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="创建人"
                          prop="username">
          <el-input v-model="search.username"
                    placeholder="请输入创建人工号(统一平台登录名)"></el-input>
        </dart-search-item>
        <dart-search-item label="开始日期"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="endTime">
          <el-date-picker v-model="search.endTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
// import parseTime from '@/utils/index'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      queryParams: {
        name: '扣款成功未回款追缴情况统计表',
        exTimeBegin: '',
        exTimeEnd: '',
      },
      tableHeight: 0,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
      search: {
        startTime: '', // 开始日期
        endTime: '', // 结束日期'
        username: '',
      },
    }
  },
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月',
        })
        return
      }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    sendReportRequest() {
      // this.loading = true
      let params = {
        name: this.queryParams.name,
        startTime: this.search.startTime,
        endTime: this.search.endTime,
        createNo: this.search.username,
      }
      console.log(params)
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          console.log(res.data)
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  text-align: center;
  img {
    width: 50%;
  }
}
</style>
