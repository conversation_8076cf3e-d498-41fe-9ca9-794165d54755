<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="待补缴类型："
                          prop="listType">
          <el-select v-model="search.listType"
                     clearable
                     placeholder="请选择">
            <el-option v-for="item in typeList.listTypes"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车牌："
                          prop="carNo">
          <el-input v-model="search.carNo"
                    clearable
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色："
                          prop="carNoColor">
          <el-select v-model="search.carNoColor"
                     clearable
                     placeholder="请选择">
            <el-option v-for="item in typeList.carColors"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <div class="collapse-wrapper"
             v-show="isCollapse">
          <dart-search-item label="开始日期："
                            prop="createTimeStart">
            <el-date-picker v-model="search.createTimeStart"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            clearable
                            placeholder="选择日期">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="createTimeEnd">
            <el-date-picker v-model="search.createTimeEnd"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            clearable
                            placeholder="选择日期">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="争议状态："
                            prop="disputeStatus">
            <el-select clearable
                       v-model="search.disputeStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="item in optiondata"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="合作机构："
                            prop="bankId">
            <el-select v-model="search.bankId"
                       clearable
                       filterable
                       placeholder="请选择">
              <el-option v-for="item in typeList.bindingBankType"
                         :key="item.index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="代扣渠道："
                            prop="payOrgId">
            <el-select v-model="search.payOrgId"
                       clearable
                       filterable
                       placeholder="请选择">
              <el-option v-for="item in typeList.bindingBankType"
                         :key="item.index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="追缴原因："
                            prop="reason">
            <el-input v-model="search.reason"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="创建人："
                            prop="createName">
            <el-input v-model="search.createName"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
        </div>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button size="mini"
                       type="primary"
                       @click="onExportHandle"><i class="el-icon-download"></i> 导出</el-button>
            <span class="collapse"
                  v-if="!isCollapse"
                  @click="isCollapse = true">展开</span>
            <span class="collapse"
                  v-else
                  @click="isCollapse = false">收起</span>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table v-loading="loading"
                :data="tableData"
                :align="center"
                :header-align="center"
                border
                height="100%"
                style="width: 100%"
                :row-style="{ height: '40px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '40px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="createdTime"
                         align="center"
                         min-width="130"
                         label="创建时间">
        </el-table-column>
        <el-table-column prop="mobile"
                         align="center"
                         label="用户手机号">
        </el-table-column>
        <el-table-column prop="carNo"
                         align="center"
                         label="车牌号">
        </el-table-column>
        <el-table-column prop="carNoColor"
                         align="center"
                         label="车牌颜色">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carNoColor) }}
          </template>
        </el-table-column>
        <el-table-column prop="oweFee"
                         align="center"
                         label="欠费金额(元)">
        </el-table-column>
        <el-table-column prop="disputeStatus"
                         align="center"
                         label="审核状态">
          <template slot-scope="scope">
            {{ getstatus(scope.row.disputeStatus) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatedTime"
                         align="center"
                         min-width="130"
                         label="审核时间">
        </el-table-column>
        <el-table-column prop="auditUserName"
                         align="center"
                         label="审核人">
        </el-table-column>
        <el-table-column align="center"
                         label="操作">
          <template slot-scope="scope">
            <el-button style="width: 80px"
                       size="mini"
                       @click="todetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.page"
                     :page-sizes="[10, 20, 50]"
                     :page-size="search.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <paymentDetail ref="paymentDetail"
                   v-if="dialogDetail"
                   :dialogFormVisible.sync="dialogDetail"
                   :handleId="handleId"
                   :paymentDetail="paymentDetail"
                   :alldetail="alldetail"
                   :typeList="typeList"
                   :type="type"
                   @updateList="updateList"></paymentDetail>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import paymentDetail from './paymentDetail'
import { getVehicleColor } from '@/common/method/formatOptions'
import { getToken } from '@/utils/auth'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    paymentDetail,
  },
  data() {
    return {
      search: {
        createTimeStart: '',
        createTimeEnd: '',
        disputeStatus: '',
        listType: '', //补缴类型
        carNo: '', //车牌
        carNoColor: '',
        reason: '', //追缴原因
        bankId: '', //合作机构
        payOrgId: '', //代扣渠道
        createName: '', //创建人
        pageSize: 10,
        page: 1,
      },
      total: 0,
      rules: {},
      tableData: [],
      loading: false,
      dialogDetail: false,
      isCollapse: false,
      center: 'center',
      paymentDetail: {},
      type: '',
      handleId: null,
      alldetail: {},
      typeList: {
        bindingBankType: [], //绑定机构
        cancelApplyFlags: [], //撤销申请标识
        carColors: [], //车牌颜色
        handleStatusss: [], //处理状态
        listTypes: [], //补缴类型
        source: [], //生成方式
        statuss: [], //
        deductionType: [], //扣款类型
      },
      optiondata: [
        { value: 0, label: '待审核' },
        { value: 1, label: '已审核通过' },
        { value: 2, label: '已审核驳回' },
      ],
    }
  },
  created() {
    // this.search.createTimeStart = moment(new Date().setDate(1)).format(
    //   'YYYY-MM-DD HH:mm:ss'
    // )
    // this.search.createTimeEnd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    // this.getlist()
    this.getDictionaries()
  },
  methods: {
    getVehicleColor,
    getstatus(value) {
      for (let i = 0; i < this.optiondata.length; i++) {
        if (this.optiondata[i].value == value) {
          return this.optiondata[i].label
        }
      }
      return ''
    },
    getDictionaries() {
      this.$store
        .dispatch('bindManagement/dictionaries')
        .then((res) => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach((key) => {
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
          })
          console.log('typeList', this.typeList)
        })
        .catch((err) => {})
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        // lvTypeList.push({
        //   label: '全部',
        //   value: '',
        // })
        typeArr.forEach((item) => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue,
          }
          lvTypeList.push(lvObj)
        })
      }
    },
    updateList() {
      this.dialogDetail = false
      this.getlist()
    },
    onSearchHandle() {
      this.search.page = 1
      this.getlist()
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
    },
    handleSizeChange(value) {
      this.search.pageSize = value
      this.getlist()
    },
    changePage(value) {
      this.search.page = value
      this.getlist()
    },
    todetail(item) {
      let params = { id: item.id }
      this.$store
        .dispatch('bindManagement/hsAuditDisputefindById', params)
        .then((res) => {
          console.log(res)
          this.paymentDetail = res.auditBlackListDetail
          this.alldetail = res
          this.type = 'scanDetail'
          this.dialogDetail = true
        })
        .catch((err) => {})
    },
    onExportHandle() {
      // this.loading = true
      let params = { ...this.search }
      params.createTimeStart = params.createTimeStart
        ? moment(params.createTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeEnd = params.createTimeEnd
        ? moment(params.createTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      // www.baidu.com?a=1&b=2&c=3
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/hsAuditDispute/exportSearch?Authorization=Bearer ' +
        getToken()
      for (let i in params) {
        if (params[i] != '') {
          url += '&' + i + '=' + params[i]
        }
      }
      console.log('url', url)
      window.open(url)
    },
    getlist() {
      this.loading = true

      let params = { ...this.search }
      params.createTimeStart = params.createTimeStart
        ? moment(params.createTimeStart).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeEnd = params.createTimeEnd
        ? moment(params.createTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''

      request({
        url: api.hsAuditDispute,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.tableData = res.data.data
          this.total = res.data.total
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  display: flex;
  height: 100%;
  flex-direction: column;
  .table {
    flex: 1;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>