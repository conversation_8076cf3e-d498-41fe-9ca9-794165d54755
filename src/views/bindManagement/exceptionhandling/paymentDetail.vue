<template>
  <div class="payment-detail"
       v-loading.fullscreen.lock="showLoading">
    <el-dialog :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :fullscreen="isFullscreen"
               :show-close="false"
               width="80%"
               :close-on-press-escape="false">
      <template slot="title">
        <div class="btn-wrapper">
          <i @click="isFullscreen = true"
             v-if="!isFullscreen"
             class="el-icon-full-screen"></i>
          <i @click="isFullscreen = false"
             v-else
             class="el-icon-copy-document"></i>
          <i @click="close()"
             class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> {{ getTitle(type) }} </span>
        </div>
      </template>
      <div class="detail-wrapper"
           v-if="paymentDetail.auditBlackList">
        <div class="section-1 margin-top">
          <el-descriptions title="待补缴详情"
                           :column="4"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="车牌">{{
              paymentDetail.auditBlackList.carNo
            }}</el-descriptions-item>
            <el-descriptions-item label="车牌颜色">{{
              getType(
                typeList.carColors,
                paymentDetail.auditBlackList.carNoColor
              )
            }}</el-descriptions-item>
            <el-descriptions-item label="涉及的卡">{{
              paymentDetail.auditBlackList.relateCardId
            }}</el-descriptions-item>
            <el-descriptions-item label="涉及的OBU">{{
              paymentDetail.auditBlackList.relateObu
            }}</el-descriptions-item>

            <el-descriptions-item label="待补缴类型">{{
              getType(typeList.listTypes, paymentDetail.auditBlackList.listType)
            }}</el-descriptions-item>
            <el-descriptions-item label="欠费金额(元)">{{
              paymentDetail.auditBlackList.oweFee | moneyFilter
            }}</el-descriptions-item>
            <el-descriptions-item label="交易总金额(元)">{{
              paymentDetail.totalTransactionAmount | moneyFilter
            }}</el-descriptions-item>
            <el-descriptions-item label="退费总金额(元)">{{
              paymentDetail.totalRefundAmount | moneyFilter
            }}</el-descriptions-item>

            <el-descriptions-item label="追缴原因">{{
              paymentDetail.auditBlackList.reason
            }}</el-descriptions-item>
            <el-descriptions-item label="处理状态">{{
              getType(
                typeList.handleStatusss,
                paymentDetail.auditBlackList.handleStatus
              )
            }}</el-descriptions-item>
            <el-descriptions-item label="创建人">{{
              paymentDetail.createName
            }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{
              paymentDetail.createTime
            }}</el-descriptions-item>

            <el-descriptions-item label="预追缴原因解析">{{
              paymentDetail.auditBlackList.reason
            }}</el-descriptions-item>
            <el-descriptions-item label="预追缴名单生成时间">{{
              getType(
                typeList.handleStatusss,
                paymentDetail.auditBlackList.creationTime
              )
            }}</el-descriptions-item>
            <el-descriptions-item label="版本号">{{
              paymentDetail.auditBlackList.version
            }}</el-descriptions-item>
            <el-descriptions-item label="文件名称">{{
              paymentDetail.auditBlackList.fileName
            }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1"
                           border
                           class="desc-wrapper">
            <el-descriptions-item contentStyle="white-space:normal"
                                  contentClassName="no-whitespace"
                                  label="备注">{{ paymentDetail.auditBlackList.remark }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="section-2 margin-top"
             v-if="paymentDetail.cardBindingDeduction">
          <el-descriptions title="扣款信息"
                           :column="3"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="扣款流水号">{{
              paymentDetail.cardBindingDeduction.sern
            }}</el-descriptions-item>
            <el-descriptions-item label="扣款金额(元)">{{
              paymentDetail.cardBindingDeduction.amount | moneyFilter
            }}</el-descriptions-item>
            <el-descriptions-item label="扣款类型">{{
              getType(
                typeList.deductionType,
                paymentDetail.cardBindingDeduction.deductionType
              )
            }}</el-descriptions-item>

            <el-descriptions-item label="扣款完成时间">{{
              paymentDetail.cardBindingDeduction.dedutionAccountDate
            }}</el-descriptions-item>

            <el-descriptions-item label="记账时间">{{
              paymentDetail.cardBindingDeduction.keepAccountDate
            }}</el-descriptions-item>
            <el-descriptions-item label="数据入账时间">{{
              paymentDetail.cardBindingDeduction.enterAccountDate
            }}</el-descriptions-item>
            <el-descriptions-item label="捷通应收时间">{{
              paymentDetail.cardBindingDeduction.bankAccountDate
            }}</el-descriptions-item>
            <el-descriptions-item label="合作机构">{{
              paymentDetail.bankIdStr
            }}</el-descriptions-item>

            <el-descriptions-item label="代扣机构">{{
              paymentDetail.payOrgIdStr
            }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1"
                           border
                           class="desc-wrapper">
            <el-descriptions-item contentStyle="white-space:normal"
                                  label="备注">
              <span v-if="paymentDetail.cardBindingDeduction.needAccountFlag == 1">扣款失败:</span>
              {{
                paymentDetail.cardBindingDeduction.remark
              }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="table">
          <span class="title">通行信息</span>
          <el-table :data="paymentDetail.transList"
                    :align="center"
                    :header-align="center"
                    border
                    style="width: 100%; margin-bottom: 20px"
                    :row-style="{ height: '35px' }"
                    :cell-style="{ padding: '0px' }"
                    :header-row-style="{ height: '35px' }"
                    :header-cell-style="{ padding: '0px' }"
                    row-key="id">
            <el-table-column prop="transactionId"
                             min-width="310"
                             align="center"
                             label="通行交易ID" />
            <el-table-column prop="time"
                             align="center"
                             label="创建时间"
                             min-width="180" />
            <!-- <el-table-column prop="handleParty" align="center" label="处理机构">
              <template slot-scope="scope">
                {{ typeAdapter(scope.row.handleParty, 'getHandlePartyType') }}
              </template>
            </el-table-column> -->
            <el-table-column prop="oweFee"
                             align="center"
                             label="扣款金额(元)">
              <template slot-scope="scope">
                {{ scope.row.oweFee | moneyFilter }}
              </template>
            </el-table-column>
            <el-table-column prop="realEnLaneId"
                             align="center"
                             label="入口站" />
            <el-table-column prop="realExLaneId"
                             align="center"
                             label="出口站" />
          </el-table>
        </div>
        <div class="table">
          <span class="title">退费信息</span>
          <el-table :data="paymentDetail.refundList"
                    :align="center"
                    :header-align="center"
                    border
                    style="width: 100%; margin-bottom: 20px"
                    :row-style="{ height: '35px' }"
                    :cell-style="{ padding: '0px' }"
                    :header-row-style="{ height: '35px' }"
                    :header-cell-style="{ padding: '0px' }"
                    row-key="id">
            <el-table-column prop="transactionId"
                             min-width="310"
                             align="center"
                             label="通行交易ID" />
            <el-table-column prop="createTime"
                             align="center"
                             label="创建时间"
                             min-width="180" />
            <el-table-column prop="oweFee"
                             align="center"
                             label="退费时间"
                             min-width="180" />
            <el-table-column prop="refundAmount"
                             align="center"
                             label="退费金额(元)">
              <template slot-scope="scope">
                {{ scope.row.refundAmount | moneyFilter }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="section-3 margin-top"
             v-if="type == 'scanDetail'">
          <el-descriptions title="审核信息"
                           :column="4"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="审核人">{{
              paymentDetail.auditBlackList.auditName
            }}</el-descriptions-item>
            <el-descriptions-item label="审核金额(元)">{{
              paymentDetail.auditBlackList.oweFee | moneyFilter
            }}</el-descriptions-item>
            <el-descriptions-item label="审核状态">
              {{
                getType(
                  typeList.handleStatusss,
                  paymentDetail.auditBlackList.handleStatus
                )
              }}
            </el-descriptions-item>
            <el-descriptions-item label="审核时间">{{
              paymentDetail.auditBlackList.auditTime
            }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1"
                           border
                           class="desc-wrapper">
            <el-descriptions-item contentStyle="white-space:normal"
                                  label="审核备注">{{
                paymentDetail.auditBlackList.auditRemark
              }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="section-3 margin-top"
             v-if="type == 'audit'">
          <el-descriptions title="审核信息"
                           :column="2"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="审核金额(元)">
              <el-input v-model="auditAmount"
                        type="number"></el-input>
            </el-descriptions-item>
            <el-descriptions-item contentStyle="white-space:normal"
                                  label="审核备注">
              <textarea v-model="auditRemark"
                        class="textarea"
                        name="suggestion"
                        id=""
                        rows="3"
                        placeholder="请输入处理意见"></textarea>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="section-4 margin-top"
             style="margin-bottom: 30px"
             v-if="type == 'scanDetail'">
          <el-descriptions title="支付信息"
                           :column="4"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="支付金额(元)">{{
              paymentDetail.auditPayOrder.amount | moneyFilter
            }}</el-descriptions-item>
            <el-descriptions-item label="支付时间">{{
              paymentDetail.auditPayOrder.payTime
            }}</el-descriptions-item>
            <el-descriptions-item label="商户流水号">{{
              paymentDetail.auditPayOrder.frontServerNo
            }}</el-descriptions-item>
            <el-descriptions-item label="ECSS流水号">{{
              paymentDetail.auditPayOrder.payNo
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="section-4 margin-top">
          <el-descriptions class="margin-top desc-wrapper"
                           :column="1"
                           border>
            <template slot="title"> 用户提交图片 </template>
            <el-descriptions-item class="imgbox"
                                  :label="'提交图片'">
              <!-- <viewer class="imgbox" 
              
                
              > -->
              <!-- <el-image :src='currnetDeatil.driving_license_front_path'></el-image> -->
              <!-- <img 
                v-for=" (item,index) in alldetail.pleadPicAddList"
                :key="index"
                :src="item" style="width: 100%"  />
              </viewer> -->

              <el-image v-for=" (item,index) in alldetail.pleadPicAddList"
                        :key="index"
                        style="width: 150px; height: 150px; margin:0 10px"
                        :src="item"
                        :fit="'cover'"
                        @click="lookimg(item,index)">
              </el-image>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :labelStyle="{ width: '201px' }"
                           :column="1"
                           border>
            <el-descriptions-item label="用户申述内容">{{
              alldetail.userPlead
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div v-if="alldetail.adminExplain"
             class="section-4 margin-top">
          <el-descriptions class="margin-top desc-wrapper"
                           :column="1"
                           border>
            <template slot="title"> 稽查员提交证据 </template>
            <el-descriptions-item :label="'提交图片'">
              <!-- <viewer class="imgbox" 
              
                
              > -->
              <!-- <el-image :src='currnetDeatil.driving_license_front_path'></el-image> -->
              <!-- <img 
                 v-for=" (item,index) in alldetail.explainPicAddList"
                :key="index"
                :src="item" style="width: 100%"  />
              </viewer> -->
              <el-image v-for=" (item,index) in alldetail.explainPicAddList"
                        :key="index"
                        style="width: 150px; height: 150px; margin:0 10px"
                        :src="item"
                        :fit="'cover'"
                        @click="lookimg2(item,index)">
              </el-image>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :labelStyle="{ width: '201px' }"
                           :column="1"
                           border>
            <el-descriptions-item label="稽核人审核内容">{{
              alldetail.adminExplain
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="section-3 margin-top"
             v-if="!alldetail.adminExplain">
          <el-descriptions title="审核内容信息"
                           :column="1"
                           border
                           class="desc-wrapper">
            <el-descriptions-item label="审核内容">
              <textarea v-model="form.adminExplain"
                        class="textarea"
                        name="suggestion" 
                        id=""
                        maxlength='70'
                        rows="3"
                        placeholder="请输审核内容,70个文字以内"></textarea>
            </el-descriptions-item>
            <el-descriptions-item :label="'审核图片,最多上传'+imgListlength+'/5张'">
              <photograph @imgLength='newValue'
                          @changeexplainPicAdd="changeexplainPicAdd"
                          :customerId="alldetail.customerId" />
            </el-descriptions-item>

          </el-descriptions>
        </div>
      </div>
      <template v-if="!alldetail.adminExplain"
                slot="footer">
        <el-button @click="passHandle(1)"
                   type="primary"
                   size="mini">
          审核
        </el-button>
        <el-button @click="passHandle(2)"
                   type="danger"
                   size="mini">
          驳回
        </el-button>
      </template>
      <PhotoComponent @arrowClick="arrowClick"
                      @closeImageShow="closeImageShow"
                      :photoVisible="photoVisible"
                      :imgSrc="imgSrc">
      </PhotoComponent>
      <PhotoComponent @arrowClick="arrowClick2"
                      @closeImageShow="closeImageShow2"
                      :photoVisible="photoVisible2"
                      :imgSrc="imgSrc2">
      </PhotoComponent>
    </el-dialog>
  </div>
</template>

<script>
// import { typeAdapter } from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import photograph from './photograph'
import PhotoComponent from './PhotoComponent'
export default {
  components: {
    photograph,
    PhotoComponent,
  },
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    handleId: {
      type: Number,
    },
    alldetail: {
      type: Object,
    },
    paymentDetail: {
      type: Object,
      require: true,
    },
    typeList: {
      type: Object,
      require: true,
    },
    type: {
      type: String,
    },
  },
  data() {
    return {
      showLoading: false,
      center: 'center',
      isFullscreen: false,
      photoVisible: false,
      chooseImage: 0,
      imgSrc: '',
      photoVisible2: false,
      chooseImage2: 0,
      imgSrc2: '',
      auditAmount: '',
      auditRemark: '',
      cancelRemark: '',
      form: {
        adminExplain: '',
        explainPicAdd: '',
      },
      imgListlength: '0', //图片列表长度
    }
  },
  watch: {
    dialogFormVisible(val) {
      if (val) {
        this.$nextTick(() => {
          this.auditAmount = this.moneyFilter(
            this.paymentDetail.auditBlackList.oweFee
          )
        })
      }
    },
    handleId(val) {
      // console.log('id', val, this.dialogFormVisible)
      // if (this.dialogFormVisible) {
      //   this.getPaymentDetail(val)
      //   console.log('id', val)
      // }
    },
  },
  methods: {
    newValue(val) {
      console.log(val, '=======================')
      this.imgListlength = val
    },
    lookimg(url, index) {
      this.imgSrc = url
      this.photoVisible = true
      this.chooseImage = index
    },
    lookimg2(url, index) {
      this.imgSrc2 = url
      this.photoVisible2 = true
      this.chooseImage2 = index
    },
    changeexplainPicAdd(val) {
      this.form.explainPicAdd = val.toString()
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getTitle() {
      if (this.type == 'audit') {
        return '审核'
      } else if (this.type == 'scanDetail') {
        return '详情'
      } else if (this.type == 'cancelApply') {
        return '撤销待补缴订单申请'
      } else if (this.type == 'cancelApplyAudit') {
        return '撤销待补缴订单申请审核'
      } else {
        return ''
      }
    },
    // accMul(arg1, arg2) {
    //   var m = 0,
    //     s1 = arg1.toString(),
    //     s2 = arg2.toString()
    //   try {
    //     m += s1.split('.')[1].length
    //   } catch (e) {}
    //   try {
    //     m += s2.split('.')[1].length
    //   } catch (e) {}
    //   return (
    //     (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
    //     Math.pow(10, m)
    //   )
    // },
    passHandle(type) {
      if (this.form.adminExplain == '') {
        this.$message({
          message: '请输入审核内容',
          type: 'warning',
        })
        return
      }
      // if(this.form.explainPicAdd == ''){
      //   this.$message({
      //     message: '请上传审核图片',
      //     type: 'warning'
      //   });
      //   return
      // }
      let data = {
        adminExplain: this.form.adminExplain,
        explainPicAdd: this.form.explainPicAdd,
        disputeStatus:type,
        id: this.alldetail.id,
        revision: this.alldetail.revision,
      }
      request({
        url: api.hsAuditDisputeupdate,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$emit('updateList')
            this.$message({
              message: '审核成功',
              type: 'success',
            })
          }
        })
        .catch(() => {})
    },
    rejectHandle() {
      if (this.type == 'cancelApplyAudit') {
        //撤销申请审核
        this.cancelApplyReject()
      } else if (this.type == 'audit') {
        this.reject()
      }
    },
    //撤销申请提交
    cancelAuditApply() {
      this.showLoading = true
      let params = {
        id: this.handleId,
        cancelRemark: this.cancelRemark,
      }
      console.log('入参', params)
      this.$store
        .dispatch('bindManagement/cancelAuditApply', params)
        .then((res) => {
          this.$message({ type: 'success', message: '撤销提交成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
          // this.$message({ type: 'error', message: '通过操作失败' })
        })
    },
    cancelApplyPass() {
      this.showLoading = true
      let params = {
        auditBlackListId: this.handleId,
      }
      // params.auditFree = this.accMul(params.auditFree, 100)
      console.log('入参', params)
      this.$store
        .dispatch('bindManagement/cancelAuditApplyPass', params)
        .then((res) => {
          this.$message({ type: 'success', message: '通过操作成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
          // this.$message({ type: 'error', message: '通过操作失败' })
        })
    },
    cancelApplyReject() {
      this.showLoading = true
      let params = {
        auditBlackListId: this.handleId,
      }
      // params.auditFree = this.accMul(params.auditFree, 100)
      console.log('入参', params)
      this.$store
        .dispatch('bindManagement/cancelAuditApplyRefuse', params)
        .then((res) => {
          this.$message({ type: 'success', message: '驳回操作成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
          // this.$message({ type: 'error', message: '通过操作失败' })
        })
    },
    pass() {
      this.showLoading = true
      let params = {
        auditFree: this.auditAmount,
        auditRemark: this.auditRemark,
        id: this.handleId,
      }
      // params.auditFree = this.accMul(params.auditFree, 100)
      console.log('入参', params)
      this.$store
        .dispatch('bindManagement/auditPass', params)
        .then((res) => {
          this.$message({ type: 'success', message: '通过操作成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
          // this.$message({ type: 'error', message: '通过操作失败' })
        })
    },
    reject() {
      this.showLoading = true
      let params = {
        // auditFree: this.auditAmount,
        auditRemark: this.auditRemark,
        id: this.handleId,
      }
      this.$store
        .dispatch('bindManagement/auditRefuse', params)
        .then((res) => {
          this.$message({ type: 'success', message: '驳回操作成功' })
          this.showLoading = false
          this.$emit('updateList')
        })
        .catch((err) => {
          this.showLoading = false
          this.$message({ type: 'error', message: '驳回操作失败' })
        })
    },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
    arrowClick(direction) {
      // console.log(direction);
      // console.log(this.list);
      if (direction === 'left') {
        if (this.chooseImage >= 1) {
          this.chooseImage = this.chooseImage - 1
          this.imgSrc = this.alldetail.pleadPicAddList[this.chooseImage]
        } else {
          this.noMore()
        }
      } else {
        if (this.chooseImage < this.alldetail.pleadPicAddList.length - 1) {
          this.chooseImage = this.chooseImage + 1
          this.imgSrc = this.alldetail.pleadPicAddList[this.chooseImage]
        } else {
          this.noMore()
        }
      }
    },
    arrowClick2(direction) {
      // console.log(direction);
      // console.log(this.list);
      if (direction === 'left') {
        if (this.chooseImage2 >= 1) {
          this.chooseImage2 = this.chooseImage2 - 1
          this.imgSrc2 = this.alldetail.explainPicAddList[this.chooseImage2]
        } else {
          this.noMore()
        }
      } else {
        if (this.chooseImage2 < this.alldetail.explainPicAddList.length - 1) {
          this.chooseImage2 = this.chooseImage2 + 1
          this.imgSrc2 = this.alldetail.explainPicAddList[this.chooseImage2]
        } else {
          this.noMore()
        }
      }
    },
    noMore() {
      this.$msgbox({
        title: '温馨提示',
        message: '没有更多啦',
        customClass: 'my_msgBox singelBtn',
        // showCancelButton: true,
        confirmButtonText: '确定',
        type: 'info',
      })
    },
    closeImageShow() {
      this.photoVisible = false
    },
    closeImageShow2() {
      this.photoVisible2 = false
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep.form_dialog .el-dialog--center {
  margin-top: 5vh !important;
  // height: 90%;
  // overflow-y: scroll;
}
::v-deep.form_dialog .el-dialog.is-fullscreen {
  margin-top: 0 !important;
}
.table {
  padding: 0;
  .title {
    display: block;
    // margin-bottom: 20px;
    font-size: 14px;
    font-weight: 700;
    color: #303133;
  }
  ::v-deep.el-table__body-wrapper {
    height: 30px;
    .el-table__empty-block {
      min-height: 30px;
    }
  }
}
.textarea {
  // overflow: hidden;
  border-color: #e8e8e8;
  padding: 10px;
  width: 100%;
}
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.margin-top {
  margin-top: 20px;
}
.desc-wrapper ::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
    .el-descriptions__title {
      font-size: 14px;
    }
  }
  .el-descriptions__body {
    .el-descriptions__table {
      border-collapse: inherit;
      .el-descriptions-row {
        .el-descriptions-item__content {
          white-space: nowrap;
          height: 30px;
          padding: 0 10px;
          .el-input__inner {
            width: 100px;
          }
        }
        .el-descriptions-item__label {
          width: 200px;
          height: 30px;
          padding: 0 10px;
          white-space: nowrap;
          background-color: #f5f7fa;
        }
      }
    }
  }
  .no-whitespace {
    white-space: normal;
  }
}

.form_dialog ::v-deep .el-dialog__body {
  padding-top: 0;
  overflow-x: auto;
}
.imgbox {
  display: inline-block;
  overflow-x: scroll;
  width: 100%;
  img {
    margin: 0 10px;
    height: 200px;
  }
}
.viewer-canvas {
  z-index: 9999 !important;
}
</style>
