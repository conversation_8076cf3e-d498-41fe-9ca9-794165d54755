<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {
  },
  data() {
    return {
      departmenttype,
      tableHeight: 0,
      rules: {

      },
    }
  },
  methods: {
    onSearchHandle() {
      this.sendReportRequest()
    },
    sendReportRequest() {

          let params = {
            name: 'messageStatistics',
          }
          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>