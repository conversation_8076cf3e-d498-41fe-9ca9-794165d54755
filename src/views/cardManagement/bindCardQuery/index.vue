<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:绑定卡查询
  * @author:dengwz42592
  * @date:2023/05/1 15:27:48
-->
<template>
  <div class="bind-card__qeury">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="form"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      :labelTextLength="8"
      class="search"
    >
      <template slot="search-form">
        <dart-search-item label="客户名称：" prop="custName">
          <el-input v-model="form.custName" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌：" prop="carNo">
          <el-input v-model="form.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="证件号码：" prop="custIdNo">
          <el-input v-model="form.custIdNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="绑定关系流水号：" prop="bindingSerialNo">
          <el-input
            v-model="form.bindingSerialNo"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <dart-search-item label="银行名称：" prop="bankId">
          <el-select
            clearable
            filterable
            v-model="form.bankId"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.orgIds"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="银行卡号：" prop="bankCardNo">
          <el-input
            v-model="form.bankCardNo"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="form.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="是否发行：" prop="issueFlag">
          <el-select
            clearable
            v-model="form.issueFlag"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in issueFlagList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="3">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button
              type="warning"
              size="mini"
              native-type="submit"
              @click="exportHandle"
              >导出</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="bindingSerialNo"
          label="绑定关系流水号"
          width="230"
          align="center"
        />
        <el-table-column
          prop="carNo"
          label="车牌号"
          width="120"
          align="center"
        />
        <el-table-column prop="carColor" label="车牌颜色" align="center">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="cardNo"
          label="八桂行卡号"
          width="180"
          align="center"
        />
        <el-table-column prop="bankIdStr" align="center" label="银行名称" />
        <el-table-column prop="payOrgIdStr" align="center" label="代扣机构" />
        <el-table-column
          prop="bankCardTypeStr"
          label="银行卡类型"
          width="120"
          align="center"
        />
        <el-table-column
          prop="bankCardNo"
          label="银行卡号"
          width="230"
          align="center"
        />
        <el-table-column
          prop="custName"
          label="客户名称"
          width="180"
          align="center"
        />
        <el-table-column
          prop="receiveDate"
          label="预绑定时间"
          width="180"
          align="center"
        />
        <el-table-column prop="issueFlagStr" label="是否发行" align="center" />
        <el-table-column
          prop="bindingStatusStr"
          label="状态"
          width="180"
          align="center"
        />
        <el-table-column
          prop="operationDate"
          label="发行时间"
          width="180"
          align="center"
        />
        <el-table-column prop="" align="center" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-permisaction="['bindingCardInterface:unbind']" :disabled="scope.row.bindingStatusStr != '已绑定' || scope.row.issueFlagStr == '已发行'" @click="cancelBind(scope.row)"
              >取消绑定</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="form.pageIndex"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { unbindCard } from '@/api/bindManagement'
import dartSlide from '@/components/dart/Slide/index.vue'
import { filterTypeList } from '@/common/method/utils'
export default {
  name: '',
  components: { dartSearch, dartSearchItem, dartSlide },
  data() {
    return {
      licenseColorOption,
      center: 'center',
      form: {
        custName: '',
        carNo: '',
        custIdNo: '',
        bindingSerialNo: '',
        bankId: '',
        bankCardNo: '',
        cardNo: '',
        issueFlag: '', //1已发行,0未发行
        pageIndex: 1,
        pageSize: 20
      },
      typeList: {
        bankExceptionTypes: [],
        bankSendBusinessTypes: [],
        cardTypes: [],
        orgIds: [] //银行名称
      },
      issueFlagList: [
        { label: '全部', value: '' },
        { label: '未发行', value: '0' },
        { label: '已发行', value: '1' }
      ],
      isCollapse: false,
      tableData: [],
      total: 0
    }
  },
  computed: {},
  watch: {},
  created() {
    // this.onSearchHandle()
    this.init() //初始化字典数据
  },
  methods: {
    getVehicleColor,
    filterTypeList,
    init() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.getCardbindingData,
        method: 'post'
      })
        .then(res => {
          if (res.code == 200) {
            console.log('typeList', this.typeList)
            let typeList = res.data
            //重构字典数据结构
            Object.keys(typeList).forEach(key => {
              this.filterTypeList(typeList[key], this.$data['typeList'][key])
            })
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    onSearchHandle() {
      if (
        !this.form.custName &&
        !this.form.carNo &&
        !this.form.custIdNo &&
        !this.form.bindingSerialNo &&
        !this.form.bankId &&
        !this.form.bankCardNo &&
        !this.form.cardNo
      ) {
        this.$message({
          type: 'warning',
          message:
            '客户名称、车牌号码、证件号码、绑定关系流水号、银行名称、银行卡号、卡号不能同时为空'
        })
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.bindCardQuery,
        method: 'post',
        data: this.form
      })
        .then(res => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    onResultHandle() {
      this.form = {
        custName: '',
        carNo: '',
        custIdNo: '',
        bindingSerialNo: '',
        bankId: '',
        bankCardNo: '',
        cardNo: '',
        issueFlag: '',
        pageIndex: 1,
        pageSize: 20
      }
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.onSearchHandle()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.onSearchHandle()
    },
    exportHandle() {
      if (!this.form.bankId) {
        this.$message.warning('请先筛选银行名称后再导出')
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.bindCardExport,
        method: 'post',
        data: this.form,
        responseType: 'blob'
      })
        .then(res => {
          this.getBlob(res, 'application/vnd.ms-excel', '绑定关系查询列表')
          this.endLoading()
        })
        .catch(err => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    async cancelBind(row){
      let parmas = {
        bindingSerialNo:row.bindingSerialNo
      }
      let res = await unbindCard(parmas)
      if(res.code == 200){
        this.$message.success('操作成功')
        this.onSearchHandle()
      }
    }
  }
}
</script>

<style lang='scss' scoped>
.bind-card__qeury {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.bind-card__qeury .search {
  margin-top: 20px;
}
.bind-card__qeury .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.bind-card__qeury .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
