<template>
  <el-dialog
    title="手动填写应收日"
    @closed="onClosedHandle"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :center="true"
    width="480px"
    custom-class="add-cancel-dialog"
  >
    <div class="form-info">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        size="medium"
        class="dt-form dt-form--max"
      >
        <el-form-item label="应收日：" prop="bankAccountDate">
          <el-date-picker
            v-model="ruleForm.bankAccountDate"
            type="date"
            value-format="yyyy-MM-dd"
            clearable
          >
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="卡号："
                      prop="cardNo">
          <el-input v-model="ruleForm.cardNo"></el-input>
        </el-form-item> -->
      </el-form>
    </div>
    <div slot="footer" class="btn">
      <el-button
        @click="addHandle"
        type="primary"
        style="width: 100px"
        size="small"
        >提交</el-button
      >
      <el-button
        @click="onClosedHandle"
        type="normal"
        style="width: 100px"
        size="small"
        >取消</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    scope: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        bankAccountDate: '',
        bankExceptionRecordId: this.scope.bankExceptionRecordId,
        cardBindingDeductionId: this.scope.deductionId,
        remark: this.scope.remark,
      },
      rules: {
        bankAccountDate: [
          { required: true, message: '请选择应收日', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
  },
  methods: {
    addHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.personHandle()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    personHandle() {
      this.startLoading()

      this.$request({
        url: this.$interfaces.personHandle,
        method: 'post',
        data: this.ruleForm,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message({
              message: '提交成功',
              type: 'success',
            })
            this.$emit('update')
            this.dialogVisible = false
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    onClosedHandle() {
      this.dialogVisible = false
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss">
.add-cancel-dialog .el-dialog__body {
  padding-bottom: 10px;
}
.form-info {
  padding: 0 25px;
}
.add-cancel-dialog .btn {
  width: 100%;
  text-align: center;
}
</style>