<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:扣款异常监控
  * @author:dengwz42592
  * @date:2023/05/1 15:27:48
-->
<template>
  <div class="deduction-error">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="form"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      :labelTextLength="8"
      class="search"
    >
      <template slot="search-form">
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="form.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="卡片类型：" prop="cardType">
          <el-select
            clearable
            v-model="form.cardType"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.cardTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车牌：" prop="carNo">
          <el-input v-model="form.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="银行名称：" prop="bankId">
          <el-select
            clearable
            filterable
            v-model="form.bankId"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.orgIds"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="处理情况：" prop="handleFlag">
          <el-select
            clearable
            v-model="form.handleFlag"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in handleFlagList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="扣款流水号：" prop="deductionSern">
          <el-input
            v-model="form.deductionSern"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <dart-search-item label="异常类型：" prop="bankExceptionTypeId">
          <el-select
            clearable
            v-model="form.bankExceptionTypeId"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.bankExceptionTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="异常发生时间：" prop="">
          <el-date-picker
            v-model="timeDate"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="3">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="deductionSern"
          label="扣款流水号"
          width="230"
          align="center"
        />
        <el-table-column
          prop="bankIdStr"
          label="银行名称"
          width="120"
          align="center"
        />
        <el-table-column
          prop="cardNo"
          label="卡号"
          width="180"
          align="center"
        />
        <el-table-column prop="carNo" label="车牌" width="120" align="center" />
        <el-table-column prop="carColorStr" label="车牌颜色" align="center" />
        <el-table-column
          prop="bankExceptionTypeIdStr"
          label="异常类型"
          width="100"
          align="center"
        />
        <el-table-column
          prop="definition"
          align="center"
          width="280"
          label="异常描述"
        />
        <el-table-column
          prop="createDate"
          label="发生时间"
          width="160"
          align="center"
        />
        <el-table-column prop="handleFlagStr" label="处理情况" align="center" />
        <el-table-column
          prop="handleByName"
          label="处理人"
          width="120"
          align="center"
        />
        <el-table-column
          prop="handleDate"
          label="处理时间"
          width="160"
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="160" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.handleFlag != 1"
              type="text"
              @click="reSend(scope.row)"
              >重发</el-button
            >
            <el-button
              v-if="scope.row.handleFlag != 1"
              type="text"
              @click="handleData(scope.row)"
              >手动填写应收日</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="form.pageIndex"
        :page-sizes="[10, 20, 50]"
        :page-size="form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <setData
      :visible.sync="visible"
      :scope="scope"
      @update="update"
      v-if="visible"
    ></setData>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import dartSlide from '@/components/dart/Slide/index.vue'
import setData from './setData'
import { filterTypeList } from '@/common/method/utils'
export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem, dartSlide, setData },
  data() {
    return {
      visible: false,
      center: 'center',
      form: {
        cardNo: '',
        cardType: '',
        carNo: '',
        bankId: '',
        handleFlag: '',
        deductionSern: '',
        bankExceptionTypeId: '',
        preDate: '',
        lastDate: '',
        pageIndex: 1,
        pageSize: 10,
      },
      timeDate: '',
      typeList: {
        bankExceptionTypes: [], //异常类型
        cardTypes: [], //卡片类型
        orgIds: [], //银行名称
      },
      handleFlagList: [
        { label: '全部', value: '' },
        { label: '未处理', value: '0' },
        { label: '已处理', value: '1' },
      ],
      tableData: [],
      total: 0,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.init()
  },
  methods: {
    filterTypeList,
    init() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.getExceptionMonitorData,
        method: 'post',
      })
        .then((res) => {
          if (res.code == 200) {
            let typeList = res.data
            //重构字典数据结构
            Object.keys(typeList).forEach((key) => {
              console.log('key', key)
              if (key == 'bankExceptionTypes') {
                if (this.typeList[key].length == 0) {
                  this.typeList[key].push({
                    label: '全部',
                    value: '',
                  })
                }
                typeList[key].forEach((item) => {
                  // console.log('item', item)
                  let lvObj = {
                    label: item.definition,
                    value: item.bankExceptionTypeId,
                  }
                  this.typeList[key].push(lvObj)
                })
              } else {
                this.filterTypeList(typeList[key], this.$data['typeList'][key])
              }
            })
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    update() {
      this.onSearchHandle()
    },
    onSearchHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.getExceptionMonitor,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    formatDate() {
      if (this.timeDate) {
        this.form.preDate = this.timeDate[0]
        this.form.lastDate = this.timeDate[1]
      } else {
        this.form.preDate = ''
        this.form.lastDate = ''
      }
    },
    reSend(scope) {
      this.startLoading()
      let params = {
        bankExceptionRecordId: scope.bankExceptionRecordId,
        cardBindingDeductionId: scope.deductionId,
        remark: scope.remark,
      }
      this.$request({
        url: this.$interfaces.exceptionResend,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message.success('重发成功')
            this.onSearchHandle()
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    handleData(scope) {
      this.visible = true
      this.scope = scope
    },
    onResultHandle() {
      this.form = {
        cardNo: '',
        cardType: '',
        carNo: '',
        bankId: '',
        handleFlag: '',
        deductionSern: '',
        bankExceptionTypeId: '',
        preDate: '',
        lastDate: '',
        pageIndex: 1,
        pageSize: 10,
      }
      this.timeDate = ''
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.onSearchHandle()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.onSearchHandle()
    },
    toDetail(val, type) {
      this.detailVisible = true
      this.itemInfo = val
      if (type == 'view') {
        this.itemInfo.isView = true
      } else {
        this.itemInfo.isView = false
      }
    },
    exportHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditExport,
        method: 'post',
        data: this.form,
        responseType: 'blob',
      })
        .then((res) => {
          this.getBlob(res, 'application/vnd.ms-excel', '稽核管理列表')
          this.endLoading()
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
  },
}
</script>

<style lang='scss' scoped>
.deduction-error {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.deduction-error .search {
  margin-top: 20px;
}
.deduction-error .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.deduction-error .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
