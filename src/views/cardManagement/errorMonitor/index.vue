<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:异常监控
  * @author:dengwz42592
  * @date:2023/05/1 15:27:48
-->
<template>
  <div class="error-monitor">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="form"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      :labelTextLength="8"
      class="search"
    >
      <template slot="search-form">
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="form.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="卡片类型：" prop="cardType">
          <el-select
            clearable
            v-model="form.cardType"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.cardTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车牌：" prop="carNo">
          <el-input v-model="form.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="消息业务类型：" prop="businessType">
          <el-select
            clearable
            v-model="form.businessType"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.bankSendBusinessTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="异常发生时间：" prop="errorDate">
          <el-date-picker
            v-model="errorDate"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="银行名称：" prop="bankId">
          <el-select
            clearable
            filterable
            v-model="form.bankId"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.orgIds"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="sern"
          label="消息发送流水号"
          width="230"
          align="center"
        />
        <el-table-column
          prop="cardNo"
          label="卡号"
          width="180"
          align="center"
        />
        <el-table-column
          prop="carNo"
          label="车牌号"
          width="120"
          align="center"
        />
        <el-table-column prop="carColor" label="车牌颜色" align="center">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankIdStr"
          label="银行"
          width="180"
          align="center"
        />
        <el-table-column
          prop="businessTypeStr"
          label="消息业务类型"
          width="120"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="bankExceptionTypeIdStr"
          label="异常类型"
          width="150"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="definition"
          label="异常描述"
          width="280"
          align="center"
        />
        <el-table-column
          prop="createDate"
          label="发生时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="handleByStr"
          label="处理人"
          width="120"
          align="center"
        />
        <el-table-column
          prop="handleDate"
          label="处理时间"
          width="180"
          align="center"
        />
        <el-table-column label="操作" fixed="right" width="100" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="reSend(scope.row)">重发</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="form.pageIndex"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import dartSlide from '@/components/dart/Slide/index.vue'
import { filterTypeList } from '@/common/method/utils'
export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem, dartSlide },
  data() {
    return {
      licenseColorOption,
      center: 'center',
      form: {
        cardNo: '',
        cardType: '',
        carNo: '',
        businessType: '',
        preDate: '',
        lastDate: '',
        bankId: '',
        pageIndex: 1,
        pageSize: 20,
      },
      isCollapse: false,
      errorDate: '',
      typeList: {
        bankExceptionTypes: [], //银行异常类型
        cardTypes: [], //卡片类型
        bankSendBusinessTypes: [], //消息业务类型
        orgIds: [], //银行名称
      },
      bankSendBusinessTypesList: [
        {
          value: '10',
          label: '预付费扣款',
        },
        {
          value: '11',
          label: '兜底(预付费欠费扣款)',
        },
        {
          value: '20',
          label: '后付费通行费扣款',
        },
        {
          value: '21',
          label: '服务费扣款',
        },
        {
          value: '30',
          label: '代追款',
        },
        {
          value: '31',
          label: '服务费代追款',
        },
        {
          value: '99',
          label: '其他兜底扣款(24h后的记录)',
        },
      ],
      tableData: [],
      total: 0,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.init()
  },
  methods: {
    getVehicleColor,
    filterTypeList,
    init() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.getCardbindingData,
        method: 'post',
      })
        .then((res) => {
          if (res.code == 200) {
            let typeList = res.data
            //重构字典数据结构
            Object.keys(typeList).forEach((key) => {
              this.filterTypeList(typeList[key], this.$data['typeList'][key])
            })

            //字典不够前端再添加
            this.typeList.bankSendBusinessTypes =
              this.typeList.bankSendBusinessTypes.concat(
                this.bankSendBusinessTypesList
              )

            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    onSearchHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.queryExceptionMonitor,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    reSend(scope) {
      this.startLoading()
      let params = {
        bankExceptionRecordId: scope.bankExceptionRecordId,
        bankSendRecordId: scope.bankSendRecordId,
        remark: scope.remark,
      }
      this.$request({
        url: this.$interfaces.exceptionMonitorResend,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('重发成功')
            this.onSearchHandle()
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    formatDate() {
      if (this.errorDate) {
        this.form.preDate = this.errorDate[0]
        this.form.lastDate = this.errorDate[1]
      } else {
        this.form.preDate = ''
        this.form.lastDate = ''
      }
    },
    onResultHandle() {
      this.form = {
        cardNo: '',
        cardType: '',
        carNo: '',
        businessType: '',
        preDate: '',
        lastDate: '',
        bankId: '',
        pageIndex: 1,
        pageSize: 20,
      }
      this.errorDate = ''
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.onSearchHandle()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.onSearchHandle()
    },
  },
}
</script>

<style lang='scss' scoped>
.error-monitor {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.error-monitor .search {
  margin-top: 20px;
}
.error-monitor .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.error-monitor .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
