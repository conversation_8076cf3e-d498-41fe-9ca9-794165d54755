<template>
  <div class="form">
    <el-dialog :title="title"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               custom-class="special_dialog form_dialog"
               width="50%"
               :before-close="handleCloseIcon">
      <el-form ref="ruleForm"
               :model="ruleForm"
               :rules="rules"
               label-width="120px"
               class="demo-ruleForm">
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="车牌颜色"
                          prop="carColr">
              <el-select v-model="ruleForm.carColr"
                         placeholder="请选择"
                         clearable
                         :disabled="dialogType == 'edit'"
                         style="width: 100%">
                <el-option v-for="item in licenseColorOption"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="车牌号码："
                          prop="carNo">
              <el-input v-model="ruleForm.carNo"
                        clearable
                        placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="卡号："
                          prop="cardNo">
              <el-input v-model="ruleForm.cardNo"
                        clearable
                        placeholder="卡号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="加值金额(元)："
                          prop="amount">
              <el-input type="number"
                        v-model="ruleForm.amount"
                        placeholder="请输入加值金额"
                        clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="备注："
                          prop="remarks">
              <el-input type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                        v-model="ruleForm.remarks"
                        clearable>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary"
                   size="medium"
                   @click="submitForm('ruleForm')">提交</el-button>
        <el-button size="medium"
                   @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// import DartDateRange from '@/components/DateRange/date-range'
// import { getVehicleColor } from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData'
import request from '@/utils/request'
import api from '@/api/index'
import { _ignoreNull, _ignoreEmpty } from '@/utils/utils'
import { mapGetters, mapActions } from 'vuex'
var moment = require('moment')
import float from '@/common/method/float.js'
export default {
  components: {
    // DartDateRange,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      licenseColorOption,
      //   provinces: provinces,
      // vehicleCatgoryType: vehicleCatgoryType,
      dialogFormVisible: false,
      ruleForm: {
        carNo: '', //车牌
        carColr: '', //车颜色
        cardNo: '', //卡号
        amount: '', //加值金额
        remarks: '', //备注
      },
      rules: {
        carColr: [
          {
            required: true,
            message: '[车牌颜色]必须选择一个!',
            trigger: 'change',
          },
        ],
        carNo: [
          { required: true, message: '[车牌号码]不能为空!', trigger: 'blur' },
        ],
        cardNo: [
          { required: true, message: '[卡号]不能为空!', trigger: 'blur' },
        ],
        amount: [
          {
            required: true,
            message: '[加值金额]不能为空!',
            trigger: 'blur',
          },
        ],
        remarks: [
          { required: true, message: '[备注]不能为空!', trigger: 'blur' },
        ],
      },
      title: '卡账加值金额',
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  created() {},
  methods: {
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          this.add()
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    add() {
      let params = _ignoreEmpty(JSON.parse(JSON.stringify(this.ruleForm)))
      this.startLoading()
      console.log('prams===>>>', params)
      //金额转换分
      params.amount = float.mul(params.amount, 100)
      this.$request({
        url: api.cardIncrease,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message.success({
              message: '添加成功',
            })
            this.dialogFormVisible = false
            this.$emit('on-submit')
          }
        })
        .catch((err) => {
          this.endLoading()
          console.log(err)
        })
    },
    cancel() {
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
.el-form-item__label {
  text-align: center;
  white-space: nowrap;
}
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
</style>
