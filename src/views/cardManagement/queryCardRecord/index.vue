<template>
  <div class="account-list" v-loading.fullscreen.lock="fullscreenLoading">
    <dart-search
      ref="searchForm1"
      class="search"
      :formSpan="24"
      label-position="right"
      :searchOperation="false"
      :model="search"
      :fontWidth="1"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="search.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="操作类型：" prop="cardAccountBusType">
          <el-select
            v-model="search.cardAccountBusType"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in cardAccountBusTypeOptions"
              :key="index"
              :label="item.fieldNameDisplay"
              :value="item.fieldValue"
            >
            </el-option>
          </el-select>
        </dart-search-item>
        <!-- <dart-search-item label="操作员：" prop="cardNo">
          <el-input
            v-model="search.applicant"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item> -->
        <dart-search-item label="操作日期从：" prop="operateDateFrom">
          <el-date-picker
            v-model="search.operateDateFrom"
            type="datetime"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="操作日期到：" prop="operateDateTo">
          <el-date-picker
            v-model="search.operateDateTo"
            type="datetime"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="1">
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button size="mini" type="primary" @click="exportHandle"
              ><i class="el-icon-download"></i> 导出</el-button
            >
            <el-button
              type="primary"
              size="mini"
              @click="dialogAddVisible = true"
              >卡账加值</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="cardno"
          align="center"
          width="180"
          label="卡号"
        />
        <el-table-column
          prop="opttype"
          align="center"
          width="150"
          label="操作类型"
        >
          <template slot-scope="scope">
            {{ getType(cardAccountBusTypeOptions, scope.row.opttype) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="optsubtype"
          align="center"
          width="150"
          label="操作子类型"
        />
        <el-table-column
          prop="beforeamount"
          align="center"
          width="120"
          label="操作前余额(元)"
        />
        <el-table-column
          prop="amount"
          align="center"
          width="120"
          label="操作金额(元)"
        />
        <el-table-column prop="orderId" align="center" label="申请号" />
        <el-table-column prop="optby" align="center" label="操作人" />
        <el-table-column
          prop="opttime"
          align="center"
          width="170"
          label="操作时间"
        />
        <el-table-column
          prop="optdept"
          align="center"
          width="180"
          label="操作网点"
        />
        <el-table-column prop="updateby" align="center" label="创建人" />
        <el-table-column
          prop="updatetime"
          align="center"
          width="170"
          label="创建时间"
        />
        <el-table-column
          prop="updatedept"
          align="center"
          width="180"
          label="创建网点"
        />
        <el-table-column
          prop="remark"
          align="center"
          width="150"
          label="备注"
        />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="search.page"
        :page-sizes="[10, 20, 50]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <add :visible.sync="dialogAddVisible" @on-submit="onUpdateList"></add>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import add from './addAndEdit'
var moment = require('moment')
import { decode } from 'js-base64'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    add,
  },
  data() {
    return {
      loading: false,
      fullscreenLoading: false,
      dialogAddVisible: false,
      center: 'center',
      search: {
        // applicant: '', //操作员id
        cardAccountBusType: '', //操作类型
        cardNo: '', // 卡号
        operateDateFrom: '', // 接收日期截至
        operateDateTo: '', // 接收日期起始
        page: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
      cardAccountBusTypeOptions: [],
    }
  },
  created() {
    // this.search.handleLastDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.handlePreDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.receiveLastDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.receivePreDate = moment(new Date()).format('YYYY-MM-DD')
    this.initDict()
  },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].fieldValue == value) {
          return typeObj[i].fieldNameDisplay
        }
      }
      return ''
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getMsgMonitorList()
    },
    handleCurrentChange(page) {
      this.search.page = page
      this.getMsgMonitorList()
    },
    initDict() {
      this.$request({
        url: this.$interfaces.cardAccountBusType,
        method: 'post',
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.cardAccountBusTypeOptions = res.data
          }
        })
        .catch((err) => {})
    },
    onUpdateList() {},
    getMsgMonitorList() {
      let params = { ...this.search }
      if (!params.cardNo) {
        this.$message({
          type: 'warning',
          message: '卡号不能为空',
        })
        return
      }
      this.loading = true
      console.log('params', params)
      params.operateDateFrom = params.operateDateFrom
        ? moment(params.operateDateFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.operateDateTo = params.operateDateTo
        ? moment(params.operateDateTo).format('YYYY-MM-DD HH:mm:ss')
        : ''

      this.$request({
        url: this.$interfaces.queryCardRecord,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.loading = false
          this.tableData = res.data.data
          this.total = res.data.total
        })
        .catch((error) => {})
    },
    exportHandle() {
      let params = { ...this.search }
      if (!params.cardNo) {
        this.$message({
          type: 'warning',
          message: '卡号不能为空',
        })
        return
      }
      this.fullscreenLoading = true
      console.log('params', params)
      params.operateDateFrom = params.operateDateFrom
        ? moment(params.operateDateFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.operateDateTo = params.operateDateTo
        ? moment(params.operateDateTo).format('YYYY-MM-DD HH:mm:ss')
        : ''

      this.$request({
        url: this.$interfaces.queryCardExport,
        method: 'post',
        data: params,
        responseType: 'blob',
      })
        .then((res) => {
          this.fullscreenLoading = false
          console.log('blob', res)
          if (res.type == 'application/json') {
            console.log('application/json')
            this.blobToJson(res).then((result) => {
              console.log('result', result)
              if (result.code != 200) {
                this.$message({
                  type: 'error',
                  message: result.msg,
                })
                return
              }
            })
          } else {
            this.getBlob(res, 'application/vnd.ms-excel', '卡账操作记录')
          }
        })
        .catch((error) => {
          console.log('error', error)
          this.fullscreenLoading = false
        })
    },
    blobToJson(data) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsText(data, 'utf-8')
        reader.onload = function () {
          const message = JSON.parse(reader.result)
          // console.log('message', message)
          // return message
          resolve(message)
        }
      })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    onSearchHandle() {
      this.search.page = 1
      this.getMsgMonitorList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.page = 1
      this.search.pageSize = 20
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
