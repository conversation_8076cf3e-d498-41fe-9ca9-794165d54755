import {
  licenseColorOption,
} from '@/common/const/optionsData.js'
import {
  getVehicleColor,
} from '@/common/method/formatOptions'

//表格
export const listColoumns = [
  {
    prop: 'customerId',
    label: '用户ID',
  },
  {
    prop: 'vehicleColor',
    width: 120,
    label: '车辆颜色',
    formatter: (row) => {
      return getVehicleColor(row)
    }
  },
  {
    prop: 'vehicleNo',
    label: '车牌号',
  },
  {
    prop: 'cardNo',
    label: '卡号',
    width: 200
  },
  {
    prop: 'newCardStatus_str',
    label: '卡号状态',
  },
  {
    prop: 'orderId',
    label: '订单编号',
  },
  {
    prop: 'orderTime',
    label: '下单时间',
  },
  {
    prop: 'branchName',
    label: '操作网点',
  },
  {
    prop: 'operatorName',
    label: '操作人',
    width: 100,
  },
  {
    prop: 'action',
    label: '操作'
  }
]

//表单
export const listForm = (state) => {
  return [
    {
      type: 'input',
      field: 'vehicleCode',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOption
    },
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号：',
      default: '',
    },
    {
      type: 'input',
      field: 'customerId',
      label: '用户ID：',
      default: '',
    },
  ]
}