<template>
  <div class="toll-record">
    <SearchForm
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="8"
      :rules="rules"
      :btnAlign="'left'"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
        showIndex
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button
              slot="btn"
              size="mini"
              type="primary"
              @click="handelRow(scope)"
              >确认发行</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { issueNotCompleteList, issueCardRepush } from '@/api/infoUpload'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      listColoumns,
      api: issueNotCompleteList,
      pageSize: '',
      pageNum: '',
      rules: {
        // obuNo: [{ required: true, message: '请填obu号', trigger: 'change' }]
      }
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 搜索框表单操作
    onSearchHandle(formData) {
      if((!formData.vehicleCode || !formData.vehicleColor) && !(!formData.vehicleCode && !formData.vehicleColor)){
        this.$message.error('请填写车牌号和车牌颜色')
        return
      }
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      // console.log(params,'searchFormData')
      this.currentFormData = params
      this.getTableData()
    },
    async handelRow(row) {
      console.log(row, 'row')
      let {
        customerId,
        cardNo,
        productType,
        vehicleNo,
        vehicleColor,
      } = row
      let params = {
        customer_id: customerId,
        cpu_card_id: cardNo,
        gx_card_type: productType,
        issue_type: 45,
        vehicle_code: vehicleNo,
        vehicle_color: vehicleColor
      }
      this.$confirm('是否确认进行发行操作？', '发行操作', {
        distinguishCancelAndClose: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        let res = await issueCardRepush(params)
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.getTableData()
          console.log(res, 'res')
        }
      })
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>