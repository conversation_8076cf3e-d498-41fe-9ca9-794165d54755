<template>
  <div class="account-list" v-loading.fullscreen.lock="fullscreenLoading">
    <dart-search
      ref="searchForm1"
      class="search"
      :formSpan="24"
      label-position="right"
      :searchOperation="false"
      :model="search"
      :fontWidth="1"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="卡号" prop="cardNo">
          <el-input v-model="search.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="2">
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="cardNo"
          align="center"
          width="180"
          label="卡号"
        />
        <el-table-column
          prop="disAmount"
          align="center"
          width="120"
          label="操作金额(元)"
        />
        <el-table-column prop="memo" align="center" width="180" label="备注" />
        <el-table-column
          prop="importBatch"
          align="center"
          width="150"
          label="操作类型"
        />
        <el-table-column
          prop="createTime"
          align="center"
          width="160"
          label="操作时间"
        />
        <el-table-column
          prop="brforeCardAmount"
          align="center"
          width="120"
          label="操作前金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.brforeCardAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="afterCardAmount"
          align="center"
          width="120"
          label="操作后金额(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.afterCardAmount | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column prop="operatorNo" align="center" label="操作人" />
        <el-table-column
          prop="branchNo"
          align="center"
          width="180"
          label="操作网点"
        />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="search.pageNum"
        :page-sizes="[20, 50, 100]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')
import { decode } from 'js-base64'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      loading: false,
      fullscreenLoading: false,
      center: 'center',
      search: {
        carNo: '', //车牌号码
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      tableData: [],
    }
  },
  created() {},
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].fieldValue == value) {
          return typeObj[i].fieldNameDisplay
        }
      }
      return ''
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getCardIncreaseList()
    },
    handleCurrentChange(page) {
      this.search.pageNum = page
      this.getCardIncreaseList()
    },
    getCardIncreaseList() {
      let params = { ...this.search }
      this.loading = true
      console.log('params', params)

      this.$request({
        url: this.$interfaces.cardIncreaseList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.loading = false
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch((error) => {})
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getCardIncreaseList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
