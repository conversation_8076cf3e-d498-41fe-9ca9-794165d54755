<template>
  <div class="import-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      title="注销凭证导入"
      :before-close="handleCloseIcon"
      width="45%"
    >
      <!-- <div class="selector g-flex g-flex-start g-flex-align-center">
        <div class="label">生成方式：</div>
        <el-select v-model="formData.source"
                   placeholder="请选择"
                   clearable>
          <el-option v-for="item in paymentSourceOptions"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </div> -->
      <fieldset class="fieldset">
        <legend class="fieldset-legend">附件上传</legend>

        <div slot="tip" class="el-upload__tip">1、仅支持xls格式的Excel文件</div>
        <div slot="tip" class="el-upload__tip">
          2、导入字段包含：[ 卡号 | 车牌号 ]
        </div>
        <div slot="tip" class="el-upload__tip">
          <p
            @click="download"
            style="font-size: 14px; cursor: pointer; color: #409eff"
          >
            下载注销凭证模板
          </p>
        </div>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
import { paymentSourceOptions } from '@/common/const/optionsData.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,

      showLoading: false,
      formData: {
        file: '',
        source: '',
      },
      fileList: [],
      paymentSourceOptions,
      responseDialogVisible: false,
      gridData: [],
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fileList = []
        this.formData.file = ''
        this.formData.source = ''
      }
      this.responseDialogVisible = false
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    download() {
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/monitor/downloadTemplate?Authorization=Bearer ' +
        getToken()
      window.open(url)
    },
    submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      this.upload()
    },
    upload() {
      this.showLoading = true
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('source', this.formData.source)

      this.$request({
        url: this.$interfaces.getMsgMonitorUpload,
        method: 'post',
        data: formData,
      })
        .then((res) => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = ''
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.data,
            })
          }
        })
        .catch((err) => {
          this.showLoading = false
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.fieldset-legend {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  width: 80px;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>