<template>
  <el-dialog title="新增注销凭证"
             @closed='onClosedHandle'
             :visible.sync="dialogVisible"
             :close-on-click-modal="false"
             :center="true"
             width="480px"
             custom-class="add-cancel-dialog">
    <div class="form-info">
      <el-form :model="ruleForm"
               :rules="rules"
               ref="ruleForm"
               label-width="140px"
               size="medium"
               class="dt-form dt-form--max">
        <el-form-item label="车牌号码："
                      prop="carNo">
          <el-input v-model="ruleForm.carNo"></el-input>
        </el-form-item>
        <el-form-item label="卡号："
                      prop="cardNo">
          <el-input v-model="ruleForm.cardNo"></el-input>
        </el-form-item>

      </el-form>

    </div>
    <div slot="footer"
         class="btn">
      <el-button  @click="addHandle" type="primary"
                 style="width: 100px;"
                 size="small">添加</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        cardNo: "",
        carNo: "",

      },
      rules: {
        carNo: [
          { required: true, message: "请输入车牌号码", trigger: "blur" },
        ],
        cardNo: [
          { required: true, message: "请输入卡号", trigger: "blur" },
        ]
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible;
    })
  },
  methods: {
    addHandle() {

      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.addMsgMonitor();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    addMsgMonitor() {

      this.startLoading();

      this.$request({
        url: this.$interfaces.addMsgMonitor,
        method: 'post',
        data: this.ruleForm,
      })
        .then((res) => {
          this.endLoading();
          if (res.code == 200) {
            this.$message({
              message: '新增成功',
              type: 'success'
            });
            this.dialogVisible = false
          }
        })
        .catch((err) => {
          this.endLoading();
        })
    },
    onClosedHandle() {
      this.dialogVisible = false;
    }

  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        this.dialogVisible = val;
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
}
</script>

<style lang="scss">
.add-cancel-dialog .el-dialog__body {
  padding-bottom: 10px;
}
.form-info {
  padding: 0 25px;
}
.add-cancel-dialog .btn {
  width: 100%;
  text-align: center;
}
</style>