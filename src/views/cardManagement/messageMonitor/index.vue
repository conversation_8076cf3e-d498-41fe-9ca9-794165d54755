<template>
  <div class="account-list">
    <dart-search
      ref="searchForm1"
      class="search"
      :formSpan="24"
      label-position="right"
      :searchOperation="false"
      :model="search"
      :fontWidth="1"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="search.cardNo" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号码：" prop="carNo">
          <el-input v-model="search.carNo" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="银行名称：" prop="bankId">
          <el-select
            v-model="search.bankId"
            clearable
            filterable
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in bankOptions"
              :key="index"
              :label="item.fieldNameDisplay"
              :value="item.fieldValue"
            >
            </el-option>
          </el-select>
        </dart-search-item>
        <dart-search-item label="消息业务类型：" prop="businessType">
          <el-select v-model="search.businessType" placeholder="请选择">
            <el-option
              v-for="(item, index) in businessTypeOptions"
              :key="index"
              :label="item.fieldNameDisplay"
              :value="item.fieldValue"
            >
            </el-option>
          </el-select>
        </dart-search-item>
        <!-- <dart-search-item label="接收开始日期：" prop="receivePreDate">
          <el-date-picker
            v-model="search.receivePreDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="接收结束日期：" prop="receiveLastDate">
          <el-date-picker
            v-model="search.receiveLastDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item> -->
        <dart-search-item label="接收日期选择" prop="">
          <el-date-picker
            v-model="receiveValue"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="接收开始日期"
            end-placeholder="接收结束日期"
            @change="receiveChange"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="处理日期选择" prop="">
          <el-date-picker
            v-model="handleValue"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="处理开始日期"
            end-placeholder="处理结束日期"
            @change="handleChange"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="处理状态：" prop="receiveStatus">
          <el-select
            v-model="search.receiveStatus"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="(item, index) in receiveStatusOptions"
              :key="index"
              :label="item.fieldNameDisplay"
              :value="item.fieldValue"
            >
            </el-option>
          </el-select>
        </dart-search-item>
        <!-- <dart-search-item label="处理开始日期：" prop="handlePreDate">
          <el-date-picker
            v-model="search.handlePreDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="处理结束日期：" prop="handleLastDate">
          <el-date-picker
            v-model="search.handleLastDate"
            type="date"
            placeholder="选择日期"
          >
          </el-date-picker>
        </dart-search-item> -->
        <dart-search-item :is-button="true" :colElementNum="1">
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button
              size="mini"
              type="primary"
              @click="importDialogVisible = true"
              ><i class="el-icon-upload"></i> 批量导入</el-button
            >
            <el-button
              v-permisaction="['monitor:cancel']"
              size="mini"
              @click="visible = true"
              >新增注销凭证</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="frontServerAccessNo"
          align="center"
          width="120"
          show-overflow-tooltip
          label="银行交易流水号"
        />
        <el-table-column prop="businessType" align="center" label="业务类型" />
        <el-table-column
          prop="cardNo"
          align="center"
          width="150"
          label="卡号"
        />
        <el-table-column prop="carNo" align="center" label="车牌号码" />
        <el-table-column
          prop="carColor"
          align="center"
          width="80"
          label="车牌颜色"
        />
        <el-table-column prop="bankId" align="center" label="银行名称" />
        <el-table-column prop="receiveDate" align="center" label="接收时间" />
        <el-table-column prop="receiveStatus" align="center" label="处理状态" />
        <el-table-column
          prop="cancelDate"
          align="center"
          width="160"
          label="处理时间"
        />
        <el-table-column prop="createUser" align="center" label="操作人" />
        <el-table-column prop="createTime" align="center" label="操作时间" />
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="search.pageIndex"
        :page-size="search.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <addCancel :visible.sync="visible" v-if="visible"></addCancel>
    <import-dialog :visible.sync="importDialogVisible"> </import-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')
import addCancel from './addCancel'
import importDialog from './importDialog'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    addCancel,
    importDialog
  },
  data() {
    return {
      loading: false,
      visible: false,
      importDialogVisible: false,
      center: 'center',
      handleValue: [],
      receiveValue: [],
      search: {
        bankId: '',
        businessType: '1',
        carNo: '', // 车牌号码
        cardNo: '', // 卡号
        handleLastDate: '', // 处理日期截至
        handlePreDate: '', // 处理日期起始
        receiveLastDate: '', // 接收日期截至
        receivePreDate: '', // 接收日期起始
        receiveStatus: '', // 处理状态
        pageIndex: 1,
        pageSize: 10
      },
      total: 0,
      userNoList: [],
      tableData: [],
      bankOptions: [],
      businessTypeOptions: [],
      receiveStatusOptions: []
    }
  },
  created() {
    // this.search.handleLastDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.handlePreDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.receiveLastDate = moment(new Date()).format('YYYY-MM-DD')
    // this.search.receivePreDate = moment(new Date()).format('YYYY-MM-DD')
    this.initDict()
  },
  methods: {
    handleSizeChange() {},
    handleCurrentChange(page) {
      this.search.pageIndex = page
      this.getMsgMonitorList()
    },
    initDict() {
      this.$request({
        url: this.$interfaces.getMsgMonitorDict,
        method: 'post'
      })
        .then(res => {
          if (res.code == 200 && res.data) {
            this.bankOptions = [
              {
                fieldNameDisplay: '全部',
                fieldValue: ''
              },
              ...res.data.orgIds
            ]
            this.businessTypeOptions = res.data.receiveBusinessTypes
            this.receiveStatusOptions = res.data.receiveHandleFlags
            // this.getMsgMonitorList()
          }
        })
        .catch(err => {})
    },
    validata() {
      if (!this.receiveValue) {
        this.receiveValue = []
      }
      if (!this.handleValue) {
        this.handleValue = []
      }
      if (
        this.receiveValue.length == 0 &&
        this.handleValue.length == 0 &&
        !this.search.carNo &&
        !this.search.cardNo
      ) {
        this.$message({
          type: 'warning',
          message: '卡号、车牌号码、接收日期、处理日期不能同时为空'
        })
        return false
      }
      return true
    },
    getMsgMonitorList() {
      if (this.validata()) {
        this.loading = true
        let params = { ...this.search }

        params.receivePreDate = this.receiveValue[0] || ''
        params.receiveLastDate = this.receiveValue[1] || ''
        params.handlePreDate = this.handleValue[0] || ''
        params.handleLastDate = this.handleValue[1] || ''

        this.$request({
          url: this.$interfaces.getMsgMonitorList,
          method: 'post',
          data: params
        })
          .then(res => {
            console.log(res.data.records)
            this.loading = false
            this.tableData = res.data.records
            this.total = res.data.total
            this.search.pageIndex = res.data.current
            this.search.pageSize = res.data.size
          })
          .catch(error => {})
      }
    },

    onSearchHandle() {
      this.search.pageIndex = 1
      this.getMsgMonitorList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.businessType = '1'
      this.search.pageIndex = 1
      this.search.pageSize = 10
    },
    handleSelectionChange(selection) {
      this.userNoList = []
      selection.forEach(item => {
        if (!this.userNoList.includes(item.netUserNo)) {
          this.userNoList.push(item.netUserNo)
        }
      })
    },
    receiveChange(e) {
      console.log('receiveChange', e)
      if (this.handleValue && this.handleValue.length > 0) {
        this.handleValue = []
      }
    },
    handleChange(e) {
      console.log('handleChange', e)
      if (this.receiveValue && this.receiveValue.length > 0) {
        this.receiveValue = []
      }
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
