<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:发送消息监控
  * @author:dengwz42592
  * @date:2023/05/1 15:27:48
-->
<template>
  <div class="send-message">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="form"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      :labelTextLength="8"
      class="search"
    >
      <template slot="search-form">
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="form.cardNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号：" prop="carNo">
          <el-input v-model="form.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="银行名称：" prop="bankId">
          <el-select
            clearable
            filterable
            v-model="form.bankId"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.orgIds"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="消息业务类型：" prop="businessType">
          <el-select
            clearable
            v-model="form.businessType"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in typeList.bankSendBusinessTypes"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="发送时间：" prop="">
          <el-date-picker
            v-model="sendDate"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </dart-search-item>

        <dart-search-item label="响应时间：" prop="">
          <el-date-picker
            v-model="anDate"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="交易码：" prop="trxCode">
          <el-input v-model="form.trxCode" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="2">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >查询</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column prop="trxCode" label="交易码" align="center" />
        <el-table-column
          prop="businessTypeStr"
          label="业务类型"
          align="center"
        />
        <el-table-column
          prop="cardNo"
          label="卡号"
          width="180"
          align="center"
        />
        <el-table-column prop="carNo" label="车牌" width="120" align="center" />
        <el-table-column prop="carColor" label="车牌颜色" align="center">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bankIdStr"
          label="银行名称"
          width="180"
          align="center"
        />
        <el-table-column
          prop="sendByName"
          label="发送人"
          width="180"
          align="center"
        />
        <el-table-column
          prop="sendDate"
          label="发送时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="answerDate"
          label="响应时间"
          width="180"
          align="center"
        />
        <el-table-column
          prop="answerResult"
          label="响应结果"
          width="180"
          align="center"
        />
      </el-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="form.pageIndex"
        :page-sizes="[20, 50, 100, 200]"
        :page-size="form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import dartSlide from '@/components/dart/Slide/index.vue'
import { filterTypeList } from '@/common/method/utils'
export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem, dartSlide },
  data() {
    return {
      licenseColorOption,
      center: 'center',
      form: {
        cardNo: '',
        carNo: '',
        bankId: '',
        businessType: '',
        sendPreDate: '',
        sendLastDate: '',
        trxCode: '',
        anPreDate: '',
        anLastDate: '',
        pageIndex: 1,
        pageSize: 20,
      },
      sendDate: '', //接收时间
      anDate: '', //响应时间
      typeList: {
        cardTypes: [], //卡片类型
        bankSendBusinessTypes: [], //消息业务类型
        orgIds: [], //银行名称
      },
      bankSendBusinessTypesList: [
        {
          value: '10',
          label: '预付费扣款',
        },
        {
          value: '11',
          label: '兜底(预付费欠费扣款)',
        },
        {
          value: '20',
          label: '后付费通行费扣款',
        },
        {
          value: '21',
          label: '服务费扣款',
        },
        {
          value: '30',
          label: '代追款',
        },
        {
          value: '31',
          label: '服务费代追款',
        },
        {
          value: '99',
          label: '其他兜底扣款(24h后的记录)',
        },
      ],
      tableData: [],
      total: 0,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.init()
  },
  methods: {
    getVehicleColor,
    filterTypeList,
    init() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.getSendMonitorData,
        method: 'post',
      })
        .then((res) => {
          if (res.code == 200) {
            let typeList = res.data
            //重构字典数据结构
            Object.keys(typeList).forEach((key) => {
              this.filterTypeList(typeList[key], this.$data['typeList'][key])
            })

            //字典不够前端再添加
            this.typeList.bankSendBusinessTypes =
              this.typeList.bankSendBusinessTypes.concat(
                this.bankSendBusinessTypesList
              )

            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    onSearchHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.logSearch,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            // this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    formatDate() {
      if (this.sendDate) {
        this.form.sendPreDate = this.sendDate[0]
        this.form.sendLastDate = this.sendDate[1]
      } else {
        this.form.sendPreDate = ''
        this.form.sendLastDate = ''
      }
      if (this.anDate) {
        this.form.anPreDate = this.anDate[0]
        this.form.anLastDate = this.anDate[1]
      } else {
        this.form.anPreDate = ''
        this.form.anLastDate = ''
      }
    },
    onResultHandle() {
      this.form = {
        cardNo: '',
        carNo: '',
        bankId: '',
        businessType: '',
        sendPreDate: '',
        sendLastDate: '',
        trxCode: '',
        anPreDate: '',
        anLastDate: '',
        pageIndex: 1,
        pageSize: 20,
      }
      this.sendDate = ''
      this.anDate = ''
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.onSearchHandle()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.onSearchHandle()
    },
  },
}
</script>

<style lang='scss' scoped>
.send-message {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.send-message .search {
  margin-top: 20px;
}
.send-message .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.send-message .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
