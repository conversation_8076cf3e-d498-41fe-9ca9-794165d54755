<template>
  <div class="page-list">
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">

          <dart-search-item :is-button="true"
                            :colElementNum='3'>
            <div class="g-flex">
              <el-button type="primary"
                         @click="visible=true">编辑页面Slide</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">

          <dart-search-item :is-button="true"
                            :colElementNum='3'>
            <div class="g-flex">
              <el-button type="primary"
                         @click="visible1=true">列表页面Slide</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">

          <dart-search-item :is-button="true"
                            :colElementNum='3'>
            <div class="g-flex">
              <el-button type="primary"
                         @click="visible2=true">遮罩区关闭Slide</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <dartSlide :visible.sync='visible'>
      <div class="modular-view-body"
           style="">
        <div v-for='(item) in 10'
             :key=item>
          <div style="height:200px">{{item}}</div>
        </div>
      </div>
      <div class="modular-view-footer"
           slot="footer">
        <el-button size='medium' @click="visible=false">提交</el-button>
        <el-button size="medium" @click="visible=false">关闭</el-button>
      </div>
    </dartSlide>
    <dartSlide :visible.sync='visible1'>
      <div v-for='(item) in 10'
           :key=item>
        <div style="height:200px">{{item}}</div>
      </div>
    </dartSlide>
    <dartSlide :visible.sync='visible2'
               :maskClosable='true'>
      <div v-for='(item) in 10'
           :key=item>
        <div style="height:200px">{{item}}</div>
      </div>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import dartSlide from '@/components/ProComponents/Slide/index'
export default {

  components: {
    dartSearch,
    dartSearchItem,
    dartSlide
  },
  created() {

  },
  data() {
    return {
      visible: false,
      visible1: false,
      visible2: false
    }
  }
}
</script>

<style lang="scss" scoped>
.page-list {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
}
.page-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.page-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
