<template>
  <!-- 搜索栏 -->
  <dart-search
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item
          :key="item.fieldKey"
          :label="item.fieldLabel"
          :prop="item.fieldKey"
        >
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="2">
        <div class="g-flex g-flex-end">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  inputSchema,
  selectSchema
} from '@/components/schemaQuery/schema'
import { datePickerOptions } from '@/components/schemaQuery/tool'
let branchTypeOptions = [
  { value: '01', label: '自营' },
  { value: '02', label: '运营' },
  { value: '03', label: '一站式' },
  { value: '04', label: '合作' },
  { value: '05', label: '银行' },
  { value: '06', label: '线上' },
  { value: '99', label: '其他' }
]

export default {
  data() {
    return {
      loading: false,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        branchType: [
          { required: true, message: '请选择部门属性', trigger: 'change' }
        ]
      },
      search: {
        startTime: '',
        endTime: '',
        branchType: '',
        payerName: '' // 付款方
      },
      formProperties: {
        startTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'startTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        endTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计开始时间',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        branchType: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: branchTypeOptions,
            placeholder: '请选择部门属性'
          },
          fieldLabel: '部门属性',
          fieldKey: 'branchType'
        },
        payerName: {
          ...inputSchema,
          fieldProps: {
            ...inputSchema.fieldProps,
            placeholder: '请输入付款方名称'
          },
          fieldLabel: '付款方名称',
          fieldKey: 'payerName'
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField
  },

  computed: {},
  created() {},
  methods: {
    onSearchHandle() {},
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
