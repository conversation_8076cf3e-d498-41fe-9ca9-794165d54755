<template>
  <div style="padding:20px">
    <div class="schema-search" style="background-color: #fff;">
      <el-tabs v-model="activeIndex" type="border-card">
        <el-tab-pane label="基础类配置" name="recharge1">
          <single></single>
        </el-tab-pane>
        <el-tab-pane label="复杂类配置" name="recharge2"> </el-tab-pane>
        <el-tab-pane label="自定义组件配置" name="recharge3"> </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import single from './single/index.vue'
export default {
  data() {
    return {
      activeIndex: 'recharge1'
    }
  },

  components: {
    single
  },

  computed: {},
  created() {},
  methods: {}
}
</script>
<style>
.schema-search .el-tabs--border-card {
  box-shadow: none !important;
}
.schema-search .el-tabs__content {
  padding: 0 !important;
}
</style>
