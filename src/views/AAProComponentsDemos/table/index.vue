<template>
  <div class="page-list">

    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <template v-if="collapse">
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
          </template>

          <dart-search-item :is-button="true"
                            :colElementNum='collapse? 1 :2'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>
              <el-button type="text"
                         @click="collapse=!collapse"><span v-if="collapse">收起</span><span v-if="!collapse">展开</span></el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table-box">
      <div class="g-flex g-flex-end operation-wrapper">
        <el-button type="primary"
                   size="mini">新增用户</el-button>
        <el-button type="primary"
                   size="mini">新增用户</el-button>
        <el-button type="primary"
                   size="mini">新增用户</el-button>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData"
                  align="center"
                  height="100%"
                  border
                  header-align="center"
                  style="width: 100%;"
                  :row-style="{ height: '52px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '52px' }"
                  :header-cell-style="{ padding: '0px' }">
          <el-table-column prop="netUserId"
                           align="center"
                           label="用户编号" />
          <el-table-column prop="netLoginName"
                           align="center"
                           label="登录名" />
          <el-table-column prop="companyName"
                           align="center"
                           label="扣款流水号"
                           min-width="80">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ scope.row.companyName }}
                </div>
                <span> {{ scope.row.companyName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="netMobile"
                           align="center"
                           label="联系电话" />
          <el-table-column prop="createdTime"
                           label="创建时间"
                           min-width="120" />
          <el-table-column prop="userType"
                           align="center"
                           min-width="80"
                           label="用户类型">
            <template slot-scope="scope">
              {{scope.row.userType == 2 ? '单位' : '个人'}}
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="操作"
                           width="160">
            <template slot-scope="scope">
              <el-button type="text">查看</el-button>
              <el-button type="text">编辑</el-button>
              <el-dropdown>
                <el-button type="text"
                           style="margin-left: 10px;">更多操作</el-button>
                <el-dropdown-menu trigger="click"
                                  slot="dropdown"
                                  style="padding:10px 20px">
                  <div class="g-flex">
                    <el-button type="text">开票</el-button>
                    <el-button type="text">红冲</el-button>
                    <el-button type="text">删除</el-button>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     @current-change="changePage"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
export default {

  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      collapse: false,
      tableData: [],
      search: {
        company: '',
        mobile: '',
        loginName: '',
        userType: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0
    }
  },
  created() {
    this.getAccountList();
  },
  methods: {
    changePage(page) {
      this.search.pageNum = page
      this.getAccountList()
    },
    getAccountList() {

      this.$request({
        url: this.$interfaces.netnetAccount,
        method: 'post',
        data: this.search,
      })
        .then((res) => {

          this.tableData = res.data.records
          this.total = res.data.total
          this.search.pageNum = res.data.current
          this.search.pageSize = res.data.size
        })
        .catch((error) => {

        })
    },
  }
}
</script>

<style lang="scss" scoped>
.page-list {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
}
.page-list .table-box {
  padding: 10px 15px;
  flex: 1;
  height: 0;
  background-color: #fff;
  flex-flow: column;
  display: flex;
  overflow: hidden;
}
.page-list .table-box .operation-wrapper {
  padding-bottom: 10px;
}
.page-list .table-box .table-wrapper {
  flex: 1;
  height: 0;
}
.page-list .pagination {
  padding: 0px 15px 10px 15px;
  background-color: #fff;
}
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
