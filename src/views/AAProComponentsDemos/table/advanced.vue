<template>
  <div class="page-list">

    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <template v-if="collapse">
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
          </template>

          <dart-search-item :is-button="true"
                            :colElementNum='collapse? 1 :2'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>
              <el-button type="text"
                         @click="collapse=!collapse"><span v-if="collapse">收起</span><span v-if="!collapse">展开</span></el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table-box">
      <div class="g-flex g-flex-end operation-wrapper">
        <el-button type="primary"
                   size="mini"
                   @click="onBatchAudit">批量审核</el-button>
        <el-button type="danger"
                   size="mini"
                   @click="onBatchDel">批量删除</el-button>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData"
                  @selection-change="handleSelectionChange"
                  align="center"
                  height="100%"
                  border
                  header-align="center"
                  style="width: 100%;"
                  :row-style="{ height: '52px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '52px' }"
                  :header-cell-style="{ padding: '0px' }">
          <el-table-column type="selection"
                           width="55">
          </el-table-column>
          <el-table-column prop="netUserId"
                           align="center"
                           label="用户编号" />
          <el-table-column prop="netLoginName"
                           align="center"
                           label="登录名" />
          <el-table-column prop="companyName"
                           align="center"
                           label="扣款流水号"
                           min-width="80">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ scope.row.companyName }}
                </div>
                <span> {{ scope.row.companyName }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="netMobile"
                           align="center"
                           label="联系电话" />
          <el-table-column prop="createdTime"
                           label="创建时间"
                           min-width="120" />
          <el-table-column prop="userType"
                           align="center"
                           min-width="80"
                           label="用户类型">
            <template slot-scope="scope">
              {{scope.row.userType == 2 ? '单位' : '个人'}}
            </template>
          </el-table-column>
          <el-table-column align="center"
                           label="操作"
                           width="160">
            <template slot-scope="scope">
              <el-button type="text" @click="detailVisible=true">查看</el-button>
              <el-button type="text"
                         @click="visible=true">编辑</el-button>
              <el-dropdown>
                <el-button type="text"
                           style="margin-left: 10px;">更多操作</el-button>
                <el-dropdown-menu trigger="click"
                                  slot="dropdown"
                                  style="padding:10px 20px">
                  <div class="g-flex">
                    <el-button type="text">开票</el-button>
                    <el-button type="text">红冲</el-button>
                    <el-button type="text">删除</el-button>
                  </div>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     @current-change="changePage"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <edit :editVisible.sync='visible' v-if="visible"></edit>
    <detailView :detailVisible.sync='detailVisible' v-if="detailVisible"></detailView>

  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import dartSlide from '@/components/ProComponents/Slide/index'
import edit from './editView.vue';
import detailView from './detailView.vue';

export default {

  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    edit,
    detailView
  },
  data() {
    return {
      collapse: false,
      tableData: [],
      search: {
        company: '',
        mobile: '',
        loginName: '',
        userType: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      multipleSelection: [],
      visible: false,
      detailVisible:false
    }
  },
  created() {
    this.getAccountList();
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 批量删除
    onBatchDel() {
      let _self = this;
      if (!(this.multipleSelection && this.multipleSelection.length)) {
        this.$message.error('请选择需要处理数据！');
        return
      }
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: function (action) {
          if (action == 'confirm') {
            _self.getAccountList();
          }
        }
      })
    },
    //批量审核
    onBatchAudit() {
      let _self = this;
      if (!(this.multipleSelection && this.multipleSelection.length)) {
        this.$message.error('请选择需要处理数据！');
        return
      }
      this.$confirm('此操作将审核该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: function (action) {
          if (action == 'confirm') {
            _self.getAccountList();
          }
        }
      })
    },
    // 页数change事件
    changePage(page) {
      this.search.pageNum = page
      this.getAccountList()
    },
    // 查询用户列表
    getAccountList() {

      this.$request({
        url: this.$interfaces.netnetAccount,
        method: 'post',
        data: this.search,
      })
        .then((res) => {

          this.tableData = res.data.records
          this.total = res.data.total
          this.search.pageNum = res.data.current
          this.search.pageSize = res.data.size
        })
        .catch((error) => {

        })
    },
  }
}
</script>

<style lang="scss" scoped>
.page-list {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
}
.page-list .table-box {
  padding: 10px 15px;
  flex: 1;
  height: 0;
  background-color: #fff;
  flex-flow: column;
  display: flex;
  overflow: hidden;
}
.page-list .table-box .operation-wrapper {
  padding-bottom: 10px;
}
.page-list .table-box .table-wrapper {
  flex: 1;
  height: 0;
}
.page-list .pagination {
  padding: 0px 15px 10px 15px;
  background-color: #fff;
}
.tooltip-item {
  display: block;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
</style>
