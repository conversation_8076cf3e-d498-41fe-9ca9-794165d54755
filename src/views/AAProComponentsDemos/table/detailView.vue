<template>
  <dartSlide :visible.sync='visible'
             title='详细信息'
             width='60%'>
    <div class="modular-view-body">
      <div class="form-box">
        <el-form :label-position="labelPosition"
                 class="form-detail"
                 :model="formLabelAlign">
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="名称">
                formLabelAlign.region
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="活动区域">
                formLabelAlign.region
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户姓名">
                formLabelAlign.region
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="名称">
                formLabelAlign.region
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="活动区域">
                formLabelAlign.region
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户姓名">
                formLabelAlign.region
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
        <div >

        </div>
      </div>

    </div>

  </dartSlide>

</template>

<script>
import dartSlide from '@/components/ProComponents/Slide/index'
export default {
  props: {
    detailVisible: [Boolean]
  },
  data() {
    return {
      visible: false,
      labelPosition: 'left',
      formLabelAlign: {
        name: '',
        region: '',
        type: ''
      }
    };
  },

  components: {
    dartSlide
  },
  watch: {
    detailVisible(val) {
      this.init()
    },
    visible(val) {
      setTimeout(() => {
        this.$emit('update:detailVisible', val);
      }, 300);
    }
  },
  created() {
    this.init();

  },
  computed: {},

  methods: {
    init() {
      let _self = this;
      this.$nextTick(() => {
        _self.visible = _self.detailVisible;
      })
    }
  }
}
</script>
<style >
.form-box {
  padding: 16px;
}
.form-detail .el-form-item {
  margin-bottom: 0px;
}
</style>