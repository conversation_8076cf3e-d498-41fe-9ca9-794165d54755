<template>
  <dartSlide :visible.sync='visible'
             title='编辑用户信息'
             width='560px'>
    <div class="modular-view-body">
      <div class="form-box">
        <el-form :label-position="labelPosition"
                 label-width="80px"
                 size="small"
                 :model="formLabelAlign">
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="用户编号">
                <el-input v-model="formLabelAlign.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登录名">
                <el-input v-model="formLabelAlign.region"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="电话">
                <el-input v-model="formLabelAlign.name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱">
                <el-input v-model="formLabelAlign.region"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item label="联系地址">
                <el-input v-model="formLabelAlign.type"></el-input>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </div>
    </div>
    <div class="modular-view-footer"
         slot="footer">
      <el-button size='medium'
                 type="primary"
                 @click="visible=false">提交</el-button>
      <el-button size="medium"
                 @click="visible=false">关闭</el-button>
    </div>

  </dartSlide>

</template>

<script>
import dartSlide from '@/components/ProComponents/Slide/index'
export default {
  props: {
    editVisible: [Boolean]
  },
  data() {
    return {
      visible: false,
      labelPosition: 'top',
      formLabelAlign: {
        name: '',
        region: '',
        type: ''
      }
    };
  },

  components: {
    dartSlide
  },
  watch: {
    editVisible(val) {
      this.init()
    },
    visible(val) {
      setTimeout(() => {
        this.$emit('update:editVisible', val);
      }, 300);
    }
  },
  created() {
    this.init();

  },
  computed: {},

  methods: {
    init() {
      let _self = this;
      this.$nextTick(() => {
        _self.visible = _self.editVisible;
      })
    }
  }
}
</script>
<style >
.form-box {
  padding: 16px;
}
.el-form--label-top .el-form-item__label {
  padding-bottom: 0px;
}
</style>