<template>
  <div class="page-list">
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>

          <dart-search-item :is-button="true"
                            :colElementNum='3'>
            <div class="g-flex">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>

            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :colElementNum='3'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>

            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :colElementNum='1'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>

            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :colElementNum='2'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>

            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
           <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :colElementNum='1'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>

            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div>
      <dart-search ref="searchForm1"
                   class="search"
                   :formSpan='24'
                   label-position="right"
                   :searchOperation='false'
                   :model="search"
                   :fontWidth="1">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="开始日期："
                            prop="company">
            <el-input v-model="search.company"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <dart-search-item label="结束日期："
                            prop="mobile">
            <el-input v-model="search.mobile"
                      placeholder=""></el-input>
          </dart-search-item>

          <template v-if="collapse">
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
            <dart-search-item label="结束日期："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        placeholder=""></el-input>
            </dart-search-item>
          </template>

          <dart-search-item :is-button="true"
                            :colElementNum='collapse? 1 :2'>
            <div class="g-flex g-flex-end">
              <el-button>重置</el-button>
              <el-button type="primary">查询</el-button>
              <el-button type="text"
                         @click="collapse=!collapse"><span v-if="collapse">收起</span><span v-if="!collapse">展开</span></el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
export default {

  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {

  },
  data() {
    return {
      collapse: false,
      search: {
        company: '',
        mobile: '',
        loginName: '',
        pageNum: 1,
        pageSize: 10,
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-list {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
}
.page-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.page-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
