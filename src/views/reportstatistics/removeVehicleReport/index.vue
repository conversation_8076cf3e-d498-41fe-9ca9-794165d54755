<template>
  <ePage>
    <!-- 搜索栏 -->
    <dart-search
      slot="report-search"
      ref="searchForm"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <template v-for="item in formProperties">
          <dart-search-item
            :key="item.fieldKey"
            :label="item.fieldLabel"
            :prop="item.fieldKey"
          >
            <template v-if="item.element != 'custom'">
              <searchField
                :fieldProps="item.fieldProps"
                :fieldOptions="item"
                :ref="item.ref"
                v-model="search[item.fieldKey]"
              ></searchField>
            </template>
          </dart-search-item>
        </template>

        <dart-search-item :is-button="true" :colElementNum="3">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </ePage>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import { datePickerOptions } from '@/components/schemaQuery/tool'
var moment = require('moment')
export default {
  data() {
    return {
      loading: false,
      rules: {
        recoverStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        recoverEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      search: {
        name: 'removeVehicleReport', // 报表名称
        recoverStartTime: '',
        recoverEndTime: ''
      },
      formProperties: {
        recoverStartTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '开始日期',
          fieldKey: 'recoverStartTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        recoverEndTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '结束日期',
          fieldKey: 'recoverEndTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            defaultTime: '23:59:59',
            pickerOptions: datePickerOptions
          }
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {},
  created() {},
  methods: {
    onValid() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      return true
    },
    formatParams() {
      let params = JSON.parse(JSON.stringify(this.search))
      return params
    },
    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = this.formatParams()
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
