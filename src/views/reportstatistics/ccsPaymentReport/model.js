
export const reportListFn = (state) => {
  // 初始化日期范围状态
  if (!state.dateRangeStatus) {
    state.dateRangeStatus = {
      selecting: false,
      startTime: null,
      endTime: null
    }
  }

  return [
    {
      id: 1,
      name: 'Iff8080810193b8dbb8dbc26201963814573c22b8',
      title: '次次顺新增欠款用户明细报表 (每月1日)',
      ref: 'ccsNewArrearsUserDetailReportRef',
      rules: {
        cupMonth: [{ required: true, message: '请选择统计月份', trigger: 'change' }]
      },
      formConfig: [
        {
          type: 'datePicker', 
          customType: 'month',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          field: 'cupMonth',
          label: '统计月份',
          default: '',
          placeholder: '请选择统计月份'
        }
      ]
    },
    {
      id: 2,
      name: 'Iff808081019613471347a0530196483641f87fb5',
      title: '次次顺催款函通知明细报表 (每月25日)',
      ref: 'ccsDunningNoticeDetailReportRef',
      rules: {
        cupMonth: [{ required: true, message: '请选择统计月份', trigger: 'change' }]
      },
      formConfig: [
        {
          type: 'datePicker', 
          customType: 'month',
          valueFormat: 'yyyy-MM',
          format: 'yyyy-MM',
          field: 'cupMonth',
          label: '统计月份',
          default: '',
          placeholder: '请选择统计月份'
        }
      ]
    },
    {
      id: 3,
      name: 'ccsCollectionStatisticsReport',
      title: '次次顺追缴统计报表',
      ref: 'ccsCollectionStatisticsReportRef',
      rules: {
        statDateRange: [{ required: true, message: '请选择统计日期', trigger: 'change' }],
        reportType: [{ required: true, message: '请选择报表类型', trigger: 'change' }]
      },
      btnSpan: 8,
      formConfig: [
        {
          type: 'dateRange',
          customType: 'daterange',
          field: 'statDateRange',
          valueFormat: 'yyyy-MM-dd',
          keys: ['cupStartDate', 'cupEndDate'],
          label: '统计日期',
          default: [],
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期',
          pickerOptions: {
            disabledDate(time) {
              // 如果已经选择了开始日期
              if (state.dateRangeStatus.selecting && state.dateRangeStatus.startTime) {
                const startDate = new Date(state.dateRangeStatus.startTime);

                // 计算开始日期前后三个月的时间范围
                const threeMonthsBefore = new Date(startDate);
                threeMonthsBefore.setMonth(threeMonthsBefore.getMonth() - 3);

                const threeMonthsAfter = new Date(startDate);
                threeMonthsAfter.setMonth(threeMonthsAfter.getMonth() + 3);

                // 禁用超出三个月范围的日期（前后均限制）
                if (time.getTime() < threeMonthsBefore.getTime() || time.getTime() > threeMonthsAfter.getTime()) {
                  return true;
                }
              }

              // 默认不禁用任何日期
              return false;
            },
            // 处理日期选择事件
            onPick({ maxDate, minDate }) {
              // 选择了第一个日期
              if (minDate && !maxDate) {
                state.dateRangeStatus.selecting = true;
                state.dateRangeStatus.startTime = minDate.getTime();
                state.dateRangeStatus.endTime = null;
              }
              // 日期范围选择完成
              else if (minDate && maxDate) {
                state.dateRangeStatus.selecting = false;
                state.dateRangeStatus.startTime = minDate.getTime();
                state.dateRangeStatus.endTime = maxDate.getTime();

                // 验证日期范围是否合理（不超过三个月）
                const startDate = new Date(minDate);
                const threeMonthsBefore = new Date(startDate);
                threeMonthsBefore.setMonth(threeMonthsBefore.getMonth() - 3);
                const threeMonthsAfter = new Date(startDate);
                threeMonthsAfter.setMonth(threeMonthsAfter.getMonth() + 3);

                if (maxDate.getTime() < threeMonthsBefore.getTime() || maxDate.getTime() > threeMonthsAfter.getTime()) {
                  console.warn('日期范围超过三个月');
                }
              }
              // 取消选择
              else {
                state.dateRangeStatus.selecting = false;
                state.dateRangeStatus.startTime = null;
                state.dateRangeStatus.endTime = null;
              }
            }
          }
        },
        {
          type: 'select',
          field: 'reportType',
          label: '报表类型',
          default: '',
          options: state.reportTypeOptions || [],
          placeholder: '请选择报表类型',
          callBack: (val) => {
            // 当选择"次次顺扣款失败情况统计表(全量)"时，动态修改验证规则
            state.$nextTick(() => {
              const reportRef = state.$refs && state.$refs.ccsCollectionStatisticsReportRef;
              if (reportRef && reportRef[0] && reportRef[0].updateRules) {
                const formComponent = reportRef[0];

                if (val === 'Iff8080810193b8dbb8dbc262019638a0c9d62666') {
                  // 次次顺扣款失败情况统计表(全量) - 统计日期非必填
                  formComponent.updateRules({
                    statDateRange: []
                  });
                } else {
                  // 其他报表类型 - 统计日期必填
                  formComponent.updateRules({
                    statDateRange: [{ required: true, message: '请选择统计日期', trigger: 'change' }]
                  });
                }
              }
            });
          }
        },
        {
          type: 'select',
          field: 'cupCategory',
          label: '车型',
          default: '',
          options: state.vehicleTypeOptions || [],
          placeholder: '全部'
        },
        {
          type: 'select',
          field: 'cupBankId',
          label: '合作机构',
          default: '',
          options: state.partnerOrgOptions || [],
          placeholder: '全部'
        }
      ]
    }
  ]
} 