<template>
  <div class="user">
    <reportForms v-for="item in reportList" :key="item.id" :formConfig="item.formConfig" :formTitle="item.title"
      :name="item.name" :rules="item.rules" :ref="item.ref" @onSearchHandle="onSearchHandle"
      :btnSpan="item.btnSpan || 6" :isDownload="item.isDownload" :stampConfig="item.stampConfig"></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import { reportListFn } from './model'

export default {
  components: {
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      dateRangeStatus: {
        selecting: false,
        startTime: null,
        endTime: null
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      },
      tableHeight: 0,
      timeField: ['statDateRange'],
      // Options from ccsReport/index.vue
      reportTypeOptions: [
        {
          value: 'Iff8080810193b8dbb8dbc262019638a7ca3426a8',
          label: '次次顺完成补缴明细表',
        },
        {
          value: 'Iff8080810193b8dbb8dbc262019638a0c9d62666',
          label: '次次顺扣款失败情况统计表(全量)',
        },
        {
          value: 'Iff8080810193b8dbb8dbc2620196387ad97d25b2',
          label: '次次顺扣款失败情况统计表(欠款金额大于300元)',
        },
        {
          value: 'Iff8080810193b8dbb8dbc2620196385ff9b52536',
          label: '次次顺扣款失败补缴情况统计表',
        },
      ],
      vehicleTypeOptions: [
        { value: '全部|', label: '全部' },
        { value: '客车|0', label: '客车' },
        { value: '货车|1', label: '货车' },
      ],
      partnerOrgOptions: [
        {
          value: '全部|',
          label: '全部',
        },
        {
          value: '银联|CUP',
          label: '银联',
        },
        {
          value: 'S_WECHAT',
          label: '微信车主平台'
        },
        {
          value: 'C_WECHAT',
          label: '微信指定卡'
        },
      ]

    }
  },
  computed: {
    reportList() {
      return reportListFn(this)
    }
  },
  methods: {
    onSearchHandle(formData, type, fileTitle, apiMethod) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      if(params.name === 'ccsCollectionStatisticsReport'){
        params.name = params.reportType
        delete params.reportType
      }
      // 额外添加的前置校验
      if (this.beforeSearchHandle && !this.beforeSearchHandle(formData)) return
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      if (type == 'search') {
        this.sendReportRequest(params)
      } else {
        this.stampDownload(params, fileTitle, apiMethod)
      }
    },
  },
  created() {}
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;

  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }

  .list {
    width: 100%;
    text-align: center;

    img {
      width: 50%;
    }
  }

  ::v-deep .el-form-item__label {
    width: 100px !important;
  }

  ::v-deep .dart-search-wrapper .el-form-item__content {
    width: calc(100% - 100px);
  }

  ::v-deep .el-range-separator {
    width: auto;
    padding: 0 5px;
  }
}
</style>
