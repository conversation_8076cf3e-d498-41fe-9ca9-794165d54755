<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      :rules="rules"
      @onReSetHandle="onReSetHandle"
    >
      <el-button size="mini" type="primary" slot="btn" @click="handleTask"
        >确认执行</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        showIndex
        tableHeight="350"
        :hasPagination="false"
      >
        <!-- 操作 -->
        <!-- <template slot="action" slot-scope="{ scope }">
          <el-button
            type="text"
            size="mini"
            @click="handelRow(scope, 'edit')"
            >查看事件</el-button
          >
        </template> -->
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { selectBillTask, reHandleBill } from '@/api/equipment'
import { getAgencyOptApi } from '../components/service'
var moment = require('moment')

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: selectBillTask,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageNum',
      pageNum:'',
      pageSize:'',
      dataKey: 'data',
      agencyOpt: [],
      rules: {
        billDay: [
          { required: true, message: '请输入实收账单日期', trigger: 'change' }
        ],
        bankId: [
          { required: true, message: '请选择合作机构', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    listColoumns() {
      return [
        {
          prop: 'billDay',
          label: '实收账单日期'
        },
        {
          prop: 'bankName',
          label: '合作机构'
        },
        {
          prop: 'status',
          label: '执行状态'
        },
        {
          prop: 'operator',
          label: '操作者'
        },
        {
          prop: 'completeDateTime',
          label: '执行完成时间'
        }
      ]
    },
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'billDay',
          placeholder: '请输入实收账单日期',
          label: '实收账单日期',
          default:'',
          valueFormat: 'yyyyMMdd',
          // pickerOptions: state.pickerOptions,
        },
        {
          type: 'select',
          field: 'bankId',
          placeholder: '请输入合作机构',
          label: '合作机构',
          default: '',
          props:{
            filterable:true
          },
          options: this.agencyOpt
        }
      ]
    }
  },
  methods: {
    async getAgency() {
      let { data } = await getAgencyOptApi()
      this.agencyOpt = data.map(item => {
        return {
          ...item,
          label: item.channelName,
          value: `${item.orgId}`
        }
      })
    },
        // 搜索框表单操作
    onSearchHandle (formData) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      this.currentFormData = params
      this.getTableData({},(res)=>{
        this.tableData = res.data
      })
    },
    async handleTask() {
      let params = {
        ...this.$refs.SearchForm.search
      }
      let { billDay,bankId } = params
      if(!billDay || !bankId){
        this.$message.error('请填写日期和合作机构')
        return
      }
      let res = await reHandleBill(params)
      if(res.code == 200){
        this.$message.success('执行成功')
      }
      console.log(res)
    }
  },
  created() {
    this.getAgency()
    // this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .table {
    margin-top: 0;
    padding-top: 0;
  }
  ::v-deep .fontWidth {
    display: flex;
    label {
      width: auto !important;
      margin-left: 18px;
    }
    .el-col-24 {
      padding: 5px !important;
    }
    div {
      width: auto;
    }
    .el-form-item {
      margin-bottom: 0;
    }
  }
  ::v-deep .btn-wrapper {
    .el-button:first-child {
      // display: none;
    }
    .el-button:nth-child(2) {
      display: none;
    }
  }
}
</style>