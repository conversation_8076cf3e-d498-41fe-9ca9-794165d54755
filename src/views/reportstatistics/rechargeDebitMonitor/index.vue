<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:充值扣款监控报表页
  * @author:zhangys
  * @date:2023/05/18 13:18:20
-->
<template>
  <ePage style="padding:20px">
    <div slot="report-search" style="background-color: #fff;">
      <el-tabs v-model="activeIndex" type="border-card">
        <el-tab-pane
          label="分对分-合作机构返回扣款时间清算报表"
          name="divisionToDivision"
        >
          <divisionToDivision></divisionToDivision>
        </el-tab-pane>

        <!-- <el-tab-pane label="总对总-银行返回扣款时间清算报表"
                     name="totalToTotal">

        </el-tab-pane> -->
      </el-tabs>
    </div>
    <div class="wrap" slot="report-search">
      <div class="wrap-title" style="margin-bottom: 0;">重新扫描历史实收账单</div>
      <HistoryList ref="HistoryList"></HistoryList>
    </div>
  </ePage>
</template>

<script>
import { queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import totalToTotal from './totalToTotal'
import divisionToDivision from './divisionToDivision'
import HistoryList from './history-list.vue'

export default {
  data() {
    return {
      activeIndex: 'divisionToDivision',
      options: []
    }
  },

  components: {
    ePage,
    totalToTotal,
    divisionToDivision,
    HistoryList
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg(data => {
      _self.options = data || []
    })
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.report-search .el-tabs--border-card {
  box-shadow: none !important;
}
.report-search .el-tabs__content {
  padding: 0 !important;
}

.wrap {
  background: #fff;
  padding: 10px;
  border-radius: 8px;
  margin-top: 10px;
  .wrap-title {
    margin: 0 0 20px;
    padding: 5px 10px 10px 10px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
  }
}
::v-deep .el-upload__tip {
  margin-top: 0;
  line-height: 20px;
}
.bottom-btn {
  margin-bottom: 5px;
}
</style>
