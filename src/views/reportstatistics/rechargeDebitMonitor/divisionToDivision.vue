<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:分对分
  * @author:zhangys
  * @date:2023/05/18 13:21:29
-->
<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item :key="item.fieldKey" :label="item.fieldLabel" :prop="item.fieldKey">
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="1">
        <div class="g-flex g-flex-end">
          <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import {
  queryReport,
  queryDeptOrg,
  getAgencyOptApi
} from '../components/service'
import ePage from '../components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'
let cooperativeAgency = [
  { value: '中视|ZSINFO', label: '中视' },
  { value: '山东信联|SDXL', label: '山东信联' },
  { value: '中国邮储银行|PSBC', label: '中国邮储银行' }
]
export default {
  data() {
    return {
      loading: false,
      rules: {
        startDateDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endDateDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        bankIdDdl: [
          { required: true, message: '请选择合作机构', trigger: 'change' }
        ]
      },
      agencyOpt: [],
      search: {
        name: 'I8aad8da70186dc34dc34ab0c0187b145e41779e4', // 报表名称
        startDateDate: '',
        endDateDate: '',
        bankIdDdl: ''
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {
    formProperties() {
      return {
        startDateDate: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'startDateDate',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        endDateDate: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'endDateDate',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        bankIdDdl: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: this.agencyOpt,
            filterable:true
          },
          fieldLabel: '合作机构',
          fieldKey: 'bankIdDdl'
        }
      }
    }
  },
  created() {
    this.getAgency()
  },
  methods: {
    onValid() {
      if (moment(this.search.startDateDate).isAfter(this.search.endDateDate)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }

      return true
    },

    onSearchHandle() {
      console.log(this.$refs['cascaderNodes'])
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.search))
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    },
    async getAgency() {
      let { data } = await getAgencyOptApi()
      this.agencyOpt = data.map(item => {
        return {
          ...item,
          label: item.channelName,
          value: `${item.channelName}|${item.orgId}`
        }
      })
    }
  }
}
</script>
<style lang="sass"></style>
