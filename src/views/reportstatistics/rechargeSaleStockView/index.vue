<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="开始日期"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="endTime">
          <el-date-picker v-model="search.endTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="支付方式"
                          prop="payType">
          <el-select v-model="search.payType"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in payTypeOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="部门名称"
                          prop="searchitem">
          <el-cascader v-model="search.searchitem"
                       class="form-select"
                       ref="deptNodes"
                       :options="deptOptions"
                       :expand-trigger="'click'"
                       :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>
        <dart-search-item label="部门属性"
                          prop="branchType">
          <el-select v-model="search.branchType"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in branchTypeOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="操作员名称"
                          prop="OPERATOR_NO">
          <el-autocomplete v-model="search.OPERATOR_NO_label"
                           size="mini"
                           clearable
                           :fetch-suggestions="querySearhOperator"
                           @select="searhOperator"
                           placeholder="请输入操作员名称"></el-autocomplete>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { payTypeOptions } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      payTypeOptions,
      deptOptions: [],
      currentDept: null, // 当前选择部门节点
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        startTime: '', // 开始日期
        endTime: '', // 结束日期
        payType: '', //支付方式
        OPERATOR_DEPT: '', //网点名称
        OPERATOR_NO: '', //操作用户ID
        OPERATOR_NO_label: '', //操作用户ID
        searchitem: [],
        branchDeptNo: '',
        branchLength: null,
        branchType: '',
      },
      optiondata: [],
      tableHeight: 0,
      groupArr: [],
      operatorArr: [],
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        // endTime: [
        //   { required: true, message: '请选择结束日期', trigger: 'change' },
        // ],
        searchitem: [
          { required: true, message: '请选择统计部门', trigger: 'change' },
        ],
      },
      branchTypeOptions: [
        { value: '01', label: '自营' },
        { value: '02', label: '运营' },
        { value: '03', label: '一站式' },
        { value: '04', label: '合作' },
        { value: '05', label: '银行' },
        { value: '06', label: '线上' },
        { value: '99', label: '其他' },
      ],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.startTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.search.endTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.getgroup()
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月',
        })
        return
      }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.searchitem = null
      })
    },

    sendReportRequest() {
      try {
        this.currentDept = this.$refs.deptNodes.getCheckedNodes()[0].data
        this.changeitem(this.currentDept)
      } catch (e) {}

      this.loading = true
      let params = {
        name: 'rechargeSaleStockView',
        ...this.search,
      }

      if (this.search.branchType != '') {
        params.branchType = this.search.branchType
      }
      delete params.OPERATOR_NO_label
      delete params.searchitem
      console.log(params)
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },

    //查询操作员
    querySearhOperator(queryString, cb) {
      console.log(queryString.length)
      if (queryString == 0) {
        this.operatorArr = []
        this.search.OPERATOR_NO = ''
        let arr = this.operatorArr
        cb(arr)
        return
      }
      let data = {
        name: queryString,
      }
      request({
        url: api.operatorList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.operatorArr = res.data.map((item) => {
            return {
              value: item.name + item.workNo,
              address: item.id,
            }
          })
          let arr = this.operatorArr
          clearTimeout(this.timeout)
          this.timeout = setTimeout(() => {
            console.log(arr)
            cb(arr)
          }, 1000 * Math.random())
        })
        .catch(() => {})
    },
    searhOperator(val) {
      this.search.OPERATOR_NO = val.address
      this.search.OPERATOR_NO_label = val.value
    },
    handleChange(value) {
      if (value.length) {
        let index = value.length
        return value[index - 1].id.toString()
      } else {
        return ''
      }
    },
    changeitem(item) {
      let deptNum = item.deptNum
      switch (item.deptLevel) {
        case 1:
          this.search.branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          this.search.branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          this.search.branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          this.search.branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          this.search.branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          this.search.branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          this.search.branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    // 查询部门组织结构
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get',
      })
        .then((res) => {
          console.log(res.data)
          this.deptOptions = res.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
