<template>
  <div>
    <ePage>
      <!-- 搜索栏 -->
      <dart-search
        slot="report-search"
        ref="searchForm"
        :formSpan="24"
        :searchOperation="false"
        :fontWidth="1"
        label-position="right"
        :model="search"
        :rules="rules"
      >
        <template slot="search-form">
          <div class="search-title">网点业务量统计报表</div>
          <template v-for="item in formProperties">
            <dart-search-item
              :key="item.fieldKey"
              :label="item.fieldLabel"
              :prop="item.fieldKey"
            >
              <template v-if="item.element != 'custom'">
                <searchField
                  :fieldProps="item.fieldProps"
                  :fieldOptions="item"
                  :ref="item.ref"
                  v-model="search[item.fieldKey]"
                ></searchField>
              </template>
            </dart-search-item>
          </template>

          <dart-search-item :is-button="true" :colElementNum="3">
            <div class="g-flex g-flex-end">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                >搜索</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>

      <userReport slot="report-search"></userReport>
      <operatorReport slot="report-search"></operatorReport>
      <!-- <unitReport slot="report-search"></unitReport> -->
    </ePage>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema,
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import userReport from './report.vue'
import operatorReport from './operator-report.vue'
import unitReport from './unit-report.vue'
import { datePickerOptions } from '@/components/schemaQuery/tool'
var moment = require('moment')
let branchTypeOptions = [
  { value: '01', label: '自营' },
  { value: '02', label: '运营' },
  { value: '03', label: '一站式' },
  { value: '04', label: '合作' },
  { value: '05', label: '银行' },
  { value: '06', label: '线上' },
  { value: '99', label: '其他' },
]

let branchIdOptions = [
  { value: '1', label: '仅当前部门' },
  { value: '2', label: '当前部门及下属' }
]

export default {
  data() {
    return {
      loading: false,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
        deptID: [
          { required: true, message: '请选择统计部门', trigger: 'change' }
        ],
        radius: [
          { required: true, message: '请选择查询范围', trigger: 'change' }
        ]
      },
      search: {
        name: 'oneBusinessReport', // 报表名称
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '', //部门id
        branchDeptNo: '', //部门编号
        branchLength: '', //部门编号长度
        branchType: '', //部门属性
        deptID: '',
      },
      formProperties: {
        startTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'startTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
        endTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
            defaultTime:"23:59:59"
          },
        },
        deptID: {
          ...cascaderSchema,
          ref: 'cascaderNodes',
          placeholder: '请选择',
          fieldLabel: '部门名称',
          fieldKey: 'deptID',
        },
        radius: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: branchIdOptions
          },
          fieldLabel: '查询范围',
          fieldKey: 'radius'
        },
        branchType: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: branchTypeOptions,
          },
          fieldLabel: '部门属性',
          fieldKey: 'branchType',
        },
      },
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
    userReport,
    operatorReport,
    unitReport
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg((data) => {
      _self.formProperties.deptID.fieldProps.options = data || []
    })
  },
  methods: {
    onValid() {
      let { startTime, endTime, radius } = this.search
      if (moment(startTime).isAfter(endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      let typeObj = {
        1: {
          months: 11,
          text: '查询仅当前部门统计日期时间段不能不能大于一年'
        },
        2: {
          months: 0,
          text: '查询当前部门及下属统计日期时间段大于一个月'
        }
      }
      if (
        moment(endTime).diff(moment(startTime), 'months') >
        typeObj[radius].months
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: typeObj[radius].text
        })
        return
      }
      return true
    },
    getDeptNo(item) {
      let deptNum = item.deptNum
      let branchDeptNo = ''
      switch (item.deptLevel) {
        case 1:
          branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.branchDeptNo = branchDeptNo
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    formatParams() {
      let cascaderData = {}
      let { radius } = this.search

      try {
        cascaderData =
          this.$refs['cascaderNodes'][0].$children[0].getCheckedNodes()[0].data
        this.getDeptNo(cascaderData)
      } catch (e) {
        console.log(e)
      }
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      if (radius == 1) {
        params.branchId = params.OPERATOR_DEPT
        delete params.branchDeptNo
        delete params.branchLength
        delete params.branchType
        delete params.radius
      } else {
        delete params.radius
      }
      return params
    },
    onSearchHandle() {
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          if (!this.onValid()) return
          let params = this.formatParams()
          // console.log(params, 'paramsparamsparams')
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm'].resetForm()
      })
    },
  },
}
</script>

<style lang="scss">
.search-title {
  margin: 4px 0 10px 20px;
}
</style>
