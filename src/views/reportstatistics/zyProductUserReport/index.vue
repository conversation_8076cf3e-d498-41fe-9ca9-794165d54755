<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:捷通自营产品用户量统计
  * @author:zhang<PERSON>
  * @date:2023/02/02 09:23:37
-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">

        <dart-search-item label="开始日期"
                          prop="zyStartTime">
          <el-date-picker v-model="search.zyStartTime"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="zyEndTime">
          <el-date-picker v-model="search.zyEndTime"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>
  
  <script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { departmenttype } from '@/common/const/optionsData.js'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: {
        zyStartTime: '', // 开始日期
        zyEndTime: '', // 结束日期,
        name: 'zyProductUserReport',
      },
      tableHeight: 0,
      rules: {
        zyStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        zyEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },

      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.zyStartTime = moment().startOf('day').format('YYYY-MM-DD')
    this.search.zyEndTime = moment().startOf('day').format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (moment(this.search.zyStartTime).isAfter(this.search.zyEndTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.zyEndTime).diff(
          moment(this.search.zyStartTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月',
        })
        return
      }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.zyStartTime = moment().startOf('day').format('YYYY-MM-DD')
        this.search.zyEndTime = moment().startOf('day').format('YYYY-MM-DD')
      })
    },

    sendReportRequest() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.zyStartTime = moment(params.zyStartTime).format('YYYY-MM-DD')
      params.zyEndTime = moment(params.zyEndTime).format('YYYY-MM-DD')
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
  },
}
</script>
  
<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>