<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:自助二次激活申订单汇总
  * @author:zhang<PERSON>
  * @date:2023/04/10 09:52:51
-->
<template>
  <!-- 搜索栏 -->
  <dart-search slot="report-search"
               ref="searchForm"
               :formSpan="24"
               :labelTextLength="8"
               :searchOperation="false"
               :fontWidth="1"
               label-position="right"
               :model="search"
               :rules="rules">
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item :key="item.fieldKey"
                          :label="item.fieldLabel"
                          :prop="item.fieldKey">
          <template v-if="item.element != 'custom'">
            <searchField :fieldProps="item.fieldProps"
                         :fieldOptions="item"
                         :ref="item.ref"
                         v-model="search[item.fieldKey]"></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true"
                        :colElementNum="3">
        <div class="g-flex g-flex-end">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema,
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'

export default {
  data() {
    return {
      loading: false,
      rules: {
        roCreateTimeStart: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        roCreateTimeEnd: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },
      search: {
        name: 'secondActivateSummaryReport', // 报表名称
        roCreateTimeStart: '',
        roCreateTimeEnd: '',
      },
      formProperties: {
        roCreateTimeStart: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'roCreateTimeStart',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
        roCreateTimeEnd: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'roCreateTimeEnd',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
      },
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
  },

  computed: {},
  created() {},
  methods: {
    onValid() {
      if (
        moment(this.search.roCreateTimeStart).isAfter(
          this.search.roCreateTimeEnd
        )
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      return true
    },

    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate((valid) => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.search))
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm'].resetForm()
      })
    },
  },
}
</script>
<style lang="sass"></style>