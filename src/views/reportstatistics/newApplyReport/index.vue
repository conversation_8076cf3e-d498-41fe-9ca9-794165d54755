<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行报表入口
  * @author:zhang<PERSON>
  * @date:2023/03/21 14:59:18
-->
<template>
  <ePage style="padding:20px">
    <div slot="report-search" style="background-color: #fff;">
      <div class="title">订单状态统计表</div>
      <commonSearch></commonSearch>

      <!-- <el-tabs v-model="activeIndex"
               type="border-card">
       
     

        <el-tab-pane label="线上发行订单状态统计表"
                     name="rechargeDeptPayType">
        <newApplyOrderStatus></newApplyOrderStatus>
        </el-tab-pane>
        <el-tab-pane label="自助二次激活申请单明细"
                     name="activateDailyReport">
          <activateDailyReport></activateDailyReport>
        </el-tab-pane>

        <el-tab-pane label="自助二次激活申订单汇总"
                     name="activateSumReport">
          <activateSumReport></activateSumReport>
        </el-tab-pane>
        <el-tab-pane label="更换订单状态统计表"
                     name="changeStatusReport">
          <changeStatusReport></changeStatusReport>
        </el-tab-pane>

        <el-tab-pane label="补办订单状态统计表"
                     name="remakeStatusReport">
          <remakeStatusReport></remakeStatusReport>
        </el-tab-pane>
        <el-tab-pane label="线上售后业务应收统计表"
                     name="afterSaleBusinesReceiveReport">
          <afterSaleBusinesReceiveReport></afterSaleBusinesReceiveReport>
        </el-tab-pane>
      </el-tabs>  -->
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">物流订单统计报表</div>
        <expressOrderReport></expressOrderReport>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">线上发行订单应收统计表</div>
        <newApplyOrderReceive></newApplyOrderReceive>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">线上发行推广业务量统计报表--已激活</div>
        <newApplyCodeReport></newApplyCodeReport>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">线上发行推广业务量统计报表--办理中</div>
        <newApplyCodeReportSta></newApplyCodeReportSta>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">自助二次激活申请单明细</div>
        <activateDailyReport></activateDailyReport>
      </div>
    </div>
    <div slot="report-search"
         style="background-color: #fff;">
      <div>
        <div class="title">线上售后业务应收统计表</div>
        <afterSaleBusinesReceiveReport></afterSaleBusinesReceiveReport>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">产品转换数量统计表</div>
        <switchProductReport></switchProductReport>
      </div>
    </div>
    <div slot="report-search" style="background-color: #fff;">
      <div>
        <div class="title">ETC产品转换技术服务费统计表</div>
        <switchProductReportV2></switchProductReportV2>
      </div>
    </div>
  </ePage>
</template>

<script>
import { queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import expressOrderReport from './expressOrderReport'
import activateDailyReport from './activateDailyReport'
import activateSumReport from './activateSumReport'
import changeStatusReport from './changeStatusReport'
import remakeStatusReport from './remakeStatusReport'
import afterSaleBusinesReceiveReport from './afterSaleBusinesReceiveReport'
import newApplyOrderReceive from './newApplyOrderReceive'
import newApplyOrderStatus from './newApplyOrderStatus'
import newApplyCodeReport from './newApplyCodeReport.vue'
import newApplyCodeReportSta from './newApplyCodeReportSta.vue'
import commonSearch from './commonSearch'
import switchProductReport from './switchProductReport.vue'
import switchProductReportV2 from './switchProductReportV2.vue'

export default {
  data() {
    return {
      activeIndex: 'expressOrderReport',
      options: []
    }
  },

  components: {
    ePage,
    expressOrderReport,
    activateDailyReport,
    activateSumReport,
    changeStatusReport,
    remakeStatusReport,
    afterSaleBusinesReceiveReport,
    activateSumReport,
    newApplyOrderReceive,
    newApplyOrderStatus,
    commonSearch,
    newApplyCodeReport,
    switchProductReport,
    newApplyCodeReportSta,
    switchProductReportV2
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg(data => {
      _self.options = data || []
    })
  },
  methods: {}
}
</script>
<style lang="scss" scoped>
.report-search .el-tabs--border-card {
  box-shadow: none !important;
}
.report-search .el-tabs__content {
  padding: 0 !important;
}

.title {
  padding: 10px 0px 0px 20px;
  font-weight: bold;
}
</style>
