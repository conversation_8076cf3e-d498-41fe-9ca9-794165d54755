<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:物流订单统计报表
  * @author:zhang<PERSON>
  * @date:2023/03/21 14:57:14
-->
<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item :key="item.fieldKey" :label="item.fieldLabel" :prop="item.fieldKey">
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="1">
        <div class="g-flex g-flex-end">
          <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'
const businessTypeOptionsList = [
  { value: '1', label: '线上发行' },
  { value: '2', label: '自助激活' },
  { value: '3', label: '线上售后更换' },
  { value: '4', label: '线上售后补办' },
  { value: '8', label: '线上注销申请' }
]
export default {
  data() {
    return {
      loading: false,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        businessType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ]
      },
      search: {
        name: '', // 报表名称
        startTime: '',
        endTime: '',
        businessType: ''
      },
      formProperties: {
        businessType: {
          ...selectSchema,
          fieldProps: Object.assign(selectSchema.fieldProps, {
            options: businessTypeOptionsList,
          }),
          fieldLabel: '线上订单类型',
          fieldKey: 'businessType'
        },
        startTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'startTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        endTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {},
  created() {},
  methods: {
    onValid() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      return true
    },

    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = JSON.parse(JSON.stringify(this.search))
          let data = {}
          if (params.businessType == '1') {
            data.name = 'hsApplyRecordStatus' // 报表名称
            data.applyStaTime = moment(params.startTime).format('YYYY-MM-DD')
            data.applyEndTime = moment(params.endTime).format('YYYY-MM-DD')
          } else if (params.businessType == '8') {
            data.name = 'etcLogoutStatusReport' // 报表名称
            data.logoutApplyStartDate = moment(params.startTime).format(
              'YYYY-MM-DD'
            )
            data.logoutApplyEndDate = moment(params.endTime).format(
              'YYYY-MM-DD'
            )
          }
          if (params.businessType == '2') {
            data.name = 'secondActivateSummaryReport' // 报表名称
            data.roCreateTimeStart = params.startTime
            data.roCreateTimeEnd = params.endTime
          }
          if (params.businessType == '3') {
            data.name = 'hsExchangeStatus' // 报表名称
            data.orderStartTime = moment(params.startTime).format('YYYY-MM-DD')
            data.orderEndTime = moment(params.endTime).format('YYYY-MM-DD')
          }
          if (params.businessType == '4') {
            data.name = 'hsReissueStatus' // 报表名称
            data.orderStartTime = moment(params.startTime).format('YYYY-MM-DD')
            data.orderEndTime = moment(params.endTime).format('YYYY-MM-DD')
          }
          queryReport(data)
        } else {
          return false
        }
      })
    },

    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
