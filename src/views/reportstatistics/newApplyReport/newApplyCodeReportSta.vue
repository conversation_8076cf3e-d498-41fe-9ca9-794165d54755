
<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item
          :key="item.fieldKey"
          :label="item.fieldLabel"
          :prop="item.fieldKey"
        >
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="1">
        <div class="g-flex g-flex-end">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import { datePickerSchema, cascaderSchema } from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'
import { getDeptInfo } from '@/api/user'

export default {
  data() {
    return {
      loading: false,
      deptId: '',
      rules: {
        tgStaTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        tgEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        deptID: [{ required: true, message: '请选择部门', trigger: 'change' }]
      },
      search: {
        name: 'onlinePromoterHandle', // 报表名称 办理中
        tgStaTime: '',
        tgEndTime: ''
      },
      formProperties: {
        tgStaTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'tgStaTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        tgEndTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'tgEndTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        deptID: {
          ...cascaderSchema,
          ref: 'cascaderNodes',
          placeholder: '请选择',
          fieldLabel: '部门名称',
          fieldKey: 'deptID'
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg(data => {
      _self.formProperties.deptID.fieldProps.options = data || []
      _self.getDept()
    })
  },
  methods: {
    async getDept() {
      let { data } = await getDeptInfo()
      let { user } = data
      this.userInfo = user
      this.$set(this.search, 'deptID', user.deptId)
    },
    onValid() {
      // if (!this.deptId) {
      //   this.$msgbox({
      //     title: '提示',
      //     showClose: true,
      //     type: 'error',
      //     customClass: 'my_msgBox singelBtn',
      //     dangerouslyUseHTMLString: true,
      //     message: '未获取到用户部门，请刷新重试'
      //   })
      //   return
      // }
      if (moment(this.search.tgStaTime).isAfter(this.search.tgEndTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      return true
    },
    getDeptNo(item) {
      let deptNum = item.deptNum
      let branchDeptNo = ''
      switch (item.deptLevel) {
        case 1:
          branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.branchDeptNo = branchDeptNo
      // this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    formatParams() {
      let cascaderData = {}
      try {
        cascaderData = this.$refs[
          'cascaderNodes'
        ][0].$children[0].getCheckedNodes()[0].data
        this.getDeptNo(cascaderData)
      } catch (e) {}
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },
    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = this.formatParams()
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
