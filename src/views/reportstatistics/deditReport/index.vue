<template>
  <div class="user">
    <!-- 次次顺记账卡注销量统计报表 -->
    <reportForms
      :formConfig="formConfig"
      :formTitle="'次次顺记账卡注销量统计报表'"
      name="silkyAccountCancelDetailReport"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="16"
    ></reportForms>

    <!-- 次次顺产品注销量报表 -->
    <reportForms
      :formConfig="formConfigDetail"
      :formTitle="'次次顺产品注销量报表'"
      name="silkyCancelDetailReport"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
    ></reportForms>
    <!-- 次次顺注销违约金统计表 -->
    <reportForms
      :formConfig="formConfigAmount"
      :formTitle="'次次顺注销违约金统计表'"
      name="silkyCancelAmountReport"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="8"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'

import upmsRequest from '@/utils/upmsRequest'
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      typeList: {
        payOrgIds: [] //机构字典
      },
      deptOptions: [],
      tableHeight: 0,
      rules: {
        stop_start: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        stop_end: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        ddpCarTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ]
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'stop_start',
          label: '开始日期',
          placeholder: '开始日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'stop_end',
          label: '结束日期',
          placeholder: '结束日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'select',
          field: 'sign_code_str',
          label: '签约机构：',
          placeholder: '签约机构：',
          options: this.typeList.payOrgIds,
          default: ''
        },
        {
          type: 'select',
          field: 'pay_org_str',
          label: '代扣机构：',
          placeholder: '代扣机构：',
          options: this.typeList.payOrgIds,
          default: ''
        }
      ]
    },
    formConfigDetail() {
      return [
        {
          type: 'datePicker',
          field: 'stop_start',
          label: '开始日期',
          placeholder: '开始日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'stop_end',
          label: '结束日期',
          placeholder: '结束日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'cascader',
          field: 'sign_code_str',
          label: '发行机构：',
          placeholder: '发行机构：',
          options: this.deptOptions,
          props: {
            props: {
              checkStrictly: true,
              value: 'name',
              label: 'name',
              emitPath: false
            }
          },
          default: ''
        }
      ]
    },
    formConfigAmount() {
      return [
        {
          type: 'datePicker',
          field: 'stop_start',
          label: '开始日期',
          placeholder: '开始日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'stop_end',
          label: '结束日期',
          placeholder: '结束日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        }
      ]
    }
  },
  created() {
    //  moment()
    //         .startOf('day')
    //         .format('YYYY-MM-DD')
    this.getTypeList()
    this.getgroup()
  },
  methods: {
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    },
    getTypeList() {
      this.$store.dispatch('refund/getTypeList').then(res => {
        console.log('字典列表', res)
        let typeList = res

        Object.keys(typeList).forEach(key => {
          if (this.typeList.hasOwnProperty(key)) {
            this.typeList[key] = typeList[key]
          }
        })
        this.typeList.payOrgIds = this.typeList.payOrgIds.map(item => {
          return {
            ...item,
            label: item.fieldNameDisplay,
            value: item.fieldNameDisplay
          }
        })
      })
    },
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      }).then(res => {
        this.deptOptions = res.data
      })
    },
    beforeSearchHandle(data) {
      if (moment(data.stop_start).isAfter(data.stop_end)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return false
      }
      if (moment(data.stop_end).diff(moment(data.stop_start), 'months') > 2) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月'
        })
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
