<template>
  <div class="user">
    <div class="sction">
      <div class="title">网址销售充值报表——销售时间</div>
      <dart-search
        ref="searchForm1"
        label-position="right"
        :formSpan="24"
        :gutter="20"
        :model="search"
        :rules="rules"
      >
        <template slot="search-form">
          <dart-search-item label="统计销售开始日期" prop="startTime">
            <el-date-picker
              v-model="search.startTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="统计销售结束日期" prop="endTime">
            <el-date-picker
              v-model="search.endTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="部门名称" prop="searchitem">
            <el-cascader
              v-model="search.searchitem"
              class="form-select"
              ref="deptNodes"
              filterable
              clearable
              :options="deptOptions"
              :expand-trigger="'click'"
              :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
                emitPath: false
              }"
            />
          </dart-search-item>
          <dart-search-item label="部门属性" prop="branchType">
            <el-select
              v-model="search.branchType"
              placeholder="请选择"
              clearable
              collapse-tags
            >
              <el-option
                v-for="item in branchTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="8">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('search')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('search')"
              >重置</el-button
            >
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="sction">
      <div class="title">网址销售充值报表——支付时间</div>
      <dart-search
        ref="searchForm2"
        label-position="right"
        :formSpan="24"
        :gutter="20"
        :model="search"
        :rules="rules"
      >
        <template slot="search-form">
          <dart-search-item label="统计支付开始日期" prop="startTime">
            <el-date-picker
              v-model="paySearch.startTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="统计支付结束日期" prop="endTime">
            <el-date-picker
              v-model="paySearch.endTime"
              type="datetime"
              :clearable="false"
              placeholder="选择日期"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="部门名称" prop="searchitem">
            <el-cascader
              v-model="paySearch.searchitem"
              class="form-select"
              ref="payDeptNodes"
              filterable
              clearable
              :options="deptOptions"
              :expand-trigger="'click'"
              :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
                emitPath: false
              }"
            />
          </dart-search-item>
          <dart-search-item label="部门属性" prop="branchType">
            <el-select
              v-model="paySearch.branchType"
              placeholder="请选择"
              clearable
              collapse-tags
            >
              <el-option
                v-for="item in branchTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true" :span="8">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('paySearch')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('paySearch')"
              >重置</el-button
            >
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem
  },
  created() {
    this.search.startTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.search.endTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.paySearch.startTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.paySearch.endTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.getgroup()
  },
  data() {
    return {
      departmenttype,
      groupArr: [],
      deptOptions: [], // 部门列表
      currentDept: null, // 当前选择部门节点
      search: {
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '',
        searchitem: '',
        // name: '',
        branchDeptNo: '',
        branchLength: '',
        branchType: ''
      },
      paySearch: {
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '',
        searchitem: '',
        // name: '',
        branchDeptNo: '',
        branchLength: '',
        branchType: ''
      },
      tableHeight: 0,
      optiondata: [],
      rules: {
        startTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择统计结束日期', trigger: 'change' }
        ],
        searchitem: [
          { required: true, message: '请选择统计部门', trigger: 'change' }
        ]
      },
      options: [],
      branchTypeOptions: [
        { value: '01', label: '自营' },
        { value: '02', label: '运营' },
        { value: '03', label: '一站式' },
        { value: '04', label: '合作' },
        { value: '05', label: '银行' },
        { value: '06', label: '线上' },
        { value: '99', label: '其他' }
      ],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  methods: {
    //兼容性改造 2023年2月17日11:10:08 dwz
    onSearchHandle(dataType) {
      if (moment(this[dataType].startTime).isAfter(this[dataType].endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this[dataType].endTime).diff(
          moment(this[dataType].startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      // console.log(this.search)
      this.sendReportRequest(dataType)
    },
    sendReportRequest(dataType) {
      let rootFormStr, rootDepStr, reportName
      if (dataType == 'search') {
        rootFormStr = 'searchForm1'
        rootDepStr = 'deptNodes'
        reportName = 'rechargeDept'
      } else {
        rootFormStr = 'searchForm2'
        rootDepStr = 'payDeptNodes'
        reportName = 'rechargeDeptPayType'
      }
      this.$refs[rootFormStr].$children[0].validate(valid => {
        if (valid) {
          try {
            this.currentDept = this.$refs[rootDepStr].getCheckedNodes()[0].data
            console.log(this.currentDept, '----this.currentDept+++++')
            this.changeitem(this.currentDept, dataType)
          } catch (e) {}
          let params = {
            name: reportName,
            startTime: moment(this[dataType].startTime).format(
              'YYYY-MM-DD HH:mm:ss'
            ),
            endTime: moment(this[dataType].endTime).format(
              'YYYY-MM-DD HH:mm:ss'
            ),
            OPERATOR_DEPT: this[dataType].OPERATOR_DEPT, //部门编号
            branchDeptNo: this[dataType].branchDeptNo,
            branchLength: this[dataType].branchLength
          }
          if (this[dataType].branchType != '') {
            params.branchType = this[dataType].branchType
          }
          console.log(params)
          this.$store
            .dispatch('report/report', params)
            .then(res => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle(dataType) {
      let rootFormStr
      if (dataType == 'search') {
        rootFormStr = 'searchForm1'
      } else {
        rootFormStr = 'searchForm2'
      }
      this.$refs[rootFormStr].$children[0].resetFields()
      this[dataType].searchitem = null
    },
    gettime(data) {
      const value = moment(data)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss')
      return value
    },
    //改造兼容多查询 2023/2/17 dwz
    changeitem(item, dataType) {
      let deptNum = item.deptNum
      switch (item.deptLevel) {
        case 1:
          this[dataType].branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          this[dataType].branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          this[dataType].branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          this[dataType].branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          this[dataType].branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          this[dataType].branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          this[dataType].branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this[dataType].OPERATOR_DEPT = item.id.toString()
      this[dataType].branchLength = this[
        dataType
      ].branchDeptNo.length.toString()
    },
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      })
        .then(res => {
          this.deptOptions = res.data
        })
        .catch(error => {
          console.log(error)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  .title {
    background: #ffffff;
    padding: 10px;
    font-size: 16px;
  }
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
  width: 140px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  width: calc(100% - 140px);
}
</style>
