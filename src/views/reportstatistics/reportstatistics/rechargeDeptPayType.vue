<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item
          :key="item.fieldKey"
          :label="item.fieldLabel"
          :prop="item.fieldKey"
        >
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="2">
        <div class="g-flex g-flex-end">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'

let branchTypeOptions = [
  { value: '01', label: '自营' },
  { value: '02', label: '运营' },
  { value: '03', label: '一站式' },
  { value: '04', label: '合作' },
  { value: '05', label: '银行' },
  { value: '06', label: '线上' },
  { value: '99', label: '其他' }
]
export default {
  data() {
    return {
      loading: false,
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ],
        deptID: [
          { required: true, message: '请选择统计部门', trigger: 'change' }
        ]
      },
      search: {
        name: 'rechargeDeptPayType', // 报表名称
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '', //部门id
        branchDeptNo: '', //部门编号
        branchLength: '', //部门编号长度
        branchType: '', //部门属性
        deptID: ''
      },
      formProperties: {
        startTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计支付开始日期',
          fieldKey: 'startTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        endTime: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '统计支付结束日期',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        deptID: {
          ...cascaderSchema,
          ref: 'cascaderNodes',
          placeholder: '请选择',
          fieldLabel: '部门名称',
          fieldKey: 'deptID'
        },
        branchType: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: branchTypeOptions
          },
          fieldLabel: '部门属性',
          fieldKey: 'branchType'
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg(data => {
      _self.formProperties.deptID.fieldProps.options = data || []
    })
  },
  methods: {
    onValid() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      return true
    },
    getDeptNo(item) {
      let deptNum = item.deptNum
      let branchDeptNo = ''
      switch (item.deptLevel) {
        case 1:
          branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.branchDeptNo = branchDeptNo
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    formatParams() {
      let cascaderData = {}
      try {
        cascaderData = this.$refs[
          'cascaderNodes'
        ][0].$children[0].getCheckedNodes()[0].data
        this.getDeptNo(cascaderData)
      } catch (e) {}
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },
    onSearchHandle() {
      console.log(this.$refs['cascaderNodes'])
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = this.formatParams()
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
