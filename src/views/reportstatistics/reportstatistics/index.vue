<template>
  <ePage style="padding:20px">
    <div slot="report-search" style="background-color: #fff;">
      <el-tabs v-model="activeIndex" type="border-card">
        <el-tab-pane label="网址销售充值报表——销售时间" name="rechargeDept">
          <rechargeDept></rechargeDept>
        </el-tab-pane>

        <el-tab-pane
          label="网址销售充值报表——支付时间"
          name="rechargeDeptPayType"
        >
          <rechargeDeptPayType></rechargeDeptPayType>
        </el-tab-pane>
      </el-tabs>
    </div>
  </ePage>
</template>

<script>
import { queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import rechargeDept from './rechargeDept'
import rechargeDeptPayType from './rechargeDeptPayType'
export default {
  data() {
    return {
      activeIndex: 'rechargeDept',
      options: []
    }
  },

  components: {
    ePage,
    rechargeDept,
    rechargeDeptPayType
  },

  computed: {},
  created() {
    let _self = this
    queryDeptOrg(data => {
      _self.options = data || []
    })
  },
  methods: {}
}
</script>
<style>
.report-search .el-tabs--border-card {
  box-shadow: none !important;
}
.report-search .el-tabs__content {
  padding: 0 !important;
}
</style>
