<template>
  <div class="deduction">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="统计开始日期"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="date"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="统计结束日期"
                          prop="endTime">
          <el-date-picker v-model="search.endTime"
                          type="date"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="报表类型"
                          prop="reportType">
          <el-select v-model="search.reportType"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="item in reportTypeOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="渠道方"
                          prop="channelId">
          <el-select v-model="search.channelId"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="item in channelIdOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: {
        startTime: '',
        endTime: '',
        reportType: 'hsOldSquareDedClear',
        channelId: '',
      },
      reportTypeOptions: [
        { value: 'hsOldSquareDedClear', label: '请款清分' },
        { value: 'hsOldSquareDispatch', label: '划款' },
      ],
      channelIdOptions: [
        { value: '', label: '全部' },
        { value: '33020002', label: '中国银行' },
        { value: '33020008', label: '交通部' },
      ],
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
        reportType: [
          { required: true, message: '请选择报表类型', trigger: 'change' },
        ],
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let params = {
            name: this.search.reportType,
            startVerificationDate: moment(this.search.startTime).format(
              'YYYY-MM-DD'
            ),
            endVerificationDate: moment(this.search.endTime).format(
              'YYYY-MM-DD'
            ),
            channelId: this.search.channelId,
          }
          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.deduction {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>