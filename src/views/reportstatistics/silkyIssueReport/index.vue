<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:次次顺记账卡发行量报表
  * @author:zhang<PERSON>
  * @date:2023/02/02 09:27:49
-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">

        <dart-search-item label="开始日期"
                          prop="zyStartTime">
          <el-date-picker v-model="search.zyStartTime"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="zyEndTime">
          <el-date-picker v-model="search.zyEndTime"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="签约机构"
                          prop="signOrg">
          <el-select v-model="search.signOrg"
                     placeholder="请选择签约机构"
                     clearable
                     collapse-tags>
            <el-option v-for="item in signOrigin"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车辆类型"
                          prop="isTrunk">
          <el-select v-model="search.isTrunk"
                     placeholder="请选择车型"
                     clearable
                     collapse-tags>
            <el-option v-for="item in vehicleType"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车籍"
                          prop="isGui">
          <el-select v-model="search.isGui"
                     placeholder="请选择车辆来源"
                     clearable
                     collapse-tags>
            <el-option v-for="item in source"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="支付机构"
                          prop="payOrg">
          <el-select v-model="search.payOrg"
                     placeholder="请选择支付机构"
                     clearable
                     collapse-tags>
            <el-option v-for="item in payOrigin"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="发行机构"
                          prop="searchitem">
          <el-cascader v-model="searchitem"
                       class="form-select"
                       ref="deptNodes"
                       filterable
                       clearable
                       @change="cascaderChange"
                       :options="deptOptions"
                       :expand-trigger="'click'"
                       :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>
        <dart-search-item label="发行网点"
                          prop="searchitemNet"
                          v-if="issueNet.length!=0">
          <el-cascader v-model="searchitemNet"
                       class="form-select"
                       ref="deptNodesNet"
                       filterable
                       clearable
                       :options="issueNet"
                       :expand-trigger="'click'"
                       :props="{
             
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>
        <dart-search-item label="
                       部门属性"
                          prop="branchType">
          <el-select v-model="search.branchType"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in branchTypeOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>
    
    <script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import { getSupportBank } from '@/api/dict'
var _ = require('lodash')
import upmsRequest from '@/utils/upmsRequest'

var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: {
        zyStartTime: '', // 开始日期
        zyEndTime: '', // 结束日期,
        name: 'silkyIssueReport',
        payOrg: '',
        isTrunk: '',
        isGui: '',
        signOrg: '',
        branchDeptNo: '',
        branchLength: '',
        branchType: '',
        branchId: '',
      },
      tableHeight: 0,
      rules: {
        zyStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        zyEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },
      //签约机构枚举
      signOrigin: [
        {
          value: '银联|CUP',
          label: '银联',
        },
             {
          value: '微信钱包|S_WECHAT',
          label: '微信钱包',
        },
             {
          value: '微信指定卡|C_WECHAT',
          label: '微信指定卡',
        },
      ],
      //车型枚举
      vehicleType: [
        { value: '客车|2', label: '客车' },
        { value: '货车|1', label: '货车' },
        { value: '专项车|3', label: '专项车' },
      ],
      //车辆来源
      source: [
        { value: '桂籍|1', label: '桂籍' },
        { value: '非桂籍|2', label: '非桂籍' },
      ],
      //支付机构枚举
      payOrigin: [],
      branchTypeOptions: [
        { value: '自营|01', label: '自营' },
        { value: '运营|02', label: '运营' },
        { value: '一站式|03', label: '一站式' },
        { value: '合作|04', label: '合作' },
        { value: '银行|05', label: '银行' },
        { value: '线上|06', label: '线上' },
        { value: '其他|99', label: '其他' },
      ],
      deptOptions: [], // 部门列表
      currentDept: null,
      searchitem: '',
      issueNet: [],
      searchitemNet: '',
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.zyStartTime = moment().startOf('day').format('YYYY-MM-DD')
    this.search.zyEndTime = moment().startOf('day').format('YYYY-MM-DD')
    this.getgroup()
    this.getPayOriginList()
  },
  mounted() {},
  methods: {
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get',
      })
        .then((res) => {
          this.deptOptions = res.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    cascaderChange(val) {
      this.issueNet = []
      let currentData = this.$refs.deptNodes.getCheckedNodes()[0].data
      if (currentData.children) {
        this.issueNet = currentData.children
      }
    },
    changeitem(item) {
      let deptNum = item.deptNum
      switch (item.deptLevel) {
        // item.name + '|' + 
        case 1:
          this.search.branchDeptNo =  deptNum.slice(9, 12)
          break
        case 2:
          this.search.branchDeptNo =  deptNum.slice(9, 15)
          break
        case 3:
          this.search.branchDeptNo =  deptNum.slice(9, 18)
          break
        case 4:
          this.search.branchDeptNo =  deptNum.slice(9, 21)
          break
        case 5:
          this.search.branchDeptNo =  deptNum.slice(9, 25)
          break
        case 6:
          this.search.branchDeptNo =  deptNum.slice(9, 30)
          break
        case 7:
          this.search.branchDeptNo =  deptNum.slice(9, 35)
          break
      }
      if (this.issueNet.length == 0) {
        this.search.branchId =  item.id.toString()
      }
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    onSearchHandle() {
      if (moment(this.search.zyStartTime).isAfter(this.search.zyEndTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.zyEndTime).diff(
          moment(this.search.zyStartTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月',
        })
        return
      }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.zyStartTime = moment().startOf('day').format('YYYY-MM-DD')
        this.search.zyEndTime = moment().startOf('day').format('YYYY-MM-DD')
        this.searchitemNet = ''
        this.searchitem = ''
        this.issueNet = ''
      })
    },

    sendReportRequest() {
      if (!this.searchitemNet) {
        this.search.branchId = ''
      }
      if (!this.searchitem) {
        this.search.branchDeptNo = ''
        this.search.branchId = ''
        this.search.branchLength = ''
      }
      try {
        this.currentDept = this.$refs.deptNodes.getCheckedNodes()[0].data
        this.changeitem(this.currentDept)
      } catch (e) {}
      try {
        let currentData = this.$refs.deptNodesNet.getCheckedNodes()[0].data
        // this.search.branchId =
        //   currentData.name + '|' + currentData.id.toString()
        this.search.branchId = currentData.id.toString()

      } catch (e) {}
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.zyStartTime = moment(params.zyStartTime).format('YYYY-MM-DD')
      params.zyEndTime = moment(params.zyEndTime).format('YYYY-MM-DD')
      if (this.search.branchType != '') {
        params.branchType = this.search.branchType
      }
      //如果值为空，删除字段
      for (let key in params) {
        if (params[key] === '' || !params[key]) {
          delete params[key]
        }
      }
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },


    /**
     * 获取支付机构
     */
    async getPayOriginList(){
      let {data}  = await getSupportBank()
      this.payOrigin = data.map(item => {
        return {
          ...item,
          label:item.bankName,
          value:item.bankName + '|' + item.bankCode
        }
      })
      this.payOrigin.push({ value: '微信钱包|WALLET', label: '微信钱包' }) // 除了这个接口的结果外，再加进去一个微信钱包|WALLET
    }
  },
}
</script>
    
    <style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>