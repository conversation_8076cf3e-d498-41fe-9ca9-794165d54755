<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:注销退费月报表 name:refundOrderMonthReport  
  * @author:zhangys
  * @date:2022/06/22 10:01:31
!-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :formSpan="24"
                 :gutter="20"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="选择日期"
                          prop="refundTime">
          <el-date-picker v-model="search.refundTime"
                          type="month"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list">
      <img src="@/image/bg-left.png" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {
    this.search.refundTime = moment().startOf('month').format('YYYY-MM')
  },
  data() {
    return {
      search: {
        refundTime: '',
      },

      rules: {
        refundTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' },
        ],
      },

      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let params = {
            name: 'refundOrderMonthReport',
            refundTime: moment(this.search.refundTime).format('YYYY-MM'),
          }

          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.search.refundTime = moment().startOf('month').format('YYYY-MM')
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>