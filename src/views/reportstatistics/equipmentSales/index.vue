<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="付款日起"
                          prop="dateStart">
          <el-date-picker v-model="search.dateStart"
                          type="date"
                          value-format="yyyy-MM-dd"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="付款日止"
                          prop="dateEnd">
          <el-date-picker v-model="search.dateEnd"
                          type="date"
                          value-format="yyyy-MM-dd"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="订单来源"
                          prop="orderSource">
          <el-select v-model="search.orderSource"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="item in orderSourceReport"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="商品类型"
                          prop="goodsType">
          <el-select v-model="search.goodsType"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="item in goodsType"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { orderSourceReport, goodsType } from '@/common/const/optionsData.js'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      orderSourceReport,
      goodsType,
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        dateStart: '', // 扣款对账月起
        dateEnd: '', // 扣款对账月止,
        orderSource: '',
        goodsType: '',
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        dateStart: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        dateEnd: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.dateStart = moment(new Date()).format('YYYY-MM-DD')
    this.search.dateEnd = moment(new Date()).format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (moment(this.search.dateStart).isAfter(this.search.dateEnd)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      this.sendReportRequest()
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },

    sendReportRequest() {
      console.log(this.search)
      this.loading = true
      let params = {
        name: 'devSale',
        'devSquare-payStartDate': this.search.dateStart, // 扣款对账月起
        'devSquare-payEndDate': this.search.dateEnd, // 扣款对账月止,
        'devSquare-orderSource': this.search.orderSource, //订单来源
        'devSquare-goodsType': this.search.goodsType, //商品类型
      }
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          // console.log(decodeUrl,'地址')
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
