// 报表服务接口
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { Message } from 'element-ui'
import upmsRequest from '@/utils/upmsRequest'
// 查询报表接口
export let queryReport = (data, cb) => {
    request({
        url: api.report,
        method: 'post',
        data
    }).then(res => {
        if (res.code == 200) {
            let url = res.data;
            let decodeUrl = decode(url)
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
                decodeUrl,
                '_blank',
                'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
        } else {
            Message.error(res.msg);
        }
        cb && cb(res)
    }).catch(err => {
        cb && cb(err)
    })
}
// 查询部门组织
export let queryDeptOrg = (cb) => {
    upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
    })
        .then(res => {
            let result = res.data && res.data.length ? res.data : []
            cb && cb(result);
        })
        .catch(error => {
            cb && cb([]);
        })
}

export function getAgencyOptApi (data) {
    return request({
        url: '/channel/config/dict',
        method: 'post',
        data
    })
}
export function queryDeptList(data) {
    return request({
        url: '/dept/reportFromDept',
        method: 'post',
        data
    })
}
