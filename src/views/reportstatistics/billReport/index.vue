<template>
  <div class="user">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="24"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import moment from 'moment'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      reportList: [
        {
          id: 1,
          name: 'CnvoiceInfoTable',
          title: '发票开票情况报表'
        }
      ],
      tableHeight: 0,
      rules: {
        invStaTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        invEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        invoiceType: [
          { required: true, message: '请选择发票类型', trigger: 'change' }
        ]
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'invStaTime',
          label: '统计开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'invEndTime',
          label: '统计结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'select',
          field: 'invoiceType',
          label: '发票类型',
          placeholder: '发票类型',
          default: 0,
          options: [
            {
              label: '普通电子发票',
              value: 0
            },
            {
              label: '电子专票',
              value: 4
            }
          ]
        }
      ]
    }
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.invStaTime).isAfter(fromData.invEndTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>