<template>
  <div class="user">
    <dart-search ref="searchForm1" :formSpan='24' :searchOperation='false' :fontWidth="1" label-position="right" :model="search" :rules="rules">
      <template slot="search-form">
        <dart-search-item label="清分日开始时间：" prop="dateStart">
          <el-date-picker v-model="search.dateStart" type="date" value-format="yyyy-MM-dd" :clearable='false' placeholder="选择日期" :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="清分日结束时间：" prop="dateEnd">
          <el-date-picker v-model="search.dateEnd" type="date" value-format="yyyy-MM-dd" :clearable='false' placeholder="选择日期" :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        dateStart: '', // 清分日开始
        dateEnd: '', // 清分日结束,
      },
      optiondata: [],
      tableHeight: 0,
      rules: {

      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.dateStart = moment(new Date()).format('YYYY-MM-DD')
    this.search.dateEnd = moment(new Date()).format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (moment(this.search.dateStart).isAfter(this.search.dateEnd)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      this.sendReportRequest()
    },
    sendReportRequest() {
      console.log(this.search)
      this.loading = true
      let params = {
        name: 'squareRestituteReport',
        'squareRestituteReport-clearDateStart': this.search.dateStart, // 清分日开始
        'squareRestituteReport-clearDateEnd': this.search.dateEnd, // 清分日结束,
      }
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          // console.log(decodeUrl,'地址')
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
    gettime(data) {
      const value =
        moment(data).startOf('month').format("YYYY-MM-DD")
      return value
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  ::v-deep.el-form-item__label{
      width: 140px !important;
  }
  ::v-deep.el-form-item__content{
    width: calc(100% - 140px) !important;
  }
}
</style>
