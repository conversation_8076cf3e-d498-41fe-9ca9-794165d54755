<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:注销退费年报表 name:refundOrderYearReport  
  * @author:zhangys
  * @date:2022/06/22 10:01:31
!-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :formSpan="24"
                 :gutter="20"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="选择日期"
                          prop="refund_year">
          <el-date-picker v-model="search.refund_year"
                          type="year"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {
    this.search.refund_year = moment().startOf('year').format('YYYY')
  },
  data() {
    return {
      search: {
        refund_year: '',
      },

      rules: {
        refund_year: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' },
        ],
      },

      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let params = {
            name: 'refundOrderYearReport',
            refund_year: moment(this.search.refund_year).format('YYYY'),
          }

          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.search.refund_year = moment().startOf('year').format('YYYY')
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>