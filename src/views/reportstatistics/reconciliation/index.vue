<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="核对时间"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="date"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="部门名称"
                          prop="searchitem">

          <el-cascader v-model="search.searchitem"
                       class="form-select"
                       ref="deptNodes"
                       filterable
                       clearable
                       :options="deptOptions"
                       :expand-trigger="'click'"
                       :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {
    this.search.startTime = moment().startOf('day').format('YYYY-MM-DD')
    this.getgroup()
  },
  data() {
    return {
      departmenttype,
      groupArr: [],
      deptOptions: [],
      currentDept: null,
      search: {
        startTime: '',
        OPERATOR_DEPT: '',
        name: 'rechargeSavleCheck',
        searchitem: [],
        branchDeptNo: '',
        branchLength: null,
      },
      tableHeight: 0,
      optiondata: [],
      rules: {
        startTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' },
        ],
        searchitem: [
          { required: true, message: '请选择统计部门', trigger: 'change' },
        ],
      },
      options: [],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    sendReportRequest() {
      try {
        this.currentDept = this.$refs.deptNodes.getCheckedNodes()[0].data
        this.changeitem(this.currentDept)
      } catch (e) {}
      let params = {
        ...this.search,
        checkTime: moment(this.search.startTime).format('YYYY-MM-DD'),
      }

      delete params.searchitem
      delete params.startTime

      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          // console.log(decodeUrl,'地址')
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
    changeitem(item) {
      let deptNum = item.deptNum
      switch (item.deptLevel) {
        case 1:
          this.search.branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          this.search.branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          this.search.branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          this.search.branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          this.search.branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          this.search.branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          this.search.branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.OPERATOR_DEPT = ''
    },
    querySearchGroup(queryString, cb) {
      // console.log(queryString.length)
      // if (queryString == 0) {
      //   this.groupArr = []
      //   this.search.OPERATOR_DEPT = ''
      //   let arr = this.groupArr
      //   cb(arr)
      // }
      this.search.OPERATOR_DEPT = ''
      let data = {
        name: queryString,
      }
      request({
        url: api.reportdept,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.groupArr = res.data.map((item) => {
            return {
              value: item.name,
              address: item.deptId,
            }
          })
          let arr = this.groupArr
          clearTimeout(this.timeout)
          this.timeout = setTimeout(() => {
            console.log(arr)
            cb(arr)
          }, 1000 * Math.random())
        })
        .catch(() => {})
    },
    checkid() {
      if (this.search.OPERATOR_DEPT == '') {
        this.search.name = ''
      }
    },
    selectGroup(val) {
      this.search.OPERATOR_DEPT = val.address
    },
    gettime(data) {
      const value = moment(data).startOf('month').format('YYYY-MM-DD HH:mm:ss')
      return value
    },
    handleChange(value) {
      if (value.length) {
        let index = value.length
        return value[index - 1].toString()
      } else {
        return ''
      }
    },
    // 查询部门组织结构
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get',
      })
        .then((res) => {
          this.deptOptions = res.data
        })
        .catch((error) => {})
    },
    formatarr: function (arr) {
      if (!arr) {
        return
      }
      let item = arr.map((item) => {
        if (item.children) {
          return {
            value: item.id,
            label: item.name,
            children: this.formatarr(item.children),
          }
        } else {
          return {
            value: item.id,
            label: item.name,
          }
        }
      })
      return item
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>