

export const reportListFn = (state) => {
  return [
    {
      id: 1,
      name: 'CnvoiceInfoTable',
      title: '次次顺产品权益包领用情况统计表',
      rules: {
        invStaTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        invEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      formConfig: [
        {
          type: 'datePicker',
          field: 'invStaTime',
          label: '统计开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'invEndTime',
          label: '统计结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'cascader',
          field: 'stateType3',
          label: '代扣机构',
          placeholder: '代扣机构',
          default: '',
          options: state.typeList.payOrgIds
        }
      ]
    },
    {
      id: 2,
      name: 'CnvoiceInfoTable',
      title: '次次顺记账卡发行统计报表',
      rules: {
        invStaTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        invEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      btnSpan: 24,
      formConfig: [
        {
          type: 'datePicker',
          field: 'invStaTime',
          label: '统计开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'invEndTime',
          label: '统计结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'select',
          field: 'state',
          label: '客货类型',
          placeholder: '客货类型',
          default: '',
          options: [
            {
              value: 0,
              label: '客车'
            },
            {
              value: 1,
              label: '货车'
            }
          ]
        },
        {
          type: 'select',
          field: 'stateType',
          label: '籍贯类型',
          placeholder: '籍贯类型',
          default: '',
          options: [
            {
              value: 0,
              label: '桂籍'
            },
            {
              value: 1,
              label: '非桂籍'
            }
          ]
        }
      ]
    },
    {
      id: 3,
      name: 'CnvoiceInfoTable',
      title: '次次顺逾期金额应实收统计报表',
      formConfig: [
        {
          type: 'datePicker',
          field: 'invStaTime',
          label: '统计开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'invEndTime',
          label: '统计结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: state.pickerOptions,
          default: ''
        }
      ],
      rules: {
        invStaTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        invEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      }
    }
  ]
}