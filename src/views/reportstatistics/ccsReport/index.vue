<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:报表组件
  * @author:zhangys
  * @date:2023/01/09 09:25:55
!-->
<template>
  <div class="report-export">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">

        <dart-search-item label="统计日期"
                          prop="searchDate">
          <el-date-picker v-model="search.searchDate"
                          type="daterange"
                          value-format="yyyy-MM-dd"
                          clearable
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="报表类型"
                          prop="reportType">
          <el-select v-model="search.reportType"
                     placeholder="请选择报表类型"
                     clearable
                     collapse-tags
                     @change="reportTypeChange">
            <el-option v-for="item in reportTypeData"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="合作机构"
                          prop="cupBankId"
                          v-if="showCpuBank">
          <el-select v-model="search.cupBankId"
                     placeholder="请选择合作机构"
                     clearable
                     collapse-tags>
            <el-option v-for="item in mechanism"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车型"
                          prop="cupCategory"
                          v-if="showVehicleType">
          <el-select v-model="search.cupCategory"
                     placeholder="请选择车型"
                     clearable
                     collapse-tags>
            <el-option v-for="item in vehicleType"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>

        <dart-search-item label="商户号"
                          prop="cupMchId"
                          v-if="showCpuBank">
          <el-select v-model="search.cupMchId"
                     placeholder="请选择商户号"
                     clearable
                     collapse-tags>
            <el-option v-for="item in vcMchIdOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>

        <dart-search-item label=" ">
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>
  
<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import { convertFormat } from '@/utils'
import { vcMchIdOptions } from '@/common/const/optionsData'

import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },

  data() {
    return {
      loading: false,
      search: {
        searchDate: '',
        reportType: '',
        cupBankId: '全部|',
        cupCategory: '全部|',
      },
      vcMchIdOptions:convertFormat(vcMchIdOptions),
      tableHeight: 0,
      rules: {
        searchDate: [
          { required: true, message: '请选择统计日期', trigger: 'change' },
        ],
        reportType: [
          { required: true, message: '请选择报表类型', trigger: 'change' },
        ],
      },
      reportTypeData: [
        {
          value: 'I8aad8da701814d484d485701018556c23bd41dcc',
          label: '次次顺请款及应收确认汇总',
        },
        {
          value: 'I8aad8da701814d484d485701018560c88c252b2d',
          label: '次次顺请款及应收确认汇总(拓展交易）',
        },
        {
          value: 'I8aad8da701814d484d48570101855b9b964825e8',
          label: '次次顺请款及应收确认汇总(高速通行）',
        },
        {
          value: 'I8aad8da701814d484d48570101855c5c25b92796',
          label: '次次顺请款及应收确认汇总(服务费+滞纳金）',
        },
        {
          value: 'I8aad8da701814d484d4857010185622ac82036d6',
          label: '次次顺用户未还款账单',
        },
        {
          value:'Iff808081018b8df88df89c74018ed187cea9226b',
          label: '次次顺用户扣款情况统计报表',
        },
        {
          value: 'I8aad8da701814d484d485701018561fc63273536',
          label: '次次顺通行费及服务费应收统计',
        },
        {
          value: 'I8aad8da701814d484d485701018561decfd830eb',
          label: '次次顺补扣（补缴）记录跟踪表',
        },
        {
          value: 'I8aad8da701814d484d485701018561a4d0762e61',
          label: '次次顺产品应收报表（财务）',
        },
        {
          value:'Iff808081018a1c411c419c93018b6fb8d1073724',
          label: '次次顺滞纳金应收统计',
        },
      ],
      mechanism: [
        {
          value: '全部|',
          label: '全部',
        },
        {
          value: '银联|CUP',
          label: '银联',
        },
        {
          value: 'S_WECHAT',
          label:'微信车主平台'
        },
        {
          value: 'C_WECHAT',
          label:'微信指定卡'
        },
      ],
      vehicleType: [
        { value: '全部|', label: '全部' },
        { value: '客车|0', label: '客车' },
        { value: '货车|1', label: '货车' },
      ],
      showCpuBank: true,
      showVehicleType: true,
    }
  },
  created() {},
  mounted() {},
  methods: {
    reportTypeChange(val) {
      if (val == 'I8aad8da701814d484d485701018561a4d0762e61') {
        this.showVehicleType = false
        this.showCpuBank = true
        this.search.cupCategory = '全部|'
        return
      }
      if (val == 'I8aad8da701814d484d485701018561decfd830eb') {
        this.showCpuBank = false
        this.showVehicleType = true
        this.search.cupBankId = '全部|'
        return
      }
      if(val == 'Iff808081018a1c411c419c93018b6fb8d1073724'){ // 次次顺滞纳金应收统计
        this.showCpuBank = true
        this.showVehicleType = false
        return
      }
      this.showVehicleType = true
      this.showCpuBank = true
    },
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let data = {
            name: this.search.reportType,
            cupStartDate: this.search.searchDate[0],
            cupEndDate: this.search.searchDate[1],
            cupCategory: this.search.cupCategory,
            cupBankId: this.search.cupBankId,
            cupMchId: this.search.cupMchId,
          }
          if (!this.showVehicleType) {
            delete data.cupCategory
          }
          if (!this.showCpuBank) {
            delete data.cupBankId
          }

          //调用接口
          console.log('data=======>>>>', data)
          this.sendReportRequest(data)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.searchDate = ''
        this.search.cupBankId = '全部|'
        this.search.cupCategory = '全部|'
      })
    },

    sendReportRequest(data) {
      this.loading = true
      request({
        url: api.report,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>
  
  <style lang="scss" scoped>
.report-export {
  // padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  .search-title {
    padding: 0 0 10px 30px;
    font-size: 18px;
  }
}
</style>
  