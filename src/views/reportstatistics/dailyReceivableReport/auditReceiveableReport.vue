<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日结网点销售充值应收实收款日报表审核
  * @author:zhangys
  * @date:2022/10/17 10:02:26
-->
<template>
  <div>
    <div class="auditTable">
      <div class="auditTable-title">报表日期：{{dateTime}}</div>
      <div class="auditTable-table">
        <el-table :data="tableData"
                  style="width: 100%"
                  height="100%"
                  border>
          <el-table-column prop="branchName"
                           align="center"
                           label="部门（中心）" />
          <el-table-column prop="checkOperator"
                           align="center"
                           label="审核人" />
          <el-table-column prop="checkTime"
                           align="center"
                           label="审核时间"
                           width="170">
          </el-table-column>
          <el-table-column prop="checkRemark"
                           min-width="220"
                           align="center"
                           label="审核意见">

          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="auditOpinion">
      <div class="auditOpinion-title">审核意见</div>
      <div class="auditOpinion-text">
        <el-input type="textarea"
                  v-model="remark"
                  placeholder="请输入审核意见"
                  :rows="6"></el-input>
      </div>
    </div>
    <div class="auditButton g-flex g-flex-center">
      <el-button type="primary"
                 style="margin-right:50px"
                 @click="audit">审核</el-button>
      <el-button @click="back">返回</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  name: '',
  props: {
    dateTime: {
      type: String,
      default: false,
    },
  },
  components: {},
  data() {
    return {
      tableData: [],
      remark: '',
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getAuditList()
  },
  methods: {
    //获取审核列表
    getAuditList() {
      let params = {
        time: this.dateTime,
      }
      request({
        url: api.saleDailyCheckList,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data
        }
      })
    },
    audit() {
      if (!this.remark) {
        this.$message({
          type: 'error',
          message: '请输入备注',
        })
        return
      }
      let params = {
        time: this.dateTime,
        remark: this.remark,
      }
      request({
        url: api.saleDailyReportAudit,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res) {
          this.$message({
            type: 'success',
            message: '审核成功！',
          })
          this.remark = ''
          this.getAuditList()
        }
      })
    },
    back() {
      this.$emit('closeSlide')
      this.remark = ''
    },
  },
}
</script>

<style lang='scss' scoped>
.auditTable {
  .auditTable-title {
    margin: 20px 0 0 20px;
    font-weight: 500;
    font-size: 18px;
  }
  .auditTable-table {
    padding: 10px 20px;
    height: 350px;
  }
}
.auditOpinion {
  .auditOpinion-title {
    margin: 20px 0 0 20px;
    font-weight: 500;
    font-size: 18px;
  }
  .auditOpinion-text {
    padding: 10px 20px;
  }
}
.auditButton {
  margin-top: 20px;
}
</style>