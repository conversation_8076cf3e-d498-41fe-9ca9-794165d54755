<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:根据时间段查询报表
  * @author:zhang<PERSON>
  * @date:2023/03/10 16:00:23
-->

<template>
  <div>

    <!-- 搜索栏 -->
    <dart-search slot="report-search"
                 ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <template v-for="item in formProperties">
          <dart-search-item :key="item.fieldKey"
                            :label="item.fieldLabel"
                            :prop="item.fieldKey">
            <template v-if="item.element != 'custom'">
              <searchField :fieldProps="item.fieldProps"
                           :fieldOptions="item"
                           :ref="item.ref"
                           v-model="search[item.fieldKey]"></searchField>
            </template>
          </dart-search-item>
        </template>

        <dart-search-item :is-button="true"
                          :colElementNum="1">
          <div class="g-flex g-flex-end">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="searchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>

    <div class="table"
         style="height:430px">
      <el-table :data="tableData"
                style="width: 100%"
                height="100%"
                border>
        <el-table-column prop="branchName"
                         width="150"
                         align="center"
                         label="网点名称">
          <template slot-scope="scope">
            <span v-if="scope.row.isTotal"
                  style="font-weight:bold">{{scope.row.branchName}}</span>
            <span v-else>{{scope.row.branchName}}</span>
          </template>
        </el-table-column>
        <el-table-column label="应收销售金额(元)"
                         align="center">
          <el-table-column prop="payPosAmount"
                           width="120"
                           align="center"
                           label="POS">
          </el-table-column>
          <el-table-column prop="payCashAmount"
                           width="120"
                           align="center"
                           label="现金">
          </el-table-column>
          <el-table-column prop="payWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
          <el-table-column prop="payAliAmount"
                           width="120"
                           align="center"
                           label="支付宝">
          </el-table-column>
          <el-table-column prop="payYueAmount"
                           width="120"
                           align="center"
                           label="互联网账户">
          </el-table-column>
          <el-table-column prop="payTotal"
                           width="120"
                           align="center"
                           label="小计">
          </el-table-column>
        </el-table-column>

        <el-table-column label="应收通行费预存金额(元)"
                         align="center">
          <el-table-column prop="rechargePosAmount"
                           width="120"
                           align="center"
                           label="POS">
          </el-table-column>
          <el-table-column prop="rechargeCashAmount"
                           width="120"
                           align="center"
                           label="现金">
          </el-table-column>
          <el-table-column prop="rechargeWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
          <el-table-column prop="rechargeAliAmount"
                           width="120"
                           align="center"
                           label="支付宝">
          </el-table-column>
          <el-table-column prop="rechargeYueAmount"
                           width="120"
                           align="center"
                           label="互联网账户">
          </el-table-column>
          <el-table-column prop="rechargeTotal"
                           width="120"
                           align="center"
                           label="小计">
          </el-table-column>
        </el-table-column>

        <el-table-column label="互联网账户充值"
                         align="center">
          <el-table-column prop="netPosAmount"
                           width="120"
                           align="center"
                           label="POS">
          </el-table-column>
          <el-table-column prop="netCashAmount"
                           width="120"
                           align="center"
                           label="现金">
          </el-table-column>
          <el-table-column prop="netWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
          <el-table-column prop="netAliAmount"
                           width="120"
                           align="center"
                           label="支付宝">
          </el-table-column>
          <el-table-column prop="netTotal"
                           width="120"
                           align="center"
                           label="小计">
          </el-table-column>
        </el-table-column>

        <el-table-column label="卡账负值补缴"
                         align="center">
          <el-table-column prop="cardWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
        </el-table-column>

        <el-table-column label="通行费补缴"
                         align="center">
          <el-table-column prop="tollWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
        </el-table-column>

        <el-table-column label="当日实际到账金额(元)"
                         align="center">
          <el-table-column prop="realityPosAmount"
                           width="120"
                           align="center"
                           label="POS">
          </el-table-column>
          <el-table-column prop="realityCashAmount"
                           width="120"
                           align="center"
                           label="现金">
          </el-table-column>
          <el-table-column prop="realityWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
          <el-table-column prop="realityAliAmount"
                           width="120"
                           align="center"
                           label="支付宝">
          </el-table-column>
          <el-table-column prop="realityTotal"
                           width="120"
                           align="center"
                           label="小计">
          </el-table-column>
        </el-table-column>
        <el-table-column label="应实收差额(元)"
                         align="center">
          <el-table-column prop="marginPosAmount"
                           width="120"
                           align="center"
                           label="POS">
          </el-table-column>
          <el-table-column prop="marginCashAmount"
                           width="120"
                           align="center"
                           label="现金">
          </el-table-column>
          <el-table-column prop="marginWechatAmount"
                           width="120"
                           align="center"
                           label="微信(财付通)">
          </el-table-column>
          <el-table-column prop="marginAliAmount"
                           width="120"
                           align="center"
                           label="支付宝">
          </el-table-column>
          <el-table-column prop="marginTotal"
                           width="120"
                           align="center"
                           label="小计">
          </el-table-column>
        </el-table-column>
        <el-table-column prop="remarks"
                         width="200"
                         align="center"
                         label="备注"
                         fixed="right">
          <template slot-scope="scope">
            <span v-if="editRemark&&!scope.row.isTotal">
              <el-input size="small"
                        v-model="scope.row.remarks"></el-input>
            </span>
            <span v-else>
              {{scope.row.remarks}}
            </span>
          </template>

        </el-table-column>
        <el-table-column prop="tabType"
                         width="120"
                         align="center"
                         fixed="right"
                         label="操作">
          <template slot-scope="scope">
            <el-button type="text"
                       size="small"
                       @click="operatorRecord(scope.row)"
                       v-if="!scope.row.isTotal">
              操作记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="pageNum"
                     :page-sizes="[10, 20, 30,50]"
                     :page-size="pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema,
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import { datePickerOptions } from '@/components/schemaQuery/tool'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'

var moment = require('moment')
import float from '@/common/method/float.js'

export default {
  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
  },
  data() {
    return {
      departmenttype,
      groupArr: [],
      loading: false,
      states: [],
      search: {
        // name: '',
        branchDeptNo: '', //部门编号
        branchLength: null, //部门编号长度
        branchType: '', //部门属性
        deptID: '',
        time: '',
        endTime: '',
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        time: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
        deptID: [
          { required: true, message: '请选择统计部门', trigger: 'change' },
        ],
      },
      deptOptions: [],
      tableData: [],
      pageSize: 10,
      pageNum: 1,
      total: null,
      branchNo: '',
      formProperties: {
        time: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计开始日期',
          fieldKey: 'time',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: {
              disabledDate(time) {
                // 今天及以后不可选
                return time.getTime() > Date.now() - 8.64e7
              },
            },
          },
        },
        endTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计结束日期',
          fieldKey: 'endTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: {
              disabledDate(time) {
                // 今天及以后不可选
                return time.getTime() > Date.now() - 8.64e7
              },
            },
          },
        },

        deptID: {
          ...cascaderSchema,
          ref: 'cascaderNodes',
          placeholder: '请选择',
          fieldLabel: '部门名称',
          fieldKey: 'deptID',
        },
      },
      totalMoney: [],
      searchDate: '',
    }
  },
  created() {
    this.search.time = moment().subtract(1, 'days').format('YYYY-MM-DD')
    this.search.endTime = moment().subtract(1, 'days').format('YYYY-MM-DD')
    let _self = this
    queryDeptOrg((data) => {
      _self.formProperties.deptID.fieldProps.options = data || []
    })
  },
  mounted() {},
  methods: {
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    changePage(value) {
      this.pageNum = value
      this.searchHandle()
    },
    handleSizeChange(value) {
      this.pageSize = value
      this.searchHandle()
    },
    validate() {
      if (moment(this.search.time).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return false
      }
      return true
    },
    //汇总金额
    dailyReceiveListTotal(params) {
      request({
        url: api.dailyReceiveListTotal,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          if (this.tableData.length != 0) {
            let data = res.data
            data.branchName = '总合计'
            data.isTotal = true
            this.tableData.push(data)
          }
        }
      })
    },
    //查询
    searchHandle() {
      if (!this.validate()) return
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          let data = this.formatParams()
          let params = {
            time: data.time,
            endTime: data.endTime,
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            branchDeptNo: data.branchDeptNo,
            branchLength: data.branchLength,
          }
       
          this.startLoading()
          request({
            url: api.dailyReceiveList,
            method: 'post',
            data: params,
          })
            .then((res) => {
              if (res.code == 200) {
                this.endLoading()
                this.dailyReceiveListTotal(params)
                this.tableData = res.data.records
                this.total = res.data.total
              } else {
                this.$message.error(res.msg)
                this.endLoading()
              }
            })
            .catch((err) => {
              this.endLoading()
            })
        }
      })
    },
    closeSlide() {
      this.auditSlideVisiable = false
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },
    formatParams() {
      let cascaderData = {}
      try {
        cascaderData =
          this.$refs['cascaderNodes'][0].$children[0].getCheckedNodes()[0].data
        this.getDeptNo(cascaderData)
      } catch (e) {
        console.log(e)
      }
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },

    getDeptNo(item) {
      let deptNum = item.deptNum
      // console.log(item.deptLevel)
      switch (item.deptLevel) {
        case 1:
          this.search.branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          this.search.branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          this.search.branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          this.search.branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          this.search.branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          this.search.branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          this.search.branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  //   ::v-deep .el-input__inner {
  //     height: 30px;
  //   }
}
.pagination {
  padding: 5px 0;
  background-color: #fff;
  text-align: center;
}
</style>
