<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:操作记录
  * @author:zhangys
  * @date:2022/10/17 16:45:51
-->
<template>
  <div>
    <div class="table">
      <el-table :data="tableData"
                style="width: 100%"
                height="100%"
                border>
        <el-table-column prop="createTime"
                         align="center"
                         label="操作时间">
        </el-table-column>
        <el-table-column prop="checkOperator"
                         align="center"
                         label="操作人" />
        <el-table-column prop="branchName"
                         align="center"
                         label="操作人部门">
        </el-table-column>
        <el-table-column prop="remarks"
                         min-width="220"
                         align="center"
                         label="操作记录">

        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background
                       @size-change="handleSizeChange"
                       @current-change="changePage"
                       :current-page="search.pageNum"
                       :page-sizes="[10, 20, 30,50]"
                       :page-size="search.pageSize"
                       layout="total, sizes, prev, pager, next, jumper"
                       :total="total">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  name: '',
  props: {
    dateTime: {
      type: String,
      default: false,
    },
    branchNo: {
      type: String,
      default: false,
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      tableData: [],
      search: { time: '', branchNo: '', pageNum: 1, pageSize: 10 },
      total: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getOperatorList()
  },
  methods: {
    getOperatorList() {
      this.search.time = this.dateTime
      this.search.branchNo = this.branchNo
      request({
        url: api.saleDailyOperatorList,
        method: 'post',
        data: this.search,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getOperatorList()
    },
    handleSizeChange(page_size) {
      this.search.pageSize = page_size
      this.getOperatorList()
    },
  },
}
</script>

<style lang='scss' scoped>
.table {
  height: 620px;
}
</style>