<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:网点销售报表查询
  * @author:zhangys
  * @date:2023/03/10 15:59:27
-->
<template>
  <div>
    <!-- 搜索栏 -->
    <dart-search
      slot="report-search"
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <template v-for="item in formProperties">
          <dart-search-item
            :key="item.fieldKey"
            :label="item.fieldLabel"
            :prop="item.fieldKey"
            v-if="item.isShow"
          >
            <template v-if="item.element != 'custom'">
              <searchField
                :fieldProps="item.fieldProps"
                :fieldOptions="item"
                :ref="item.ref"
                v-model="search[item.fieldKey]"
              ></searchField>
            </template>
          </dart-search-item>
        </template>

        <dart-search-item :is-button="true" :colElementNum="1">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="searchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button size="mini" @click="audit" type="warning"
              >去审核</el-button
            >
            <el-button size="mini" @click="exportReport" type="warning"
              >导出报表</el-button
            >
            <el-button size="mini" @click="editRemarkHandle" type="primary"
              >编辑备注</el-button
            >
            <el-button
              size="mini"
              @click="save"
              type="primary"
              :disabled="!isEdit"
              >保存编辑</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>

    <div class="table" style="height:610px">
      <el-table :data="tableData" style="width: 100%" height="100%" border>
        <el-table-column
          prop="branchName"
          width="150"
          align="center"
          label="网点名称"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.isTotal" style="font-weight:bold">{{
              scope.row.branchName
            }}</span>
            <span v-else>{{ scope.row.branchName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="应收销售金额(元)" align="center">
          <el-table-column
            prop="payPosAmount"
            width="120"
            align="center"
            label="POS"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payPosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="payCashAmount"
            width="120"
            align="center"
            label="现金"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payCashAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="payWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payWechatAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="payAliAmount"
            width="120"
            align="center"
            label="支付宝"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payAliAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="payYueAmount"
            width="120"
            align="center"
            label="互联网账户"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payYueAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="payTotal"
            width="120"
            align="center"
            label="小计"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.payTotal) }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="应收通行费预存金额(元)" align="center">
          <el-table-column
            prop="rechargePosAmount"
            width="120"
            align="center"
            label="POS"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargePosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rechargeCashAmount"
            width="120"
            align="center"
            label="现金"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargeCashAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rechargeWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargeWechatAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rechargeAliAmount"
            width="120"
            align="center"
            label="支付宝"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargeAliAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rechargeYueAmount"
            width="120"
            align="center"
            label="互联网账户"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargeYueAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="rechargeTotal"
            width="120"
            align="center"
            label="小计"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.rechargeTotal) }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="互联网账户充值" align="center">
          <el-table-column
            prop="netPosAmount"
            width="120"
            align="center"
            label="POS"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.netPosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="netCashAmount"
            width="120"
            align="center"
            label="现金"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.netCashAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="netWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.netWechatAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="netAliAmount"
            width="120"
            align="center"
            label="支付宝"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.netAliAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="netTotal"
            width="120"
            align="center"
            label="小计"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.netTotal) }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="卡账负值补缴" align="center">
          <el-table-column
            prop="cardWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.cardWechatAmount) }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="通行费补缴" align="center">
          <el-table-column
            prop="tollWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.tollWechatAmount) }}
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="当日实际到账金额(元)" align="center">
          <el-table-column
            prop="realityPosAmount"
            width="120"
            align="center"
            label="POS"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.realityPosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="realityCashAmount"
            width="120"
            align="center"
            label="现金"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.realityCashAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="realityWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.realityWechatAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="realityAliAmount"
            width="120"
            align="center"
            label="支付宝"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.realityAliAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="realityTotal"
            width="120"
            align="center"
            label="小计"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.realityTotal) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="应实收差额(元)" align="center">
          <el-table-column
            prop="marginPosAmount"
            width="120"
            align="center"
            label="POS"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.marginPosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="marginCashAmount"
            width="120"
            align="center"
            label="现金"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.marginCashAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="marginWechatAmount"
            width="120"
            align="center"
            label="微信(财付通)"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.marginWechatAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="marginAliAmount"
            width="120"
            align="center"
            label="支付宝"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.marginPosAmount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="marginTotal"
            width="120"
            align="center"
            label="小计"
          >
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.marginPosAmount) }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          prop="remarks"
          width="200"
          align="center"
          label="备注"
          fixed="right"
        >
          <template slot-scope="scope">
            <span v-if="editRemark && !scope.row.isTotal">
              <el-input size="small" v-model="scope.row.remarks"></el-input>
            </span>
            <span v-else>
              {{ scope.row.remarks }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="tabType"
          width="120"
          align="center"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="operatorRecord(scope.row)"
              v-if="!scope.row.isTotal"
            >
              操作记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="pageNum"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div> -->
    <dartSlide
      :visible.sync="operationRecordVisible"
      title="操作记录"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <operationRecord
        v-if="operationRecordVisible"
        :dateTime="searchDate"
        :branchNo="branchNo"
      ></operationRecord>
    </dartSlide>

    <dartSlide
      :visible.sync="auditSlideVisiable"
      :title="
        search.dateType == '1'
          ? '网点销售充值应收实收款日报表审核'
          : '网点销售充值应收实收款月报表审核'
      "
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <auditReceiveableReport
        :auditSlideVisiable="auditSlideVisiable"
        @closeSlide="closeSlide"
        v-if="auditSlideVisiable"
        :dateTime="searchDate"
      ></auditReceiveableReport>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptList } from '../components/service'
import ePage from '../components/ePage.vue'
import request from '@/utils/request'
import api from '@/api/index'
import { departmenttype } from '@/common/const/optionsData.js'
import dartSlide from '@/components/dart/Slide/index.vue'
import auditReceiveableReport from './auditReceiveableReport'
import operationRecord from './operationRecord'
var moment = require('moment')
import float from '@/common/method/float.js'

let dateTypeOptions = [
  { value: '1', label: '日粒度' },
  { value: '2', label: '月粒度' }
]
export default {
  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    auditReceiveableReport,
    operationRecord,
    searchField,
    ePage
  },
  data() {
    return {
      departmenttype,
      groupArr: [],
      loading: false,
      states: [],
      search: {
        // name: '',
        // branchDeptNo: '', //部门编号
        // branchLength: null, //部门编号长度
        branchType: '', //部门属性
        deptID: '',
        dateType: '1',
        dateTime: '',
        monthTime: '',
        branchNo:'' // 网点部门编号
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        dateType: [
          { required: true, message: '请选审核类型', trigger: 'change' }
        ],
        dateTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        monthTime: [
          { required: true, message: '请选择月份', trigger: 'change' }
        ],
        deptID: [
          { required: true, message: '请选择统计部门', trigger: 'change' }
        ]
      },
      deptOptions: [],

      auditSlideVisiable: false, //审核弹框
      tableData: [],
      editRemark: false, //编辑备注标识符
      operationRecordVisible: false, //操作记录
      pageSize: 10,
      pageNum: 1,
      total: null,
      branchNo: '',
      editRemarkData: {
        //编辑备注数据对象
        list: [],
        time: ''
      },
      backUpTableData: [], //深拷贝列表，对比数据用
      formProperties: {
        dateType: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: dateTypeOptions,
            clearable: false
          },
          fieldLabel: '审核类型',
          fieldKey: 'dateType',
          isShow: true
        },
        dateTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '统计日期',
          fieldKey: 'dateTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: {
              disabledDate(time) {
                // 今天及以后不可选
                return time.getTime() > Date.now() - 8.64e7
              }
            }
          },
          isShow: true
        },
        monthTime: {
          ...datePickerSchema.monthPicker,
          fieldLabel: '统计月份',
          fieldKey: 'monthTime',
          fieldProps: {
            ...datePickerSchema.monthPicker.fieldProps,
            pickerOptions: {
              disabledDate(time) {
                // 本月及以后不可选
                const t = new Date().getDate()
                return time.getTime() > Date.now() - 8.64e7 * t
              }
            }
          },
          isShow: false
        },
        // deptID: {
        //   ...cascaderSchema,
        //   ref: 'cascaderNodes',
        //   placeholder: '请选择',
        //   fieldLabel: '部门名称',
        //   fieldKey: 'deptID',
        //   isShow: true,
        // },
        branchNo: {
          ...selectSchema,
          fieldProps: {
            ...selectSchema.fieldProps,
            options: [],
            clearable: true
          },
          fieldLabel: '部门名称',
          fieldKey: 'branchNo',
          isShow: true
        }
      },
      totalMoney: [],
      searchDate: '',
      isEdit: false
    }
  },
  created() {
    this.getQueryList()
    this.search.dateTime = moment()
      .subtract(1, 'days')
      .format('YYYY-MM-DD')
  },
  watch: {
    'search.dateType'(val) {
      if (val == '1') {
        this.formProperties.dateTime.isShow = true
        this.formProperties.monthTime.isShow = false
        this.search.dateTime = moment()
          .subtract(1, 'days')
          .format('YYYY-MM-DD')
      }
      if (val == '2') {
        this.formProperties.monthTime.isShow = true
        this.formProperties.dateTime.isShow = false
        this.search.monthTime = moment()
          .subtract(1, 'month')
          .format('YYYY-MM')
      }
    }
  },
  methods: {
    moneyFilter(val) {
      return parseFloat(val + '').toFixed(2)
    },
    changePage(value) {
      this.pageNum = value
      this.searchHandle()
    },
    handleSizeChange(value) {
      this.pageSize = value
      this.searchHandle()
    },
    //编辑备注
    editRemarkHandle() {
      this.editRemark = true
      this.isEdit = true
    },
    //保存编辑
    save() {
      //只更改修改的数据，未修改的数据不做更改
      let list = []
      this.editRemarkData.list = []
      for (let i = 0; i < this.backUpTableData.length; i++) {
        for (let j = 0; j < this.tableData.length; j++) {
          if (
            this.tableData[j].branchNo == this.backUpTableData[i].branchNo &&
            this.tableData[j].remarks != this.backUpTableData[i].remarks
          ) {
            list.push(this.tableData[j])
          }
        }
      }
      this.editRemarkData.list = list.map(item => {
        return {
          branchNo: item.branchNo,
          remark: item.remarks
        }
      })
      if (this.search.dateType == '1') {
        this.editRemarkData.time = moment(this.search.dateTime).format(
          'YYYY-MM-DD'
        )
      }
      if (this.search.dateType == '2') {
        this.editRemarkData.time = moment(this.search.monthTime).format(
          'YYYY-MM'
        )
      }

      request({
        url: api.saleDailyReportRemark,
        method: 'post',
        data: this.editRemarkData
      }).then(res => {
        console.log(res)
        if (res && res.code == 200) {
          this.isEdit = false
          this.$message({
            type: 'success',
            message: '编辑备注成功！'
          })
          this.editRemark = false
        }
      })
    },
    matchDate() {
      if (this.search.dateType == '1') {
        this.searchDate = moment(this.search.dateTime).format('YYYY-MM-DD')
      }
      if (this.search.dateType == '2') {
        this.searchDate = moment(this.search.monthTime).format('YYYY-MM')
      }
    },
    //操作记录
    operatorRecord(row) {
      this.matchDate()
      this.branchNo = row.branchNo
      this.operationRecordVisible = true
    },
    //审核
    audit() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          this.matchDate()
          this.auditSlideVisiable = true
        }
      })
    },
    //汇总金额
    dailyReceiveListTotal(params) {
      request({
        url: api.dailyReceiveListTotal,
        method: 'post',
        data: params
      }).then(res => {
        if (res.code == 200) {
          if (this.tableData.length != 0) {
            let data = res.data
            data.branchName = '总合计'
            data.isTotal = true
            this.tableData.push(data)
          }
        }
      })
    },
    //查询
    searchHandle() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          let data = this.formatParams()
          if (data.dateType == '1') {
            data.time = moment(this.search.dateTime).format('YYYY-MM-DD')
          }
          if (data.dateType == '2') {
            data.time = moment(this.search.monthTime).format('YYYY-MM')
          }
          let params = {
            time: data.time,
            branchNo:data.branchNo,
            // pageNum: this.pageNum,
            // pageSize: this.pageSize,
          }
          this.backUpTableData = []
          this.startLoading()
          request({
            url: api.dailyReceiveList,
            method: 'post',
            data: params
          })
            .then(res => {
              if (res.code == 200) {
                this.endLoading()
                !data.branchNo && this.dailyReceiveListTotal(params) // 当选择网点后，不在调用汇总接口
                this.tableData = res.data.records
                this.backUpTableData = JSON.parse(
                  JSON.stringify(this.tableData)
                )
                this.total = res.data.total
              } else {
                this.$message.error(res.msg)
                this.endLoading()
              }
            })
            .catch(err => {
              this.endLoading()
            })
        }
      })
    },
    closeSlide() {
      this.auditSlideVisiable = false
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm1'].resetForm()
      })
    },
    formatParams() {
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },
    exportReport() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          let data = this.formatParams()
          let dayParams = {
            name: 'checkFixDayTable',
            time: data.dateTime,
          }
          let monthParams = {
            name: 'checkFixMonthTable',
            time: data.monthTime,
            // branchDeptNo: data.branchDeptNo,
            // branchLength: data.branchLength,
            vetStartTime: moment(data.monthTime)
              .startOf('month')
              .format('YYYY-MM-DD'),
            vetEndTime: moment(data.monthTime)
              .endOf('month')
              .format('YYYY-MM-DD')
          }
          if (this.search.dateType == '1') {
            queryReport(dayParams)
          }
          if (this.search.dateType == '2') {
            queryReport(monthParams)
          }
        } else {
          return false
        }
      })
    },

    async getQueryList() {
      let {data} = await queryDeptList()
      let list = data.map(item => {
        return {
          ...item,
          label:item.name,
          value:item.id
        }
      })
      this.formProperties.branchNo.fieldProps.options = list
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  //   ::v-deep .el-input__inner {
  //     height: 30px;
  //   }
}
.pagination {
  padding: 5px 0;
  background-color: #fff;
  text-align: center;
}
</style>
