<template>
  <ePage>
    <!-- 搜索栏 -->
    <dart-search
      slot="report-search"
      ref="searchForm"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <template v-for="item in formProperties">
          <dart-search-item :key="item.fieldKey" :label="item.fieldLabel" :prop="item.fieldKey">
            <template v-if="item.element != 'custom'">
              <searchField
                :fieldProps="item.fieldProps"
                :fieldOptions="item"
                :ref="item.ref"
                v-model="search[item.fieldKey]"
              ></searchField>
            </template>
          </dart-search-item>
        </template>

        <dart-search-item :is-button="true" :colElementNum="3">
          <div class="g-flex">
            <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </ePage>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import { queryReport, queryDeptOrg } from '../components/service'
import ePage from '../components/ePage.vue'
import { datePickerOptions } from '@/components/schemaQuery/tool'
var moment = require('moment')

export default {
  data() {
    return {
      loading: false,
      rules: {
        START_TIME: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        END_TIME: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      search: {
        name: 'NetAccountRechargeView', // 报表名称
        START_TIME: '',
        END_TIME: ''
      },
      formProperties: {
        START_TIME: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '开始日期',
          fieldKey: 'START_TIME',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        END_TIME: {
          ...datePickerSchema.datetimePicker,
          fieldLabel: '结束日期',
          fieldKey: 'END_TIME',
          fieldProps: {
            ...datePickerSchema.datetimePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  methods: {
    onValid() {
      if (moment(this.search.START_TIME).isAfter(this.search.END_TIME)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this.search.END_TIME).diff(
          moment(this.search.START_TIME),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      return true
    },

    formatParams() {
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },
    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = this.formatParams()
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
