<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="开始日期"
                          prop="startTime">
          <el-date-picker v-model="search.startTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="endTime">
          <el-date-picker v-model="search.endTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :clearable='false'
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="支付方式"
                          prop="payType">
          <el-select v-model="search.payType"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in payArr"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="部门名称"
                          prop="OPERATOR_DEPT">
          <!-- <el-autocomplete v-model="search.OPERATOR_DEPT_label"
                           size="mini"
                           :clearable='false'
                           :fetch-suggestions="querySearchGroup"
                           @blur="checkid"
                           @select="selectGroup"
                           placeholder="请输入部门名称"></el-autocomplete> -->
          <!-- <el-cascader
              v-model="search.OPERATOR_DEPT"
              :options="options"
              :props="{ checkStrictly: true }"
              clearable></el-cascader>                -->
          <el-cascader v-model="search.OPERATOR_DEPT"
                       class="form-select"
                       ref="deptNodes"
                       filterable
                       clearable
                       :options="deptOptions"
                       :expand-trigger="'click'"
                       :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>
        <dart-search-item label="操作员名称"
                          prop="OPERATOR_NO">
          <el-autocomplete v-model="search.OPERATOR_NO_label"
                           size="mini"
                           clearable
                           @blur="checkno"
                           :fetch-suggestions="querySearhOperator"
                           @select="searhOperator"
                           placeholder="请输入操作员名称"></el-autocomplete>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { payTypeOptions } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      payTypeOptions,
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        startTime: '', // 开始日期
        endTime: '', // 结束日期
        payType: '', //支付方式
        OPERATOR_DEPT: '', //网点名称
        OPERATOR_NO: '', //操作用户ID
        OPERATOR_DEPT_label: '', //网点名称
        OPERATOR_NO_label: '', //操作用户ID
      },
      optiondata: [],
      tableHeight: 0,
      groupArr: [],
      operatorArr: [],
      payArr: [],
      rules: {
        startTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        endTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
        OPERATOR_DEPT: [
          { required: true, message: '请选择统计部门', trigger: 'change' },
        ],
      },
      options: [],
      deptOptions: [],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.payArr = JSON.parse(JSON.stringify(this.payTypeOptions))
    this.payArr.forEach((item, i) => {
      if (item.value == '99999999') {
        console.log(item, i)
        this.payArr.splice(i, 1) // 从下标 i 开始, 删除 1 个元素
      }
    })
    this.search.startTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD HH:mm:ss')
    this.search.endTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.getgroup()
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月',
        })
        return
      }

      this.sendReportRequest()
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.OPERATOR_DEPT = ''
      })
    },

    sendReportRequest() {
      console.log(this.search)
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.loading = true
          let params = {
            ...this.search,
            name: 'rechargeViewReport',
            OPERATOR_DEPT: this.search.OPERATOR_DEPT.toString(),
            // OPERATOR_DEPT: this.handleChange(this.search.OPERATOR_DEPT),
          }
          delete params.OPERATOR_DEPT_label
          delete params.OPERATOR_NO_label
          console.log(params)
          this.$store
            .dispatch('report/report', params)
            .then((res) => {
              let url = res
              let decodeUrl = decode(url)
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    //查询部门
    querySearchGroup(queryString, cb) {
      // console.log(queryString.length)
      // if (queryString == 0) {
      //   this.groupArr = []
      //   this.search.OPERATOR_DEPT = ''
      //   let arr = this.groupArr
      //   cb(arr)
      // }
      this.search.OPERATOR_DEPT = ''
      let data = {
        name: queryString,
      }
      request({
        url: api.reportdept,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.groupArr = res.data.map((item) => {
            return {
              value: item.name,
              address: item.deptId,
            }
          })
          let arr = this.groupArr
          clearTimeout(this.timeout)
          this.timeout = setTimeout(() => {
            console.log(arr)
            cb(arr)
          }, 1000 * Math.random())
        })
        .catch(() => {})
    },
    checkid() {
      if (this.search.OPERATOR_DEPT == '') {
        this.search.name = ''
      }
    },
    checkno() {
      if (this.search.OPERATOR_NO == '') {
        this.search.OPERATOR_NO_label = ''
      }
    },
    selectGroup(val) {
      this.search.OPERATOR_DEPT = val.address
      this.search.OPERATOR_DEPT_label = val.value
    },

    //查询操作员
    querySearhOperator(queryString, cb) {
      console.log(queryString.length)
      if (queryString == 0) {
        this.operatorArr = []
        this.search.OPERATOR_NO = ''
        let arr = this.operatorArr
        cb(arr)
        return
      }
      let data = {
        name: queryString,
      }
      request({
        url: api.operatorList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.operatorArr = res.data.map((item) => {
            return {
              value: item.name + item.workNo,
              address: item.id,
            }
          })
          let arr = this.operatorArr
          clearTimeout(this.timeout)
          this.timeout = setTimeout(() => {
            console.log(arr)
            cb(arr)
          }, 1000 * Math.random())
        })
        .catch(() => {})
    },
    searhOperator(val) {
      this.search.OPERATOR_NO = val.address
      this.search.OPERATOR_NO_label = val.value
    },
    handleChange(value) {
      if (value.length) {
        let index = value.length
        return value[index - 1].toString()
      } else {
        return ''
      }
    },
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get',
      })
        .then((res) => {
          this.deptOptions = res.data
          // let arr = this.formatarr(res.data)
          // this.options = arr
        })
        .catch((error) => {
          console.log(error)
        })
    },
    formatarr: function (arr) {
      if (!arr) {
        return
      }
      let item = arr.map((item) => {
        if (item.children) {
          return {
            value: item.id,
            label: item.name,
            children: this.formatarr(item.children),
          }
        } else {
          return {
            value: item.id,
            label: item.name,
          }
        }
      })
      return item
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
