<!--
  * @projectName:gxetc-issue-manage-web
  * @desc: 网点销售统计报表 name:saleViewReport2
  * @author:zhangys
  * @date:2022/10/09 10:43:25
!-->
<template>
  <div>
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <div class="title">网点销售统计报表</div>
        <dart-search-item label="部门属性"
                          prop="branchType">
          <el-select v-model="search.branchType"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in branchTypeOptions"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="开始日期"
                          prop="SALEstartStatDate">
          <el-date-picker v-model="search.SALEstartStatDate"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束日期"
                          prop="SALEendStatDate">
          <el-date-picker v-model="search.SALEendStatDate"
                          type="date"
                          clearable
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { departmenttype } from '@/common/const/optionsData.js'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: {
        SALEstartStatDate: '', // 开始日期
        SALEendStatDate: '', // 结束日期,
        name: 'saleViewReport2',
        branchType: '',
      },
      branchTypeOptions: departmenttype,
      tableHeight: 0,
      rules: {
        SALEstartStatDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        SALEendStatDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },

      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.search.SALEstartStatDate = moment().startOf('day').format('YYYY-MM-DD')
    this.search.SALEendStatDate = moment().startOf('day').format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      if (
        moment(this.search.SALEstartStatDate).isAfter(
          this.search.SALEendStatDate
        )
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      //   if (
      //     moment(this.search.SALEendStatDate).diff(
      //       moment(this.search.SALEstartStatDate),
      //       'days'
      //     ) > 90
      //   ) {
      //     this.$msgbox({
      //       title: '提示',
      //       showClose: true,
      //       type: 'error',
      //       customClass: 'my_msgBox singelBtn',
      //       dangerouslyUseHTMLString: true,
      //       message: '统计日期时间段不得超过三个月',
      //     })
      //     return
      //   }
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
        this.search.SALEstartStatDate = moment()
          .startOf('day')
          .format('YYYY-MM-DD')
        this.search.SALEendStatDate = moment()
          .startOf('day')
          .format('YYYY-MM-DD')
        this.search.branchType = ''
      })
    },

    sendReportRequest() {
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.SALEstartStatDate = moment(params.SALEstartStatDate).format(
        'YYYY-MM-DD'
      )
      params.SALEendStatDate = moment(params.SALEendStatDate).format(
        'YYYY-MM-DD'
      )
      this.$store
        .dispatch('report/report', params)
        .then((res) => {
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>