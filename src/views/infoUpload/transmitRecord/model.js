export const reportListFn = (state) => {
  // let validatePass = (rule, value, callback) => {
  //   console.log(state.fromData)
  //   if (value === '' && (!state.fromData.blackStartTime || !state.fromData.blackEndTime)) {
  //     callback(new Error('卡号或obu号和时间不能全部为空'));
  //   } else {
  //     callback();
  //   }
  // };
  return [
    {
      id: 1,
      name: 'blackTransReport',
      title: '黑名单传输记录',
      rules: {
        // stop_end: [
        //   { required: true, message: '请选择日期', trigger: 'change' }
        // ]
      },
      btnSpan:24,
      formConfig: [
        {
          type: 'input',
          field: 'goodCode',
          label: '卡号或obu号',
          placeholder: '请填写卡号或obu号',
          default: ''
        },
        {
          type: 'datePicker',
          field: 'blackStartTime',
          label: '黑名单生成开始时问',
          placeholder: '请选择日期',
          // valueFormat: 'yyyy-MM-dd',
          // pickerOptions: state.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'blackEndTime',
          label: '黑名单生成结束时间',
          placeholder: '请选择日期',
          defaultTime:'23:59:59',
          // valueFormat: 'yyyy-MM-dd',
          // pickerOptions: state.pickerOptions,
          default: ''
        },
      ]
    },

  ]
}