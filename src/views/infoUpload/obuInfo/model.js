//客户信息上传表格
export const listColoumns = [
  {
    prop: 'obuMastId',
    label: 'ID',
  },
  {
    prop: 'obuNo',
    label: 'OBU号',
  },
  {
    prop: 'carNo',
    label: '车牌',
  },
  {
    prop: 'carColor',
    label: '车牌颜色',
  },
  {
    prop: 'delFlag',
    label: '是否删除',
  },
  {
    prop: 'ggjTime',
    label: '上传高管局时间',
  },
  {
    prop: 'ggjFlag',
    label: '上传高管局标识',
  },
  {
    prop: 'action',
    label: '操作'
  }
]

//客户信息上传表单
export const listForm = (state) => {
  return [
    {
      type: 'input',
      field: 'obuNo',
      label: 'OBU号',
      placeholder: 'OBU号',
      default: '',
      span: 6
    },
  ]
}