<template>
  <div class="toll-record">
    <SearchForm
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="8"
      :rules="rules"
      :btnAlign="'left'"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
        showIndex
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button slot="btn" size="mini" type="primary" @click="handelRow(scope)">重传</el-button>
          </div>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { obuView, oubUpload } from '@/api/infoUpload'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      listColoumns,
      api: obuView,
      pageSize: '',
      pageNum: '',
      rules: {
        obuNo: [{ required: true, message: '请填obu号', trigger: 'change' }]
      }
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 搜索框表单操作
    onSearchHandle(formData) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      // console.log(params,'searchFormData')
      this.currentFormData = params
      this.getTableData({}, res => {
        this.tableData = res.data
      })
    },
    async handelRow(row) {
      console.log(row, 'row')
      let { obuMastId } = row
      let params = {
        obuMastId
      }
      let result = await oubUpload(params)
      console.log(result, 'result')
      this.getTableData({}, res => {
        this.tableData = res.data
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>