//客户信息上传表格
export const listColoumns = [
  {
    prop: 'cardMastId',
    label: '卡片ID',
  },
  {
    prop: 'cardNo',
    label: '卡号',
  },
  {
    prop: 'carNo',
    label: '车牌',
  },
  {
    prop: 'carColor',
    label: '车牌颜色',
  },
  {
    prop: 'delFlag',
    label: '是否删除',
  },
  {
    prop: 'ggjTime',
    label: '上传高管局时间',
  },
  {
    prop: 'ggjFlag',
    label: '上传高管局标识',
  },
  {
    prop: 'action',
    label: '操作'
  }
]

//客户信息上传表单
export const listForm = (state) => {
  return [
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号',
      placeholder: '卡号',
      default: '',
      span: 6
    },
    {
      type: 'dateRange',
      field: 'moretime',
      keys: ['startTime', 'endTime'],
      label: '上传时间',
      default: [],
      props: {
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            state.$set(state.$refs.SearchForm.search, 'moretime', minDate.getTime())
            // state.$refs.SearchForm.search.moretime = minDate.getTime()
            if (maxDate) {
              state.$set(state.$refs.SearchForm.search, 'moretime', '')

              // state.$refs.SearchForm.search.moretime = ''
            }
            console.log(state.$refs.SearchForm.search.moretime, 'state.$refs.SearchForm.search.moretime')
          },
          disabledDate: (time) => {
            if (state.$refs.SearchForm.search.moretime !== '' && state.$refs.SearchForm.search.moretime !== null && !isEmptyArray(state.$refs.SearchForm.search.moretime)) {
              const one = 30 * 24 * 3600 * 1000;//一个月
              const minTime = state.$refs.SearchForm.search.moretime - one;
              const maxTime = state.$refs.SearchForm.search.moretime + one;
              return time.getTime() < minTime || time.getTime() > maxTime
            }
          }
        }
      }
    },
  ]
}


function isEmptyArray(arr) {
  return Array.isArray(arr) && arr.length === 0;
}