<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="8"
      :btnAlign="'left'"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
        showIndex
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button
              slot="btn"
              size="mini"
              type="primary"
              @click="handelRow(scope)"
              >重传</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { cardView, cardUpload } from '@/api/infoUpload'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      listColoumns,
      api: cardView,
      pageSize: '',
      pageNum: '',
      timeField: ['moretime'],
      rules: {
        cardNo: [{ required: true, message: '请填写卡号', trigger: 'change' }],
        moretime: []
      }
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    async handelRow(row) {
      console.log(row, 'row')
      let { cardMastId } = row
      let params = {
        cardMastId
      }
      let result = await cardUpload(params)
      console.log(result, 'result')
      this.getTableData({}, res => {
        this.tableData = res.data
      })
    },
    // 搜索框表单操作
    onSearchHandle(formData) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      console.log(params, 'searchFormData')
      let check = false
      for (let key in params) {
        if (params[key]) {
          check = true
        }
      }
      if (!check) {
        this.$message.error('查询条件不能为空!')
        return
      }
      this.currentFormData = params
      this.getTableData({}, res => {
        this.tableData = res.data
      })
    },
    // 重置的回调
    onReSetHandle(formData) {
      this.currentFormData = formData
    }
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>