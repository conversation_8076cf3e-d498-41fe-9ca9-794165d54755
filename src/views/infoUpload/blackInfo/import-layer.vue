<template>
  <div class="form-layer">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="130px"
      class="demo-formData"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="拉黑原因备注" prop="stopType">
            <!-- <el-input
              placeholder="请输入拉黑原因备注"
              maxLength="11"
              v-model="formData.stopType"
              class="input-with-select"
            >
            </el-input> -->
            <el-select
              clearable
              v-model="formData.stopType"
              class="input-with-select"
              placeholder="请选择拉黑原因备注"
            >
              <el-option
                v-for="(item, index) in stopTypeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <fieldset class="fieldset">
      <legend>附件上传</legend>
      <div slot="tip" class="el-upload__tip">注意事项：</div>
      <div slot="tip" class="el-upload__tip">
        1、仅支持xls、xlsx格式的Excel文件
      </div>
      <div slot="tip" class="el-upload__tip">
        2、导入字段包含：[ 卡号 ]
      </div>
      <div slot="tip" class="el-upload__tip">
        <a
          style="font-size: 14px;cursor:pointer;color:#409EFF"
          @click="exportTemp"
          >拉黑文件导入模板</a
        >
      </div>
      <el-upload
        class="upload"
        ref="upload"
        :on-remove="handleRemove"
        :auto-upload="false"
        action="action"
        accept=".xls,.xlsx"
        :file-list="fileList"
        :multiple="false"
        :on-change="onChange"
      >
        <el-button slot="trigger" size="small" type="primary"
          >选取文件</el-button
        >
      </el-upload>
    </fieldset>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="handleSubmit" type="primary" size="mini"
        >确定</el-button
      >
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
import layerMix from '@/utils/layerMixins'
import { balckTempExport } from '@/api/equipment'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {
        file: '',
        stopType: ''
      },
      fileList: [],
      rules: {
        stopType: [
          { required: true, message: '请选择拉黑原因备注', trigger: 'blur' }
        ]
      },
      stopTypeOptions: [
        {
          label: '稽查停用（捷通自行拉黑）',
          value: '16'
        },
        {
          label: '车型不符（捷通自行拉黑）',
          value: '15'
        },
        {
          label: '欠费（合作机构黑名单）',
          value: '14'
        },
        {
          label: '欠费停用（捷通自行拉黑）',
          value: '9'
        },
        {
          label: '挂失',
          value: '2'
        }
      ]
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.submitUpload()
          // this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    close() {
      this.closeDialog()
    },
    verifyHandle() {
      if (!this.formData.file) {
        this.$message({
          type: 'warning',
          message: '请先添加文件'
        })
        return
      }
      return true
    },
    submitUpload() {
      if (!this.verifyHandle()) return
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }
      this.upload()
    },
    upload() {
      this.startLoading()
      let formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('stopType', this.formData.stopType)
      console.log(formData)
      let url =
        process.env.VUE_APP_BASE_API + '/issue-web/retrans/uploadTemplateData'
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken()
          }
        })
        .then(res => {
          console.log('res', res)
          this.endLoading()

          if (res.data.code !== 200) {
            this.endLoading()
            this.$message({
              type: 'error',
              message: res.data.msg
            })
            return
          }
          this.$refs.upload.clearFiles()
          this.formData.stopType = ''
          this.formData.file = ''
          this.$message.success('操作成功')
          this.close()
          this.$emit('uploadSuccess')
        })
        .catch(err => {
          this.endLoading()
          this.$message({
            type: 'error',
            message: res.data.msg
          })
        })
    },
    handleRemove() {
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    exportTemp() {
      let params = {}
      let fileObj = {
        fileName: '拉黑导入文件模版.xlsx'
      }
      this.exportFile(params, balckTempExport, fileObj)
    },
    /**
     * 下载导出
     * @param {*} data 接口入参
     * @param {*} apiMethod 导出的接口方法
     * @param {*} fileObj 如果是流blob的形式，则传文件对象
     * @returns
     */
    async exportFile(data, apiMethod, fileObj) {
      this.loading = true
      if (!apiMethod) {
        console.error('缺少导出地址apiMethod！')
        return
      }
      let res = await apiMethod(data)
      this.loading = false
      if (fileObj) {
        // 如果是流blob的形式
        const link = document.createElement('a')
        let blob = new Blob([res]) //构造一个blob对象来处理数据
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = `${fileObj.fileName}` //下载的文件名
        document.body.appendChild(link)
        link.click() // 执行下载
        document.body.removeChild(link) // 释放标签
        return
      }
      // 如果是链接的形式
      if (res.code == 200) {
        let url = res.data
        let decodeUrl = decode(url)
        // console.log(decodeUrl,'地址')
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
  ::v-deep .el-range-editor {
    width: 100%;
  }
  .input-with-select{
    width: 80%;
  }
}

.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>
