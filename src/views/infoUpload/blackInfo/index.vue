<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="12"
      :rules="rules"
      :btnAlign="'left'"
    >
      <el-button
        slot="btn"
        type="primary"
        size="mini"
        native-type="submit"
        @click="importHandle"
        v-permisaction="['retrans:uploadTemplateData']"
        >批量拉黑导入</el-button
      >
      <el-button
        slot="btn"
        type="primary"
        size="mini"
        native-type="submit"
        @click="cardBlackHandle"
        v-permisaction="['retrans:cardBlack']"
        >重传卡片状态名单</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
        showIndex
      >
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button type="text" @click="handelRow(scope)">查看</el-button>
          </div>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { blackView, cardBlack } from '@/api/infoUpload'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm,
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      listColoumns,
      api: blackView,
      pageSize: '',
      pageNum: '',
      rules: {
        cardNo: [{ required: true, message: '请填写卡号', trigger: 'change' }],
      },
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    },
  },
  methods: {
    // 搜索框表单操作
    onSearchHandle(formData) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach((item) => {
        delete params[item]
      })
      // console.log(params,'searchFormData')
      this.currentFormData = params
      this.getTableData({}, (res) => {
        this.tableData = res.data
      })
    },
    cardBlackHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      console.log('query', query)
      if (!query.cardNo) {
        this.$message.warning('请先输入卡号！')
        return
      }
      this.$confirm('将进行卡片状态重传操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        callback: async (action) => {
          if (action == 'confirm') {
            let params = { cardNo: query.cardNo }
            let res = await cardBlack(params)
            if (res.code == 200) {
              this.$message.success(res.msg)
            }
          }
        },
      })
    },
    handelRow(row) {
      console.log(row, 'row')
    },
    importHandle() {
      this.$openPage(
        '@/views/infoUpload/blackInfo/import-layer',
        '批量拉黑导入文件',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
          },
        },
        {
          area: ['41%', '480px'],
        }
      )
    },
  },
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>