<template>
  <div class="toll-record">
    <SearchForm
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="8"
      :rules="rules"
      :btnAlign="'left'"
    >
      <el-button
        slot="btn"
        type="primary"
        size="mini"
        native-type="submit"
        @click="importHandle"
        v-permisaction="['retrans:uploadTemplateData']"
        >批量拉黑导入</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
        showIndex
      ></my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { blackView } from '@/api/infoUpload'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      listColoumns,
      api: blackView,
      pageSize: '',
      pageNum: '',
      rules: {
        cardNo: [{ required: true, message: '请填写卡号', trigger: 'change' }]
      }
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 搜索框表单操作
    onSearchHandle(formData) {
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      // console.log(params,'searchFormData')
      this.currentFormData = params
      this.getTableData({}, res => {
        this.tableData = res.data
      })
    },
    handelRow(row) {
      console.log(row, 'row')
    },
    importHandle() {
      this.$openPage(
        '@/views/infoUpload/blackInfo/import-layer',
        '批量拉黑导入文件',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
          }
        },
        {
          area: ['41%', '480px']
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>