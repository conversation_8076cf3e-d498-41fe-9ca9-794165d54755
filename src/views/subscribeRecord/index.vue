<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:消息订阅记录
  * @author:zhangys
  * @date:2022/07/22 16:14:50
!-->
<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="开始时间：" prop="staHicTime">
            <el-date-picker
              v-model="search.dateSta"
              type="datetime"
              clearable
              :picker-options="pickerOptions"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="结束时间：" prop="endHicTime">
            <el-date-picker
              v-model="search.dateEnd"
              type="datetime"
              clearable
              default-time="23:59:59"
              :picker-options="pickerOptions"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="服务类型：" prop="contractType">
            <el-select
              v-model="search.serviceType"
              clearable
              placeholder="请选择"
            >
              <el-option
                v-for="item in messageType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>

          <dart-search-item label="渠道：" prop="channel">
            <el-select v-model="search.channel" clearable placeholder="请选择">
              <el-option
                v-for="item in channelType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>

          <!-- <dart-search-item label="车牌颜色："
                            prop="vehicleColor">
            <el-select v-model="search.vehicleColor"
                       clearable
                       placeholder="请选择">
              <el-option v-for="item in licenseColorOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item> -->
          <dart-search-item label="车牌：" prop="vehicleCode">
            <el-input
              v-model="search.vehicleCode"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>

          <dart-search-item
            :is-button="true"
            style="margin-top: 10px"
            :span="24"
          >
            <div class="btn-wrapper">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                ><i class="el-icon-search"></i> 搜索</el-button
              >
              <el-button size="mini" @click="onResultHandle">重置</el-button>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table">
        <el-table
          v-loading="loading"
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          :max-height="550"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '40px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '40px' }"
          :header-cell-style="{ padding: '0px' }"
        >
          <el-table-column
            prop="custName"
            align="center"
            min-width="120"
            label="用户名"
          >
          </el-table-column>

          <el-table-column
            prop="vehicleCode"
            align="center"
            min-width="100"
            label="车牌号"
          />
          <!-- <el-table-column prop="carColor"
                           align="center"
                           label="车牌颜色">
            <template slot-scope="scope">
              {{ getVehicleColor(scope.row.vehicleColor) }}
            </template>
          </el-table-column> -->
          <el-table-column
            prop="cardNo"
            align="center"
            min-width="180"
            label="ETC卡号"
          />
          <el-table-column
            prop="serviceType"
            align="center"
            width="200"
            label="服务类型"
          >
            <template slot-scope="scope">
              {{ getMessageType(scope.row.serviceType) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="channel"
            align="center"
            width="130"
            label="通知渠道"
          >
            <template slot-scope="scope">
              {{ getChannelType(scope.row.channel) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="sendTime"
            align="center"
            min-width="160"
            label="通知时间"
          />
          <el-table-column
            prop="sendStatus"
            align="center"
            width="80"
            label="通知结果"
          >
            <template slot-scope="scope">
              <!-- {{ scope.row.sendStatus == '0' ? '成功' : '失败' }} -->
              {{ getMsgRemindStatus(scope.row.sendStatus) }}
            </template>
          </el-table-column>
          <!-- <el-table-column prop="remark"
                           align="center"
                           show-overflow-tooltip
                           min-width="220"
                           label="备注" /> -->
        </el-table>
        <div class="pagination">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="changePage"
            :current-page="search.page"
            :page-sizes="[10, 20, 50]"
            :page-size="search.size"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getMessageType,
  getChannelType,
  getMsgRemindStatus,
} from '@/common/method/formatOptions'
import {
  messageType,
  channelType,
  licenseColorOption,
  msgRemindStatus,
} from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
var moment = require('moment')
// import { decode } from 'js-base64'
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      messageType,
      channelType,
      licenseColorOption,
      msgRemindStatus,
      loading: false,
      dialogHandleDetail: false,
      dialogTransactionDetail: false,
      isCollapse: false,
      center: 'center',
      search: {
        channel: '0', //通知渠道
        dateEnd: '', //通知日期截至
        dateSta: '', //通知日期起始
        page: 1,
        size: 10,
        serviceType: '0', //服务类型
        vehicleCode: '',
        // vehicleColor: '',
      },
      sendStatus: [
        { value: '0', label: '成功' },
        { value: '1', label: '失败' },
      ],
      total: 0,
      tableData: [],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    this.getSignList()
  },
  methods: {
    getVehicleColor,
    getMessageType,
    getChannelType,
    getMsgRemindStatus,
    getSignList() {
      if (!this.search.channel) {
        this.$message({
          message: '请先选择通知渠道！',
          type: 'warning',
        })
        return
      }
      if (!this.search.serviceType) {
        this.$message({
          message: '请先选择服务类型！',
          type: 'warning',
        })
        return
      }
      let params = JSON.parse(JSON.stringify(this.search))
      params.dateSta = params.dateSta
        ? moment(params.dateSta).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.dateEnd = params.dateEnd
        ? moment(params.dateEnd).format('YYYY-MM-DD HH:mm:ss')
        : ''
      if (
        params.dateSta &&
        params.dateEnd &&
        moment(this.search.dateSta).isAfter(this.search.dateEnd)
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      this.loading = true
      request({
        url: api.subscribeRecord,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    },

    changePage(page) {
      this.search.page = page
      this.getSignList()
    },
    handleSizeChange(size) {
      this.search.size = size
      this.getSignList()
    },
    onSearchHandle() {
      this.search.page = 1
      this.getSignList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.page = 1
      this.search.size = 10
      this.search.channel = '0'
      this.search.serviceType = '0'

      this.getSignList()
    },
  },
}
</script>

<style lang="scss" scoped>
.refund {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }

  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .btn-wrapper {
    margin-left: 40px;
    // margin-top: 10px;
  }

  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
}
</style>
