<template>
  <div class="page-box">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="search">
      <template slot="search-form">
        <dart-search-item label="退费类型："
                          prop="vcchannelid">
          <el-select v-model="search.refund_type"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="item in optiondata"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="toupload()">退费转账结果导入</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table :data="tableData"
                v-loading="tableloading"
                style="width: 100%"
                height="100%"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="createdTime"
                         align="center"
                         label="创建日期" />
        <el-table-column prop="serialNumber"
                         align="center"
                         label="批次号" />
        <el-table-column prop="tabType"
                         align="center"
                         label="退费类型">
          <template slot-scope="scope">
            {{ gettabType(scope.row.tabType) }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="tabUrl"
                         align="center"
                         label="批复表地址" /> -->
        <el-table-column prop=""
                         min-width="220"
                         align="center"
                         label="操作">
          <template slot-scope="scope">
            <el-button size="mini"
                       type="primary"
                       @click="onExport(scope.row)">申请表下载</el-button>
            <el-button size="mini"
                       type="primary"
                       @click="onExportHandle(scope.row.id)">转账表导出</el-button>
            <el-button v-permisaction="['refund:tableReturn']"
                       size="mini" 
                       type="primary"
                       @click="backPoint(scope.row)">退回待制表节点</el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>
    <div class="pagination">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.page_num"
                     :page-sizes="[10, 20, 50]"
                     :page-size="search.page_size"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <upload-dialog v-if="uploadDialogVisible"
                   :visible.sync="uploadDialogVisible"
                   :tabId="tabId"
                   :refund_type="refund_type"
                   @uploadSuccess="uploadSuccess">
    </upload-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import uploadDialog from './uploadDialog'
import {
  refundTypeAll,
  refundType,
  redundOrderStatusList,
} from '@/common/const/optionsData.js'
import { tableReturn } from '@/api/refund'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    uploadDialog,
  },
  data() {
    return {
      uploadDialogVisible: false,
      search: {
        refund_type: '1',
        page_num: 1,
        page_size: 10,
      },
      tableData: [],
      total: 0,
      tableloading: false,
      optiondata: refundTypeAll,
      tabId: null,
      refund_type: null,
    }
  },
  created() {
    this.onSearchHandle()
  },
  mounted() {},
  methods: {
    handleSizeChange(e) {
      this.search.page_size = e
      this.search.page_num = 1
      this.onSearchHandle()
    },
    changePage(e) {
      this.search.page_num = e
      this.onSearchHandle()
    },
    gettabType(val) {
      for (let i = 0; i < this.optiondata.length; i++) {
        if (this.optiondata[i].value == val) {
          console.log(this.optiondata[i].label)
          return this.optiondata[i].label
        }
      }
      return ''
    },
    onExport(item) {
      let that = this
      this.getBlob(item.tabUrl, function (blob) {
        that.saveAs(blob, item.tabFileName)
      })
    },
    onExportHandle(id) {
      let data = {
        tab_id: id,
      }
      request({
        url: api.transferExport,
        method: 'post',
        data: data,
      })
        .then((res) => {
          let that = this
          this.getBlob(res.data.transferUrl, function (blob) {
            that.saveAs(blob, res.data.fileName)
          })
        })
        .catch(() => {})
    },
    onSearchHandle() {
      this.tableloading = true
      let data = {
        refund_type: this.search.refund_type,
        page_num: this.search.page_num,
        page_size: this.search.page_size,
      }
      request({
        url: api.queryTable,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total
          this.tableloading = false
        })
        .catch(() => {
          this.tableloading = false
        })
    },
    toupload() {
      // this.tabId = item.id
      // this.refund_type = item.tabType
      this.uploadDialogVisible = true
    },
    uploadSuccess() {
      this.uploadDialogVisible = false
      this.onSearchHandle()
    },
    getBlob(url, cb) {
      var xhr = new XMLHttpRequest()
      xhr.open('GET', url, true)
      xhr.responseType = 'blob'
      xhr.onload = function () {
        if (xhr.status === 200) {
          cb(xhr.response)
        }
      }
      xhr.send()
    },
    saveAs(blob, filename) {
      if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename)
      } else {
        var link = document.createElement('a')
        var body = document.querySelector('body')

        link.href = window.URL.createObjectURL(blob)
        link.download = filename

        // fix Firefox
        link.style.display = 'none'
        body.appendChild(link)

        link.click()
        body.removeChild(link)

        window.URL.revokeObjectURL(link.href)
      }
    },
    async backPoint(row){
      let params = { 
        tabId:row.id
       }
      let res = await tableReturn(params)
      if(res.code == 200){
        this.$message.success('操作成功！')
        this.onSearchHandle()
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.page-box {
  height: 100%;
  // min-height: calc(100vh - 94px);
  padding: 20px 20px 0 20px;
  flex-flow: column;
  display: flex;
}

.page-box .table-box,
.table {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.page-box .pagination {
  margin: 10px 0;
}
</style>
