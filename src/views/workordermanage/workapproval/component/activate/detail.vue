<template>
  <div class="detail-wrap">
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <basicInfo :orderInfo="orderInfo"></basicInfo>
    </div>
    <!-- 激活档案 -->
    <div class="orderItem">
      <archives :orderInfo="orderInfo"></archives>
    </div>
    <!-- 行驶证信息对比 -->
    <div class="orderItem">
      <licenseInfo :orderInfo="orderInfo"></licenseInfo>
    </div>
    <!-- 操作记录 -->
    <div class="orderItem">
      <operatorRecord :orderInfo="orderInfo"
                      @update="update"></operatorRecord>
    </div>
    <!-- 订单操作 -->
    <div class="orderItem"
         v-if="!isView&&orderInfo.orderStatus=='101'">
      <el-descriptions :column="2"
                       border
                       size="medium"
                       title=""
                       class="descriptions-content">
        <el-descriptions-item label="处理意见"
                              labelStyle="width:120px">
          <el-input type="textarea"
                    placeholder="请输入处理意见"
                    v-model="remark"></el-input>
        </el-descriptions-item>
      </el-descriptions>

    </div>

    <div class="orderItem btn-style"
         v-if="!isView&&orderInfo.orderStatus=='101'">
      <div class="g-flex g-flex-algin-center g-flex-center">
        <el-button type="danger"
                   size="medium"
                   style="margin:0 20px"
                   @click="operatorHandle('reject')">驳回</el-button>
        <el-button type="primary"
                   size="medium"
                   style="margin:0 20px"
                   @click="operatorHandle('accept')">通过</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'

import { stepOptions, rejectNodeOptions } from '@/common/const/optionsData.js'

import basicInfo from './basicInfo'
import archives from './archives.vue'
import licenseInfo from './licenseInfo'
import operatorRecord from './operatorRecord'
export default {
  components: {
    basicInfo,
    archives,
    licenseInfo,
    operatorRecord,
  },
  props: {
    applyId: [String, Number],
    slideVisible: {
      type: Boolean,
      default: '',
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    slideVisible(val) {
      if (val) {
        this.remark = ''
        this.getDetail()
        if (!this.isView) {
          //   this.orderLock()
        }
      }
      if (!val) {
        if (!this.isView) {
          this.$emit('refreshList')
          //   this.orderOpen()
        }
      }
    },
  },
  data() {
    return {
      orderInfoDetail: {},
      orderInfo: {},
      rejectVisible: false,
      loading: false,
      orderStatus: 1,
      stepOptions: stepOptions,
      rejectNodeOptions: rejectNodeOptions,
      stepItem: [],
      stepIndex: '',
      appealInfo: {},
      refundGoodsDialog: false,
      expandItem: '',
      afterSaleIData: {},
      id: '',
      remark: '',
      orderBasicInfo: {},
    }
  },
  created() {},
  methods: {
    //获取订单详情
    getDetail() {
      let params = {
        id: this.applyId,
      }
      this.orderInfo = {}
      this.orderBasicInfo = {}
      this.startLoading()
      this.$request({
        url: this.$interfaces.activateDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.orderInfo = res.data
            this.orderInfo.isView = this.isView

            this.getAfterSaleInfo()
            this.remark = ''
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    operatorHandle(val) {
      let params = {
        handleType: '1',
        id: this.orderInfo.id,
        remark: this.remark,
      }
      if (val == 'accept') {
        params.handleType = '1'
      }
      if (val == 'reject') {
        params.handleType = '2'
      }
      if (params.handleType == '2' && !params.remark) {
        this.$message.warning('请输入备注！')
        return
      }

      this.startLoading()
      this.$request({
        url: this.$interfaces.activateAudit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('操作成功！')
            this.remark = ''
            this.getDetail()
            this.endLoading()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    update(val) {
      let params = {
        handleType: '3',
        id: this.orderInfo.id,
        remark: val,
        isSpecialCase: true,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.activateAudit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('修改审核结果成功！')
            this.remark = ''
            this.getDetail()
            this.endLoading()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
.btn-style {
  padding: 10px;
}
</style>
