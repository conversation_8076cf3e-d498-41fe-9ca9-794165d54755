<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:操作记录
  * @author:zhangys
  * @date:2023/03/15 10:13:50
-->
<template>
  <div>
    <el-descriptions :column="1"
                     border
                     title="操作记录"
                     style="padding:24px 16px 0px 16px">
    </el-descriptions>
    <div style="padding:0 16px 10px 16px">
      <el-table :data="tableData"
                align="center"
                header-align="center"
                border
                style="width: 100%; margin-bottom: 20px"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="createdTime"
                         align="center"
                         label="创建时间"
                         width="160" />
        <el-table-column prop="processNode_str"
                         align="center"
                         label="申请单流程">
        </el-table-column>
        <el-table-column prop="opByName"
                         align="center"
                         label="操作人" />
        <el-table-column prop="platform"
                         align="center"
                         label="平台" />
        <el-table-column prop="handleType_str"
                         align="center"
                         label="操作状态" />
        <el-table-column prop="opTime"
                         align="center"
                         label="操作时间"
                         width="160" />
        <el-table-column prop="remark"
                         align="center"
                         min-width="250"
                         label="处理意见" />
        <el-table-column prop=""
                         align="center"
                         label="操作">
          <template slot-scope="scope"
                    v-if="orderInfo.orderStatus=='103'">
            <el-button type="text"
                       @click="edit">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderInfo(val) {
      if (val && val.id) {
        this.getRecord()
      }
    },
  },
  created() {},
  methods: {
    getRecord() {
      let params = {
        id: this.orderInfo.id,
      }
      this.$request({
        url: this.$interfaces.activateOperationRecord,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {})
    },
    edit() {
      this.$prompt('请输入审核结果', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(({ value }) => {
          this.$emit('update', value)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入',
          })
        })
    },
  },
}
</script>

<style lang='scss' scoped>
</style>