<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行激活——基础订单信息
  * @author:zhang<PERSON>
  * @date:2023/03/26 10:31:06
-->
<template>
  <div>
    <el-descriptions :column="3"
                     border
                     size="medium"
                     title="申请材料"
                     class="descriptions-content">

    </el-descriptions>
    <archivesBox uploadType="CACHEIMGUPLAOD"
                 :pictureSource="imgList"
                 previewMode></archivesBox>

  </div>
</template>

<script>
import {
  getbusinessType,
  gxCardTypeFilter,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
} from '@/common/method/formatOptions'
import archivesBox from '../../../component/photograph.vue'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: { archivesBox },
  data() {
    return {
      radio: 9,
      imgList: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseFrontUrl',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseSubpageUrl',
          lable: '行驶证副页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'obuFailurePhotoUrl',
          lable: 'OBU失效图片',
        },
      ],
      applyPic: [],
    }
  },
  computed: {},
  watch: {
    orderInfo(val) {
      if (val && val.applyPic) {
        this.applyPic = val.applyPic
        this.init()
      }
    },
  },
  created() {},
  methods: {
    getbusinessType,
    gxCardTypeFilter,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    init() {
      for (let i = 0; i < this.imgList.length; i++) {
        if (this.orderInfo.applyPic[this.imgList[i]['photo_code']]) {
          this.imgList[i]['file_url'] =
            this.orderInfo.applyPic[this.imgList[i]['photo_code']]
        }
      }
    },
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>