<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行激活——基础订单信息
  * @author:zhang<PERSON>
  * @date:2023/03/26 10:31:06
-->
<template>
  <div>
    <el-descriptions :column="2"
                     border
                     size="medium"
                     title="行驶证信息对比"
                     class="descriptions-content">
      <el-descriptions-item contentStyle="text-align:center;font-weight:bold">
        行驶证OCR识别信息
      </el-descriptions-item>
      <el-descriptions-item contentStyle="text-align:center;font-weight:bold">
        系统内车辆信息
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆所有人">
        {{ocrVehicleData.vehicleOwner}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆所有人">
        {{sysVehicleData.ownerName}}
      </el-descriptions-item>

      <el-descriptions-item labelStyle="text-align:center"
                            label="车牌号">
        {{ocrVehicleData.plateNum}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车牌号">
        {{sysVehicleData.carNo}}
      </el-descriptions-item>

      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆类型">
        {{ocrVehicleData.vehicleType}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆类型">
        {{sysVehicleData.vehicleType}}
      </el-descriptions-item>

      <el-descriptions-item labelStyle="text-align:center"
                            label="核定载人数">
        {{ocrVehicleData.approvedCount}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="核定载人数">
        {{sysVehicleData.carSeatNum}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆尺寸">
        {{ocrVehicleData.length}}x {{ocrVehicleData.width}}x
        {{ocrVehicleData.height}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车辆尺寸">
        {{sysVehicleData.outsideDimensions}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="总质量">
        {{ocrVehicleData.totalMass}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="总质量">
        {{sysVehicleData.totalMass}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="">

      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="车型">
        {{getCarType(sysVehicleData.carType)}}
      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="">

      </el-descriptions-item>
      <el-descriptions-item labelStyle="text-align:center"
                            label="客货标识">
        {{getVehicleType(sysVehicleData.isTrunk)}}
      </el-descriptions-item>

    </el-descriptions>
  </div>
</template>

<script>
import { getCarType, getVehicleType } from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return {
      ocrVehicleData: {},
      sysVehicleData: {},
    }
  },
  computed: {},
  watch: {
    orderInfo(val) {
      if (val && val.systemDrivingLicense) {
        this.sysVehicleData = val.systemDrivingLicense
      }
      if (val && val.ocrDrivingLicense) {
        this.ocrVehicleData = val.ocrDrivingLicense
      }
    },
  },
  created() {},
  methods: {
    getCarType,
    getVehicleType,
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>