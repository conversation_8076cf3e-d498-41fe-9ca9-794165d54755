<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行激活——基础订单信息
  * @author:zhang<PERSON>
  * @date:2023/03/26 10:31:06
-->
<template>
  <div>
    <el-descriptions :column="3"
                     border
                     size="medium"
                     title="订单基础信息"
                     class="descriptions-content">

      <el-descriptions-item label="用户名称">
        {{orderInfo.custName}}
      </el-descriptions-item>
      <el-descriptions-item label="车牌号">
        {{orderInfo.carNo}}
      </el-descriptions-item>

      <el-descriptions-item label="车牌颜色">
        {{getVehicleColor(orderInfo.carColor)}}
      </el-descriptions-item>

      <el-descriptions-item label="ETC卡号">
        {{orderInfo.cardNo}}
      </el-descriptions-item>

      <el-descriptions-item label="OBU号">
        {{orderInfo.obuNo}}
      </el-descriptions-item>

      <el-descriptions-item label="申请时间">
        {{orderInfo.applyTime}}
      </el-descriptions-item>

      <el-descriptions-item span="3"
                            label="申请理由">
        <!-- <el-radio-group v-model="orderInfo.applyReasonType">
          <el-radio label="0"
                    border
                    disabled>标签掉落</el-radio>
          <el-radio label="1"
                    border
                    disabled>插卡显示标签失效/标签失效</el-radio>
          <el-radio label="2"
                    border
                    disabled>其他</el-radio>
        </el-radio-group> -->
        {{orderInfo.applyReasonType=='0'?'标签掉落':(orderInfo.applyReasonType=='1'?'插卡显示标签失效/标签失效':'其他')}}
      </el-descriptions-item>

      <el-descriptions-item label="补充描述">
        {{orderInfo.applyReason}}
      </el-descriptions-item>

    </el-descriptions>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    getVehicleColor,
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>