<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--售后订单详情
  * @author:zhang<PERSON>
  * @date:2023/03/02 17:27:50
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     title="售后申请信息"
                     class="descriptions-content"
                     style="padding: 20px 20px 0 20px;position:relative">
      <template slot="extra">
        <div class="g-flex g-flex-align-center"
             style="">
          <div style="position:absolute;left:130px;">
            <el-button type="text"
                       @click="saleList">查看售后列表</el-button>
          </div>
          <div @click="isExpand = !isExpand"
               class="expand">
            <i class="expand-icon"
               :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
          </div>
        </div>

      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="售后类型"
                              labelStyle="width:120px">
          {{afterSaleDetail.deviceType=='0'?'退货':'换货'}}
        </el-descriptions-item>

        <el-descriptions-item label="申请时间">
          {{afterSaleDetail.applyTime}}
        </el-descriptions-item>

        <el-descriptions-item label="设备类型">
          {{deviceTypeFilter(afterSaleDetail.equipmentType)}}
        </el-descriptions-item>

        <el-descriptions-item label="换货原因">
          {{afterSaleDetail.exchangeReason}}
        </el-descriptions-item>

        <el-descriptions-item label="补充描述"
                              span="4">
          {{afterSaleDetail.replenishDesc}}
        </el-descriptions-item>
        <el-descriptions-item label="设备退回方式"
                              span="2"
                              labelStyle="width:120px">
          {{afterSaleDetail.equipmentReturnType=='0'?'上门取件':'自行寄回'}}
        </el-descriptions-item>

        <el-descriptions-item label="取件时间"
                              labelStyle="width:120px"
                              v-if="afterSaleDetail.equipmentReturnType=='0'">
          {{afterSaleDetail.takeTime}}
        </el-descriptions-item>

        <el-descriptions-item label="换货原因"
                              span="4"
                              labelStyle="width:120px">
          {{afterSaleDetail.exchangeReason}}
        </el-descriptions-item>

        <el-descriptions-item label="取件地址"
                              span="4"
                              v-if="afterSaleDetail.equipmentReturnType=='0'">
          {{afterSaleDetail.takeAddress}}
        </el-descriptions-item>

        <el-descriptions-item label="新设备收货地址"
                              span="4"
                              labelStyle="width:120px"
                              v-if="afterSaleDetail.deviceType=='1'">
          <div class="g-flex g-flex-justify g-flex-align-center">
            <!--  -->
            <span v-if="isEdit&&isEditInstall"
                  class="g-flex  g-flex-align-center">
              <!-- <el-input v-model="afterSaleDetail.equipmentReceiveAddress"></el-input> -->
              <el-select v-model="addRessData.provinceName"
                         style="margin:0 4px"
                         placeholder="请选择">
                <el-option v-for="(value,key) in provinceNameData"
                           :label="value.label"
                           :value="value.label"
                           :key='key'></el-option>
              </el-select>

              <el-select v-model="addRessData.cityName"
                         placeholder="请选择市"
                         style="margin:0 4px"
                         @change="cityChange">
                <el-option v-for="(value,key) in cityNameData"
                           :label="value.label"
                           :value="value.label"
                           :key='key'></el-option>
              </el-select>

              <el-select v-model="addRessData.areaName"
                         placeholder="请选择区/县"
                         style="margin:0 4px"
                         @change="areaChange">
                <el-option v-for="(value,key) in areaNameData"
                           :label="value.label"
                           :value="value.label"
                           :key='key'></el-option>
              </el-select>

              <el-input style="width:50%"
                        placeholder="详细地址"
                        v-model="addRessData.house"></el-input>
            </span>
            <span v-else>{{ afterSaleDetail.equipmentReceiveAddress }}</span>
            <!-- -->
            <el-button type="primary"
                       v-if="isEdit&&isEditInstall"
                       size="mini"
                       @click="updateAddress">修改</el-button>

          </div>

        </el-descriptions-item>
      </template>
    </el-descriptions>

    <el-descriptions :column="3"
                     border
                     class="descriptions-content"
                     style="padding: 20px 20px 0 20px; border: 1px solid #ebeef5;margin:0 16px 16px 16px;"
                     title="用户寄件信息"
                     v-if="isExpand">
      <template slot="extra">

        <el-button type="text"
                   v-if="afterSaleDetail.expressInfo&&afterSaleDetail.expressInfo.logisticsNo"
                   @click="viewExpressDetail(afterSaleDetail.expressInfo.logisticsNo)">{{afterSaleDetail.expressInfo.logisticsNo?'查看详情':''}}</el-button>

      </template>
      <template>
        <el-descriptions-item label="物流公司">
          <div v-if="afterSaleDetail.expressInfo&&afterSaleDetail.equipmentReturnType=='1'&&isEdit&&isEditInstall">
            <el-input v-model="afterSaleDetail.expressInfo.logisticsCompany"
                      :disabled="!afterSaleDetail.expressInfo.logisticsNo"></el-input>
          </div>
          <div v-else>{{afterSaleDetail.expressInfo.logisticsCompany?afterSaleDetail.expressInfo.logisticsCompany:''}}</div>
        </el-descriptions-item>
        <el-descriptions-item label="物流单号">
          <div v-if="afterSaleDetail.expressInfo&&afterSaleDetail.equipmentReturnType=='1'&&isEdit&&isEditInstall">
            <el-input v-model="afterSaleDetail.expressInfo.logisticsNo"
                      :disabled="!afterSaleDetail.expressInfo.logisticsNo"></el-input>
          </div>
          <div v-else>{{afterSaleDetail.expressInfo.logisticsNo?afterSaleDetail.expressInfo.logisticsNo:''}}</div>

        </el-descriptions-item>
        <el-descriptions-item label="寄件时间">

          <!-- <div v-if="afterSaleDetail.expressInfo&&afterSaleDetail.equipmentReturnType=='1'&&isEdit&&isEditInstall">
            <el-date-picker v-model="afterSaleDetail.expressInfo.sendTime"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择日期">
            </el-date-picker>
          </div> -->
          <div>{{afterSaleDetail.expressInfo.sendTime?afterSaleDetail.expressInfo.sendTime:''}}</div>
        </el-descriptions-item>
        <el-descriptions-item label="收货时间">

          <!-- <div v-if="afterSaleDetail.expressInfo&&afterSaleDetail.equipmentReturnType=='1'&&isEdit&&isEditInstall">
            <el-date-picker v-model="afterSaleDetail.expressInfo.receiveTime"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择日期">
            </el-date-picker>
          </div> -->
          <div>{{afterSaleDetail.expressInfo.receiveTime?afterSaleDetail.expressInfo.receiveTime:''}}</div>
        </el-descriptions-item>
        <el-descriptions-item label="物流状态">

          <!-- <div v-if="afterSaleDetail.expressInfo&&afterSaleDetail.equipmentReturnType=='1'&&isEdit&&isEditInstall">
            <el-select v-model="afterSaleDetail.expressInfo.logisticsNode"
                       placeholder="请选择">
              <el-option v-for="(value, key) in expressStatus"
                         :label="value.label"
                         :value="value.value"
                         :key="key"></el-option>
            </el-select>

          </div> -->
          <div>{{afterSaleDetail.expressInfo.logisticsNode?getExpressStatus(afterSaleDetail.expressInfo.logisticsNode):''}}</div>
        </el-descriptions-item>

      </template>
    </el-descriptions>
    <div style=" margin: 0px 16px 16px 16px"
         v-if="isExpand&&isEdit&&afterSaleDetail.equipmentReturnType=='1'&&isEditInstall">
      <el-button size="medium"
                 type="primary"
                 plain
                 :disabled="!afterSaleDetail.expressInfo.logisticsNo"
                 style="width: 100%; border-style: dashed"
                 @click="updateMailNo">修改</el-button>
    </div>
    <div class="archives"
         v-if="isExpand">
      <div class="archives-box">

        <archivesBox uploadType="CACHEIMGUPLAOD"
                     :pictureSource="imgList"
                     previewMode></archivesBox>

      </div>
    </div>
    <el-descriptions :column="3"
                     border
                     class="descriptions-content"
                     style="padding:  0 20px 20px  20px;"
                     v-if="isExpand">
      <template>
        <el-descriptions-item label="有偿/免费退换"
                              labelStyle="width:120px">

          <el-select v-if="afterSaleDetail.renewalInfo"
                     v-model="afterSaleDetail.renewalInfo.renewalType"
                     placeholder="请选择"
                     :disabled="!isEdit||!isEditInstall||!installEditFlag">
            <el-option v-for="(item,index) in afterSaleType"
                       :label="item.label"
                       :value="item.value"
                       :key='index'></el-option>
          </el-select>

        </el-descriptions-item>

        <el-descriptions-item label="退换设备">

          <el-checkbox-group v-model="renewalEquipmentSelected"
                             @change="checkChange">
            <el-checkbox label="ETC卡"
                         :disabled="!isEdit||!isEditInstall||!installEditFlag"
                         border></el-checkbox>
            <el-checkbox label="OBU"
                         :disabled="!isEdit||!isEditInstall||!installEditFlag"
                         border></el-checkbox>
          </el-checkbox-group>
        </el-descriptions-item>

        <el-descriptions-item label="邮费承担方式">

          <el-radio-group v-if="afterSaleDetail.renewalInfo"
                          v-model="afterSaleDetail.renewalInfo.frankingBy"
                          :disabled="!isEdit||!isEditInstall||!installEditFlag||afterSaleDetail.equipmentReturnType=='1'">
            <el-radio label="0"
                      border>捷通承担</el-radio>
            <el-radio label="1"
                      border>用户承担</el-radio>
          </el-radio-group>

        </el-descriptions-item>

      </template>
    </el-descriptions>
    <div style=" margin: 0px 16px 16px 16px"
         v-if="isExpand&&isEdit&&isEditInstall&&installEditFlag&&isShowBtn">
      <el-button size="medium"
                 type="primary"
                 plain
                 style="width: 100%; border-style: dashed"
                 @click="update">修改</el-button>
    </div>

    <afterSaleListVue v-if="listDialog"
                      :visible.sync="listDialog"
                      :afterSaleDetail="afterSaleDetail">
    </afterSaleListVue>

    <expressDetail :visible.sync="expressDetailDialog"
                   :v-if="expressDetailDialog"
                   :expressNo="expressNo"></expressDetail>
  </div>

</template>

<script>
import {
  refundReason,
  refundGoodsType,
  afterSaleType,
  expressStatus,
} from '@/common/const/optionsData.js'
import archivesBox from '../../../component/photograph.vue'
import {
  getAfterSaleType,
  getExpressStatus,
} from '@/common/method/formatOptions.js'
import afterSaleListVue from './afterSaleList.vue'
import expressDetail from './expressDetail.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  props: {
    afterSaleIData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    afterSaleDetail(val) {
      this.provinceNameData = this.address[0]
      this.cityNameData = this.provinceNameData[0].child
      this.getArea(this.afterSaleDetail.equipmentReceiveAddress)

      if (Object.keys(val).length != 0) {
      }
    },
    'afterSaleDetail.filePathData': {
      deep: true,
      handler(newVal, oldVal) {
        if (newVal) {
          this.spliceImg()
        }
      },
    },
    'afterSaleDetail.renewalInfo': {
      handler(newVal, old) {
        console.log(newVal, old, '<<---------new,old')
        this.$emit('changeAfterSaleInfo', newVal)
      },
      deep: true,
    },
  },
  created() {
    this.getExpandStatus()
    this.$nextTick(() => {
      this.afterSaleDetail = {
        ...this.afterSaleIData.orderInfo,
        ...this.afterSaleIData.postSaleInfo,
      }
      this.isEditInstallHandle()
      if (Object.keys(this.afterSaleDetail).length != 0) {
        if (this.afterSaleDetail.renewalInfo.renewalType == 'null') {
          this.afterSaleDetail.renewalInfo.renewalType = ''
        }
        // 自行机会默认用户承担邮费
        if (this.afterSaleDetail.equipmentReturnType == '1') {
          this.afterSaleDetail.renewalInfo.frankingBy = '1'
        }
        if (this.afterSaleDetail.renewalInfo.renewalEquipment == '2') {
          this.renewalEquipmentSelected = ['ETC卡', 'OBU']
          this.formData.exchangeEquipment = '2'
        }
        if (this.afterSaleDetail.renewalInfo.renewalEquipment == '1') {
          this.renewalEquipmentSelected = ['OBU']
          this.formData.exchangeEquipment = '1'
        }
        if (this.afterSaleDetail.renewalInfo.renewalEquipment == '0') {
          this.renewalEquipmentSelected = ['ETC卡']
          this.formData.exchangeEquipment = '1'
        }
      }
    })
  },
  data() {
    return {
      afterSaleDetail: {},
      isExpand: null,
      refundReason,
      refundGoodsType,
      imgList: [],
      formData: {
        exchangeEquipment: '',
      },
      afterSaleType,
      renewalEquipmentSelected: [],
      pictureSource: [],
      listDialog: false,
      expressDetailDialog: false,
      expressNo: '',
      expressStatus,
      installEditFlag: false,
      provinceNameData: [],
      cityNameData: [],
      areaNameData: [],
      areaCode: '',
      addRessData: { provinceName: '', cityName: '', areaName: '', house: '' },
    }
  },

  components: { archivesBox, afterSaleListVue, expressDetail },

  computed: {
    ...mapGetters(['address']),
    isEdit() {
      return !this.afterSaleDetail.isView
    },
    isEditInstall() {
      return (
        this.afterSaleDetail.nodeCode == '2000' ||
        this.afterSaleDetail.nodeCode == '2020' ||
        this.afterSaleDetail.nodeCode == '2030' ||
        this.afterSaleDetail.nodeCode == '2040' ||
        this.afterSaleDetail.nodeCode == '2400'
      )
    },
    isShowBtn() {
      return this.afterSaleDetail.nodeCode > 2000
    },
  },

  methods: {
    getAfterSaleType,
    getExpressStatus,
    getArea(str) {
      //对省市区进行分割

      let index11 = 0
      let index1 = str.indexOf('省')
      if (index1 == -1) {
        index11 = str.indexOf('自治区')
        if (index11 != -1) {
          this.addRessData.provinceName = str.substring(0, index11 + 3)
        } else {
          this.addRessData.provinceName = str.substring(0, 0)
        }
      } else {
        this.addRessData.provinceName = str.substring(0, index1 + 1)
      }

      let index2 = str.indexOf('市')
      if (index11 == -1) {
        this.addRessData.cityName = str.substring(index11 + 1, index2 + 1)
      } else {
        if (index11 == 0) {
          this.addRessData.cityName = str.substring(index1 + 1, index2 + 1)
        } else {
          this.addRessData.cityName = str.substring(index11 + 3, index2 + 1)
        }
      }

      let index3 = str.indexOf('区', 7)

      if (index3 == -1) {
        index3 = str.indexOf('县')
        this.addRessData.areaName = str.substring(index2 + 1, index3 + 1)
      } else {
        this.addRessData.areaName = str.substring(index2 + 1, index3 + 1)
      }

      this.cityChange(this.addRessData.cityName, 'first')
      let address = (
        this.addRessData.provinceName +
        this.addRessData.cityName +
        this.addRessData.areaName
      ).length
      this.addRessData.house = str.slice(address)
      return this.addRessData
    },
    cityChange(val, type) {
      this.areaNameData = []
      if (!type) {
        delete this.afterSaleDetail.areaName
      }
      for (let i = 0; i < this.cityNameData.length; i++) {
        if (this.cityNameData[i].label == val) {
          this.areaNameData = this.cityNameData[i].child
        }
      }
    },
    areaChange(val) {
      for (let i = 0; i < this.areaNameData.length; i++) {
        if (this.areaNameData[i].label == val) {
          this.areaCode = this.areaNameData[i].value
        }
      }
    },
    saleList() {
      this.listDialog = true
    },
    //查看快递详情
    viewExpressDetail(val) {
      this.expressDetailDialog = true
      this.expressNo = val
    },
    isEditInstallHandle() {
      let params = {
        id: this.afterSaleDetail.id,
      }
      this.$request({
        url: this.$interfaces.isEditInstall,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          this.installEditFlag = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //快递信息修改
    updateMailNo() {
      let params = {
        id: this.afterSaleDetail.id,
        logisticsCompany: this.afterSaleDetail.expressInfo.logisticsCompany,
        logisticsNo: this.afterSaleDetail.expressInfo.logisticsNo,
      }
      if (!params.logisticsCompany || !params.logisticsNo) {
        this.$message.warning('请完善自行寄回物流信息')
        return
      }
      console.log(params, '<<---------params')
      this.startLoading()
      this.$request({
        url: this.$interfaces.expressUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('物流信息修改成功！')
            this.$emit('getDetail')
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //修改收货地址
    updateAddress() {
      if (
        !this.addRessData.provinceName ||
        !this.addRessData.cityName ||
        !this.addRessData.areaName ||
        !this.addRessData.house
      ) {
        this.$message.warning('请完善地址信息')
        return
      }
      let params = {
        id: this.afterSaleDetail.id,
        address:
          this.addRessData.provinceName +
          this.addRessData.cityName +
          this.addRessData.areaName +
          this.addRessData.house,
        areaCode: this.areaCode,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.addressUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('收货地址修改成功！')
            this.$emit('getDetail')
            this.getArea(this.afterSaleDetail.equipmentReceiveAddress)
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    checkChange() {
      if (this.renewalEquipmentSelected.length == 2) {
        this.formData.exchangeEquipment = '2'
        this.afterSaleDetail.renewalInfo.renewalEquipment = '2'
      }
      if (this.renewalEquipmentSelected.length == 1) {
        if (this.renewalEquipmentSelected[0] == 'OBU') {
          this.formData.exchangeEquipment = '1'
          this.afterSaleDetail.renewalInfo.renewalEquipment = '1'
        } else {
          this.formData.exchangeEquipment = '0'
          this.afterSaleDetail.renewalInfo.renewalEquipment = '0'
        }
      }
      if (this.renewalEquipmentSelected.length == 0) {
        this.formData.exchangeEquipment = ''
        this.afterSaleDetail.renewalInfo.renewalEquipment = ''
      }
    },
    //更新售后审核结果
    update() {
      if (
        !this.formData.exchangeEquipment ||
        !this.afterSaleDetail.renewalInfo.renewalType ||
        this.afterSaleDetail.renewalInfo.renewalType == 'null' ||
        !this.afterSaleDetail.renewalInfo.frankingBy
      ) {
        this.$message.error('请完善售后结果信息')
        return
      }
      let params = {
        exchangeEquipment: this.formData.exchangeEquipment,
        exchangeType: this.afterSaleDetail.renewalInfo.renewalType,
        frankingBy: this.afterSaleDetail.renewalInfo.frankingBy,
        id: this.afterSaleDetail.id,
        type: '1',
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('售后审核结果修改成功！')
            this.$emit('getDetail')
          } else {
            this.endLoading()

            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getExpandStatus() {
      if (this.isExpand == null) {
        this.isExpand = !(
          this.afterSaleDetail.businessType == '1' ||
          this.afterSaleDetail.businessType == '2'
        )
      } else {
        this.isExpand = !this.isExpand
      }
    },
    deviceTypeFilter(val) {
      for (let i = 0; i < this.refundGoodsType.length; i++) {
        if (val == this.refundGoodsType[i].value) {
          return this.refundGoodsType[i].label
        }
      }
      return '卡签套装'
    },
    spliceImg() {
      let urlList = this.afterSaleDetail.filePathData.split(',')
      this.imgList = urlList.map((item, index) => {
        return {
          isShow: true,
          file_url: item,
          file_serial: '',
          photo_code: 'applicationCertificate' + index,
          lable: '申请凭证',
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>