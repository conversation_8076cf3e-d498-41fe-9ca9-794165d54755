<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行——用户订单信息
  * @author:zhang<PERSON>
  * @date:2023/03/01 10:40:06
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     size="medium"
                     title="开户人信息"
                     class="descriptions-content">
      <template slot="extra">
        <div @click="isExpand = !isExpand"
             class="expand">
          <i class="expand-icon"
             :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </div>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="客户名称">
          {{ currnetDeatil.customerName }}
        </el-descriptions-item>

        <el-descriptions-item label="客户类型">
          {{ getcustomerType(currnetDeatil.customerType) }}
        </el-descriptions-item>

        <el-descriptions-item label="证件类型">
          <div v-if="currnetDeatil.customerType == '1'">
            {{ getenterpriseOCRType(currnetDeatil.certificateType) }}
          </div>
          <div v-if="currnetDeatil.customerType == '0'">
            {{ getPersonalOCRType(currnetDeatil.certificateType) }}
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="证件号">
          {{ currnetDeatil.certificateNo }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ currnetDeatil.contactNo }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <archivesBox v-if="isExpand&&Object.keys(this.currnetDeatil).length != 0"
                 previewMode
                 uploadType="CACHEIMGUPLAOD"
                 :pictureSource="pictureSource"
                 style="padding: 0 16px;"></archivesBox>
  </div>
</template>

<script>
import {
  getcustomerType,
  getPersonalOCRType,
  getenterpriseOCRType,
  gxCardTypeFilter,
} from '@/common/method/formatOptions'
import archivesBox from '../../../component/photograph.vue'
export default {
  props: {
    userInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    userInfo(val) {
      this.currnetDeatil = {
        ...val.orderInfo,
        ...val.customerInfo,
      }
      if (Object.keys(this.currnetDeatil).length != 0) {
        this.init()
        this.getExpandStatus(this.currnetDeatil)
      }
    },
  },
  data() {
    return {
      currnetDeatil: {},
      isExpand: null,
      pictureSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'identityCardFront',
          lable: '开户人身份证正面',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'identityCardBack',
          lable: '开户人身份证反面',
        },
      ],
    }
  },

  components: {
    archivesBox,
  },

  computed: {},
  created() {},
  methods: {
    getcustomerType,
    getPersonalOCRType,
    getenterpriseOCRType,
    gxCardTypeFilter,
    init() {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.currnetDeatil[this.pictureSource[i]['photo_code']]) {
          this.pictureSource[i]['file_url'] =
            this.currnetDeatil[this.pictureSource[i]['photo_code']]
        }
      }
    },
    getExpandStatus(val) {
      if (val.isView) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null || !val.isView) {
        this.isExpand = !(val.businessType == '4' || val.businessType == '3')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>
