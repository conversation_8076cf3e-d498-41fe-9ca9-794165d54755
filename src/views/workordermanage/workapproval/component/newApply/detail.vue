<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行——详情
  * @author:zhangys
  * @date:2023/03/01 10:40:06
-->
<template>
  <div class="detail-wrap">
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <basicOrderInfo :orderInfo="orderBasicInfo"></basicOrderInfo>
    </div>

    <!-- 开户人信息&档案 -->
    <div class="orderItem">
      <customerComponent :userInfo="orderInfo"></customerComponent>
    </div>

    <!-- 车辆信息&档案 -->
    <div class="orderItem" ref="vehicleSection">
      <vehicleComponent
        :orderDetail="orderInfo"
        @getDetail="getDetail"
        @updateFlagHandle="updateFlagHandle"
      ></vehicleComponent>
    </div>

    <!-- 售后信息 -->
    <div ref="infoSection">
      <div
        class="orderItem"
        v-if="
          orderInfo &&
          orderInfo.orderInfo &&
          (orderInfo.orderInfo.businessType == '4' ||
            orderInfo.orderInfo.businessType == '3')
        "
      >
        <!-- -->
        <afterSaleInfo
          :afterSaleIData="orderInfo"
          @getDetail="getDetail"
          @changeAfterSaleInfo="changeAfterSaleInfo"
        ></afterSaleInfo>
      </div>
    </div>
    <!-- 安装方式信息
        审核通过后不允许更改
    -->
    <div class="orderItem">
      <InstallationComponent
        :detail="orderInfo"
        @getDetail="getDetail"
        @updateFlagHandle="updateWlFlagHandle"
      ></InstallationComponent>
    </div>

    <!-- 快递物流信息 -->
    <div class="orderItem">
      <mailInfo :detail="orderInfo"></mailInfo>
    </div>

    <!-- 支付信息 -->
    <div class="orderItem">
      <payInfo :orderDetail="orderInfo"></payInfo>
    </div>

    <!-- 开票信息 -->
    <div class="orderItem">
      <invoiceInfo :orderDetail="orderInfo"></invoiceInfo>
    </div>

    <!-- 订单履历 -->

    <div class="orderItem">
      <orderHistory :orderDetail="orderInfo"></orderHistory>
    </div>

    <!-- 处理记录 -->
    <div class="orderItem">
      <dealRecord :orderDetail="orderInfo"></dealRecord>
    </div>

    <!-- 订单取消原因 -->
    <div class="orderItem" v-if="orderInfo.business_type == '2'">
      <el-descriptions
        :column="3"
        border
        title="订单取消原因"
        class="descriptions-content"
      >
        <el-descriptions-item label="订单取消原因" labelStyle="width:120px">
          {{ orderInfoDetail.cancelRemark }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 驳回原因 -->
    <div class="orderItem" v-if="orderInfo.node_status == 4">
      <el-descriptions
        :column="3"
        border
        title="驳回原因"
        class="descriptions-content"
      >
        <el-descriptions-item label="驳回原因" labelStyle="width:120px">
          {{ orderInfo.reviewComments }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="orderItem" v-if="!isView">
      <el-descriptions :column="1" border title="" class="descriptions-content">
        <el-descriptions-item label="常用语" labelStyle="width:120px">
          <el-select
            @change="tipsChange"
            v-model="remarkText"
            placeholder="请选择"
            style="width: 80%"
          >
            <el-option
              v-for="(value, key) in remarkTips"
              :label="value.label"
              :value="value.value"
              :key="key"
            >
            </el-option>
          </el-select>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions :column="1" border title="" class="descriptions-content">
        <el-descriptions-item label="备注" labelStyle="width:120px">
          <el-input type="textarea" v-model="remark"></el-input>
        </el-descriptions-item>
      </el-descriptions>
      <div class="foot g-flex g-flex-center">
        <!-- 
        新办待审核
        按钮显示条件：未审核
        
        审核不通过/取消订单时，备注为必填项

        设备已发货可申请退货

        审核不通过&未审核均可取消订单

     -->
        <div v-if="orderBasicInfo.nodeCode < 1010">
          <el-button
            size="medium"
            type="primary"
            @click="operatorHandle('accept')"
            >申请通过</el-button
          >
          <el-button
            style="margin-left: 30px"
            size="medium"
            type="warning"
            @click="showMsgBox('operatorHandle')"
            >申请不通过</el-button
          >
        </div>
        <div
          style="margin: 0 30px"
          v-if="
            orderBasicInfo.nodeCode < 1010 ||
            orderBasicInfo.nodeCode == '1010' ||
            orderBasicInfo.nodeCode == '1020'
          "
        >
          <el-button size="medium" @click="showMsgBox('applyCancel')"
            >取消订单</el-button
          >
        </div>
        <div
          style="margin: 0 30px"
          v-if="
            orderBasicInfo.nodeCode == '1020' ||
            orderBasicInfo.nodeCode == '1030'
          "
        >
          <el-button type="primary" @click="refundGoodsDialog = true"
            >申请退货</el-button
          >
        </div>

        <!-- 
        售后审核操作

     -->
        <div v-if="orderBasicInfo.nodeCode == '2000'">
          <el-button
            size="medium"
            type="primary"
            @click="operatorSaleHandle('accept')"
            >申请通过</el-button
          >
          <el-button
            style="margin-left: 30px"
            size="medium"
            type="warning"
            @click="showMsgBox('operatorSaleHandle')"
            >申请不通过</el-button
          >
        </div>
        <div
          style="margin: 0 30px"
          v-if="
            orderBasicInfo.nodeCode == '2000' ||
            ((orderBasicInfo.nodeCode == '2010' ||
              orderBasicInfo.nodeCode == '2020') &&
              orderBasicInfo.nodeStatus == '4') ||
            (orderBasicInfo.nodeCode == '2020' &&
              orderInfo.postSaleInfo.equipmentReturnType == '0')
          "
        >
          <el-button size="medium" @click="showMsgBox('afterCancel')"
            >撤销售后申请</el-button
          >
        </div>
        <div
          style="margin: 0 30px"
          v-if="
            (orderBasicInfo.nodeCode == '2020' ||
              orderBasicInfo.nodeCode == '2030' ||
              orderBasicInfo.nodeCode == '2040' ||
              orderBasicInfo.nodeCode == '2400') &&
            orderBasicInfo.nodeStatus == '3'
          "
        >
          <el-button type="primary" @click="receiveGoods">确认收货</el-button>
        </div>
        <div style="margin: 0 15px">
          <el-button
            type="primary"
            v-if="
              (orderBasicInfo.nodeCode == '2060' ||
                (orderBasicInfo.nodeCode == '2080' &&
                  orderInfo.postSaleInfo.renewalInfo.renewalType == '1') ||
                orderBasicInfo.nodeCode == '2090') &&
              orderBasicInfo.businessType == '3'
            "
            v-permisaction="['release:amountUpdate']"
            @click="showMsgBox('refundMoney')"
            >全额退款</el-button
          >
        </div>
      </div>
    </div>

    <refundGoods
      v-if="refundGoodsDialog"
      :visible.sync="refundGoodsDialog"
      :orderInfo="orderInfo"
      @refreshDetail="getDetail"
    >
    </refundGoods>
    <msgBox
      :visible.sync="dialogVisible"
      :msgBoxType="msgBoxType"
      :remark.sync="remark"
      @msgBoxHandle="msgBoxHandle"
    ></msgBox>
    <moreUserChoose
      v-if="dialogChooseVisible"
      :visible.sync="dialogChooseVisible"
      :multipleSelection="multipleSelection"
      @chooseUser="chooseUser"
    ></moreUserChoose>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import customerComponent from './customer.vue'
import vehicleComponent from './vehicle.vue'
import InstallationComponent from './Installation.vue'
import mailInfo from './mailInfo.vue'
import basicOrderInfo from './basicOrderInfo'
import refundGoods from './refundGoods'
import afterSaleInfo from './afterSaleInfo'
import payInfo from './payInfo'
import invoiceInfo from './invoiceInfo'
import orderHistory from './orderHistory'
import dealRecord from './dealRecord'
import msgBox from '../msgBox/msgBox.vue'
import moreUserChoose from '../moreUserChoose/moreUserChoose.vue'
export default {
  components: {
    customerComponent,
    vehicleComponent,
    InstallationComponent,
    mailInfo,
    basicOrderInfo,
    refundGoods,
    afterSaleInfo,
    payInfo,
    orderHistory,
    dealRecord,
    msgBox,
    invoiceInfo,
    moreUserChoose,
  },
  props: {
    applyId: [String, Number],
    slideVisible: {
      type: Boolean,
      default: '',
    },
    isView: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    slideVisible(val) {
      if (val) {
        this.remark = ''
        this.getDetail()
        if (!this.isView) {
          this.orderLock()
        }
      }
      if (!val) {
        this.$emit('refreshList')
        if (!this.isView) {
          if (!this.orderIsLock) {
            this.orderOpen()
          }
        }
      }
    },
    remark(val) {
      //兼容重新选择常用语
      if (this.remarkText) {
        this.remarkText = ''
      }
    },
  },
  data() {
    return {
      dialogVisible: false,
      dialogChooseVisible: false,
      orderInfoDetail: {},
      orderInfo: {},
      rejectVisible: false,
      loading: false,
      orderStatus: 1,
      refundGoodsDialog: false,
      afterSaleIData: {},
      id: '',
      remark: '',
      orderBasicInfo: {},
      orderIsLock: false,
      changeSaleInfo: {},
      remarkTips: [],
      remarkText: '',
      msgBoxType: '', //弹框类型
      multipleSelection: [], //一证多户用户数据
      chooseType: '', //一证多户类型数据
      updateFlag: false, //修改车辆信息返回
      updateWlFlag: false, //修改物流信息返回
    }
  },
  created() {},
  methods: {
    //   订单锁定
    orderLock() {
      let params = {
        id: this.applyId,
      }
      this.$request({
        url: this.$interfaces.orderLock,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res && res.code == 200) {
            this.orderIsLock = false
          } else {
            this.orderIsLock = true
            this.$emit('closeDartSlide')
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {})
    },
    //订单释放
    orderOpen() {
      let params = {
        id: this.applyId,
      }
      this.$request({
        url: this.$interfaces.orderOpen,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {})
    },
    //获取订单详情
    getDetail(type) {
      //修改车辆信息/取货信息返回flag
      if (type == 'vehicle') {
        this.updateFlag = false
      }
      if (type == 'wl') {
        this.updateWlFlag = false
      }
      let params = {
        id: this.applyId,
      }
      this.orderInfo = {}
      this.orderBasicInfo = {}
      this.startLoading()
      this.$request({
        url: this.$interfaces.applyOrderDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.orderBasicInfo = res.data.orderInfo
            console.log('orderBasicInfo')
            this.orderInfo = res.data
            this.orderInfo.orderInfo.isView = this.isView

            this.remark = ''

            //获取常用提示语
            this.getTips()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    getTips() {
      console.log('没进来吗1')
      let remarkArr = []
      //审核不通过(新办)
      if (this.orderBasicInfo.nodeCode < 1010) {
        console.log('没进来吗2')
        remarkArr = [
          {
            key: '0',
            value:
              '未按要求提供授权书，授权书模板可通过车主证件上传页面右上方下载',
          },
          {
            key: '1',
            value: '授权书未按手印或未盖章，请修改后重新上传',
          },
          {
            key: '2',
            value:
              '很抱歉，当前仅支持广西区内用户在线申办，请确保所填地址为广西区内有效地址',
          },
          {
            key: '3',
            value: '道路运输证不清晰，请重新拍照上传',
          },
          {
            key: '4',
            value: '用户来电要求取消订单',
          },
        ]
      } else if (this.orderBasicInfo.nodeCode == '2000') {
        console.log('没进来吗3')
        remarkArr = [
          {
            key: '0',
            value: '设备编号不清晰，请重新上传带有编号的ETC卡及设备照片',
          },
          {
            key: '1',
            value: '用户来电要求撤销售后申请',
          },
        ]
      } else if (
        this.orderBasicInfo.nodeCode == '2000' ||
        ((this.orderBasicInfo.nodeCode == '2010' ||
          this.orderBasicInfo.nodeCode == '2020') &&
          this.orderBasicInfo.nodeStatus == '4') ||
        (this.orderBasicInfo.nodeCode == '2020' &&
          this.orderInfo.postSaleInfo.equipmentReturnType == '0')
      ) {
        console.log('没进来吗4')
        remarkArr = [
          {
            key: '0',
            value: '用户来电要求撤销售后申请',
          },
        ]
      } else if (
        this.orderBasicInfo.nodeCode < 1010 ||
        this.orderBasicInfo.nodeCode == '1010' ||
        this.orderBasicInfo.nodeCode == '1020'
      ) {
        console.log('没进来吗5')
        remarkArr = [
          {
            key: '0',
            value: '用户来电要求取消订单',
          },
        ]
      }
      this.remarkTips = remarkArr
    },
    tipsChange(event) {
      console.log('envent', event)
      this.remark = event
    },
    chooseCustomerId(callback) {
      let params = {
        id: this.orderInfo.orderInfo.id,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.chooseCustomerId,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()

          if (res.code == 200) {
            this.endLoading()
            if (res.data && res.data.length > 0) {
              //有数据的时候
              callback(res.data)
            } else {
              //无数据返回false
              callback(false)
            }
          } else {
            this.endLoading()
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    chooseUser(custMastId) {
      this.dialogChooseVisible = false
      this.opertorAction(this.chooseType, custMastId)
    },
    operatorHandle(val) {
      if (val == 'accept') {
        if (this.updateFlag || this.updateWlFlag) {
          //审核的时候忘记按修改按钮了提示
          // this.$message.warning('车辆信息/提货信息内容已被变更，请前往确认修改。')
          this.$confirm(
            '车辆信息/提货信息内容已被变更，请前往确认修改。',
            '提示',
            {
              confirmButtonText: '前往修改',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              console.log('前往修改')
              this.scrollToVehicle()
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消操作',
              })
            })
        } else {
          //申请前判断ETC用户是否多个
          this.chooseCustomerId((res) => {
            if (res) {
              //弹出选择对话框
              this.multipleSelection = res
              this.chooseType = val
              this.dialogChooseVisible = true
            } else {
              this.opertorAction(val)
            }
          })
        }
      } else {
        this.opertorAction(val)
      }
    },
    updateFlagHandle(flag) {
      this.updateFlag = flag
    },
    updateWlFlagHandle(flag) {
      this.updateWlFlag = flag
    },
    scrollToVehicle() {
      // 确保 DOM 更新完成后再滚动
      this.$nextTick(() => {
        let container
        if (this.updateFlag && this.updateWlFlag) {
          container = this.$refs.vehicleSection
        } else if (!this.updateFlag && this.updateWlFlag) {
          container = this.$refs.infoSection
        } else {
          container = this.$refs.vehicleSection
        }
        console.log('container', container)
        if (container) {
          container.scrollIntoView({
            behavior: 'smooth', // 平滑滚动效果
            block: 'start', // 对齐到视口顶部
          })
        }
      })
    },
    opertorAction(val, custMastId) {
      let params = {
        checkFlag: '0',
        checkType: '1',
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
        custMastId: custMastId || '',
      }
      if (val == 'accept') {
        params.checkFlag = '0'
      }
      if (val == 'reject') {
        params.checkFlag = '1'
      }
      console.log('prams', params)
      // if (params.checkFlag == '1' && !params.remark) {
      //   this.$message.warning('请输入备注！')
      //   return
      // }

      this.startLoading()
      this.$request({
        url: this.$interfaces.orderCheck,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    showMsgBox(msgBoxType) {
      this.msgBoxType = msgBoxType
      this.dialogVisible = true
    },
    //操作前确认
    msgBoxHandle(msgBoxType) {
      this.dialogVisible = false
      switch (msgBoxType) {
        case 'applyCancel':
          this.applyCancel()
          break
        case 'operatorSaleHandle':
          //售后审核不通过
          this.operatorSaleHandle('reject')
          break
        case 'operatorHandle':
          //新办审核不通过
          this.operatorHandle('reject')
          break
        case 'afterCancel':
          //撤销售后申请
          this.afterCancel()
          break
        case 'refundMoney':
          //全额退款
          this.refundMoney()
          break
        default:
          break
      }
    },
    //订单取消
    applyCancel() {
      let params = {
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
      }

      // if (!params.remark) {
      //   this.$message.warning('请输入备注！')
      //   return
      // }
      this.startLoading()
      this.$request({
        url: this.$interfaces.orderCancel,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()

          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    changeAfterSaleInfo(val) {
      this.changeSaleInfo = val
      console.log(this.changeSaleInfo, '<<---------this.changeSaleInfo')
    },
    //售后审核通过、不通过
    operatorSaleHandle(val) {
      let params = {
        checkFlag: '0',
        checkType: '1',
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
      }

      if (val == 'accept') {
        params.checkFlag = '0'
      }
      if (val == 'reject') {
        params.checkFlag = '1'
      }
      // if (params.checkFlag == '1' && !params.remark) {
      //   this.$message.warning('请输入备注！')
      //   return
      // }
      if (
        params.checkFlag == '0' &&
        (!this.changeSaleInfo.frankingBy ||
          !this.changeSaleInfo.renewalEquipment ||
          this.changeSaleInfo.renewalEquipment == 'null' ||
          !this.changeSaleInfo.renewalType)
      ) {
        this.$message.error('请完善售后审核结果信息')
        return
      }
      if (params.checkFlag == '0') {
        this.updateSaleResult(params)
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleAudit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()

          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')

            this.getDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    updateAndOperator(val) {
      this.$request({
        url: this.$interfaces.afterSaleAudit,
        method: 'post',
        data: val,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()
            this.getDetail()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    updateSaleResult(val) {
      let params = {
        exchangeEquipment: this.changeSaleInfo.renewalEquipment,
        exchangeType: this.changeSaleInfo.renewalType,
        frankingBy: this.changeSaleInfo.frankingBy,
        id: this.orderInfo.orderInfo.id,
        // type: this.orderBasicInfo.nodeCode < 2000 ? '0' : '1',
        // type: '1',
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.updateAndOperator(val)
          } else {
            this.endLoading()

            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },

    //撤销售后申请
    afterCancel() {
      let params = {
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
      }

      // if (!params.remark) {
      //   this.$message.warning('请输入备注！')
      //   return
      // }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterCancel,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    //确认收货
    receiveGoods() {
      let params = {
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
      }

      if (!params.remark) {
        this.$message.warning('请输入备注！')
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.receiveGoods,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            if (this.orderBasicInfo.businessType != '3') {
              this.endLoading()
              this.getDetail()
              return
            }
            if (res.data) {
              this.$confirm('该订单存在可退款金额，请确定是否退款', '提示', {
                confirmButtonText: '确认退款',
                cancelButtonText: '确认不退款',
                type: 'warning',
              })
                .then(() => {
                  this.isRefund(true)
                })
                .catch(() => {
                  this.isRefund(false)
                })
            } else {
              this.isRefund(false)
            }
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    //是否退款
    isRefund(val) {
      let params = {
        id: this.orderInfo.orderInfo.id,
        flag: val,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.isRefundMoney,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    //全额退款
    refundMoney() {
      let params = {
        id: this.orderInfo.orderInfo.id,
        remark: this.remark,
      }

      // if (!params.remark) {
      //   this.$message.warning('请输入备注！')
      //   return
      // }
      this.$confirm('是否确定全额退款给用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.startLoading()
          this.$request({
            url: this.$interfaces.refundAllMoney,
            method: 'post',
            data: params,
          })
            .then((res) => {
              if (res.code == 200) {
                this.endLoading()
                this.$message.success('操作成功！')
                this.getDetail()
              } else {
                this.endLoading()

                this.$message({
                  message: res.msg,
                  type: 'error',
                })
              }
            })
            .catch(() => {
              this.endLoading()
            })
        })
        .catch(() => {})
    },
  },
}
</script>
<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>
