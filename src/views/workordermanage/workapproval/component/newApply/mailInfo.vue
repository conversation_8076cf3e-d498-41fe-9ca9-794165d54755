<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--物流信息展示
  * @author:zhang<PERSON>
  * @date:2022/11/03 16:57:17
-->
<template>
  <div>
    <el-descriptions :column="5"
                     border
                     title="物流信息"
                     class="descriptions-content"
                     style="padding:20px 20px 0 20px">
      <template slot="extra">
        <el-button type="text"
                   @click="isExpand=!isExpand">
          <i class="expand-icon"
             :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>

      <template v-if="isExpand">
        <template v-if="mailInfo.length!=0">
          <template v-for="(item,index) in mailInfo">
            <el-descriptions-item label="物流公司">
              {{item.logisticsCompany }}
            </el-descriptions-item>
            <el-descriptions-item label="物流单号">
              {{item.logisticsNo}}
              <el-button size="mini"
                         type="text"
                         style="margin-right:10px"
                         @click="viewExpressDetail(item.logisticsNo)">{{item.logisticsNo?'物流详情':''}}</el-button>

              <el-button size="mini"
                         type="text"
                         @click="viewExpressStatus(item.logisticsNo)">{{item.logisticsNo?'查看状态':''}}</el-button>
            </el-descriptions-item>
            <el-descriptions-item label="最新状态">
              {{ getExpressStatus(item.logisticsNode)}}
            </el-descriptions-item>
          </template>
        </template>
        <template v-else>

          <el-descriptions-item label="物流公司">

          </el-descriptions-item>
          <el-descriptions-item label="物流单号">

          </el-descriptions-item>
          <el-descriptions-item label="最新状态">

          </el-descriptions-item>

        </template>
      </template>

    </el-descriptions>

    <expressDetail :visible.sync="expressDetailDialog"
                   :v-if="expressDetailDialog"
                   :expressNo="expressNo"></expressDetail>

    <el-dialog title="物流状态"
               :close-on-click-modal="false"
               :visible.sync="dialogVisible"
               center
               append-to-body
               modal-append-to-body
               width="65%">
      <div>
        <el-table :data="tableData"
                  align="center"
                  header-align="center"
                  border
                  style="width: 100%; margin-bottom: 10px">
          <el-table-column prop="waybillNo"
                           align="center"
                           label="运单号" />

          <el-table-column prop="orderStateDesc"
                           align="center"
                           label="订单状态" />
          <el-table-column prop="netCode"
                           align="center"
                           label="网点" />

          <el-table-column prop="lastTime"
                           align="center"
                           label="最晚上门时间" />
          <el-table-column prop="bookTime"
                           align="center"
                           label="客户预约时间" />
          <el-table-column prop="carrierCode"
                           align="center"
                           label="承运商编码" />
        </el-table>
      </div>
    </el-dialog>
  </div>

</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import expressDetail from './expressDetail.vue'
import { getExpressStatus } from '@/common/method/formatOptions.js'
export default {
  name: '',
  props: {
    mailNo: {
      type: String,
    },
    detail: {
      type: Object,
      default() {
        return {}
      },
    },
    afterSaleIData: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  components: { expressDetail },
  data() {
    return {
      expressInfo: null,

      isExpand: null,
      expressDetailDialog: false,
      mailInfo: [],
      expressNo: '',
      dialogVisible: false,
      tableData: [],
    }
  },
  computed: {},
  watch: {
    detail(val) {
      if (Object.keys(this.detail).length != 0) {
        this.mailInfo = this.detail.expressInfo
        this.getExpandStatus(this.detail.orderInfo)
      }
    },
  },

  created() {},
  methods: {
    getExpressStatus,
    //查看快递详情
    viewExpressDetail(val) {
      this.expressDetailDialog = true
      this.expressNo = val
    },
    //查看物流详情
    viewExpressStatus(val) {
      let params = {
        waybillNos: val,
      }
      this.tableData = []
      this.startLoading()
      this.$request({
        url: this.$interfaces.queryExpressStatus,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.tableData = res.data
            this.dialogVisible = true
          } else {
            this.endLoading()
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getExpandStatus(val) {
      if (val.isView && this.mailInfo.length != 0) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null) {
        this.isExpand = !(val.businessType == '3' || val.businessType == '4')
      }
    },
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';

.no-info {
  background-color: #fff;
  padding: 10px 0;
}
</style>