<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--售后列表
  * @author:zhang<PERSON>
  * @date:2023/03/23 16:46:34
-->

<template>
  <div>
    <el-dialog title="设备售后信息列表"
               :close-on-click-modal="false"
               :visible.sync="dialogVisible"
               center
               append-to-body
               @close='closeDialog'
               width="80%">
      <div class="dialog-content">
        <div v-for="(item,index) in dataList"
             :key="index"
             style="border: 1px solid #ebeef5;margin-bottom: 10px;">
          <el-descriptions :column="4"
                           border
                           title="售后申请信息"
                           class="descriptions-content"
                           style="padding: 20px 20px 0 20px;position:relative">

            <template>
              <el-descriptions-item label="售后类型"
                                    labelStyle="width:120px">
                {{item.type=='0'?'退货':'换货'}}
              </el-descriptions-item>

              <el-descriptions-item label="申请时间">
                {{item.createdTime}}
              </el-descriptions-item>

              <el-descriptions-item label="换货设备">
                {{deviceTypeFilter(item.deviceType)}}
              </el-descriptions-item>

              <el-descriptions-item label="换货原因">
                {{item.reason}}
              </el-descriptions-item>

              <el-descriptions-item label="补充描述"
                                    span='4'>
                {{item.remarks}}
              </el-descriptions-item>

              <el-descriptions-item label="设备退回方式"
                                    span="2"
                                    labelStyle="width:120px">
                {{item.routeType=='0'?'上门取件':'自行寄回'}}
              </el-descriptions-item>

              <el-descriptions-item label="取件时间"
                                    labelStyle="width:120px"
                                    v-if="item.routeType=='0'">
                {{item.pickUpTime}}
              </el-descriptions-item>

              <el-descriptions-item label="换货原因"
                                    labelStyle="width:120px">
                {{item.reason}}
              </el-descriptions-item>

              <el-descriptions-item label="取件地址"
                                    span="4"
                                    v-if="item.routeType=='0'">
                {{item.pickUpAddress}}
              </el-descriptions-item>

              <el-descriptions-item label="新设备收货地址"
                                    span="4"
                                    labelStyle="width:120px"
                                    v-if="item.deviceType=='1'">
                {{ item.address }}
              </el-descriptions-item>

            </template>
          </el-descriptions>

          <el-descriptions :column="3"
                           border
                           class="descriptions-content"
                           style="padding: 0 20px 0 20px; border: 1px solid #ebeef5;margin:0 16px 16px 16px;"
                           title="用户寄件信息">
            <template>
              <el-descriptions-item label="物流公司">
                {{item.expressInfo.logisticsCompany}}
              </el-descriptions-item>
              <el-descriptions-item label="物流单号">
                {{item.expressInfo.logisticsNo}}

              </el-descriptions-item>

              <el-descriptions-item label="寄件时间">
                {{item.expressInfo.sendTime}}

              </el-descriptions-item>

              <el-descriptions-item label="收货时间">
                {{item.expressInfo.receiveTime}}

              </el-descriptions-item>
              <el-descriptions-item label="物流状态">
                {{getExpressStatus(item.expressInfo.logisticsNode)}}

              </el-descriptions-item>

            </template>
          </el-descriptions>

          <div class="archives">
            <div class="archives-box">

              <archivesBox uploadType="CACHEIMGUPLAOD"
                           v-if="item.filePathData.length!=0"
                           :pictureSource="spliceImg(item.filePathData)"
                           previewMode></archivesBox>

            </div>
          </div>

        </div>
      </div>
    </el-dialog>

  </div>
</template>
  
  <script>
import request from '@/utils/request'
import api from '@/api/index'
import {
  refundReason,
  refundGoodsType,
  afterSaleType,
} from '@/common/const/optionsData.js'
import {
  getAfterSaleType,
  getExpressStatus,
} from '@/common/method/formatOptions.js'
import archivesBox from '../../../component/photograph.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    afterSaleDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  data() {
    return {
      dialogVisible: false,
      imgList: [],
      afterSaleType,
      renewalEquipmentSelected: [],
      pictureSource: [],
      listDialog: false,
      dataList: [],
      refundGoodsType,
    }
  },

  watch: {
    visible(val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: { archivesBox },

  computed: {},
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
      this.getSaleList()
    })
  },
  methods: {
    getAfterSaleType,
    getExpressStatus,
    closeDialog() {},
    spliceImg(val) {
      if (!val) return
      let urlList = val.split(',')
      let arr = []
      arr = []
      arr = urlList.map((item, index) => {
        return {
          isShow: true,
          file_url: item,
          file_serial: '',
          photo_code: 'applicationCertificate' + index,
          lable: '申请凭证',
        }
      })
      return arr
    },
    getSaleList() {
      let params = {
        id: this.afterSaleDetail.id,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.saleList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.dataList = res.data
            if (this.dataList.length == 0) {
              this.$message.warning('暂无记录！')
              this.dialogVisible = false
            }
            this.endLoading()
          } else {
            this.endLoading()

            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    deviceTypeFilter(val) {
      for (let i = 0; i < this.refundGoodsType.length; i++) {
        if (val == this.refundGoodsType[i].value) {
          return this.refundGoodsType[i].label
        }
      }
      return '卡签套装'
    },
  },
}
</script>
  <style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
.descriptions-content {
  padding: 8px 16px !important;
}
.dialog-content {
  max-height: 600px;
  overflow-y: scroll;
}
</style>