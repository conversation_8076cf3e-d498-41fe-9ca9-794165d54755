<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行审核——基础订单信息
  * @author:zhangys
  * @date:2023/03/01 10:40:06
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     size="medium"
                     title="订单基础信息"
                     class="descriptions-content">

      <el-descriptions-item label="订单号">
        {{orderInfo.id}}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        {{orderInfo.orderStatus}}
      </el-descriptions-item>

      <el-descriptions-item label="订单类型">
        {{getbusinessType(orderInfo.businessType)}}
      </el-descriptions-item>

      <el-descriptions-item label="提交时间">
        {{orderInfo.orderTime}}
      </el-descriptions-item>

      <el-descriptions-item label="产品类型">
        {{getProductTypeOptions(orderInfo.productType)}}
      </el-descriptions-item>

      <el-descriptions-item label="支付权益费(元)">
        {{orderInfo.benefitServiceFee}}
      </el-descriptions-item>

      <el-descriptions-item label="申请渠道">
        {{getApplyChannelOptions(orderInfo.applyChannel)  }}
      </el-descriptions-item>

      <el-descriptions-item label="审核方式">
        {{getCheckTypeOptions(orderInfo.checkType)  }}
      </el-descriptions-item>

      <el-descriptions-item label="签约银行">
        {{orderInfo.signBank}}
      </el-descriptions-item>

      <el-descriptions-item label="签约时间">
        {{orderInfo.signTime}}
      </el-descriptions-item>

      <el-descriptions-item label="发行时间">
        {{orderInfo.issueTime}}
      </el-descriptions-item>

      <el-descriptions-item label="激活时间">
        {{orderInfo.activateTime}}
      </el-descriptions-item>

      <el-descriptions-item label="ETC卡号"
                            span="2">
        {{orderInfo.cardNo}}
      </el-descriptions-item>

      <el-descriptions-item label="OBU号" span="2">
        {{orderInfo.obuNo}}
      </el-descriptions-item>

      <el-descriptions-item label="设备类型">
        {{orderInfo.deviceType == 2?'进阶款':'基础款'}}
      </el-descriptions-item>

    </el-descriptions>
    <div style="padding:16px;">关联的{{orderInfo.businessType=='3'||orderInfo.businessType=='4'?'新办':'售后'}}订单</div>
    <div style="padding:0 16px 10px 16px">
      <el-table :data="tableData"
                align="center"
                header-align="center"
                border
                style="width: 100%; margin-bottom: 20px"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="id"
                         align="center"
                         label="订单号" />
        <el-table-column prop="orderStatus"
                         align="center"
                         label="订单状态">
        </el-table-column>
        <el-table-column prop="businessType"
                         align="center"
                         label="业务类型">
          <template slot-scope="scope">
            {{getbusinessType(scope.row.businessType)}}
          </template>
        </el-table-column>

        <el-table-column prop="createTime"
                         align="center"
                         label="下单时间">

        </el-table-column>

      </el-table>
    </div>
  </div>
</template>

<script>
import {
  getbusinessType,
  gxCardTypeFilter,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
} from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderInfo(val) {
      if (val) {
        this.tableData = this.orderInfo.relationOrder
      }
    },
  },
  created() {},
  methods: {
    getbusinessType,
    gxCardTypeFilter,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>