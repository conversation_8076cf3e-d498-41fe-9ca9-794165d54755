<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--商品退货
  * @author:zhangys
  * @date:2023/03/02 10:18:08
-->
<template>
  <div>
    <el-dialog
      title="线上发行前退货"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      center
      append-to-body
      @close="closeDialog"
      width="60%"
    >
      <div class="balance-pay">
        <el-form
          ref="formData"
          :model="formData"
          :rules="rules"
          label-width="120px"
        >
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="车牌号：" prop="vehicleNo">
                <el-input v-model="formData.vehicleNo" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车牌颜色：" prop="vehicleClolor">
                <el-select
                  v-model="formData.vehicleClolor"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(value, key) in licenseColorOption"
                    :label="value.label"
                    :value="value.value"
                    :key="key"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="姓名：" prop="custName">
                <el-input v-model="formData.custName" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号：" prop="certificateNo">
                <el-input v-model="formData.certificateNo" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="联系电话：" prop="contactNo">
                <el-input v-model="formData.contactNo" clearable></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="ETC卡号：" prop="cardNo">
                <el-input v-model="formData.cardNo" clearable>
                  <template slot="append">
                    <el-button type="primary" @click="readCardHandle"
                      >读卡</el-button
                    >
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="OBU号：" prop="obuNo">
                <el-input v-model="formData.obuNo" clearable>
                  <template slot="append">
                    <el-button type="primary" @click="readObuHandle"
                      >读签</el-button
                    >
                  </template></el-input
                >
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="设备外观：" prop="equipment">
                <el-select
                  v-model="formData.equipment"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(value, key) in appearanceList"
                    :label="value.label"
                    :value="value.value"
                    :key="key"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="需赔付设备：">
                <el-checkbox-group v-model="isPaySelected">
                  <el-checkbox
                    border
                    :disabled="formData.equipment != '1'"
                    @change="checkChange"
                    v-for="(item, index) in equipmentOptions"
                    :label="item.label"
                    :key="index"
                  ></el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="12">
              <el-form-item label="支付金额：" prop="payAmount">
                <div v-if="formData.equipment == '0'">0元</div>
                <div v-else>{{ moneyFilter(formData.payAmount) }}元</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-form-item label="申请附件：" prop="appendix">
              <div>
                <archivesBox
                  uploadType="CACHEIMGUPLAOD"
                  :pictureSource="pictureSource"
                  @on-upload="onUploadHandle"
                  @on-delete="onDeleteHandle"
                  :imgAreaStyle="archivesStyle"
                ></archivesBox>
              </div>
            </el-form-item>
          </el-row>
          <el-row :span="24">
            <el-col :span="20">
              <el-form-item label="备注：" prop="remark">
                <el-input type="textarea" v-model="formData.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="g-flex g-flex-center">
          <el-button @click="print">打印</el-button>
          <el-button @click="onSubmitHandle" type="primary">确认提交</el-button>
        </div>
      </div>
    </el-dialog>
    <div class="pdf-view" v-show="false" style="z-index: 100">
      <iframe id="previewPdf" :src="fileUrl" height="99%" width="100%">
      </iframe>
    </div>
  </div>
</template>
  
  <script>
import request from '@/utils/request'
import api from '@/api/index'
import electronicArchives from '@/components/photoGraph/photograph.vue'
var moment = require('moment')
import { licenseColorOption } from '@/common/const/optionsData.js'
import WsConsts from '@/utils/wsConsts'
import WsApi from '@/api/wsApi'
import float from '@/common/method/float.js'
import archivesBox from '../../../component/photograph.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderInfo: {
      type: Object,
      default() {
        return {}
      },
    },
  },

  data() {
    return {
      licenseColorOption,
      formData: {
        appendix: '',
        cardNo: '',
        certificateNo: '',
        contactNo: '',
        custName: '',
        equipment: '',
        id: 0,
        isPay: '',
        obuNo: '',
        payAmount: '',
        remark: '',
        vehicleClolor: '',
        vehicleNo: '',
      },
      archivesStyle: { height: '120px', width: '120px' },
      isPaySelected: [],
      dialogVisible: false,
      rules: {
        vehicleClolor: [
          { required: true, message: '请选择车牌颜色', trigger: 'blur' },
        ],
        vehicleNo: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
        ],
        obuNo: [
          { required: true, message: '请输入或读取OBU号', trigger: 'blur' },
        ],
        cardNo: [
          { required: true, message: '请输入或读取卡号', trigger: 'blur' },
        ],
        custName: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        contactNo: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的联系方式',
            trigger: 'blur',
          },
        ],

        certificateNo: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          {
            pattern: /^\d{18}$/,
            message: '请输入正确的身份证号',
            trigger: 'blur',
          },
        ],
        equipment: [
          { required: true, message: '请选择设备外观', trigger: 'blur' },
        ],
      },
      pictureSource: [
        {
          lable: '身份证正面',
          photo_code: '1',
          file_url: '',
          file_serial: '',
          md5Code: '',
          isShow: true,
        },
        {
          lable: '身份证反面',
          photo_code: '2',
          file_url: '',
          file_serial: '',
          md5Code: '',
          isShow: true,
        },
        {
          lable: 'ETC卡照片',
          photo_code: '3',
          file_url: '',
          file_serial: '',
          md5Code: '',
          isShow: true,
        },
        {
          lable: 'OBU照片',
          photo_code: '4',
          file_url: '',
          file_serial: '',
          md5Code: '',
          isShow: true,
        },
        {
          lable: '回执单',
          photo_code: '5',
          file_url: '',
          file_serial: '',
          md5Code: '',
          isShow: true,
          isRequire: true,
        },
      ],
      appearanceList: [
        { value: '0', label: '外观完好' },
        { value: '1', label: '已损坏' },
      ],
      equipmentOptions: [
        { value: '0', label: 'ETC卡' },
        { value: '1', label: 'OBU' },
      ],
      printFlag: false,
      fileUrl: '',
      cardFee: null, //设备卡金额
      obuFee: null, //设备OBU金额
    }
  },

  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    orderInfo(val) {},
  },
  components: { electronicArchives, archivesBox },

  computed: {},
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
    if (Object.keys(this.orderInfo).length != 0) {
      console.log(this.orderInfo, '<<---------this.orderInfo')
      this.formData.id = this.orderInfo.orderInfo.id
      this.formData.vehicleNo = this.orderInfo.carInfo.carNo
      this.formData.vehicleClolor = this.orderInfo.carInfo.carColor
    }
    //获取需支付设备金额
    this.getPayMoney()
  },
  methods: {
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    onUploadHandle(result) {
      if (result.data) {
        for (let i = 0; i < this.pictureSource.length; i++) {
          if (this.pictureSource[i].photo_code == result.data.photoCode) {
            this.pictureSource[i].file_url = result.data.fileUrl
            this.pictureSource[i].md5Code = result.data.md5Code
          }
        }
      }
    },
    onDeleteHandle(data) {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.pictureSource[i].photo_code == data.photo_code) {
          this.pictureSource[i].file_url = ''
          this.pictureSource[i].file_serial = ''
          this.pictureSource[i].md5Code = ''
        }
      }
    },
    checkChange() {
      if (this.isPaySelected.length == 2) {
        this.formData.isPay = '2'
      }
      if (this.isPaySelected.length == 1) {
        if (this.isPaySelected[0] == 'OBU') {
          this.formData.isPay = '1'
        } else {
          this.formData.isPay = '0'
        }
      }
      // this.getPayMoney()
      this.colMoney()
    },
    colMoney() {
      if (this.formData.isPay == '2') {
        this.formData.payAmount = parseInt(this.cardFee) + parseInt(this.obuFee)
      } else if (this.formData.isPay == '1') {
        this.formData.payAmount = this.obuFee
      } else if (this.formData.isPay == '0') {
        this.formData.payAmount = this.cardFee
      }

      console.log(' this.payAmount', this.formData.payAmount)
    },
    //获取需支付金额
    getPayMoney() {
      this.startLoading()
      let params = {
        id: this.formData.id,
      }
      this.$request({
        url: this.$interfaces.getPayMoney,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            // this.formData.payAmount = res.data
            console.log('res', res)
            this.cardFee = res.data.cardFee
            this.obuFee = res.data.obuFee
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    closeDialog() {
      this.dialogVisible = false
    },
    validate() {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (
          !this.pictureSource[i].file_url &&
          !this.pictureSource[i].isRequire
        ) {
          this.$message.warning(`请上传${this.pictureSource[i].lable}档案`)
          return
        }
      }
      return true
    },
    //打印
    print() {
      if (!this.validate()) return
      this.$refs['formData'].validate((validate) => {
        if (validate) {
          if (this.formData.equipment == '1' && !this.formData.isPay) {
            this.$message.warning('请选择需赔付设备！')
          }
          let picUrl = this.pictureSource.map((item) => {
            return item.file_url
          })
          this.formData.appendix = picUrl.join(',')
          let params = JSON.parse(JSON.stringify(this.formData))
          if (params.equipment == '0') {
            params.payAmount = '0'
            params.isPay = ''
          }

          this.startLoading()
          this.$request({
            url: this.$interfaces.refundApplyPrint,
            method: 'post',
            data: params,
            responseType: 'blob',
          })
            .then((response) => {
              this.printFlag = true
              this.blobToObj(response)
                .then((res) => {
                  if (res.code != 200) {
                    this.$message.error(res.msg)
                  }
                  this.endLoading()
                })
                .catch((error) => {})
              this.endLoading()
              let binaryData = []
              binaryData.push(response)

              this.fileUrl = window.URL.createObjectURL(
                new Blob(binaryData, { type: 'application/pdf' })
              )
              document.getElementById('previewPdf').onload = () => {
                //等待iframe加载完成后再执行doPrint.每次iframe设置src之后都会重新执行这部分代码。
                if (this.fileUrl) {
                  // this.endLoading()
                  document.getElementById('previewPdf').contentWindow.print()
                  this.$emit('change')
                }
              }
            })
            .catch((error) => {})
        }
      })
    },
    blobToObj(blobData) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader() // 创建读取文件对象
        reader.readAsText(blobData, 'utf-8')
        reader.onload = function (result) {
          try {
            let result = JSON.parse(reader.result)
            resolve(result)
          } catch (e) {
            reject(e)
          }
        }
      })
    },
    onSubmitHandle() {
      if (!this.printFlag) {
        this.$message.warning('请先打印回执单！')
        return
      }
      this.$refs['formData'].validate((validate) => {
        if (validate) {
          this.refundHandle()
        }
      })
    },
    refundHandle() {
      if (this.formData.equipment == '1' && !this.formData.isPay) {
        this.$message.warning('请选择需赔付设备！')
      }
      if (!this.validate()) return
      let picUrl = this.pictureSource.map((item) => {
        return item.file_url
      })
      this.formData.appendix = picUrl.join(',')
      this.startLoading()
      let params = JSON.parse(JSON.stringify(this.formData))
      if (params.equipment == '0') {
        params.payAmount = '0'
        params.isPay = ''
      }
      console.log(params, '<<---------params')

      this.$request({
        url: this.$interfaces.refundApply,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('操作成功！')
            this.dialogVisible = false
            this.$emit('refreshDetail')
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    //读obu
    readObuHandle() {
      let param = {
        read_type: '2',
      }
      this.startLoading()
      WsApi.readObuInfo(param, this.onMsg, this.onErr)
    },
    //读卡
    readCardHandle() {
      this.startLoading()
      let param = {}
      WsApi.readCpuInfo(param, this.onMsg, this.onErr)
    },
    onMsg: function (msg) {
      let rspData = JSON.parse(msg.data)
      this.endLoading()
      if (rspData.resultCode === '0') {
        //获取业务数据
        let bizContent = JSON.parse(rspData.data)
        console.log(bizContent)
        // 读取卡信息
        console.log(
          rspData.method,
          '<<---------rspData.method',
          WsConsts.methods.readCpuInfo
        )
        if (rspData.method === WsConsts.methods.readCpuInfo) {
          this.formData.cardNo = bizContent['cpu_card_id']
        }
        if (rspData.method === WsConsts.methods.readObuInfo) {
          this.formData.obuNo = bizContent['obu_id']
        }
      } else {
        if (this.ws) {
          this.ws.close()
          this.ws = null
        }
        this.$msgbox({
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: rspData.resultMsg,
        })
      }
      this.endLoading()
      console.log('收到消息：', WsConsts, msg.data)
    },
    //websocket异常处理回调
    onErr: function (err) {
      //隐藏进度条
      this.endLoading()
      this.$msgbox({
        title: '提示',
        showClose: true,
        type: 'error',
        customClass: 'my_msgBox singelBtn',
        dangerouslyUseHTMLString: true,
        message: '请求异常：' + err.message,
      })
      if (this.ws) {
        this.ws.close()
        this.ws = null
      }
      console.log('请求异常：' + err.message)
    },
  },
}
</script>
  <style lang="scss" scoped>
.balance-pay .view-item {
  margin-bottom: 6px;
}
.verify-info .form {
  padding-bottom: 200px;
}

.balance-pay .el-input {
  flex: 1;
}

::v-deep .archives-item {
  border-radius: 4px !important;
}
</style>