<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--订单履历
  * @author:zhang<PERSON>
  * @date:2023/03/15 10:13:50
-->
<template>
  <div>
    <el-descriptions
      :column="4"
      border
      title="订单履历"
      style="padding: 24px 16px 0px 16px"
    >
    </el-descriptions>
    <div style="padding: 0 16px 10px 16px">
      <el-table
        :data="tableData"
        align="center"
        header-align="center"
        border
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column prop="operateUser" align="center" label="操作人" />
        <el-table-column
          prop="operateTime"
          align="center"
          label="操作时间"
          min-width="100"
        >
        </el-table-column>
        <el-table-column prop="operateResult" align="center" label="操作内容" />
        <el-table-column prop="" align="center" label="操作结果">
          <template>成功</template>
        </el-table-column>
        <el-table-column
          prop="operateRemark"
          align="center"
          min-width="250"
          label="备注"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (
        Object.keys(this.orderDetail).length != 0 &&
        this.orderDetail.operateInfo
      ) {
        this.tableData = this.orderDetail.operateInfo
      }
    },
  },
  created() {},
  methods: {},
}
</script>

<style lang='scss' scoped>
</style>