<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--处理记录
  * @author:zhangys
  * @date:2023/03/17 16:49:09
-->

<template>
  <div>
    <el-descriptions :column="4"
                     border
                     title="处理记录"
                     class="descriptions-content"
                     style="padding:20px 20px 0 20px">

      <template>
        <el-descriptions-item label="操作人">
          {{tableData.operateUser }}
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{tableData.operateTime}}
        </el-descriptions-item>
        <el-descriptions-item label="操作内容">
          {{tableData.operateResult}}
        </el-descriptions-item>
        <el-descriptions-item label="操作结果">
          成功
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          {{ tableData.operateRemark}}
        </el-descriptions-item>
      </template>

    </el-descriptions>
  </div>

</template>

<script>
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return { tableData: {} }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (
        Object.keys(this.orderDetail).length != 0 &&
        this.orderDetail.operateInfo
      ) {
        this.tableData = this.orderDetail.operateInfo[0]
      }
    },
  },
  created() {
    // if (this.orderDetail.operateInfo.length > 0) {
    //   this.tableData = this.orderDetail.operateInfo[0]
    // }
  },
  methods: {},
}
</script>

<style lang='scss' scoped>
</style>