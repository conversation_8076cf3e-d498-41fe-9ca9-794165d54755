<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--支付信息
  * @author:zhang<PERSON>
  * @date:2023/03/08 09:36:52
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     title="支付信息"
                     class="descriptions-content">
      <template slot="extra">
        <el-button type="text"
                   @click="isExpand=!isExpand">
          <i class="expand-icon"
             :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="订单支付状态">
          {{getpayStatus(currnetDeatil.orderStatus)}}
        </el-descriptions-item>

        <el-descriptions-item label="支付时间">
          {{currnetDeatil.payTime}}
        </el-descriptions-item>

        <el-descriptions-item label="支付流水号">
          {{currnetDeatil.thirdOrderId  }}
        </el-descriptions-item>

        <el-descriptions-item label="支付方式">
          {{currnetDeatil.payType}}
        </el-descriptions-item>
        <el-descriptions-item label="支付款项类型">
          {{getPaymentOptions(currnetDeatil.paymentType)}}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <div style="padding:0 16px 10px 16px"
         v-if="isExpand">
      <el-table :data="currnetDeatil.refundList"
                align="center"
                header-align="center"
                border
                style="width: 100%; margin-bottom: 10px">
        <el-table-column prop="refundTime"
                         align="center"
                         label="退款时间" />
        <el-table-column prop="refundType"
                         align="center"
                         label="退款方式">
          <template slot-scope="scope">
            {{scope.row.refundType=='0'?'原路退款':''}}
          </template>
        </el-table-column>
        <el-table-column prop="refundOrderId"
                         align="center"
                         label="退款流水号" />
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  getcustomerType,
  getPersonalOCRType,
  getenterpriseOCRType,
  gxCardTypeFilter,
  getpayStatus,
  getPaymentOptions,
} from '@/common/method/formatOptions'

export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    orderDetail(val) {
      this.currnetDeatil = {
        ...this.orderDetail.orderInfo,
        ...this.orderDetail.paymentInfo,
      }
      if (Object.keys(this.currnetDeatil).length != 0) {
        this.getExpandStatus(this.currnetDeatil)
      }
    },
  },
  created() {},
  data() {
    return {
      currnetDeatil: {},
      isExpand: null,
    }
  },

  components: {},

  computed: {},

  methods: {
    getcustomerType,
    getPersonalOCRType,
    getenterpriseOCRType,
    gxCardTypeFilter,
    getpayStatus,
    getPaymentOptions,
    getExpandStatus(val) {
      if (val.isView && this.orderDetail.paymentInfo) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null) {
        this.isExpand = !(val.businessType == '3' || val.businessType == '4')
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>