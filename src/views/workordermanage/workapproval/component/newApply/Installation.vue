<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行——取货信息
  * @author:zhang<PERSON>
  * @date:2023/03/01 10:40:06
-->
<template>
  <div>
    <el-descriptions
      :column="4"
      border
      title="取货信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <el-button type="text" @click="isExpand = !isExpand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="安装方式">
          <div v-if="isEdit && isEditCode">
            <el-select
              :class="getDynamicClass('installType')"
              v-model="currnetDeatil.installType"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in installTypeOptions"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>
            {{
              currnetDeatil.businessType == '3' ||
              currnetDeatil.businessType == '4'
                ? getAfterSaleMailType(currnetDeatil.installType)
                : getMailType(currnetDeatil.installType)
            }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="订单状态">
          {{ currnetDeatil.orderStatus }}
        </el-descriptions-item>
        <el-descriptions-item label="订单类型">
          {{ getbusinessType(currnetDeatil.businessType) }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ currnetDeatil.orderTime }}
        </el-descriptions-item>
        <el-descriptions-item label="收货人">
          <div v-if="isEdit && isEditCode">
            <el-input
              :class="getDynamicClass('receiver')"
              v-model="currnetDeatil.receiver"
            >
            </el-input>
          </div>
          <div v-else>{{ currnetDeatil.receiver }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话" span="3">
          <div v-if="isEdit && isEditCode">
            <el-input
              :class="getDynamicClass('contactNo')"
              v-model="currnetDeatil.contactNo"
            >
            </el-input>
          </div>
          <div v-else>{{ currnetDeatil.contactNo }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="收货地址" span="4">
          <div
            v-if="isEdit && isEditCode"
            class="g-flex g-flex-nowrap g-flex-align-center"
          >
            <el-select
              :class="getDynamicClass('provinceName')"
              v-model="currnetDeatil.provinceName"
              style="margin: 0 4px"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in provinceNameData"
                :label="value.label"
                :value="value.label"
                :key="key"
              ></el-option>
            </el-select>

            <el-select
              :class="getDynamicClass('cityName')"
              v-model="currnetDeatil.cityName"
              placeholder="请选择市"
              style="margin: 0 4px"
              @change="cityChange"
            >
              <el-option
                v-for="(value, key) in cityNameData"
                :label="value.label"
                :value="value.label"
                :key="key"
              ></el-option>
            </el-select>

            <el-select
              :class="getDynamicClass('areaName')"
              v-model="currnetDeatil.areaName"
              placeholder="请选择区/县"
              style="margin: 0 4px"
              @change="areaChange"
            >
              <el-option
                v-for="(value, key) in areaNameData"
                :label="value.label"
                :value="value.label"
                :key="key"
              ></el-option>
            </el-select>

            <el-input
              :class="getDynamicClass('house')"
              style="width: 30%"
              placeholder="详细地址"
              v-model="currnetDeatil.house"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.address }}</div>
        </el-descriptions-item>
      </template>
    </el-descriptions>

    <div
      style="margin: 0px 16px 16px 16px"
      v-if="isExpand && isEdit && isEditCode"
    >
      <el-button
        size="medium"
        type="primary"
        plain
        style="width: 100%; border-style: dashed"
        @click="updateInstallInfo"
        >修改</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  getcustomerType,
  getcertificatesType,
  getbusinessType,
  getMailType,
  getAfterSaleMailType,
} from '@/common/method/formatOptions'
import { mapGetters, mapActions } from 'vuex'

export default {
  props: {
    detail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    detail(val) {
      this.currnetDeatil = {
        ...this.detail.goodsInfo,
        ...this.detail.orderInfo,
      }

      if (Object.keys(this.currnetDeatil).length != 0) {
        this.getExpandStatus(this.currnetDeatil)
      }
      //记录原始表数据
      this.originObj = {
        installType: this.currnetDeatil.installType,
        receiver: this.currnetDeatil.receiver,
        contactNo: this.currnetDeatil.contactNo,
        provinceName: this.currnetDeatil.provinceName,
        cityName: this.currnetDeatil.cityName,
        areaName: this.currnetDeatil.areaName,
        house: this.currnetDeatil.house,
      }
      this.changeObj = JSON.parse(JSON.stringify(this.originObj))
      console.log(this.address, '<<---------this.address')
      this.provinceNameData = this.address[0]
      this.cityNameData = this.provinceNameData[0].child
      this.cityChange(this.currnetDeatil.cityName, 'first')
    },

    diran(val) {
      this.place = val
      console.log(this.place, '111')
    },
    //监听车的表数据改动
    currnetDeatil: {
      handler: function (nowVal, oldVal) {
        if (Object.keys(oldVal).length > 0) {
          this.changeObj = {
            installType: nowVal.installType,
            receiver: nowVal.receiver,
            contactNo: nowVal.contactNo,
            provinceName: nowVal.provinceName,
            cityName: nowVal.cityName,
            areaName: nowVal.areaName,
            house: nowVal.house,
          }
        }
      },
      deep: true,
    },
    changeObj: {
      handler: function (nowVal, oldVal) {
        if (Object.keys(nowVal).length > 0 && Object.keys(oldVal).length > 0) {
          const hasDiff = !_.isEqual(this.originObj, nowVal)
          //是否已变更了车辆数据
          this.$emit('updateFlagHandle', hasDiff)
          this.setDataJudge(this.originObj, nowVal)
          console.log('hasDiff====>>>>', hasDiff)
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      currnetDeatil: {},
      originObj: {}, //原数据表
      changeObj: {}, //改变数据表
      installTypeOptions: [
        // { value: '0', label: '网点自提' },
        { value: '1', label: '快递邮寄' },
      ],
      isExpand: null,
      provinceNameData: [],
      cityNameData: [],
      areaNameData: [],
      areaCode: '',
      //样式数据对比
      flagList: {
        installType: true,
        receiver: true,
        contactNo: true,
        provinceName: true,
        cityName: true,
        areaName: true,
        house: true,
      },
      //   isEdit: null,
    }
  },

  components: {},

  computed: {
    ...mapGetters(['address']),
    isEdit() {
      return !this.currnetDeatil.isView
    },
    isEditCode() {
      return (
        (this.currnetDeatil.businessType == '1' ||
          this.currnetDeatil.businessType == '2') &&
        (this.currnetDeatil.nodeCode < 1010 ||
          this.currnetDeatil.nodeCode == '2000')
      )
    },
  },

  methods: {
    getcustomerType,
    getcertificatesType,
    getbusinessType,
    getMailType,
    getAfterSaleMailType,
    // 接收字段名参数，返回对应的样式类
    getDynamicClass(field) {
      // console.log('field', field, this.flagList[field])
      return this.flagList[field] ? '' : 'red-text'
    },
    cityChange(val, type) {
      this.areaNameData = []
      if (!type) {
        delete this.currnetDeatil.areaName
      }
      for (let i = 0; i < this.cityNameData.length; i++) {
        if (this.cityNameData[i].label == val) {
          this.areaNameData = this.cityNameData[i].child
        }
      }
    },
    areaChange(val) {
      for (let i = 0; i < this.areaNameData.length; i++) {
        if (this.areaNameData[i].label == val) {
          this.areaCode = this.areaNameData[i].value
        }
      }
    },
    getExpandStatus(val) {
      console.log('this.isExpand', this.isExpand)
      if (val.isView) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null) {
        this.isExpand = !(val.businessType == '4' || val.businessType == '3')
      }
    },
    setDataJudge(oldVal, nowVal) {
      this.flagList.installType = oldVal.installType == nowVal.installType
      this.flagList.receiver = oldVal.receiver == nowVal.receiver
      this.flagList.contactNo = oldVal.contactNo == nowVal.contactNo
      this.flagList.provinceName = oldVal.provinceName == nowVal.provinceName
      this.flagList.cityName = oldVal.cityName == nowVal.cityName
      this.flagList.areaName = oldVal.areaName == nowVal.areaName
      this.flagList.house = oldVal.house == nowVal.house

      console.log('开始比对数据===================>>>>', this.flagList)
    },
    //取货方式修改
    updateInstallInfo() {
      if (!this.currnetDeatil.installType) {
        this.$message.error('请选择安装方式')
        return
      }
      if (
        !this.currnetDeatil.provinceName ||
        !this.currnetDeatil.cityName ||
        !this.currnetDeatil.areaName ||
        !this.currnetDeatil.house
      ) {
        this.$message.error('请填写完整详细地址')
        return
      }
      let params = {
        provinceName: this.currnetDeatil.provinceName,
        cityName: this.currnetDeatil.cityName,
        areaName: this.currnetDeatil.areaName,
        house: this.currnetDeatil.house,
        address:
          this.currnetDeatil.provinceName +
          this.currnetDeatil.cityName +
          this.currnetDeatil.areaName +
          this.currnetDeatil.house,
        contactsName: this.currnetDeatil.receiver,
        contactsTelephone: this.currnetDeatil.contactNo,
        id: this.currnetDeatil.id,
        installType: this.currnetDeatil.installType,
        areaCode: this.areaCode,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.goodsUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('取货信息修改成功！')
            this.$emit('getDetail', 'wl')
          } else {
            this.endLoading()

            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';
/* 输入框文字颜色 */
::v-deep .el-select.red-text {
  .el-input__inner {
    color: red !important;
  }
}
::v-deep .el-input.red-text {
  .el-input__inner {
    color: red !important;
  }
}
.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>