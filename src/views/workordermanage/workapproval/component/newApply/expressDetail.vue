<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--快递订单详情
  * @author:zhang<PERSON>
  * @date:2023/03/06 14:20:32
-->

<template>
  <div>
    <el-dialog title="物流信息详情"
               :close-on-click-modal="false"
               :visible.sync="dialogVisible"
               center
               append-to-body
               modal-append-to-body
               width="50%">
      <div class="mail-progress">
        <div class="progress-style">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in expressInfo"
                              :key="index"
                              size="large"
                              color='#0bbd87'
                              icon='el-icon-success'
                              :timestamp="item.acceptTime"
                              hide-timestamp
                              type="success">
              <p style="font-weight:bold">{{item.opName}}</p>
              <p>{{item.remark}}（地址：{{item.acceptAddress}}）{{item.acceptTime}}</p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    expressNo: {
      type: String,
      default: '',
    },
  },

  data() {
    return { dialogVisible: false, expressInfo: [] }
  },

  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.sfExpressInfo(this.expressNo)
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },

  computed: {},
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
  },
  methods: {
    sfExpressInfo(val) {
      let params = {
        waybillNos: val,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.expressDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()

            if (res.data) {
              this.expressInfo = null
              this.expressInfo = res.data.reverse()
            } else {
              this.$message.warning('暂无数据')
              this.expressInfo = null
            }
          } else {
            this.endLoading()
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
  },
}
</script>
  <style   lang='scss' scoped>
.mail-progress {
  background-color: #fafafa;
  width: 100%;
  height: 400px;
  overflow-y: scroll;
  .progress-style {
    height: 400px;
    width: 70%;
    margin: 10px 0 10px 40px;
  }
}
</style>