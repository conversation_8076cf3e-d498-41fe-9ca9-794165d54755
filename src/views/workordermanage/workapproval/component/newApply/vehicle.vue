<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--车辆信息
  * @author:zhangys
  * @date:2023/03/01 10:40:06
-->
<template>
  <div>
    <el-descriptions
      :column="3"
      border
      size="medium"
      title="车辆信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <div @click="isExpand = !isExpand" class="expand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </div>
      </template>

      <template v-if="isExpand">
        <el-descriptions-item label="车牌号码">
          {{ currnetDeatil.carNo }}
        </el-descriptions-item>

        <el-descriptions-item label="车牌颜色">
          {{ getVehicleColor(currnetDeatil.carColor) }}
        </el-descriptions-item>

        <el-descriptions-item label="车辆归属人">
          {{
            currnetDeatil.ownerName == '0'
              ? '本人'
              : currnetDeatil.ownerName == '1'
              ? '他人'
              : '单位'
          }}
        </el-descriptions-item>

        <el-descriptions-item label="车型">
          <div v-if="isEdit && isEditVehicle">
            <el-select
              :class="getDynamicClass('carType')"
              v-model="currnetDeatil.carType"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in vehicleCatgoryType"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>{{ getCarType(currnetDeatil.carType) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="客货类型">
          <div v-if="isEdit && isEditVehicle">
            <el-select
              :class="getDynamicClass('isTrunk')"
              v-model="currnetDeatil.isTrunk"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in vehicleType"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>{{ getVehicleType(currnetDeatil.isTrunk) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="车辆使用性质">
          <div v-if="isEdit && isEditVehicle">
            <el-select
              :class="getDynamicClass('ownerType')"
              v-model="currnetDeatil.ownerType"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in cartype"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>{{ getcartype2(currnetDeatil.ownerType) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="行驶证车辆类型">
          <div v-if="isEdit && isEditVehicle">
            <el-input
              :class="getDynamicClass('vehicleType')"
              v-model="currnetDeatil.vehicleType"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleType }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="车辆识别代号">
          <div v-if="isEdit && isEditVehicle">
            <el-input
              :class="getDynamicClass('vin')"
              v-model="currnetDeatil.vin"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vin }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="行驶证发动机号">
          <div v-if="isEdit && isEditVehicle">
            <el-input
              :class="getDynamicClass('engineNum')"
              v-model="currnetDeatil.engineNum"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.engineNum }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="核定载人数（座位数）">
          <div v-if="isEdit && isEditVehicle">
            <el-input
              :class="getDynamicClass('carSeatNum')"
              v-model="currnetDeatil.carSeatNum"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.carSeatNum }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="长x宽x高(mm)">
          <div
            v-if="
              isEdit &&
              isEditVehicle &&
              Object.keys(this.currnetDeatil).length != 0
            "
            class="g-flex g-flex-align-center"
          >
            <el-input
              :class="getDynamicClass('length')"
              v-model="vehicleSize.length"
              class="input-with-select-part1"
            ></el-input
            >x
            <el-input
              :class="getDynamicClass('width')"
              v-model="vehicleSize.width"
              class="input-with-select-part1"
            ></el-input
            >x
            <el-input
              :class="getDynamicClass('height')"
              v-model="vehicleSize.height"
              class="input-with-select-part2"
              ><template slot="append">mm</template></el-input
            >
          </div>

          <div v-else>
            {{ currnetDeatil.outsideDimensions }}
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          label="车轴数"
          v-if="currnetDeatil.isTrunk != '2'"
        >
          <div v-if="isEdit && isEditVehicle">
            <el-input
              :class="getDynamicClass('axleCount')"
              v-model="currnetDeatil.axleCount"
            ></el-input>
          </div>
          <div v-else>{{ currnetDeatil.axleCount }}</div>
        </el-descriptions-item>
        <el-descriptions-item
          label="总质量"
          v-if="currnetDeatil.isTrunk != '2'"
        >
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleTon"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleTon }}</div>
        </el-descriptions-item>
        <el-descriptions-item
          label="总牵引质量"
          v-if="currnetDeatil.isTrunk != '2'"
        >
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.permittedTowWeight"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.permittedTowWeight }}</div>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <template v-if="isExpand && Object.keys(this.currnetDeatil).length != 0">
      <archivesBox
        uploadType="CACHEIMGUPLAOD"
        :pictureSource="pictureSource"
        @on-upload="onUploadHandle"
        @on-delete="onDeleteHandle"
        :previewMode="!isEdit || !isEditVehicle"
        style="padding: 0 16px"
      ></archivesBox>
      <div style="margin: 0px 16px 16px 16px" v-if="isEdit && isEditVehicle">
        <el-button
          size="medium"
          type="primary"
          plain
          style="width: 100%; border-style: dashed"
          @click="updateVehicleInfo"
          >修改</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
var _ = require('lodash')
import {
  getVehicleColor,
  getCarType,
  getcartype2,
  getVehicleType,
} from '@/common/method/formatOptions'
import {
  vehicleCatgoryType,
  vehicleType,
  cartype,
} from '@/common/const/optionsData.js'
import archivesBox from '../../../component/photograph.vue'
export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    orderDetail(val) {
      this.currnetDeatil = {
        ...this.orderDetail.orderInfo,
        ...this.orderDetail.carInfo,
        ...this.orderDetail.customerInfo,
      }
      if (Object.keys(this.orderDetail).length != 0) {
        this.currnetDeatil = {
          ...this.orderDetail.orderInfo,
          ...this.orderDetail.carInfo,
          ...this.orderDetail.customerInfo,
        }
        //记录车的原始表数据
        this.originObj = {
          carType: this.currnetDeatil.carType,
          isTrunk: this.currnetDeatil.isTrunk,
          ownerType: this.currnetDeatil.ownerType,
          vehicleType: this.currnetDeatil.vehicleType,
          vin: this.currnetDeatil.vin,
          engineNum: this.currnetDeatil.engineNum,
          carSeatNum: this.currnetDeatil.carSeatNum,
          axleCount: this.currnetDeatil.axleCount,
        }
        this.init()
        this.getExpandStatus()
      }
    },
    //监听车的表数据改动
    currnetDeatil: {
      handler: function (nowVal, oldVal) {
        if (Object.keys(oldVal).length > 0) {
          this.changeObj = {
            ...this.changeObj,
            carType: nowVal.carType,
            isTrunk: nowVal.isTrunk,
            ownerType: nowVal.ownerType,
            vehicleType: nowVal.vehicleType,
            vin: nowVal.vin,
            engineNum: nowVal.engineNum,
            carSeatNum: nowVal.carSeatNum,
            axleCount: nowVal.axleCount,
          }
        }
      },
      deep: true,
    },
    //监听车的长宽高数据改动
    vehicleSize: {
      handler: function (nowVal, oldVal) {
        this.changeObj = {
          ...this.changeObj,
          length: nowVal.length,
          width: nowVal.width,
          height: nowVal.height,
        }
      },
      deep: true,
    },
    changeObj: {
      handler: function (nowVal, oldVal) {
        if (Object.keys(nowVal).length > 0 && Object.keys(oldVal).length > 0) {
          const hasDiff = !_.isEqual(this.originObj, nowVal)
          //是否已变更了车辆数据
          this.$emit('updateFlagHandle', hasDiff)
          this.setDataJudge(this.originObj, nowVal)
          console.log('hasDiff====>>>>', hasDiff)
        }
      },
      deep: true,
    },
  },

  data() {
    return {
      currnetDeatil: {},
      originObj: {}, //原车数据表
      changeObj: {}, //改变数据表
      vehicleCatgoryType,
      vehicleType,
      cartype,
      isExpand: null,
      pictureSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'ownerFront',
          lable: '车主证件正面',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'ownerBack',
          lable: '车主证件反面',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseFront',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseBack',
          lable: '行驶证副页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseCar',
          lable: '行驶证车辆照片',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'transportNoPath',
          lable: '车辆道路运输证',
        },
      ],
      vehicleSize: {
        length: '',
        width: '',
        height: '',
      },
      updateImgUrl: {},
      md5CodeData: {},
      vehicleImg: '',
      transportIdImg: '',
      negativeVehicleImg: '',
      positiveVehicleImg: '',
      ownerNegativeImg: '',
      ownerPositiveImg: '',
      authLetterImg: '',
      //样式数据对比
      flagList: {
        carType: true,
        isTrunk: true,
        ownerType: true,
        vehicleType: true,
        vin: true,
        engineNum: true,
        carSeatNum: true,
        axleCount: true,
        length: true,
        width: true,
        height: true,
      },
    }
  },

  components: {
    archivesBox,
  },

  computed: {
    isEdit() {
      return !this.currnetDeatil.isView
    },
    isEditVehicle() {
      return (
        this.currnetDeatil.nodeCode < 1010 ||
        this.currnetDeatil.nodeCode == '2000'
      )
    },
  },
  created() {
    for (let i = 0; i < this.vehicleType.length; i++) {
      if (this.vehicleType[i].label == '全部') {
        this.vehicleType.splice(i, 1)
      }
    }
  },
  methods: {
    getVehicleColor,
    getCarType,
    getcartype2,
    getVehicleType,
    // 接收字段名参数，返回对应的样式类
    getDynamicClass(field) {
      // console.log('field', field, this.flagList[field])
      return this.flagList[field] ? '' : 'red-text'
    },
    init() {
      this.pictureSource = [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'ownerFront',
          md5CodeName: 'ownerPositiveImg',
          md5Code: '',
          lable: '车主证件正面',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'ownerNegativeImg',
          md5Code: '',
          photo_code: 'ownerBack',
          lable: '车主证件反面',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'positiveVehicleImg',
          md5Code: '',
          photo_code: 'drivingLicenseFront',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'negativeVehicleImg',
          md5Code: '',
          photo_code: 'drivingLicenseBack',
          lable: '行驶证副页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'vehicleImg',
          md5Code: '',
          photo_code: 'drivingLicenseCar',
          lable: '行驶证车辆照片',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'transportIdImg',
          md5Code: '',
          flag: true,
          photo_code: 'transportNoPath',
          lable: '车辆道路运输证',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'authLetterImg',
          md5Code: '',
          flag: true,
          photo_code: 'authLetterImgUrl',
          lable: '车辆授权书',
        },
      ]
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.currnetDeatil[this.pictureSource[i]['photo_code']]) {
          this.pictureSource[i]['file_url'] =
            this.currnetDeatil[this.pictureSource[i]['photo_code']]
        }
        if (
          this.currnetDeatil.ownerName == '2' &&
          this.pictureSource[i].photo_code == 'ownerFront'
        ) {
          this.$set(this.pictureSource[i], 'lable', '单位证件照')
        }
        if (
          this.currnetDeatil.ownerName == '2' &&
          this.pictureSource[i].photo_code == 'ownerBack'
        ) {
          this.$set(this.pictureSource[i], 'lable', '车辆授权书')
        }
        if (
          this.pictureSource[i].photo_code == 'transportNoPath' &&
          this.pictureSource[i].file_url
        ) {
          this.$set(this.pictureSource[i], 'flag', false)
        }
        if (
          this.pictureSource[i].photo_code == 'authLetterImgUrl' &&
          this.pictureSource[i].file_url
        ) {
          this.$set(this.pictureSource[i], 'flag', false)
        }
      }
      if (this.currnetDeatil.outsideDimensions) {
        let outSide = this.currnetDeatil.outsideDimensions.split('x')
        this.vehicleSize.length = outSide[0]
        this.vehicleSize.width = outSide[1]
        this.vehicleSize.height = outSide[2]
        //原车数据表数据
        this.originObj.length = outSide[0]
        this.originObj.width = outSide[1]
        this.originObj.height = outSide[2]
        console.log('初始化数据===》》》', this.originObj)
        this.changeObj = JSON.parse(JSON.stringify(this.originObj))
      }
    },
    onUploadHandle(result) {
      console.log(result, '<<---------result')
      if (result.data) {
        for (let i = 0; i < this.pictureSource.length; i++) {
          if (this.pictureSource[i].photo_code == result.data.photoCode) {
            this.pictureSource[i].file_url = result.data.fileUrl
            this.pictureSource[i].md5Code = result.data.md5Code
          }
          //
        }
      }
    },
    onDeleteHandle(data) {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.pictureSource[i].photo_code == data.photo_code) {
          this.pictureSource[i].file_url = ''
          this.pictureSource[i].file_serial = ''
          this.pictureSource[i].md5Code = ''
        }
      }
    },
    getExpandStatus() {
      if (this.currnetDeatil.isView) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null || !this.currnetDeatil.isView) {
        this.isExpand = !(
          this.currnetDeatil.businessType == '4' ||
          this.currnetDeatil.businessType == '3'
        )
      }
    },
    matchUrl() {
      this.pictureSource.map((item) => {
        this.updateImgUrl[item.photo_code] = item.file_url
      })

      this.pictureSource.map((item) => {
        this.md5CodeData[item.md5CodeName] = item.md5Code || ''
      })
    },
    //车辆信息修改
    updateVehicleInfo() {
      this.matchUrl()
      let params = {
        id: this.currnetDeatil.id,
        outsideDimensions:
          this.vehicleSize.length +
          'x' +
          this.vehicleSize.width +
          'x' +
          this.vehicleSize.height,
        ownerIdNum: this.currnetDeatil.certificateNo,
        ownerIdType: this.currnetDeatil.certificateType,
        ownerNegativeImg: this.md5CodeData.ownerNegativeImg,
        ownerNegativeImgUrl: this.updateImgUrl.ownerBack, //车主证件反面图片的url
        ownerPositiveImg: this.md5CodeData.ownerPositiveImg,
        ownerPositiveImgUrl: this.updateImgUrl.ownerFront, //车主证件正面图片的url
        positiveVehicleImg: this.md5CodeData.positiveVehicleImg,
        positiveVehicleImgUrl: this.updateImgUrl.drivingLicenseFront, //行驶证正面图片url
        negativeVehicleImg: this.md5CodeData.negativeVehicleImg,
        negativeVehicleImgUrl: this.updateImgUrl.drivingLicenseBack, //行驶证副页图片url
        transportIdImg: this.md5CodeData.transportIdImg,
        transportIdImgUrl: this.updateImgUrl.transportNoPath, //道路运输证图片url
        transportIdNum: this.currnetDeatil.transportIdNum,
        vehicleAxles: this.currnetDeatil.axleCount,
        vehicleTon: this.currnetDeatil.vehicleTon,
        permittedTowWeight: this.currnetDeatil.permittedTowWeight,
        vehicleCarType: this.currnetDeatil.vehicleType,
        vehicleDistinguish: this.currnetDeatil.vin,
        vehicleEngine: this.currnetDeatil.engineNum,
        vehicleImg: this.md5CodeData.vehicleImg,
        vehicleImgUrl: this.updateImgUrl.drivingLicenseCar, //行驶证车辆url
        vehicleNationalType: this.currnetDeatil.carType,
        vehicleSeat: this.currnetDeatil.carSeatNum,
        vehicleType: this.currnetDeatil.isTrunk,
        vehicleUserType: this.currnetDeatil.ownerType,
        authLetterImg: this.md5CodeData.authLetterImg,
        authLetterImgUrl: this.updateImgUrl.authLetterImgUrl,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.vehicleUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('车辆信息修改成功！')
            this.$emit('getDetail', 'vehicle')
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    setDataJudge(oldVal, nowVal) {
      this.flagList.carType = oldVal.carType == nowVal.carType
      this.flagList.isTrunk = oldVal.isTrunk == nowVal.isTrunk
      this.flagList.ownerType = oldVal.ownerType == nowVal.ownerType
      this.flagList.vehicleType = oldVal.vehicleType == nowVal.vehicleType
      this.flagList.vin = oldVal.vin == nowVal.vin
      this.flagList.engineNum = oldVal.engineNum == nowVal.engineNum
      this.flagList.carSeatNum = oldVal.carSeatNum == nowVal.carSeatNum
      this.flagList.axleCount = oldVal.axleCount == nowVal.axleCount

      this.flagList.length = oldVal.length == nowVal.length
      this.flagList.width = oldVal.width == nowVal.width
      this.flagList.height = oldVal.height == nowVal.height

      // console.log('开始比对数据===================>>>>', this.flagList)
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';
/* 输入框文字颜色 */
::v-deep .el-select.red-text {
  .el-input__inner {
    color: red !important;
  }
}
::v-deep .el-input.red-text {
  .el-input__inner {
    color: red !important;
  }
}

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}

.input-with-select-part1 {
  width: 70px;
}
.input-with-select-part2 {
  width: 140px;
}
</style>
