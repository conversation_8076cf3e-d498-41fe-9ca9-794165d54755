<template>
  <div class="authorizeDialog">
    <fieldset class="fieldset">
      <legend>附件上传</legend>
      <div slot="tip" class="el-upload__tip">注意事项：</div>
      <div slot="tip" class="el-upload__tip">
        <p>1.导入字段需包含： 【订单编号|订单应付金额|优惠总金额|运费|订单提交时间|支付方式|支付完成时间|渠道|订单状态|承诺发货时间|取消原因|发货时间|省|市|区|街道|售后状态】</p>
        <p>2.仅支持csv格式的Excel文件</p>
      </div>
      <div slot="tip" class="el-upload__tip">
        <p
          @click="download"
          style="font-size: 14px; cursor: pointer; color: #409eff"
        >
          下载导入模板
        </p>
      </div>
      <el-upload
        class="upload"
        ref="upload"
        :on-remove="handleRemove"
        :auto-upload="false"
        action="action"
        accept=".csv"
        :file-list="fileList"
        :multiple="false"
        :on-change="onChange"
      >
        <!-- accept=".xls, .xlsx"  -->
        <el-button
          slot="trigger"
          :disabled="fileList.length >= 1"
          size="small"
          type="primary"
          >选取文件</el-button
        >
      </el-upload>
    </fieldset>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="handleSubmit" type="primary" size="mini"
        >确定</el-button
      >
      <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { getToken } from '@/utils/auth'
import layerMix from '@/utils/layerMixins'

export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {
        file: ''
      },
      fileList: []
    }
  },
  methods: {
    async handleSubmit() {
      let fullFilePath = await this.submitUpload()
      if (!fullFilePath) return
      this.formData.fullFilePath = fullFilePath
      console.log(fullFilePath, 'fullFilePath')
    },
    async submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请上传文件'
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['csv']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }

      this.upload()
    },
    async upload() {
      this.startLoading()
      let formData = new FormData()
      formData.append('file', this.formData.file)
      console.log(formData)
      let url =
        process.env.VUE_APP_BASE_API + '/issue-web' + '/tikTok/saleImport'
      let config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: getToken()
        }
      } //添加请求头
      let res = await axios.post(url, formData, config)

      this.endLoading()

      if (res.data.code !== 200) {
        this.endLoading()
        this.$message({
          type: 'error',
          message: res.data.msg
        })
      }else{
        this.$message.success('导入成功')
      }
      // this.$refs.upload.clearFiles()

      // this.formData.file = ''
      console.log(res.data, 'res.data')
      this.closeDialog()
      this.getParam('callBack')()
    },
    handleRemove() {
      this.formData.file = ''
      this.fileList = []
    },

    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.closeDialog()
    },
    download() {
      let url = 'https://portal.gxetc.com.cn/public-static/file/tiktok/saleOrder.csv'
      window.open(url)
    },
  },
}
</script>

<style lang="scss" scoped>
.authorizeDialog {
  width: 100%;
  height: 100%;
  padding: 20px;
}
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>