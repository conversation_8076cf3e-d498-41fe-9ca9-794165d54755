<template>
  <div>
    <el-descriptions
      :column="4"
      border
      title="订单履历"
      style="padding:24px 16px 0px 16px"
    ></el-descriptions>
    <div style="padding:0 16px 10px 16px">
      <el-table
        :data="tableData"
        align="center"
        header-align="center"
        border
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column prop="createdBy" align="center" label="操作人" />
        <el-table-column
          prop="createdTime"
          align="center"
          label="操作时间"
          min-width="100"
        ></el-table-column>
        <el-table-column prop="nodeCode" align="center" label="操作内容">
          <!-- <template slot-scope="scope">
            <div>{{ queryGroup.nodeCodeObj[scope.row.nodeCode] }}</div>
          </template> -->
        </el-table-column>

        <el-table-column prop="result" align="center" label="操作结果">
          <template slot-scope="scope">
            <div>{{ scope.row.result == 0 ? '成功' : '失败' }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          align="center"
          min-width="250"
          label="备注"
        />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    queryGroup: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (
          Object.keys(this.orderInfo).length != 0 &&
          this.orderInfo.operateLog
        ) {
          this.tableData = this.orderInfo.operateLog || []
        }
      }
    }
  },
  created() {},
  methods: {}
}
</script>

<style lang='scss' scoped>
</style>