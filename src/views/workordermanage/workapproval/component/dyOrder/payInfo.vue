
<template>
  <div>
    <el-descriptions :column="5" border title="支付信息" class="descriptions-content">
      <template slot="extra">
        <el-button type="text" @click="isExpand=!isExpand">
          <i class="expand-icon" :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="订单支付金额">{{currnetDeatil.orderAmount}}</el-descriptions-item>
        <el-descriptions-item label="优惠总金额">{{currnetDeatil.promotionAmount}}</el-descriptions-item>

        <el-descriptions-item label="支付方式">{{currnetDeatil.payType}}</el-descriptions-item>

        <el-descriptions-item label="支付完成时间">{{currnetDeatil.payTime}}</el-descriptions-item>

        <el-descriptions-item label="支付流水号">{{currnetDeatil.paySequence }}</el-descriptions-item>

        <!-- <el-descriptions-item label="支付款项类型">{{currnetDeatil.paymentType == 0 ?'线上注销订单支付':''}}</el-descriptions-item>

        <el-descriptions-item label="退款时间">{{currnetDeatil.refundTime}}</el-descriptions-item>

        <el-descriptions-item label="退款方式">{{currnetDeatil.refundMethod == 0?'原路退款':''}}</el-descriptions-item>
        <el-descriptions-item label="退款流水号">{{currnetDeatil.refundSerialNo }}</el-descriptions-item> -->
      </template>
    </el-descriptions>
  </div>
</template>

<script>
import { getpayStatus, getPaymentOptions } from '@/common/method/formatOptions'

export default {
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        this.currnetDeatil = {
          ...this.orderInfo.defrayInfo
        }
      }
    }
  },
  data() {
    return {
      currnetDeatil: {},
      isExpand: true
    }
  },
  created() {
    if (!this.isView) {
      this.isExpand = false
    }
  },
  methods: {
    getpayStatus,
    getPaymentOptions
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>