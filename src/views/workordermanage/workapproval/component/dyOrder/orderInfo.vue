<template>
  <div>
    <el-descriptions
      :column="4"
      border
      size="medium"
      title="订单信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <el-button type="text" @click="isExpand = !isExpand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="渠道">抖音</el-descriptions-item>

        <el-descriptions-item label="订单编号">{{
          logoutOrderBaseInfo.orderId
        }}</el-descriptions-item>
        <el-descriptions-item label="订单提交时间">{{
          logoutOrderBaseInfo.createTime
        }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{
          logoutOrderBaseInfo.orderStatus
        }}</el-descriptions-item>
        <el-descriptions-item label="承诺发货时间">{{
          logoutOrderBaseInfo.shipTime
        }}</el-descriptions-item>
        <el-descriptions-item label="发货时间">{{
          logoutOrderBaseInfo.createTime
        }}</el-descriptions-item>
        <el-descriptions-item label="收货人地区">{{
          logoutOrderBaseInfo.address
        }}</el-descriptions-item>
        <el-descriptions-item label="取消原因">{{
          logoutOrderBaseInfo.cancelReason
        }}</el-descriptions-item>
        <el-descriptions-item label="申办提交时间">{{
          logoutOrderBaseInfo.applyTime
        }}</el-descriptions-item>
        <el-descriptions-item label="产品类型">

        {{getProductTypeOptions(logoutOrderBaseInfo.productType)}}

        </el-descriptions-item>
        <el-descriptions-item label="签约银行">{{
          logoutOrderBaseInfo.signBank
        }}</el-descriptions-item>
        <el-descriptions-item label="签约时间">{{
          logoutOrderBaseInfo.signTime
        }}</el-descriptions-item>

        <el-descriptions-item label="发行时间">{{
          logoutOrderBaseInfo.issueTime
        }}</el-descriptions-item>
        <el-descriptions-item label="激活时间">{{
          logoutOrderBaseInfo.activeTime
        }}</el-descriptions-item>
        <el-descriptions-item label="ETC卡号">{{
          logoutOrderBaseInfo.cardNo
        }}</el-descriptions-item>
        <el-descriptions-item label="OBU号">{{
          logoutOrderBaseInfo.obuNo
        }}</el-descriptions-item>
        <el-descriptions-item label="申办状态">{{
          logoutOrderBaseInfo.nodeCodeStr
        }}</el-descriptions-item>
        <el-descriptions-item label="售后状态">{{
          logoutOrderBaseInfo.afterSaleStatus
        }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">{{
          logoutOrderBaseInfo.deviceType == 1?'基础款':logoutOrderBaseInfo.deviceType == 2?'进阶款':'-'
        }}</el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>

<script>

import { getbusinessType,getProductTypeOptions } from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    queryGroup: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return { 
      logoutOrderBaseInfo: {},
      isExpand: true

     }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          console.log(val, 'logoutOrderBaseInfo')
          this.logoutOrderBaseInfo = this.orderInfo.orderInfo
        }
      }
    }
  },
  created() {},
  methods: {
    getbusinessType,
    getProductTypeOptions
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>