<template>
  <div class="apply-info" style="padding-bottom:10px;">
    <!-- 开户信息以及图片 -->
    <el-descriptions
      :column="3"
      border
      size="medium"
      title="开户人信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <el-button type="text" @click="isExpand = !isExpand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="客户名称">
          <el-input
            v-if="isChange"
            v-model="form.customerName"
            clearable
          ></el-input>
          <span v-else>{{ form.customerName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户类型">
          <el-input
            v-if="isChange"
            v-model="form.customerType"
            clearable
          ></el-input>
          <span v-else>{{ getcustomerType(form.customerType) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="证件类型">
          <el-input
            v-if="isChange"
            v-model="form.certificatesType"
            clearable
          ></el-input>
          <span v-else>{{ getPersonalOCRType(form.certificatesType) }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="证件号">{{
          form.certificatesCode
        }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{
          form.linkMobile
        }}</el-descriptions-item>
        <el-descriptions-item label="联系地址">{{
          form.linkAddress
        }}</el-descriptions-item>
      </template>
    </el-descriptions>
    <archivesBox
      class="archivesBox"
      uploadType="CACHEIMG"
      v-if="isExpand"
      :customerId="basicUserInfo.custMastId"
      :vehicle_code="basicUserInfo.vehicleNo"
      :vehicle_color="vehicleColorCode"
      :other_code="basicUserInfo.cardNo"
      :scene="6"
      :pictureSource="pictureSource"
      @on-upload="onUploadHandle"
      @on-delete="onDeleteHandle"
      :previewMode="!isChange"
    ></archivesBox>
  </div>
</template>

<script>
import {
  getcustomerType,
  getPersonalOCRType
} from '@/common/method/formatOptions'
import archivesBox from '../../../component/photograph.vue'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    },
    vehicleColorCode: [String, Number]
  },
  components: { archivesBox },
  data() {
    return {
      form: {},
      isExpand: true,
      getcustomerType,
      getPersonalOCRType,
      pictureSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'positiveImageUrl',
          lable: '开户人身份证正面'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'negativeImageUrl',
          lable: '开户人身份证反面'
        }
      ],
    }
  },
  computed: {
    orderStatus() {
      return this.orderInfo.orderInfo.orderStatus
    },
    basicUserInfo() {
      return this.orderInfo.accountHolder
    },
    isChange() { // 暂时没有用到 
      console.log(['401'].includes(this.orderStatus) && !this.isView)
      return ['401'].includes(this.orderStatus) && !this.isView
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.form = {
            ...this.orderInfo.accountHolder
          }
          this.init()
        }
      }
    }
  },
  methods: {
    init() {
      this.pictureSource.forEach((item, idx) => {
        if (this.form.hasOwnProperty(item.photo_code)) {
          item.file_url = this.form[item.photo_code]
        }
      })

    },
    onUploadHandle(result) {
      if (result.data) {
        for (let i = 0; i < this.pictureSource.length; i++) {
          if (this.pictureSource[i].photo_code == result.data.photo_code) {
            this.pictureSource[i].file_url = result.data.file_url
            this.pictureSource[i].code = result.data.code
            console.log(this.pictureSource[i], 'this.pictureSource[i]')
          }
        }
      }
    },
    onDeleteHandle(data) {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.pictureSource[i].photo_code == data.photo_code) {
          this.pictureSource[i].file_url = ''
          this.pictureSource[i].file_serial = ''
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  .label {
    width: 80px;
    padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 50px;
    flex: 1;
  }
}

.archivesBox {
  padding-left: 15px;
  // ::v-deep .archives-item__text-area {
  //   display: none;
  // }
}
.apply-info {
  ::v-deep .el-input {
    width: 100%;
  }
}
</style>