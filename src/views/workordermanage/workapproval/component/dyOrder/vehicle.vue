<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--车辆信息
  * @author:zhangys
  * @date:2023/03/01 10:40:06
-->
<template>
  <div>
    <el-descriptions
      :column="4"
      border
      size="medium"
      title="车辆信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <div @click="isExpand = !isExpand" class="expand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </div>
      </template>

      <template v-if="isExpand">
        <el-descriptions-item label="车牌号码">
          {{ currnetDeatil.vehicleCode }}
        </el-descriptions-item>

        <el-descriptions-item label="车牌颜色">
          {{ getVehicleColor(currnetDeatil.vehicleColor) }}
        </el-descriptions-item>

        <el-descriptions-item label="车辆归属人">
          {{
            currnetDeatil.owner == '0'
              ? '本人'
              : currnetDeatil.owner == '1'
              ? '他人'
              : currnetDeatil.owner == '2'
              ? '单位'
              : ''
          }}
        </el-descriptions-item>

        <el-descriptions-item label="车型">
          <div v-if="isEdit && isEditVehicle">
            <el-select
              v-model="currnetDeatil.vehicleNationalType"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in vehicleCatgoryType"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>{{ getCarType(currnetDeatil.vehicleNationalType) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="客货类型">
          <div v-if="isEdit && isEditVehicle">
            <el-select v-model="currnetDeatil.vehicleType" placeholder="请选择">
              <el-option
                v-for="(value, key) in vehicleType"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>
            {{ getVehicleType(currnetDeatil.vehicleType) }}
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="车辆用户类型">
          <div v-if="isEdit && isEditVehicle">
            <el-select
              v-model="currnetDeatil.vehicleUserType"
              placeholder="请选择"
            >
              <el-option
                v-for="(value, key) in cartype"
                :label="value.label"
                :value="value.value"
                :key="key"
              ></el-option>
            </el-select>
          </div>
          <div v-else>{{ getcartype2(currnetDeatil.vehicleUserType) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="行驶证车辆类型">
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleCarType"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleCarType }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="车辆识别代号">
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleDistinguish"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleDistinguish }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="行驶证发动机号">
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleEngine"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleEngine }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="核定载人数（座位数）">
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleSeat"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleSeat }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="长x宽x高(mm)">
          <div
            v-if="
              isEdit &&
                isEditVehicle &&
                Object.keys(this.currnetDeatil).length != 0
            "
            class="g-flex g-flex-align-center"
          >
            <el-input
              v-model="currnetDeatil.vehicleLength"
              class="input-with-select-part1"
            ></el-input
            >x
            <el-input
              v-model="currnetDeatil.vehicleWidth"
              class="input-with-select-part1"
            ></el-input
            >x
            <el-input
              v-model="currnetDeatil.vehicleHeight"
              class="input-with-select-part2"
              ><template slot="append">mm</template></el-input
            >
          </div>

          <div v-else>
            {{
              `${currnetDeatil.vehicleLength} x ${currnetDeatil.vehicleWidth} x ${currnetDeatil.vehicleHeight} mm `
            }}
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          label="车轴数"
          v-if="currnetDeatil.isTrunk != '2'"
        >
          <div v-if="isEdit && isEditVehicle">
            <el-input v-model="currnetDeatil.vehicleAxles"></el-input>
          </div>
          <div v-else>{{ currnetDeatil.vehicleAxles }}</div>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <template v-if="isExpand && Object.keys(this.currnetDeatil).length != 0">
      <archivesBox
        uploadType="CACHEIMGUPLAOD"
        :pictureSource="pictureSource"
        @on-upload="onUploadHandle"
        @on-delete="onDeleteHandle"
        :previewMode="!isEdit || !isEditVehicle"
        style="padding: 0 16px"
      ></archivesBox>
      <div style="margin: 0px 16px 16px 16px" v-if="isEdit && isEditVehicle">
        <el-button
          size="medium"
          type="primary"
          plain
          style="width: 100%; border-style: dashed"
          @click="updateVehicleInfo"
          >修改</el-button
        >
      </div>
    </template>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getCarType,
  getcartype2,
  getVehicleType
} from '@/common/method/formatOptions'
import {
  vehicleCatgoryType,
  vehicleType,
  cartype
} from '@/common/const/optionsData.js'
import archivesBox from '../../../component/photograph.vue'
import { vehicleUpdate } from '@/api/workordermanage'

export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    orderDetail: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.currnetDeatil = {
            ...this.orderDetail.orderInfo,
            ...this.orderDetail.vehicleInfo,
            ...this.orderDetail.accountHolder
          }
          this.init()
          this.getExpandStatus()
        }
      }
    }
  },

  data() {
    return {
      currnetDeatil: {},
      vehicleCatgoryType,
      vehicleType,
      cartype,
      isExpand: true,
      pictureSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'ownerPositiveImgUrl',
          lable: '车主证件正面'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'ownerNegativeImgUrl',
          lable: '车主证件反面'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'positiveVehicleImgUrl',
          lable: '行驶证正页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'negativeVehicleImgUrl',
          lable: '行驶证副页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'vehicleImgUrl',
          lable: '行驶证车辆照片'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'transportIdImgUrl',
          lable: '车辆道路运输证'
        }
      ],
      vehicleSize: {
        length: '',
        width: '',
        height: ''
      },
      updateImgUrl: {},
      md5CodeData: {},
      vehicleImg: '',
      transportIdImg: '',
      negativeVehicleImg: '',
      positiveVehicleImg: '',
      ownerNegativeImg: '',
      ownerPositiveImg: '',
      authLetterImg: ''
    }
  },

  components: {
    archivesBox
  },

  computed: {
    isEdit() {
      return !this.isView
    },
    isEditVehicle() {
      // this.orderDetail.orderInfo.nodeCode == '5020' || this.orderDetail.orderInfo.nodeCode == '5010' || this.orderDetail.orderInfo.nodeCode == '5000'
      return (
        this.orderDetail.orderInfo.applyId &&
        (this.orderDetail.orderInfo.nodeCode == '5020' ||
          this.orderDetail.orderInfo.nodeCode == '5010' ||
          this.orderDetail.orderInfo.nodeCode == '5000')
      )
    },
    orderStatus() {
      return this.orderDetail.orderInfo.orderStatus
    }
  },
  created() {
    for (let i = 0; i < this.vehicleType.length; i++) {
      if (this.vehicleType[i].label == '全部') {
        this.vehicleType.splice(i, 1)
      }
    }
  },
  methods: {
    getVehicleColor,
    getCarType,
    getcartype2,
    getVehicleType,
    init() {
      console.log(this.currnetDeatil, 'orderDetail')
      this.pictureSource = [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'ownerPositiveImg',
          md5CodeNameUrl: 'ownerPositiveImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          photo_code: 'ownerPositiveImgUrl',
          lable: '车主证件正面'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'ownerNegativeImg',
          md5CodeNameUrl: 'ownerNegativeImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          photo_code: 'ownerNegativeImgUrl',
          lable: '车主证件反面'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'positiveVehicleImg',
          md5CodeNameUrl: 'positiveVehicleImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          photo_code: 'positiveVehicleImgUrl',
          lable: '行驶证正页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'negativeVehicleImg',
          md5CodeNameUrl: 'negativeVehicleImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          photo_code: 'negativeVehicleImgUrl',
          lable: '行驶证副页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'vehicleImg',
          md5CodeNameUrl: 'vehicleImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          photo_code: 'vehicleImgUrl',
          lable: '行驶证车辆照片'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'transportIdImg',
          md5CodeNameUrl: 'transportIdImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          flag: true,
          photo_code: 'transportIdImgUrl',
          lable: '车辆道路运输证'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          md5CodeName: 'authLetterImg',
          md5CodeNameUrl: 'authLetterImgUrlCode',
          md5CodeNameUrlCode: '',
          md5Code: '',
          flag: true,
          photo_code: 'authLetterImgUrl',
          lable: '车辆授权书'
        }
      ]
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.currnetDeatil[this.pictureSource[i]['photo_code']]) {
          this.pictureSource[i]['file_url'] = this.currnetDeatil[
            this.pictureSource[i]['photo_code']
          ]
        }
        if (
          this.currnetDeatil.owner == '2' &&
          this.pictureSource[i].photo_code == 'ownerPositiveImgUrl'
        ) {
          this.$set(this.pictureSource[i], 'lable', '单位证件照')
        }
        if (
          this.currnetDeatil.owner == '2' &&
          this.pictureSource[i].photo_code == 'ownerNegativeImgUrl'
        ) {
          this.$set(this.pictureSource[i], 'isShow', false)
        }
        if (
          this.pictureSource[i].photo_code == 'transportIdImgUrl' &&
          this.pictureSource[i].file_url
        ) {
          this.$set(this.pictureSource[i], 'flag', false)
        }
        if (
          this.pictureSource[i].photo_code == 'authLetterImgUrl' &&
          this.pictureSource[i].file_url
        ) {
          this.$set(this.pictureSource[i], 'flag', false)
        }
      }
      if (this.currnetDeatil.outsideDimensions) {
        let outSide = this.currnetDeatil.outsideDimensions.split('x')
        this.vehicleSize.length = outSide[0]
        this.vehicleSize.width = outSide[1]
        this.vehicleSize.height = outSide[2]
      }
    },
    onUploadHandle(result) {
      console.log(result, '<<---------result')
      if (result.data) {
        for (let i = 0; i < this.pictureSource.length; i++) {
          if (this.pictureSource[i].photo_code == result.data.photoCode) {
            this.pictureSource[i].file_url = result.data.fileUrl
            this.pictureSource[i].md5Code = result.data.md5Code
            this.pictureSource[i].md5CodeNameUrlCode = result.data.code
            console.log(this.pictureSource)
          }
          //
        }
      }
    },
    onDeleteHandle(data) {
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.pictureSource[i].photo_code == data.photo_code) {
          this.pictureSource[i].file_url = ''
          this.pictureSource[i].file_serial = ''
          this.pictureSource[i].md5Code = ''
        }
      }
    },
    getExpandStatus() {
      if (this.currnetDeatil.isView) {
        this.isExpand = true
        return
      }
      if (this.isExpand == null || !this.currnetDeatil.isView) {
        this.isExpand = !(
          this.currnetDeatil.businessType == '4' ||
          this.currnetDeatil.businessType == '3'
        )
      }
    },
    matchUrl() {
      this.pictureSource.map(item => {
        this.updateImgUrl[item.photo_code] = item.file_url
        console.log(this.updateImgUrl)
      })

      this.pictureSource.map(item => {
        this.md5CodeData[item.md5CodeName] = item.md5Code || ''
      })

      this.pictureSource.map(item => {
        this.md5CodeData[item.md5CodeNameUrl] = item.md5CodeNameUrlCode || ''
        console.log(this.md5CodeData)
      })
    },
    //车辆信息修改
    async updateVehicleInfo() {
      this.matchUrl()
      let {
        orderId,
        applyId,
        vehicleType,
        vehicleNationalType,
        vehicleUserType,
        vehicleCarType,
        vehicleDistinguish,
        vehicleEngine,
        vehicleSeat,
        vehicleLength,
        vehicleWidth,
        vehicleHeight,
        vehicleAxles
      } = this.currnetDeatil
      let params = {
        orderId,
        applyId,
        vehicleType,
        vehicleNationalType,
        vehicleUserType,
        vehicleCarType,
        vehicleDistinguish,
        vehicleEngine,
        vehicleSeat,
        vehicleLength,
        vehicleWidth,
        vehicleHeight,
        vehicleAxles,
        ownerPositiveImg: this.md5CodeData.ownerPositiveImg,
        ownerPositiveImgUrl: this.updateImgUrl.ownerPositiveImgUrl, //车主证件正面图片的url
        ownerPositiveImgUrlCode: this.md5CodeData.ownerPositiveImgUrlCode,

        ownerNegativeImg: this.md5CodeData.ownerNegativeImg,
        ownerNegativeImgUrl: this.updateImgUrl.ownerNegativeImgUrl, //车主证件反面图片的url
        ownerNegativeImgUrlCode: this.md5CodeData.ownerNegativeImgUrlCode,

        positiveVehicleImg: this.md5CodeData.positiveVehicleImg,
        positiveVehicleImgUrl: this.updateImgUrl.positiveVehicleImgUrl, //行驶证正面图片url
        positiveVehicleImgUrlCode: this.md5CodeData.positiveVehicleImgUrlCode,

        negativeVehicleImg: this.md5CodeData.negativeVehicleImg,
        negativeVehicleImgUrl: this.updateImgUrl.negativeVehicleImgUrl, //行驶证副页图片url
        negativeVehicleImgUrlCode: this.md5CodeData.negativeVehicleImgUrlCode,

        transportIdImg: this.md5CodeData.transportIdImg,
        transportIdImgUrl: this.updateImgUrl.transportIdImgUrl, //道路运输证图片url
        transportIdImgUrlCode: this.md5CodeData.transportIdImgUrlCode,

        vehicleImg: this.md5CodeData.vehicleImg,
        vehicleImgUrl: this.updateImgUrl.vehicleImgUrl, //行驶证车辆url
        vehicleImgUrlCode: this.md5CodeData.vehicleImgUrlCode,

        authLetterImg: this.md5CodeData.authLetterImg,
        authLetterImgUrl: this.updateImgUrl.authLetterImgUrl,
        authLetterImgUrlCode: this.md5CodeData.authLetterImgUrlCode
      }
      this.startLoading()
      console.log(params)

      try {
        let res = await vehicleUpdate(params)
        if (res.code == 200) {
          this.endLoading()
          this.$message.success('车辆信息修改成功！')
          this.$emit('getDetail')
        } else {
          this.endLoading()
          this.$message.error(res.msg)
        }
      } catch (err) {
        console.log(err)
        this.endLoading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}

.input-with-select-part1 {
  width: 70px;
}
.input-with-select-part2 {
  min-width: 140px;
  max-width: 150px;
}
</style>
