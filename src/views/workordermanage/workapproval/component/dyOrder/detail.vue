<template>
  <div class="detail-wrap" v-if="orderInfo">
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <orderInfo v-bind="$attrs" :orderInfo="orderInfo" />
    </div>

    <!-- 支付信息 -->
    <div class="orderItem">
      <payInfo
        v-bind="$attrs"
        :isView="isView"
        :orderInfo="orderInfo"
      ></payInfo>
    </div>

    <!-- 开户人信息 -->
    <div class="orderItem">
      <userApplyInfo
        ref="userApplyInfo"
        v-bind="$attrs"
        :isView="isView"
        :orderInfo="orderInfo"
        :vehicleColorCode="vehicleColorCode"
      ></userApplyInfo>
    </div>

    <!-- 车辆信息 -->
    <div class="orderItem">
      <vehicleComponent
        :isView="isView"
        :orderDetail="orderInfo"
        @getDetail="getDetail"
      ></vehicleComponent>
    </div>

    <!-- 物流信息 -->
    <div class="orderItem">
      <materialFlow v-bind="$attrs" :isView="isView" :orderInfo="orderInfo" />
    </div>

    <!-- 售后信息 -->
    <div class="orderItem">
      <afterInfo v-bind="$attrs" :isView="isView" :orderInfo="orderInfo" />
    </div>

    <!-- 处理记录 -->
    <div class="orderItem">
      <processRecord
        ref="processRecord"
        v-bind="$attrs"
        :isView="isView"
        :orderInfo="orderInfo"
      />
    </div>

    <!-- 订单履历 -->
    <div class="orderItem">
      <orderHistory
        v-bind="$attrs"
        v-if="isView"
        :queryGroup="queryGroup"
        :orderInfo="orderInfo"
      ></orderHistory>
    </div>
    <!-- 操作 v-show="showBtn('pass')" -->
    <div class="orderItem" style="margin-bottom: 30px">
      <div
        class="btns g-flex g-flex-center"
        v-if="!isView && showBtn('operateWrap')"
      >
        <el-button
          type="primary"
          style="margin: 0 20px"
          :disabled="!showBtn('pass') || !this.applyId"
          @click="handle('pass')"
          >审核通过</el-button
        >
        <el-button
          type="primary"
          :disabled="!showBtn('noPass') || !this.applyId"
          @click="handle('noPass')"
          >审核不通过</el-button
        >
        <el-button
          style="margin: 0 20px"
          :disabled="!showBtn('cancel') || !this.applyId"
          @click="handle('cancel')"
          >取消申办</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import orderInfo from './orderInfo.vue'
import payInfo from './payInfo.vue'
import orderHistory from './orderHistory.vue'
import userApplyInfo from './user-apply-infor.vue'
import materialFlow from './material-flow.vue'
import vehicleComponent from './vehicle.vue'
import afterInfo from './after-info.vue'
import processRecord from './process-record.vue'
import { orderDetails, applyReview, applyCancel } from '@/api/workordermanage'

export default {
  components: {
    orderInfo,
    materialFlow,
    payInfo,
    orderHistory,
    userApplyInfo,
    vehicleComponent,
    afterInfo,
    processRecord,
  },
  props: {
    orderId: [String, Number],
    applyId: [String, Number],
    slideVisible: {
      type: Boolean,
      default: '',
    },
    vehicleColorCode: [String, Number],
    isView: {
      type: Boolean,
      default: false,
    },
    queryGroup: [Object],
  },
  data() {
    return {
      orderInfo: null,
    }
  },
  computed: {
    orderStatus() {
      // 订单状态
      return this.orderInfo.orderInfo.orderStatus
    },
    nodeCode() {
      // 申办状态
      return this.orderInfo.orderInfo.nodeCode
    },
    afterSaleStatus() {
      // 售后状态
      return this.orderInfo.orderInfo.afterSaleStatus
    },
  },
  methods: {
    //获取订单详情
    async getDetail() {
      if (!this.orderId) return
      let params = {
        orderId: this.orderId,
        applyId: this.applyId,
      }
      this.startLoading()
      let res = await orderDetails(params)
      if (res.code == 200) {
        this.endLoading()
        this.orderInfo = res.data
      } else {
        this.endLoading()
        this.$message({
          message: res.msg,
          type: 'error',
        })
      }
    },
    // 根据详情状态显示按钮
    showBtn(type) {
      //nodeCode"'5030":"审核通过”,"待审核”，5020”:“5010”:"待签署”，“5000”:"订单创建”，'5070”:"已退款退货”，'5060”:"已发行激活”，5050”:"已取消申办”，'5040”:"审核不通过”
      let config = {
        pass: ['5020', '售后已关闭', '售后关闭','换货成功'],
        noPass: ['5020', '售后已关闭', '售后关闭','换货成功'],
        cancel: ['5020', '5030', '5040', , '售后已关闭','售后关闭', '换货成功'],
        operateWrap: [
          '5020',
          '5030',
          '5040',
          '售后待处理',
          '售后拒绝',
          '待退货',
          '补寄成功',
          '售后已关闭',
          '售后关闭',
          '换货成功',
        ],
      }
      return (
        config[type].includes(this.nodeCode) &&
        (config[type].includes(this.afterSaleStatus) || !this.afterSaleStatus)
      )
      // return true
    },
    // 操作
    async handle(type) {
      const { remark } = this.$refs.processRecord.form
      const params = {
        orderId: this.orderId,
        applyId: this.applyId,
        remark,
      }
      let fn = {
        pass: applyReview,
        noPass: applyReview,
        cancel: applyCancel,
      }
      this.$refs.processRecord.checkRules(type)
      try {
        switch (type) {
          case 'pass':
            params.checkFlag = 0
            params.checkType = 1
            break
          case 'noPass':
            if (!remark) {
              this.$message.error('请输入备注内容')
              return
            }
            params.checkFlag = 1
            params.checkType = 1
            break

          case 'cancel':
            if (!remark) {
              this.$message.error('请输入备注内容')
              return
            }
            break
          default:
            return
        }
        this.startLoading()
        let res = await fn[type](params)
        console.log(res, params, type)

        if (res.code === 200) {
          this.endLoading()
          this.$message.success('操作成功')
          if (type == 'noPass') {
            this.$emit('closeDartSlide')
          }
          this.getDetail()
        } else {
          this.endLoading()
        }
      } catch (error) {
        this.endLoading()
        // 处理异步操作的错误
        console.error(error)
        if (type == 'noPass') {
          this.$emit('closeDartSlide')
        }
        this.getDetail()
      }
    },
  },
  created() {
    this.getDetail()
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>