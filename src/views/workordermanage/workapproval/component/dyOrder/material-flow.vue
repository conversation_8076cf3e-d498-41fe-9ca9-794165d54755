<template>
  <div style="padding-bottom:10px;">
    <el-descriptions :column="5" border title="物流信息" class="descriptions-content">
      <template slot="extra">
        <el-button type="text" @click="isExpand=!isExpand">
          <i class="expand-icon" :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="物流公司">{{logistics.company}}</el-descriptions-item>
        <el-descriptions-item label="物流单号">{{logistics.number}}</el-descriptions-item>
        <el-descriptions-item label="寄件时间">{{logistics.shipTime}}</el-descriptions-item>
        <el-descriptions-item label="收货时间">{{logistics.acceptTime}}</el-descriptions-item>
        <el-descriptions-item label="物流状态">{{logistics.status }}</el-descriptions-item>
    </template>
    </el-descriptions>

  </div>
</template>

<script>
export default {
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {
          logistics:{}
        }
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpand: true
    }
  },
  watch:{
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          console.log(val, 'logistics')
          this.logistics = this.orderInfo.logistics
        }
      }
    }
  },
  created() {
    if (!this.isView) {
      this.isExpand = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  margin-bottom: 20px;
  .label {
    width: 80px;
    // padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 200px;
    flex: 1;
  }
}
</style>