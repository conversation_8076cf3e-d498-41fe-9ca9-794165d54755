<template>
  <div style="padding-bottom:10px;">
    <el-descriptions
      :column="4"
      border
      v-for="(item, idx) in afterInfoList"
      :key="idx"
      :title="idx == 0 ? '售后信息' : ''"
      class="descriptions-content"
    >
      <template slot="extra" v-if="idx == 0">
        <el-button type="text" @click="isExpand = !isExpand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="售后单号">{{
          item.afterSaleId
        }}</el-descriptions-item>
        <el-descriptions-item label="售后类型">{{
          item.afterSaleType
        }}</el-descriptions-item>
        <el-descriptions-item label="退款方式">{{
          item.refundType
        }}</el-descriptions-item>
        <el-descriptions-item label="售后原因">{{
          item.afterSaleReason
        }}</el-descriptions-item>
        <el-descriptions-item label="商家退款时间">{{
          item.refundTime
        }}</el-descriptions-item>
        <el-descriptions-item label="退货物流状态">{{
          item.logisticsState
        }}</el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpand: true,
      afterInfoList: [{}]
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.afterInfoList = this.orderInfo.afterSalesInfo
          if (this.orderInfo.afterSalesInfo.length <= 0) {
            this.orderInfo.afterSalesInfo = [{}]
          }
        }
      }
    }
  },
  created() {
    if (!this.isView) {
      this.isExpand = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  margin-bottom: 20px;
  .label {
    width: 80px;
    // padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 200px;
    flex: 1;
  }
}
</style>