<template>
  <div class="process-record" style="padding-bottom:10px;">
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm"
    >
      <!-- 备注记录 待审核时隐藏 v-if="form.remarkInfo && !['401'].includes(orderStatus)"-->
      <el-form-item label="处理记录：" prop="remarkInfo">
        <el-input
          type="textarea"
          :disabled="true"
          :rows="5"
          v-model="form.remarkInfo"
        ></el-input>
      </el-form-item>
      <el-descriptions v-if="!isView" :column="1" border title="" class="descriptions-remark">
        <el-descriptions-item label="常用语" labelStyle="width:120px">
          <el-select
            @change="tipsChange"
            v-model="remarkText"
            placeholder="请选择"
            style="width: 80%"
          >
            <el-option
              v-for="(value, key) in remarkTips"
              :label="value.label"
              :value="value.value"
              :key="key"
            >
            </el-option>
          </el-select>
        </el-descriptions-item>
      </el-descriptions>
      <!-- 备注填写 !isView && ['401','402', '403', '404'].includes(orderStatus) label="备注"-->
      <el-descriptions v-if="!isView" :column="1" border title="" class="descriptions-remark">
        <el-descriptions-item label="备注" labelStyle="width:120px">
          <el-form-item prop="remark">
            <el-input
              type="textarea"
              :disabled="isView"
              :rows="3"
              placeholder="请输入内容"
              v-model="form.remark"
            ></el-input>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    },
    vehicleColorCode: [String, Number]
  },
  data() {
    return {
      form: {
        remark: ''
      },
      remarkText: '',
      remarkTips: [],
      rulesEx: {
        remark: [{ required: true, message: '请输入备注内容', trigger: 'blur' }]
      },
      handleType:''
    }
  },
  computed: {
    rules() {
      return ['noPass', 'notice', 'cancel'].includes(this.handleType)
        ? this.rulesEx
        : {}
    },
    orderStatus() {
      return this.orderInfo.orderInfo.orderStatus
    },
    isChange() {
      console.log(['401'].includes(this.orderStatus) && !this.isView)
      return ['401'].includes(this.orderStatus) && !this.isView
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.getRemarkInfo()
          this.getTips()
        }
      }
    }
  },
  methods: {
    checkRules(type) {
      this.handleType = type
      this.$refs.ruleForm.clearValidate()
      this.$nextTick(() => {
        this.$refs.ruleForm.validate(valid => {})
      })
    },
    // 获取备注内容
    getRemarkInfo() {
      if (!this.orderInfo.operateLog || this.orderInfo.operateLog.length <= 0) {
        this.form.remarkInfo = ''
        return
      }
      this.form.remarkInfo = this.orderInfo.operateLog
        .map(
          item =>
            `${item.nodeCode ? item.nodeCode : '无'}\n<${item.createdBy}> ${
              item.createdTime
            }`
        )
        .join('\n')
    },
    getTips() {
      console.log('没进来吗1')
      let remarkArr = [
        {
          key: '0',
          value:
            '未按要求提供授权书，授权书模板可通过车主证件上传页面右上方下载'
        },
        {
          key: '1',
          value: '授权书未按手印或未盖章，请修改后重新上传'
        },
        {
          key: '2',
          value:
            '很抱歉，当前仅支持广西区内用户在线申办，请确保所填地址为广西区内有效地址'
        },
        {
          key: '3',
          value: '道路运输证不清晰，请重新拍照上传'
        },
        {
          key: '4',
          value: '用户来电要求取消订单'
        },
        {
          key: '5',
          value: '设备编号不清晰，请重新上传带有编号的ETC卡及设备照片'
        },
        {
          key: '6',
          value: '用户来电要求撤销售后申请'
        }
      ]
      // //审核不通过(新办)
      // if (this.orderBasicInfo.nodeCode < 1010) {
      //   console.log('没进来吗2')
      //   remarkArr = [
      //     {
      //       key: '0',
      //       value:
      //         '未按要求提供授权书，授权书模板可通过车主证件上传页面右上方下载'
      //     },
      //     {
      //       key: '1',
      //       value: '授权书未按手印或未盖章，请修改后重新上传'
      //     },
      //     {
      //       key: '2',
      //       value:
      //         '很抱歉，当前仅支持广西区内用户在线申办，请确保所填地址为广西区内有效地址'
      //     },
      //     {
      //       key: '3',
      //       value: '道路运输证不清晰，请重新拍照上传'
      //     },
      //     {
      //       key: '4',
      //       value: '用户来电要求取消订单'
      //     }
      //   ]
      // } else if (this.orderBasicInfo.nodeCode == '2000') {
      //   console.log('没进来吗3')
      //   remarkArr = [
      //     {
      //       key: '0',
      //       value: '设备编号不清晰，请重新上传带有编号的ETC卡及设备照片'
      //     },
      //     {
      //       key: '1',
      //       value: '用户来电要求撤销售后申请'
      //     }
      //   ]
      // } else if (
      //   this.orderBasicInfo.nodeCode == '2000' ||
      //   ((this.orderBasicInfo.nodeCode == '2010' ||
      //     this.orderBasicInfo.nodeCode == '2020') &&
      //     this.orderBasicInfo.nodeStatus == '4') ||
      //   (this.orderBasicInfo.nodeCode == '2020' &&
      //     this.orderInfo.postSaleInfo.equipmentReturnType == '0')
      // ) {
      //   console.log('没进来吗4')
      //   remarkArr = [
      //     {
      //       key: '0',
      //       value: '用户来电要求撤销售后申请'
      //     }
      //   ]
      // } else if (
      //   this.orderBasicInfo.nodeCode < 1010 ||
      //   this.orderBasicInfo.nodeCode == '1010' ||
      //   this.orderBasicInfo.nodeCode == '1020'
      // ) {
      //   console.log('没进来吗5')
      //   remarkArr = [
      //     {
      //       key: '0',
      //       value: '用户来电要求取消订单'
      //     }
      //   ]
      // }
      this.remarkTips = remarkArr
    },
    tipsChange(event) {
      console.log('envent', event)
      this.form.remark = event
    }
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  .label {
    width: 80px;
    padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 50px;
    flex: 1;
  }
}

.process-record {
  padding-top: 20px;
  ::v-deep .el-input {
    width: 100%;
  }
  ::v-deep .descriptions-remark {
    padding: 10px;
    .el-form-item {
      margin-bottom: 8px;
    }
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }
}
</style>