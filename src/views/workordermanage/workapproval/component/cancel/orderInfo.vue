<template>
  <div>
    <el-descriptions :column="4" border size="medium" title="订单基础信息" class="descriptions-content">
      <el-descriptions-item label="订单号">{{logoutOrderBaseInfo.orderId}}</el-descriptions-item>
      <el-descriptions-item
        label="订单状态"
      >{{ queryGroup.orderStatusObj[logoutOrderBaseInfo.orderStatus] }}</el-descriptions-item>

      <el-descriptions-item label="订单类型">{{logoutOrderBaseInfo.orderType}}</el-descriptions-item>

      <el-descriptions-item label="提交时间">{{logoutOrderBaseInfo.createTime}}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getbusinessType } from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    queryGroup: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return { logoutOrderBaseInfo: {} }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          console.log(val, 'logoutOrderBaseInfo')
          this.logoutOrderBaseInfo = this.orderInfo.logoutOrderBaseInfo
        }
      }
    }
  },
  created() {},
  methods: {
    getbusinessType
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>