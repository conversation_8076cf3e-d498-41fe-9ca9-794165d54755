<template>
  <div class="detail-wrap" v-if="orderInfo">
    <!-- 用户基础信息 -->
    <div class="orderItem">
      <basicUserInfo v-bind="$attrs" :orderInfo="orderInfo" />
    </div>
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <orderInfo v-bind="$attrs" :orderInfo="orderInfo" />
    </div>
    <!-- 注销原因调研 -->
    <div class="orderItem">
      <cancelReason v-bind="$attrs" :isView="isView" :orderInfo="orderInfo" />
    </div>
    <!-- 支付信息 -->
    <div class="orderItem">
      <payInfo
        v-bind="$attrs"
        :isView="isView"
        :orderInfo="orderInfo"
      ></payInfo>
    </div>
    <!-- 用户申请信息 银行收款信息 退款信息 -->
    <div class="orderItem">
      <userApplyInfo
        ref="userApplyInfo"
        v-bind="$attrs"
        :isView="isView"
        :orderInfo="orderInfo"
        :vehicleColorCode="vehicleColorCode"
      ></userApplyInfo>
    </div>
    <!-- 订单履历 -->
    <div class="orderItem">
      <orderHistory
        v-bind="$attrs"
        v-if="isView"
        :orderInfo="orderInfo"
      ></orderHistory>
    </div>
    <!-- 操作 -->
    <div class="orderItem" style="margin-bottom:30px">
      <div
        class="btns g-flex g-flex-center"
        v-if="!isView && showBtn('operateWrap')"
      >
        <el-button
          type="primary"
          style="margin:0 20px"
          v-show="showBtn('pass')"
          @click="handle('pass')"
          >审核通过</el-button
        >
        <el-button
          type="primary"
          style="margin:0 20px"
          v-show="showBtn('keep') && !cancelDate"
          @click="handle('pass')"
          >继续注销</el-button
        >
        <el-button
          type="primary"
          style="margin:0 20px"
          v-show="showBtn('logout')"
          @click="handle('logout')"
          >发起注销</el-button
        >
        <el-button
          type="primary"
          v-show="showBtn('noPass')"
          @click="handle('noPass')"
          >审核不通过</el-button
        >
        <el-button
          type="primary"
          v-show="showBtn('notice')"
          @click="handle('notice')"
          >通知用户处理</el-button
        >
        <el-button
          style="margin:0 20px"
          v-show="showBtn('change')"
          @click="handle('change')"
          v-permisaction="['onlineLogout:logoutModify']"
          >修改信息</el-button
        >
        <el-button
          style="margin:0 20px"
          v-show="showBtn('cancel')"
          @click="handle('cancel')"
          >取消订单</el-button
        >
        <el-button
          style="margin:0 20px"
          v-show="showBtn('totalCancel') && isTotal"
          @click="handle('cancel')"
          >取消订单</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import basicUserInfo from './basic-userInfo'
import orderInfo from './orderInfo.vue'
import payInfo from './payInfo.vue'
import orderHistory from './orderHistory.vue'
import userApplyInfo from './user-apply-infor.vue'
import cancelReason from './cancel-reason.vue'
import {
  cancelOrderDetail,
  logoutProcess,
  logoutDeviceHandle,
  logoutRecordCancel,
  noticeUserHandle,
  logoutModify
} from '@/api/workordermanage'

export default {
  components: {
    basicUserInfo,
    orderInfo,
    cancelReason,
    payInfo,
    orderHistory,
    userApplyInfo
  },
  props: {
    orderId: [String, Number],
    slideVisible: {
      type: Boolean,
      default: ''
    },
    vehicleColorCode: [String, Number],
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      orderInfo: null
    }
  },
  computed: {
    orderStatus() {
      // {
      //   '410': '注销欠款',
      //   '401': '待审核',
      //   '402': '审核通过',
      //   '403': '审核不通过',
      //   '404': '注销不成功',
      //   '405': '已注销清算中',
      //   '406': '注销退款中',
      //   '407': '注销退款不成功',
      //   '408': '已完结',
      //   '409': '已取消'
      // }
      return this.orderInfo.logoutOrderBaseInfo.orderStatus
    },
    isTotal() {
      return this.orderInfo.logoutUserBaseInfo.bindingCode == 'ETCCHINA'
    },
    cancelDate() {
      // 配合继续注销使用。 当状态为审核通过且cancelDate无值时，隐藏继续注销按钮
      return this.orderInfo.logoutOrderBaseInfo.cancelDate
    }
  },
  methods: {
    //获取订单详情
    async getDetail() {
      if (!this.orderId) return
      let params = {
        orderId: this.orderId
      }
      this.orderBasicInfo = {}
      this.startLoading()
      let res = await cancelOrderDetail(params)
      if (res.code == 200) {
        this.endLoading()
        this.orderBasicInfo = res.data.orderInfo
        this.orderInfo = res.data
      } else {
        this.endLoading()
        this.$message({
          message: res.msg,
          type: 'error'
        })
      }
    },
    // 根据详情状态显示按钮
    showBtn(type) {
      let config = {
        pass: ['401'],
        keep: ['402'],
        noPass: ['401'],
        logout: ['404'],
        notice: ['404'],
        cancel: ['401', '403', '404'],
        totalCancel: ['402'],
        change: ['401'],
        operateWrap: ['401', '402', '403', '404']
      }
      console.log(type, config[type])
      return config[type].includes(this.orderStatus)
      // return true
    },
    // 操作
    async handle(type) {
      const { remark } = this.$refs.userApplyInfo.form
      const params = {
        orderId: this.orderId,
        remark
      }
      let fn = {
        pass: logoutProcess,
        noPass: logoutProcess,
        logout: logoutDeviceHandle,
        notice: noticeUserHandle,
        cancel: logoutRecordCancel
      }
      this.$refs.userApplyInfo.checkRules(type)
      try {
        switch (type) {
          case 'pass':
            params.checkFlag = 0
            break
          case 'noPass':
            if (!remark) {
              this.$message.error('请输入备注内容')
              return
            }
            params.checkFlag = 1
            break
          case 'logout':
            delete params.remark
            break
          case 'notice':
            if (!remark) {
              this.$message.error('请输入备注内容')
              return
            }
            break
          case 'cancel':
            if (!remark) {
              this.$message.error('请输入备注内容')
              return
            }
            break
          case 'change':
            this.changeInfo()
            return
          default:
            return
        }
        let res = await fn[type](params)
        console.log(res, params)
        if (res.code === 200) {
          if (type == 'pass' && res.data) {
            this.$alert(`操作成功，此车辆已做解除车牌占用`, '提示', {
              dangerouslyUseHTMLString: true,
              showClose: false,
              confirmButtonText: '确定'
            })
          } else {
            this.$message.success('操作成功')
          }
          this.getDetail()
        }
        this.getDetail()
      } catch (error) {
        // 处理异步操作的错误
        console.error(error)
        this.getDetail()
      }
    },
    async changeInfo() {
      console.log(
        this.$refs.userApplyInfo.form,
        this.$refs.userApplyInfo.imgList
      )
      let info = JSON.parse(JSON.stringify(this.$refs.userApplyInfo.form))
      let userInfoImg = this.$refs.userApplyInfo.imgList
      let bankImgList = this.$refs.userApplyInfo.bankImgList
      let imgArr = userInfoImg.concat(bankImgList)
      let logoutArchivesModel = imgArr
        .filter(el => el.code)
        .map(item => {
          return { uploadPath: item.code, uploadType: item.photo_code }
        })
      let params = {
        orderId: this.orderId,
        bankName: info.bankName,
        bankAccount: info.bankNo,
        // remark:info.remark,
        beneficiaryName: info.bankHolder,
        archives: logoutArchivesModel
      }
      console.log(params, bankImgList, 'bankImgList')
      let res = await logoutModify(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.getDetail()
      }
    }
  },
  created() {
    this.getDetail()
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>