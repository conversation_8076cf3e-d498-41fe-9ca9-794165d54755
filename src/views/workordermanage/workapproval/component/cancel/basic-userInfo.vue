<template>
  <div>
    <el-descriptions
      :column="4"
      border
      size="medium"
      title="用户基础信息"
      class="descriptions-content"
    >
      <el-descriptions-item label="用户名称">{{
        logoutUserBaseInfo.userName
      }}</el-descriptions-item>
      <el-descriptions-item label="用户类型">{{
        logoutUserBaseInfo.accountType == 1 ? '单位' : '个人'
      }}</el-descriptions-item>

      <el-descriptions-item label="联系人">{{
        logoutUserBaseInfo.contacts
      }}</el-descriptions-item>

      <el-descriptions-item label="联系电话">{{
        logoutUserBaseInfo.mobile
      }}</el-descriptions-item>

      <el-descriptions-item label="车牌号">{{
        logoutUserBaseInfo.vehicleNo
      }}</el-descriptions-item>

      <el-descriptions-item label="车牌颜色">{{
        logoutUserBaseInfo.vehicleColor
      }}</el-descriptions-item>

      <el-descriptions-item label="八桂行卡号">{{
        logoutUserBaseInfo.cardNo
      }}</el-descriptions-item>

      <el-descriptions-item label="OBU号">{{
        logoutUserBaseInfo.obuNo
      }}</el-descriptions-item>

      <el-descriptions-item label="车型">{{
        getCarType(logoutUserBaseInfo.carType)
      }}</el-descriptions-item>

      <el-descriptions-item label="产品类型">{{
        queryGroup.productTypeObj[logoutUserBaseInfo.productType] || '-'
      }}</el-descriptions-item>

      <el-descriptions-item label="绑定渠道">{{
        logoutUserBaseInfo.bindingChannel
      }}</el-descriptions-item>
      <el-descriptions-item label="代扣渠道">{{
        logoutUserBaseInfo.deductionChannel
      }}</el-descriptions-item>

      <el-descriptions-item label="八桂行卡状态">{{
        logoutUserBaseInfo.cardStatus
      }}</el-descriptions-item>

      <el-descriptions-item label="卡质保期">{{
        logoutUserBaseInfo.cardPeriod
      }}</el-descriptions-item>

      <el-descriptions-item label="OBU状态">{{
        logoutUserBaseInfo.obuStatus
      }}</el-descriptions-item>

      <el-descriptions-item label="OBU质保期">{{
        logoutUserBaseInfo.obuPeriod
      }}</el-descriptions-item>
      <el-descriptions-item label="证件类型">
        <span v-show="logoutUserBaseInfo.accountType == '0'">{{
          getPersonalOCRType(logoutUserBaseInfo.custIdType)
        }}</span>
        <span v-show="logoutUserBaseInfo.accountType == '1'">{{
          getenterpriseOCRType(logoutUserBaseInfo.custIdType)
        }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="证件号">
        {{ logoutUserBaseInfo.custIdNo }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {
  getbusinessType,
  getCarType,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getPersonalOCRType,
  getenterpriseOCRType
} from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    queryGroup: {
      type: Object,
      default: () => {}
    }
  },
  components: {},
  data() {
    return { logoutUserBaseInfo: {} }
  },
  computed: {},
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          console.log(val, 'logoutUserBaseInfo')
          this.logoutUserBaseInfo = this.orderInfo.logoutUserBaseInfo
        }
      }
    }
  },
  created() {},
  methods: {
    getbusinessType,
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getPersonalOCRType,
    getenterpriseOCRType
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>