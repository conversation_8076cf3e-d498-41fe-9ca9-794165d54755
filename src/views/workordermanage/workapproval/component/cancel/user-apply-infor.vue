<template>
  <div class="apply-info" style="padding-bottom:10px;">
    <!-- 用户申请信息以及图片 -->
    <el-descriptions
      :column="3"
      border
      size="medium"
      title="用户申请信息"
      class="descriptions-content"
    ></el-descriptions>
    <archivesBox
      class="archivesBox"
      uploadType="CACHEIMG"
      :customerId="basicUserInfo.custMastId"
      :vehicle_code="basicUserInfo.vehicleNo"
      :vehicle_color="vehicleColorCode"
      :other_code="basicUserInfo.cardNo"
      :scene="6"
      :pictureSource="imgList"
      @on-upload="onUploadHandle"
      @on-delete="onDeleteHandle"
      :previewMode="!isChange"
    ></archivesBox>

    <!-- 开户信息以及图片 -->
    <el-descriptions
      :column="3"
      border
      size="medium"
      :title="form.accountFlag == 2? '互联网账户收款信息' : '银行收款信息'"
      class="descriptions-content"
    >
      <el-descriptions-item label="开户人">
        <el-input
          v-if="isChange"
          v-model="form.bankHolder"
          clearable
        ></el-input>
        <span v-else>{{ form.bankHolder }}</span>
      </el-descriptions-item>
      <el-descriptions-item :label="form.accountFlag == 2 ? '登录账号' : '开户行'">
        <el-input v-if="isChange" v-model="form.bankName" clearable></el-input>
        <span v-else>{{ form.bankName }}</span>
      </el-descriptions-item>
      <el-descriptions-item :label="form.accountFlag == 2 ? '账户编号' : '银行卡号'">
        <el-input v-if="isChange" v-model="form.bankNo" clearable></el-input>
        <span v-else>{{ form.bankNo }}</span>
      </el-descriptions-item>
      <el-descriptions-item :label="'卡内可用余额 (仅供参考)'">{{ form.cardAmount | moneyFilter }}  元</el-descriptions-item>
      <el-descriptions-item :label="'退款方式'">{{ form.accountFlag == 2?'退至当前ETC账户余额':form.accountFlag == 1?'直接退至银行账户':'放弃余额' }}</el-descriptions-item>
    </el-descriptions>
    <archivesBox
      class="archivesBox"
      uploadType="CACHEIMG"
      :customerId="basicUserInfo.custMastId"
      :vehicle_code="basicUserInfo.vehicleNo"
      :vehicle_color="vehicleColorCode"
      :other_code="basicUserInfo.cardNo"
      :scene="6"
      :pictureSource="bankImgList"
      @on-upload="onUploadHandleBank"
      @on-delete="onDeleteHandleBank"
      :previewMode="!isChange"
    ></archivesBox>

    <!-- 退款信息 -->
    <refundInfo v-if="orderInfo.logoutRefundInfo" :orderInfo="orderInfo" />

    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="ruleForm"
    >
      <!-- 备注记录 待审核时隐藏-->
      <el-form-item
        label="备注："
        v-if="form.remarkInfo && !['401'].includes(orderStatus)"
        prop="remarkInfo"
      >
        <el-input
          type="textarea"
          :disabled="true"
          :rows="4"
          v-model="form.remarkInfo"
        ></el-input>
      </el-form-item>
      <!-- 备注填写 -->
      <el-form-item
        label="备注"
        v-if="!isView && ['401','402', '403', '404'].includes(orderStatus)"
        prop="remark"
      >
        <el-input
          type="textarea"
          :disabled="isView"
          :rows="3"
          placeholder="请输入内容"
          v-model="form.remark"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import archivesBox from '../../../component/photograph.vue'
import refundInfo from './refund-info.vue'

const imgCodeObj = {
  22: '退款身份证(人)',
  23: '退款身份证(徽)',
  21: '退款申请单',
  33: '银行卡',
  74: '注销回执单',
  24: '退款单位介绍信',
  25: '委托书',
  26: '退款代办人身份证(人)',
  27: '退款代办人身份证(徽)'
}
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    },
    vehicleColorCode:[String, Number],
  },
  components: { archivesBox, refundInfo },
  data() {
    return {
      form: {},
      imgList: [
        // {
        //   isShow: true,
        //   file_url: '',
        //   file_serial: '',
        //   photo_code: 'drivingLicenseFrontUrl',
        //   lable: '身份证人像面'
        // },
        // {
        //   isShow: true,
        //   file_url: '',
        //   file_serial: '',
        //   photo_code: 'drivingLicenseSubpageUrl',
        //   lable: '身份证国徽面'
        // },
        // {
        //   isShow: true,
        //   file_url: '',
        //   file_serial: '',
        //   photo_code: 'obuFailurePhotoUrl',
        //   lable: '车辆授权书'
        // }
      ],
      bankImgList: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '33',
          lable: '银行卡照片'
        }
      ],
      applyPic: [],
      handleType: '',
      rulesEx: {
        remark: [{ required: true, message: '请输入备注内容', trigger: 'blur' }]
      }
    }
  },
  computed: {
    rules() {
      return ['noPass', 'notice', 'cancel'].includes(this.handleType)
        ? this.rulesEx
        : {}
    },
    orderStatus() {
      return this.orderInfo.logoutOrderBaseInfo.orderStatus
    },
    basicUserInfo() {
      return this.orderInfo.logoutUserBaseInfo
    },
    isChange() {
      console.log(['401'].includes(this.orderStatus) && !this.isView)
      return ['401'].includes(this.orderStatus) && !this.isView
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.form = {
            ...this.orderInfo.logoutBankCollectionInfo
          }
          this.getRemarkInfo()

          if (val.logoutUserApplyInfo) {
            this.applyPic = val.logoutUserApplyInfo
            this.init()
          }
        }
      }
    }
  },
  methods: {
    init() {
      // this.imgList.forEach((item, idx) => {
      //   item.file_url = this.applyPic[idx]
      // })

      this.imgList = this.applyPic.map(item => {
        let { code, url, des } = item
        let imgObj = {
          isShow: true,
          file_url: url,
          file_serial: '',
          photo_code: code,
          lable: des
        }
        return imgObj
      })

      let { bankCardImg } = this.form
      this.bankImgList[0].file_url = bankCardImg
      console.log(this.bankImgList, 'bankImgList')
    },
    checkRules(type) {
      this.handleType = type
      this.$refs.ruleForm.clearValidate()
      this.$nextTick(() => {
        this.$refs.ruleForm.validate(valid => {})
      })
    },
    // 获取备注内容
    getRemarkInfo() {
      this.form.remarkInfo = this.orderInfo.logoutRemarkInfo
        .map(
          item =>
            `${item.operateRemark ? item.operateRemark : '无'}\n${
              item.operateRecord
            }`
        )
        .join('\n')
    },
    onUploadHandle(result) {
      if (result.data) {
        for (let i = 0; i < this.imgList.length; i++) {
          if (this.imgList[i].photo_code == result.data.photo_code) {
            this.imgList[i].file_url = result.data.file_url
            this.imgList[i].code = result.data.code
            console.log(this.imgList[i], 'this.imgList[i]')
          }
        }
      }
    },
    onDeleteHandle(data) {
      for (let i = 0; i < this.imgList.length; i++) {
        if (this.imgList[i].photo_code == data.photo_code) {
          this.imgList[i].file_url = ''
          this.imgList[i].file_serial = ''
        }
      }
    },
    onUploadHandleBank(result) {
      console.log(************)
      if (result.data) {
        for (let i = 0; i < this.bankImgList.length; i++) {
          if (this.bankImgList[i].photo_code == result.data.photo_code) {
            this.bankImgList[i].file_url = result.data.file_url
            this.bankImgList[i].code = result.data.code
            this.form.bankCardImg = result.data.file_url
          }
        }
      }
    },
    onDeleteHandleBank(data) {
      for (let i = 0; i < this.bankImgList.length; i++) {
        if (this.bankImgList[i].photo_code == data.photo_code) {
          this.bankImgList[i].file_url = ''
          this.bankImgList[i].file_serial = ''
        }
      }
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  .label {
    width: 80px;
    padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 50px;
    flex: 1;
  }
}

.archivesBox {
  padding-left: 15px;
  // ::v-deep .archives-item__text-area {
  //   display: none;
  // }
}
.apply-info {
  ::v-deep .el-input {
    width: 100%;
  }
}
</style>