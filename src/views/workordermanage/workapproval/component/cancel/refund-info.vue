<template>
  <div>
    <!-- 开户信息以及图片 -->
    <el-descriptions
      :column="3"
      border
      size="medium"
      title="退款信息"
      class="descriptions-content"
    >
      <el-descriptions-item label="退款订单号">{{
        form.orderId
      }}</el-descriptions-item>
      <el-descriptions-item label="退款进度">{{
        form.orderStatus
      }}</el-descriptions-item>
      <el-descriptions-item label="更新时间">{{
        form.updateTime
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {},
      orderStatusConfig: {
        0: '用户申请退款',
        1: '业务员审核',
        2: '清算中',
        3: '网点审核',
        4: '运营部复核',
        5: '制表',
        6: '财务打款',
        7: '待退互联网账户',
        9: '已归档'
      }
    }
  },
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          this.form = {
            ...this.orderInfo.logoutRefundInfo
          }
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  .label {
    width: 80px;
    padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 50px;
    flex: 1;
  }
}
</style>