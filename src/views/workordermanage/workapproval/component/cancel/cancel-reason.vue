<template>
  <div style="padding-bottom:10px;">
    <el-descriptions :column="5" border title="注销原因调研" class="descriptions-content">
      <template slot="extra">
        <el-button type="text" @click="isExpand=!isExpand">
          <i class="expand-icon" :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>
    </el-descriptions>
    <template v-if="isExpand">
      <div class="g-flex">
        <div class="label">问题1:</div>
        <div class="val">{{orderInfo.logoutSurveyInfo.questionOne}}</div>
      </div>
      <div class="g-flex">
        <div class="label">答案:</div>
        <div class="val">
          <el-input
            type="textarea"
            :rows="2"
            :disabled="true"
            v-model="orderInfo.logoutSurveyInfo.answerOne"
          ></el-input>
        </div>
      </div>
      <div class="g-flex">
        <div class="label">问题2:</div>
        <div class="val">{{orderInfo.logoutSurveyInfo.questionTwo}}</div>
      </div>
      <div class="g-flex">
        <div class="label">答案:</div>
        <div class="val">
          <el-input
            type="textarea"
            :rows="2"
            :disabled="true"
            v-model="orderInfo.logoutSurveyInfo.answerTwo"
          ></el-input>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpand: true
    }
  },
  created() {
    if (!this.isView) {
      this.isExpand = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';
.g-flex {
  margin-bottom: 20px;
  .label {
    width: 80px;
    // padding: 10px;
    margin-left: 10px;
  }
  .val {
    padding-right: 200px;
    flex: 1;
  }
}
</style>