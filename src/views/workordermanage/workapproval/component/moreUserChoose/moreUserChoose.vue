<template>
  <div class="form">
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
      :modal="false"
    >
      <el-table
        :data="multipleSelection"
        border
        height="300"
        style="width: 100%; margin-bottom: 10px"
      >
        <el-table-column
          prop="custMastId"
          align="center"
          label="选择(用户编号)"
        >
          <template slot-scope="scope">
            <el-radio
              v-model="custMastId"
              :label="scope.row.custMastId"
            ></el-radio>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="custMastId" align="center" label="用户编号" /> -->
        <el-table-column
          prop="custName"
          align="center"
          label="用户名称"
          width="180"
        />
        <el-table-column prop="custMobile" align="center" label="用户手机" />
        <el-table-column prop="custIdNo" align="center" label="证件号码" />
        <el-table-column prop="operateTime" align="center" label="操作时间" />
        <el-table-column prop="operatorStr" align="center" label="操作人" />
        <el-table-column prop="branchCodeStr" align="center" label="网点代码" />
      </el-table>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >选择</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    multipleSelection: {
      type: Array,
      default: [],
    },
  },

  data() {
    return {
      dialogFormVisible: false,
      title: '一证多户选择',
      custMastId: '', //选择的id
    }
  },
  // computed: {
  //   msgDesc() {
  //     return this.msgBoxTypeList[msgBoxType]
  //   },
  // },
  watch: {
    visible: function (val) {
      console.log('一阵多户======>>>>>>>>>>', val)
      this.$nextTick(() => {
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  created() {
    console.log('一阵多户创建====', this.visible)
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible
    })
  },
  methods: {
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.selection = []
    },
    // 表单提交
    submitForm() {
      this.$emit('chooseUser', this.custMastId)
    },
    cancel() {
      // this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      // this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
// .el-form-item__label {
//   text-align: center;
//   white-space: nowrap;
// }
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
</style>
