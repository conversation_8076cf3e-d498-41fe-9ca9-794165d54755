<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:订单履历
  * @author:zhangys
  * @date:2023/04/11 16:40:25
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     title="订单履历"
                     style="padding:24px 16px 0px 16px">
    </el-descriptions>
    <div style="padding:0 16px 10px 16px">
      <el-table :data="tableData"
                align="center"
                header-align="center"
                border
                style="width: 100%; margin-bottom: 20px"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="opName"
                         align="center"
                         label="操作人" />
        <el-table-column prop="opTime"
                         align="center"
                         label="操作时间"
                         width="180px">
        </el-table-column>
        <el-table-column prop="opContent"
                         align="center"
                         label="操作内容" />
        <el-table-column prop="opResult"
                         align="center"
                         label="操作结果">

        </el-table-column>

        <el-table-column prop="remark"
                         align="center"
                         min-width="250"
                         label="备注" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (
        Object.keys(this.orderDetail).length != 0 &&
        this.orderDetail.orderLog
      ) {
        this.tableData = this.orderDetail.orderLog
      } else {
        this.tableData = []
      }
    },
  },
  created() {},
  methods: {},
}
</script>

<style lang='scss' scoped>
</style>