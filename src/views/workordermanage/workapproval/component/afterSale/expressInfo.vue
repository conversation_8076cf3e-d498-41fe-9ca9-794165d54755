<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:物流信息
  * @author:zhangys
  * @date:2023/04/11 16:30:07
-->
<template>
  <div>
    <el-descriptions :column="5"
                     border
                     title="物流信息"
                     class=""
                     style="padding:16px 16px 0 16px">
      <template slot="extra">
        <el-button type="text"
                   @click="isExpand=!isExpand">
          <i class="expand-icon"
             :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>

    </el-descriptions>
    <div v-if="isExpand">
      <div v-for="(item,index) in orderInfo" :key="index">
        <div style="padding:0 20px;font-size: 13px;">
          <span style="font-weight:bold;">{{item.expressType=='0'?'捷通寄出':'用户寄回'}}</span>
          <span style="font-size: 13px;"
                >{{ item.routeType=='1' ? ':自行寄回' : item.routeType=='0'? ':上门取件' :'' }}
          </span>
        </div>

        <el-descriptions :column="5"
                         border
                         class=""
                         style="padding: 8px 16px 16px 16px;">

          <el-descriptions-item label="物流公司">
            {{item.expressName }}
          </el-descriptions-item>
          <el-descriptions-item label="物流单号">
            {{item.expressId}} <el-button size="mini"
                       type="text"
                       @click="viewExpressDetail(item.expressId)">{{item.expressId?'查看详情':''}}</el-button>
          </el-descriptions-item>
          <el-descriptions-item label="寄件时间">
            {{item.DeliveryTime}}
          </el-descriptions-item>
          <el-descriptions-item label="收货时间">
            {{item.SignTime}}
          </el-descriptions-item>
          <el-descriptions-item label="物流状态">
            {{ getAfterExpressStatus(item.expressStatus)}}
          </el-descriptions-item>

        </el-descriptions>
      </div>
    </div>
    <expressDetail :visible.sync="expressDetailDialog"
                   :v-if="expressDetailDialog"
                   :expressNo="expressNo"></expressDetail>
  </div>
</template>

<script>
import { getAfterExpressStatus } from '@/common/method/formatOptions.js'
import expressDetail from '../newApply/expressDetail.vue'
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: { expressDetail },
  data() {
    return {
      orderInfo: {},
      isExpand: false,
      expressDetailDialog: false,
      expressNo: '',
    }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (Object.keys(val) != 0) {
        this.orderInfo = val.expressInfo
        this.getExpandStatus()
      } else {
        this.orderInfo = {}
      }
    },
  },
  created() {},
  methods: {
    getAfterExpressStatus,
    viewExpressDetail(val) {
      this.expressDetailDialog = true
      this.expressNo = val
    },
    getExpandStatus() {
      if (this.orderDetail.isView) {
        this.isExpand = true
      } else {
        this.isExpand = false
      }
    },
  },
}
</script>

<style lang='scss' scoped>
</style>