<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:订单基础信息
  * @author:zhangys
  * @date:2023/04/11 15:50:13
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     size="medium"
                     title="订单基础信息"
                     class="descriptions-content">

      <el-descriptions-item label="订单号">
        {{ orderInfo.orderId }}
      </el-descriptions-item>
      <el-descriptions-item label="订单状态">
        <div v-if="orderInfo.orderType=='1'">{{getAfterSaleChangeStatus(orderInfo.orderStatus)  }}</div>
        <div v-if="orderInfo.orderType=='0'">{{getAfterSaleRemakeStatus(orderInfo.orderStatus)  }}</div>
      </el-descriptions-item>
      <el-descriptions-item label="订单类型">
        {{ orderInfo.orderType=='0'?'补领':'更换' }}

      </el-descriptions-item>
      <el-descriptions-item label="提交时间">
        {{ orderInfo.createTime }}

      </el-descriptions-item>
      <el-descriptions-item label="申请渠道">
        {{getApplyChannelOptions(orderInfo.applyChannel)  }}

      </el-descriptions-item>
      <el-descriptions-item label="收货人"
                            :span="addressData.editInfo?2:2">
        <div v-if="isEdit&&addressData.editInfo">
          <el-input v-model=" orderInfo.recipient "></el-input>
        </div>
        <div v-else>
          {{ orderInfo.recipient }}

        </div>

      </el-descriptions-item>
      <el-descriptions-item label="收货电话"
                            :span="addressData.editInfo?2:2">
        <div v-if="isEdit&&addressData.editInfo">
          <el-input v-model="orderInfo.recipientPhone"></el-input>
        </div>
        <div v-else>
          {{ orderInfo.recipientPhone }}

        </div>
      </el-descriptions-item>
      <el-descriptions-item label="收货地址"
                            span='4'>
        <div v-if="isEdit&&addressData.editInfo">
          <el-select v-model="addressData.provinceName"
                     style="margin:0 4px"
                     placeholder="请选择">
            <el-option v-for="(value,key) in provinceNameData"
                       :label="value.label"
                       :value="value.label"
                       :key='key'></el-option>
          </el-select>

          <el-select v-model="addressData.cityName"
                     placeholder="请选择市"
                     style="margin:0 4px"
                     @change="cityChange">
            <el-option v-for="(value,key) in cityNameData"
                       :label="value.label"
                       :value="value.label"
                       :key='key'></el-option>
          </el-select>

          <el-select v-model="addressData.areaName"
                     placeholder="请选择区/县"
                     style="margin:0 4px"
                     @change="areaChange">
            <el-option v-for="(value,key) in areaNameData"
                       :label="value.label"
                       :value="value.label"
                       :key='key'></el-option>
          </el-select>

          <el-input style="width:30%"
                    placeholder="详细地址"
                    v-model="orderInfo.recipientAddress"></el-input>
        </div>
        <div v-else>
          {{orderInfo.recipientAreaName}} {{ orderInfo.recipientAddress }}

        </div>
      </el-descriptions-item>

    </el-descriptions>
    <div style=" margin: 0px 16px 16px 16px"
         v-if="isEdit&&isEditInfo">
      <el-button size="medium"
                 type="primary"
                 plain
                 v-if="!addressData.editInfo"
                 style="width: 100%; border-style: dashed"
                 @click="editOrderInfo">修改订单信息</el-button>
      <el-button size="medium"
                 type="primary"
                 plain
                 v-if="addressData.editInfo"
                 style="width: 100%; border-style: dashed"
                 @click="updateOrderInfo">确认修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  getbusinessType,
  gxCardTypeFilter,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getAfterSaleChangeStatus,
  getAfterSaleRemakeStatus,
} from '@/common/method/formatOptions'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return {
      orderInfo: {},

      provinceNameData: [],
      cityNameData: [],
      areaNameData: [],

      addressData: {
        areaName: '',
        cityName: '',
        provinceName: '广西壮族自治区',
        recipientAddress: '',
        recipientAreaCode: '',
        editInfo: false,
      },
    }
  },
  computed: {
    ...mapGetters(['address']),
    isEdit() {
      return !this.orderDetail.isView
    },
    isEditInfo() {
      if (
        (this.orderInfo.orderType == '1' && this.orderInfo.orderStatus < 209) ||
        (this.orderInfo.orderType == '0' && this.orderInfo.orderStatus < 304)
      ) {
        return true
      }
      return false
    },
  },
  watch: {
    orderDetail(val) {
      if (Object.keys(this.orderDetail).length != 0) {
        this.orderInfo = this.orderDetail.orderInfo
        for (let key in this.addressData) {
          this.addressData[key] = ''
        }
        this.provinceNameData = this.address[0]
        this.cityNameData = this.provinceNameData[0].child
        this.getArea(this.orderInfo.recipientAreaName)
        this.addressData.recipientAreaCode = this.orderInfo.recipientAreaCode
      } else {
        this.orderInfo = {}
      }
    },
  },
  created() {},
  methods: {
    getbusinessType,
    gxCardTypeFilter,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getAfterSaleChangeStatus,
    getAfterSaleRemakeStatus,

    getArea(str) {
      if (!str) return
      let splitArea = str.split('-')
      this.addressData.provinceName = splitArea[0]
      this.addressData.cityName = splitArea[1]
      this.addressData.areaName = splitArea[2]
      this.cityChange(this.addressData.cityName, 'first')
    },
    cityChange(val, type) {
      this.areaNameData = []
      if (!type) {
        delete this.addressData.areaName
      }
      for (let i = 0; i < this.cityNameData.length; i++) {
        if (this.cityNameData[i].label == val) {
          this.areaNameData = this.cityNameData[i].child
        }
      }
    },
    areaChange(val) {
      for (let i = 0; i < this.areaNameData.length; i++) {
        if (this.areaNameData[i].label == val) {
          this.addressData.recipientAreaCode = this.areaNameData[i].value
        }
      }
    },
    editOrderInfo() {
      this.addressData.editInfo = true
    },
    updateOrderInfo() {
      if (
        this.addressData.editInfo &&
        (!this.addressData.provinceName ||
          !this.addressData.cityName ||
          !this.addressData.areaName)
      ) {
        this.$message.warning('请完善订单信息收货地址')
        return
      }

      let params = {
        orderId: this.orderInfo.orderId,
        recipient: this.orderInfo.recipient,
        recipientPhone: this.orderInfo.recipientPhone,
        recipientAreaCode: this.addressData.recipientAreaCode,
        recipientAddress: this.orderInfo.recipientAddress,
        recipientAreaName:
          this.addressData.provinceName +
          '-' +
          this.addressData.cityName +
          '-' +
          this.addressData.areaName,
      }
      this.$request({
        url: this.$interfaces.afterSaleInfoEdit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('订单信息修改成功！')
            this.$emit('getDetail')
          } else {
            this.$emit('getDetail')
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.$emit('getDetail')
        })
    },
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>