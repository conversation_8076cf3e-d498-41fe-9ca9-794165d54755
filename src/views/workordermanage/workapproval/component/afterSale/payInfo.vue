<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:补办更换--支付信息
  * @author:zhangys
  * @date:2023/04/11 16:30:07
-->
<template>
  <div>
    <el-descriptions :column="4"
                     border
                     title="支付信息"
                     class="descriptions-content"
                     style="padding:20px 20px 0 20px">
      <template slot="extra">
        <el-button type="text"
                   @click="isExpand=!isExpand">
          <i class="expand-icon"
             :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"></i>
        </el-button>
      </template>

      <template v-if="isExpand">

        <el-descriptions-item label="订单支付状态">
          {{getpayStatus(orderInfo.payStatus) }}
        </el-descriptions-item>
        <el-descriptions-item label="支付时间">
          {{orderInfo.payTime}}
        </el-descriptions-item>
        <el-descriptions-item label="支付流水号">
          {{orderInfo.payId}}
        </el-descriptions-item>
        <el-descriptions-item label="支付方式">
          {{orderInfo.payType=='10000601'?'微信小程序支付':''}}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="支付款项类型">
          {{orderInfo.paymentType}}
        </el-descriptions-item> -->

        <el-descriptions-item label="退款时间">
          {{orderInfo.refundTime}}
        </el-descriptions-item>
        <el-descriptions-item label="退款方式">
          {{orderInfo.refundType=='10000601'?'微信小程序':''}}
        </el-descriptions-item>
        <el-descriptions-item label="退款流水号">
          {{orderInfo.refundId}}
        </el-descriptions-item>
        <el-descriptions-item label="平台订单号">
          {{orderInfo.thirdOrderId}}
        </el-descriptions-item>
      </template>

    </el-descriptions>
  </div>
</template>

<script>
import { getpayStatus } from '@/common/method/formatOptions.js'
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: {},
  data() {
    return {
      orderInfo: {},
      isExpand: false,
    }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (Object.keys(val) != 0) {
        this.orderInfo = val.payInfo
        this.getExpandStatus()
      } else {
        this.orderInfo = {}
      }
    },
  },
  created() {},
  methods: {
    getpayStatus,
    getExpandStatus() {
      if (this.orderDetail.isView) {
        this.isExpand = true
      } else {
        this.isExpand = false
      }
    },
  },
}
</script>

<style lang='scss' scoped>
</style>