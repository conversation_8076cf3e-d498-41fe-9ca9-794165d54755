<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:用户基础信息
  * @author:zhangys
  * @date:2023/04/11 15:50:13
-->
<template>
  <div>
    <el-descriptions
      :column="4"
      border
      size="medium"
      title="用户基础信息"
      class="descriptions-content"
    >
      <el-descriptions-item label="用户名称">
        {{ orderInfo.custName }}
      </el-descriptions-item>
      <el-descriptions-item label="账户类型">
        {{ orderInfo.custType == '1' ? '单位' : '个人' }}
      </el-descriptions-item>
      <el-descriptions-item label="联系人">
        {{ orderInfo.custContact }}
      </el-descriptions-item>
      <el-descriptions-item label="联系电话">
        {{ orderInfo.custMobile }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌号">
        {{ orderInfo.carNo }}
      </el-descriptions-item>
      <el-descriptions-item label="车牌颜色">
        {{ getVehicleColor(orderInfo.carColor) }}
      </el-descriptions-item>
      <el-descriptions-item label="八桂行卡号">
        {{ orderInfo.cardNo }}
      </el-descriptions-item>
      <el-descriptions-item label="OBU号">
        {{ orderInfo.obuNo }}
      </el-descriptions-item>
      <el-descriptions-item label="车型">
        {{ getVehicleType(orderInfo.carType) }}
      </el-descriptions-item>
      <el-descriptions-item label="产品类型">
        {{ getallGxCardType(orderInfo.productType) }}
      </el-descriptions-item>
      <el-descriptions-item label="绑定渠道">
        {{ orderInfo.bindChannel }}
      </el-descriptions-item>
      <el-descriptions-item label="代扣渠道">
        {{ orderInfo.payChannel }}
      </el-descriptions-item>
      <el-descriptions-item label="八桂行卡状态">
        <div :style="orderInfo.cardStatus > 3 ? 'color:red' : ''">
          {{ getCpuStatus(orderInfo.cardStatus) }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="卡质保期">
        <div :style="!cpuValidityPeriod ? 'color:red' : ''">
          {{ orderInfo.cardEndDate }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="OBU状态">
        <div :style="orderInfo.obuStatus > 3 ? 'color:red' : ''">
          {{ getObuStatus(orderInfo.obuStatus) }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="OBU质保期">
        <div :style="!obuValidityPeriod ? 'color:red' : ''">
          {{ orderInfo.obuEndDate }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="证件类型">
        <span v-show="orderInfo.custType == '0'">{{
          getType(personalOCRType, orderInfo.custIdType)
        }}</span>
        <span v-show="orderInfo.custType == '1'">{{
          getType(enterpriseOCRType, orderInfo.custIdType)
        }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="证件号">
        {{ orderInfo.custIdNo }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import {
  getbusinessType,
  gxCardTypeFilter,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
  getVehicleColor,
  getCpuStatus,
  getObuStatus,
  getallGxCardType
} from '@/common/method/formatOptions'
import {
  personalOCRType,
  enterpriseOCRType
} from '@/common/const/optionsData.js'
export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return {
      personalOCRType,
      enterpriseOCRType,
      orderInfo: {},
      cpuValidityPeriod: true,
      obuValidityPeriod: true,
      vehicleTypeAll: [
        { value: '', label: '全部' },
        { value: '1', label: '客车' },
        { value: '2', label: '货车' },
        { value: '3', label: '专项作业车' }
      ]
    }
  },
  computed: {},
  watch: {
    orderDetail(val) {
      if (Object.keys(this.orderDetail).length != 0) {
        this.orderInfo = this.orderDetail.baseInfo
        this.cpuValidityPeriod = true
        this.obuValidityPeriod = true
        this.validateDate()
      } else {
        this.orderInfo = {}
      }
    }
  },
  created() {},
  methods: {
    getbusinessType,
    gxCardTypeFilter,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getVehicleType(value) {
      for (let i = 0; i < this.vehicleTypeAll.length; i++) {
        if (this.vehicleTypeAll[i].value == value) {
          return this.vehicleTypeAll[i].label
        }
      }
      return ''
    },
    getCarType,
    getVehicleColor,
    getCpuStatus,
    getObuStatus,
    getallGxCardType,
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    validateDate() {
      if (this.orderInfo.cardEndDate) {
        let date1 = new Date(this.orderInfo.cardEndDate)
        let date2 = new Date()
        if (date1 - date2 < 0) {
          this.cpuValidityPeriod = false
        } else {
          this.cpuValidityPeriod = true
        }
      }
      if (this.orderInfo.obuEndDate) {
        let date1 = new Date(this.orderInfo.obuEndDate)
        let date2 = new Date()
        if (date1 - date2 < 0) {
          this.obuValidityPeriod = false
        } else {
          this.obuValidityPeriod = true
        }
      }
    }
  }
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
</style>