<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行--支付信息
  * @author:dwz
  * @date:2025/02/20 09:46:52
-->
<template>
  <div>
    <el-descriptions
      :column="6"
      border
      title="发票信息"
      class="descriptions-content"
    >
      <template slot="extra">
        <el-button type="text" @click="isExpand = !isExpand">
          <i
            class="expand-icon"
            :class="[isExpand ? 'el-icon-arrow-down' : 'el-icon-arrow-right']"
          ></i>
        </el-button>
      </template>
      <template v-if="isExpand">
        <el-descriptions-item label="开票金额(元)">
          {{ moneyFilter(currnetDeatil.totalFee) }}
        </el-descriptions-item>

        <el-descriptions-item label="开票日期">
          {{ currnetDeatil.createdTime }}
        </el-descriptions-item>

        <el-descriptions-item label="发票编号">
          {{ currnetDeatil.invoiceNum }}
        </el-descriptions-item>

        <el-descriptions-item label="收款方">
          {{ currnetDeatil.buyerName }}
        </el-descriptions-item>
        <el-descriptions-item label="开票状态">
          {{ Object.keys(currnetDeatil).length > 0 ? '已开票' : '' }}
        </el-descriptions-item>
        <el-descriptions-item label="查看发票">
          <a
            v-if="Object.keys(currnetDeatil).length > 0"
            style="
              text-decoration-line: underline;
              color: #92abd6;
              cursor: pointer;
            "
            target="_blank"
            :href="currnetDeatil.url"
          >
            预览
          </a>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <!-- <div style="padding:0 16px 10px 16px"
         v-if="isExpand">
      <el-table :data="currnetDeatil.refundList"
                align="center"
                header-align="center"
                border
                style="width: 100%; margin-bottom: 10px">
        <el-table-column prop="refundTime"
                         align="center"
                         label="退款时间" />
        <el-table-column prop="refundType"
                         align="center"
                         label="退款方式">
          <template slot-scope="scope">
            {{scope.row.refundType=='0'?'原路退款':''}}
          </template>
        </el-table-column>
        <el-table-column prop="refundOrderId"
                         align="center"
                         label="退款流水号" />
      </el-table>
    </div> -->
  </div>
</template>

<script>
import {
  getcustomerType,
  getPersonalOCRType,
  getenterpriseOCRType,
  gxCardTypeFilter,
  getpayStatus,
  getPaymentOptions,
} from '@/common/method/formatOptions'

export default {
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    orderDetail(val) {
      if (Object.keys(val) != 0) {
        this.currnetDeatil = val.invoiceOrders[0]
        this.getExpandStatus()
      } else {
        this.currnetDeatil = {}
      }
    },
  },
  created() {},
  data() {
    return {
      currnetDeatil: {},
      isExpand: null,
    }
  },

  components: {},

  computed: {},

  methods: {
    getcustomerType,
    getPersonalOCRType,
    getenterpriseOCRType,
    gxCardTypeFilter,
    getpayStatus,
    getPaymentOptions,
    getExpandStatus() {
      if (this.orderDetail.isView) {
        this.isExpand = true
      } else {
        this.isExpand = false
      }
    },
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '../../style/newApplyCommon.css';

.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>