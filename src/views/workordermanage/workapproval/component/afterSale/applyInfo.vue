<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:用户申请信息
  * @author:zhangys
  * @date:2023/04/11 16:31:50
-->
<template>
  <div>
    <div v-if="orderInfo.orderType=='1'">
      <el-descriptions :column="4"
                       border
                       size="medium"
                       title="用户申请信息"
                       class="descriptions-content">

        <el-descriptions-item label="更换原因"
                              labelStyle="width:120px">
          {{getChangeReasonStatus(orderInfo.reasonType)}}{{archiveInfo.reason?'，'+archiveInfo.reason:''}}
        </el-descriptions-item>

      </el-descriptions>
      <archivesBox previewMode
                   uploadType="CACHEIMGUPLAOD"
                   :pictureSource="pictureSource"
                   style="padding: 0 16px;"></archivesBox>
    </div>

    <el-descriptions :column="4"
                     border
                     size="medium"
                     :title="orderInfo.orderType=='1'?'更换信息':'补办信息'"
                     class="descriptions-content">

      <el-descriptions-item label="需更换设备"
                            :span="orderInfo.orderType=='1'?1:2">
        <!--  -->
        <div v-if="isEdit&&orderInfo.orderStatus=='205'">
          <el-select v-model="orderInfo.deviceType"
                     placeholder="请选择">
            <el-option v-for="(value, key) in changeEquipment"
                       :label="value.label"
                       :value="value.value"
                       :key="key"></el-option>
          </el-select>
        </div>
        <div v-else>
          {{getChangeEquipment(orderInfo.deviceType)}}
        </div>

      </el-descriptions-item>

      <el-descriptions-item label="设备外观"
                            v-if="orderInfo.orderType=='1'">
        <div v-if="isEdit&&orderInfo.orderStatus=='205'">
          <el-select v-model="orderInfo.equipmentAppearanceType"
                     placeholder="请选择"
                     @change="equipmentChange"
                     :disabled="orderInfo.deviceType=='3'">
            <el-option v-for="(value, key) in changeEquipmentAppearance"
                       :label="value.label"
                       :value="value.value"
                       :key="key"></el-option>
          </el-select>
        </div>
        <div v-else>{{getChangeEquipmentAppearance(orderInfo.equipmentAppearanceType)}} </div>

      </el-descriptions-item>
      <el-descriptions-item label="更换类型"
                            v-if="orderInfo.orderType=='1'">
        <div v-if="isEdit&&orderInfo.orderStatus=='205'">
          <el-select v-model="orderInfo.exchangeFeeType"
                     placeholder="请选择"
                     :disabled="orderInfo.deviceType=='3'">
            <el-option v-for="(value, key) in changeType"
                       :label="value.label"
                       :value="value.value"
                       :key="key"
                       :disabled="value.disabled"></el-option>
          </el-select>
        </div>
        <div v-else>
          {{getChangeType(orderInfo.exchangeFeeType)}}
        </div>

      </el-descriptions-item>
      <el-descriptions-item label="应付金额(元)"
                            :span="orderInfo.orderType=='1'?1:2">
        {{moneyFilter(orderInfo.payAmount)}}
      </el-descriptions-item>
      <!-- <el-descriptions-item label="新卡片编号">
        {{orderInfo.newCardNo}}
      </el-descriptions-item>
      <el-descriptions-item label="卡片发行时间">
        {{orderInfo.cardReleaseDate}}
      </el-descriptions-item>
      <el-descriptions-item label="新OBU编号">
        {{orderInfo.newObuNo}}
      </el-descriptions-item>
      <el-descriptions-item label="新OBU发行时间">
        {{orderInfo.obuReleaseDate}}
      </el-descriptions-item> -->
      <el-descriptions-item label="审核备注（选填）"
                            v-if="orderInfo.orderType=='1'">
        <div v-if="isEdit&&orderInfo.orderStatus=='205'">
          <el-input v-model="orderInfo.auditRease"></el-input>
        </div>
        <div v-else> {{orderInfo.auditRease}}</div>

      </el-descriptions-item>

    </el-descriptions>
    <!-- <div style=" margin: 0px 16px 16px 16px"
         v-if="isEdit&&orderInfo.orderStatus=='205'">
      <el-button size="medium"
                 type="primary"
                 plain
                 style="width: 100%; border-style: dashed"
                 @click="confirmHandle">确认提交</el-button>
    </div> -->
  </div>
</template>

<script>
import archivesBox from '../../../component/photograph.vue'
import {
  changeType,
  changeEquipmentAppearance,
  changeEquipment,
} from '@/common/const/optionsData.js'
import {
  getChangeReasonStatus,
  getChangeEquipment,
  getChangeEquipmentAppearance,
  getChangeType,
} from '@/common/method/formatOptions.js'
import float from '@/common/method/float.js'

export default {
  name: '',
  props: {
    orderDetail: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  components: { archivesBox },
  data() {
    return {
      pictureSource: [],
      orderInfo: {},
      changeType,
      changeEquipmentAppearance,
      changeEquipment,
      archiveInfo: {},
    }
  },
  computed: {
    isEdit() {
      return !this.orderDetail.isView
    },
  },
  watch: {
    orderDetail(val) {
      if (Object.keys(this.orderDetail).length != 0) {
        if (this.orderDetail.orderInfo.orderType == '1') {
          this.orderInfo = {
            ...this.orderDetail.orderInfo,
            ...this.orderDetail.exchangeInfo,
          }
        }
        if (this.orderDetail.orderInfo.orderType == '0') {
          this.orderInfo = {
            ...this.orderDetail.orderInfo,
            ...this.orderDetail.reissueInfo,
          }
        }
        this.getArchives()
      } else {
        this.orderInfo = {}
        this.pictureSource = []
      }
    },
    'orderInfo.deviceType': {
      handler(val) {
        if (
          val &&
          this.orderInfo.equipmentAppearanceType &&
          this.orderInfo.exchangeFeeType &&
          this.orderInfo.orderStatus == '205'
        ) {
          this.orderInfo.payAmount
          this.getPayMoney()
        }
        if (val == '3') {
          this.orderInfo.exchangeFeeType = ''
          this.orderInfo.equipmentAppearanceType = ''
        }
      },
      deep: true,
    },
    'orderInfo.equipmentAppearanceType': {
      handler(val) {
        if (
          val &&
          this.orderInfo.deviceType &&
          this.orderInfo.exchangeFeeType &&
          this.orderInfo.deviceType != '3' &&
          this.orderInfo.orderStatus == '205'
        ) {
          this.orderInfo.payAmount
          this.getPayMoney()
        }
      },
      deep: true,
    },
    'orderInfo.exchangeFeeType': {
      handler(val) {
        if (
          val &&
          this.orderInfo.equipmentAppearanceType &&
          this.orderInfo.deviceType &&
          this.orderInfo.deviceType != '3' &&
          this.orderInfo.orderStatus == '205'
        ) {
          this.orderInfo.payAmount
          this.getPayMoney()
        }
      },
      deep: true,
    },
  },
  created() {},
  methods: {
    getChangeReasonStatus,
    getChangeEquipment,
    getChangeEquipmentAppearance,
    getChangeType,
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    equipmentChange(val) {
      if (val == '1') {
        for (let i = 0; i < this.changeType.length; i++) {
          if (this.changeType[i].value != '0') {
            this.$set(this.changeType[i], 'disabled', true)
          }
        }
      } else {
        for (let i = 0; i < this.changeType.length; i++) {
          this.$set(this.changeType[i], 'disabled', false)
        }
      }
    },
    //获取金额
    getPayMoney() {
      let params = {
        orderId: this.orderInfo.orderId,
        orderType: this.orderInfo.orderType,
        deviceType: this.orderInfo.deviceType,
        equipmentAppearanceType: this.orderInfo.equipmentAppearanceType,
        exchangeFeeType: this.orderInfo.exchangeFeeType,
      }
      if (params.orderType == '0') {
        delete params.equipmentAppearanceType
        delete params.exchangeFeeType
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleGetAmount,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.orderInfo.payAmount = res.data.payAmount
          } else {
            this.endLoading()
            this.orderInfo.payAmount = ''
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
          this.orderInfo.payAmount = ''
        })
    },

    //确认提交
    confirmHandle() {
      let params = {
        orderId: this.orderInfo.orderId,
        deviceType: this.orderInfo.deviceType,
        equipmentAppearanceType: this.orderInfo.equipmentAppearanceType,
        exchangeFeeType: this.orderInfo.exchangeFeeType,
        remark: this.orderInfo.auditRease,
      }
      if (params.deviceType == '3') {
        params.equipmentAppearanceType = ''
        params.exchangeFeeType = ''
      }
      if (!params.deviceType) {
        this.$message.warning('请选择需更换设备!')
        return
      }
      if (!params.equipmentAppearanceType && params.deviceType != '3') {
        this.$message.warning('请选择设备外观!')
        return
      }
      if (!params.exchangeFeeType && params.deviceType != '3') {
        this.$message.warning('请选择更换类型!')
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleConfirm,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('申请信息修改成功！')
            this.$emit('getDetail')
            this.$emit('closeDartSlide')
          } else {
            this.endLoading()
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    //获取档案
    getArchives() {
      let params = {
        orderId: this.orderInfo.orderId,
      }
      this.$request({
        url: this.$interfaces.afterSaleArchives,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.archiveInfo = res.data
            this.pictureSource = []
            this.pictureSource = res.data.imgList.map((item) => {
              return {
                file_url: item.fileUrl,
                md5Code: item.md5Code,
                photoCode: item.photo_code,
                lable: '',
              }
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang='scss' scoped>
</style>