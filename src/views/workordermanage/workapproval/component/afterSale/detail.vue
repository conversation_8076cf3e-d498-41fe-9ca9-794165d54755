<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:设备更换详情入口
  * @author:zhangys
  * @date:2023/04/11 15:46:39
-->
<template>
  <div class="detail-wrap">
    <!-- 用户基础信息 -->
    <div class="orderItem">
      <userBasicInfo :orderDetail="orderDetail"></userBasicInfo>
    </div>
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <orderBasicInfo
        :orderDetail="orderDetail"
        @changeAfterSaleOrderInfo="changeAfterSaleOrderInfo"
        @getDetail="getDetail"
      ></orderBasicInfo>
    </div>

    <!-- 物流信息 -->
    <div class="orderItem">
      <expressInfo :orderDetail="orderDetail"></expressInfo>
    </div>

    <!-- 支付信息 -->
    <div class="orderItem">
      <payInfo :orderDetail="orderDetail"></payInfo>
    </div>
    <!-- 申请信息 -->
    <div class="orderItem">
      <applyInfo
        :orderDetail="orderDetail"
        @getDetail="getDetail"
        @closeDartSlide="closeDartSlide"
        ref="applyInfo"
      ></applyInfo>
    </div>
     <!-- 开票信息 -->
     <div class="orderItem">
      <invoiceInfo :orderDetail="orderDetail"></invoiceInfo>
    </div>

    <!-- 订单履历 -->
    <div class="orderItem">
      <orderHistory :orderDetail="orderDetail"></orderHistory>
    </div>
    <!-- 审核备注 -->
    <div
      class="orderItem"
      v-if="orderInfo.orderStatus == '201' && detailType == 'deal'"
    >
      <el-descriptions
        :column="5"
        border
        title="审核备注"
        class="descriptions-content"
        style="padding: 20px 20px 0 20px"
      >
        <el-descriptions-item label="审核备注（选填）" labelStyle="width:140px">
          <el-input v-model="remark" type="textarea"></el-input>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!-- 操作 -->
    <div class="orderItem" style="margin-bottom: 30px">
      <div
        class="btns g-flex g-flex-center"
        v-if="orderInfo.orderStatus == '201' && detailType == 'deal'"
      >
        <!--  -->
        <el-button
          type="primary"
          style="margin: 0 20px"
          @click="operatorHandle('accept')"
          >审核通过</el-button
        >
        <el-button type="primary" @click="operatorHandle('reject')"
          >审核不通过</el-button
        >
        <el-button style="margin: 0 20px" @click="operatorHandle('cancel')"
          >取消订单</el-button
        >
      </div>
      <div
        class="btns g-flex g-flex-center"
        v-if="orderInfo.orderStatus == '204' && detailType == 'deal'"
      >
        <el-button
          type="primary"
          style="margin: 0 20px"
          @click="operatorHandle('receive')"
          >确认收货</el-button
        >
        <el-button
          style="margin: 0 20px"
          v-if="
            orderInfo.orderType == '1' &&
            expressInfoObj &&
            (expressInfoObj.expressStatus == '1' ||
              expressInfoObj.expressStatus == '0')
          "
          @click="operatorHandle('cancel')"
          >取消订单</el-button
        >
      </div>
      <!-- <div class="btns g-flex g-flex-center"
           v-if="detailType=='deal'&&(orderInfo.orderStatus=='208')">
 <el-button type='primary'
                   style="margin:0 20px"
                   @click="postHandle">快递下单</el-button>
      </div> -->
      <div
        class="btns g-flex g-flex-center"
        v-if="detailType == 'deal' && orderInfo.orderStatus == '205'"
      >
        <!--  -->
        <el-button type="primary" style="margin: 0 20px" @click="confirmHandle"
          >确认提交</el-button
        >
      </div>
      <div class="btns g-flex g-flex-center" v-if="detailType == 'deal'">
        <!-- <el-button type='primary'
                   style="margin:0 20px"
                   @click="readCardHandle"
                   v-if="(orderInfo.deviceType=='0'||orderInfo.deviceType=='2')&&!orderInfo.newCardNo">读取卡号</el-button>
        <el-button type='primary'
                   @click="readObuHandle"
                   v-if="(orderInfo.deviceType=='1'||orderInfo.deviceType=='2')&&!orderInfo.newObuNo">读取OBU号</el-button> -->
        <el-button
          v-if="
            orderInfo.orderStatus == '302' || orderInfo.orderStatus == '208'
          "
          type="primary"
          style="margin: 0 20px"
          @click="postHandle"
          >快递下单</el-button
        >
        <el-button
          style="margin: 0 20px"
          v-if="
            orderInfo.orderType == '0' &&
            (orderInfo.orderStatus == '302' || orderInfo.orderStatus == '208')
          "
          @click="operatorHandle('cancel')"
          >取消订单</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import userBasicInfo from './userBasicInfo'
import orderBasicInfo from './orderBasicInfo'
import applyInfo from './applyInfo'
import expressInfo from './expressInfo'
import payInfo from './payInfo'
import invoiceInfo from './invoiceInfo'
import orderHistory from './orderHistory'
import WsConsts from '@/utils/wsConsts'
import WsApi from '@/api/wsApi'
import { getToken } from '@/utils/auth'
import axios from 'axios'
export default {
  name: '',
  props: {
    detailType: {
      type: String,
      default: '',
    },
    orderId: {
      type: String,
      default: '',
    },
    slideVisible: {
      type: Boolean,
      default: '',
    },
    orderType: {
      type: String,
      default: '',
    },
  },
  components: {
    userBasicInfo,
    orderBasicInfo,
    applyInfo,
    expressInfo,
    payInfo,
    orderHistory,
    invoiceInfo
  },
  data() {
    return {
      orderDetail: {
        isView: false,
      },
      acceptChangeOrderInfo: {},
      orderInfo: {},
      expressInfoObj: null,
      cardNo: '',
      obuNo: '',
      remark: '',
    }
  },
  computed: {},
  watch: {
    slideVisible(val) {
      console.log(val, '<<---------val')
      if (val) {
        this.getDetail()
        this.remark = ''
      }
      if (!val) {
        this.$emit('refreshList')
        if (this.detailType == 'deal' && this.orderDetail.isLock != '1') {
          this.unLockOrder()
        }
      }
    },
  },
  created() {},
  methods: {
    changeAfterSaleOrderInfo(val) {
      this.acceptChangeOrderInfo = val
    },
    confirmHandle() {
      this.$refs.applyInfo.confirmHandle()
    },
    //释放订单
    unLockOrder() {
      let params = {
        orderId: this.orderId,
      }
      this.$request({
        url: this.$interfaces.afterSaleFreeOrder,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {})
    },
    //查询详情
    getDetail() {
      let params = {
        orderId: this.orderId,
        orderType: this.orderType == 'change' ? '1' : '0',
        opType: this.detailType == 'deal' ? '1' : '0',
      }
      this.orderDetail = {}
      this.orderInfo = {}
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleListDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.orderDetail = res.data
            this.orderDetail.isView = this.detailType != 'deal'
            this.orderInfo = res.data.orderInfo
            let expressInfo = res.data.expressInfo || []
            console.log('expressInfo', expressInfo)
            //新增优化，取消订单逻辑
            if (expressInfo.length > 0) {
              let filterArr = expressInfo.filter((item) => {
                return item.routeType == '0' && item.expressType == '1'
              })
              console.log('filterArr', filterArr)
              if (filterArr.length > 0) {
                this.expressInfoObj = filterArr[0]
              }
            }
            if (this.orderInfo.orderType == '1') {
              Object.assign(this.orderInfo, res.data.exchangeInfo)
            }
            if (this.orderInfo.orderType == '0') {
              Object.assign(this.orderInfo, res.data.reissueInfo)
            }
            if (this.orderDetail.isLock == '1' && this.detailType == 'deal') {
              this.$message.warning(
                `此订单正在处理中（处理人:${this.orderDetail.lockOpName},工号:${this.orderDetail.lockOpCode}），仅支持查看`
              )
              this.$emit('closeDartSlide')
              return
            }
          } else {
            this.orderDetail = {}
            this.orderInfo = {}
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    //审核
    operatorHandle(type) {
      let params = {
        orderId: this.orderInfo.orderId,
        orderStatus: '',
        remark: this.remark,
        isSendMsg: '0',
        userUpdateTime: this.orderInfo.userUpdateTime,
      }

      if (type == 'accept') {
        params.orderStatus = '202'
        params.isSendMsg = '1'
      }
      if (type == 'reject') {
        params.orderStatus = '203'
        params.isSendMsg = '1'
      }
      if (type == 'cancel') {
        if (this.orderInfo.orderType == '1') {
          params.orderStatus = '212'
        }
        if (this.orderInfo.orderType == '0') {
          params.orderStatus = '307'
        }
        params.isSendMsg = '1'
      }
      if (type == 'receive') {
        params.orderStatus = '205'
      }
      this.startLoading()
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web/' +
        this.$interfaces.afterSaleStatusEdit
      let config = {
        headers: {
          Authorization: getToken(),
        },
      }

      return new Promise((resolve, reject) => {
        axios
          .post(url, params, config)
          .then((res) => {
            console.log(res, '<<---------res')
            if (res.data.code == 200) {
              this.$message.success('操作成功！')
              this.endLoading()
              this.getDetail()
              if (params.orderStatus == '205') return
              this.$emit('closeDartSlide')
            } else {
              this.endLoading()
              if (
                res.data.code == '999' &&
                res.data.msg == '用户已更新当前订单信息，请重新审核'
              ) {
                this.$confirm(res.data.msg, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning',
                })
                  .then(() => {
                    this.getDetail()
                  })
                  .catch(() => {})
                return
              }
              this.$message({
                message: res.data.msg,
                type: 'error',
              })
            }
          })
          .catch((error) => {
            this.endLoading()
          })
      })
    },
    closeDartSlide() {
      this.$emit('closeDartSlide')
    },
    //发行
    issueHandle(type) {
      let params = {
        orderId: this.orderInfo.orderId,
        deviceType: '',
        deviceId: '',
      }
      if (type == 'card') {
        params.deviceType = '0'
        params.deviceId = this.cardNo
        // params.deviceId = '45112000000062248118'
      }
      if (type == 'obu') {
        params.deviceType = '1'
        params.deviceId = this.obuNo
        // params.deviceId = '****************'
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleIssue,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('操作成功！')
            this.getDetail()
          } else {
            this.endLoading()
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },

    //快递下单
    postHandle() {
      let params = {
        orderId: this.orderInfo.orderId,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSalePost,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('快递下单成功！')
            this.getDetail()
            // this.$emit('closeDartSlide')
          } else {
            this.endLoading()
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },

    //读obu
    readObuHandle() {
      //   this.issueHandle('obu')
      //   return
      let param = {
        read_type: '2',
      }
      this.startLoading()
      WsApi.readObuInfo(param, this.onMsg, this.onErr)
    },
    //读卡
    readCardHandle() {
      //   this.issueHandle('card')
      //   return
      this.startLoading()
      let param = {}
      WsApi.readCpuInfo(param, this.onMsg, this.onErr)
    },
    onMsg: function (msg) {
      let rspData = JSON.parse(msg.data)
      this.endLoading()
      if (rspData.resultCode === '0') {
        //获取业务数据
        let bizContent = JSON.parse(rspData.data)
        console.log(bizContent)
        // 读取卡信息
        if (rspData.method === WsConsts.methods.readCpuInfo) {
          this.cardNo = bizContent['cpu_card_id']
          this.issueHandle('card')
        }
        if (rspData.method === WsConsts.methods.readObuInfo) {
          this.obuNo = bizContent['obu_id']
          this.issueHandle('obu')
        }
      } else {
        if (this.ws) {
          this.ws.close()
          this.ws = null
        }
        this.$msgbox({
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: rspData.resultMsg,
        })
      }
      this.endLoading()
      console.log('收到消息：', WsConsts, msg.data)
    },
    //websocket异常处理回调
    onErr: function (err) {
      //隐藏进度条
      this.endLoading()
      this.$msgbox({
        title: '提示',
        showClose: true,
        type: 'error',
        customClass: 'my_msgBox singelBtn',
        dangerouslyUseHTMLString: true,
        message: '请求异常：' + err.message,
      })
      if (this.ws) {
        this.ws.close()
        this.ws = null
      }
      console.log('请求异常：' + err.message)
    },
  },
}
</script>

<style lang='scss' scoped>
@import '../../style/newApplyCommon.css';
.btns {
  padding: 8px 0;
}
</style>