<template>
  <div class="form">
    <el-dialog
      :title="title"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="50%"
      :before-close="handleCloseIcon"
      :modal="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-row :xs="24" :sm="24">
          <el-col :span="16" :offset="4">
            <el-form-item prop="remark" style="font-weight: 700">
              {{ msgBoxTypeList[msgBoxType] }}
            </el-form-item>
            <el-form-item label="备注：" prop="remark">
              <el-input
                type="textarea"
                :rows="3"
                placeholder="请输入备注"
                v-model="ruleForm.remark"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    msgBoxType: {
      type: String,
      default: '',
    },
    remark: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      dialogFormVisible: false,
      ruleForm: {
        remark: '', //备注
      },
      // rules: {
      //   remark: [
      //     { required: true, message: '[备注]不能为空!', trigger: 'blur' },
      //   ],
      // },
      msgBoxTypeList: {
        applyCancel: '确定需取消此笔订单吗',
        operatorSaleHandle: '确定此笔订单审核不通过吗',
        operatorHandle: '确定此笔订单审核不通过吗',
        afterCancel: '确定撤销售后申请吗',
        refundMoney: '确定需全额退款给用户吗',
      },
      title: '确认操作',
    }
  },
  // computed: {
  //   msgDesc() {
  //     return this.msgBoxTypeList[msgBoxType]
  //   },
  // },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    remark(val) {
      this.ruleForm.remark = val
    },
    'ruleForm.remark'(val) {
      this.$emit('update:remark', val)
    },
  },
  created() {},
  methods: {
    // 表单提交
    submitForm() {
      if (!this.ruleForm.remark) {
        this.$message.warning('请输入备注！')
        return
      }
      // this.$refs[formName].validate((valid) => {
      //   if (valid) {
      //     //确认操作
      this.$emit('msgBoxHandle', this.msgBoxType)
      // } else {
      //   console.log('error submit!!')
      //   return false
      // }
      // })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
// .el-form-item__label {
//   text-align: center;
//   white-space: nowrap;
// }
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
</style>
