<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:通用搜索组件
  * @author:zhangys
  * @date:2023/03/07 16:02:53
-->
<template>
  <div>
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="form"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 class="search">
      <template slot="search-form">
        <dart-search-item label="订单号："
                          prop="id">
          <el-input v-model="form.id"
                    clearable
                    placeholder=""></el-input>
        </dart-search-item>

        <dart-search-item label="车牌号："
                          prop="vehicleNo">
          <el-input v-model="form.vehicleNo"
                    clearable
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色："
                          prop="vehicleColor">
          <el-select clearable
                     v-model="form.vehicleColor"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="(item,index) in licenseColorOptionAll"
                       :key="index"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>

        <div class="collapse-wrapper"
             v-show="isCollapse">
          <dart-search-item label="审核方式："
                            prop="checkType"
                            v-if="type=='newApply'">
            <el-select clearable
                       v-model="form.checkType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in checkTypeOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="审核方式："
                            prop="checkType"
                            v-if="type=='activate'">
            <el-select clearable
                       v-model="form.checkType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateCheckOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="取货方式："
                            prop="installType"
                            v-if="type=='newApply'">
            <el-select clearable
                       v-model="form.installType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in mailType"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="ETC卡号："
                            prop="cardNo"
                            v-if="type=='change'||type=='activate'">
            <el-input v-model="form.cardNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="OBU号："
                            prop="obuNo"
                            v-if="type=='change'||type=='activate'">
            <el-input v-model="form.obuNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="客货类型："
                            prop="isTrunk">
            <el-select clearable
                       v-model="form.isTrunk"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in vehicleTypeAll"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="用户名称："
                            prop="userName">
            <el-input v-model="form.userName"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="手机号："
                            prop="mobile">
            <el-input v-model="form.mobile"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="用户类型："
                            prop="accountType">
            <el-select clearable
                       v-model="form.accountType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in customerTypeAll"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="产品类型："
                            prop="productType"
                            v-if="type=='activate'">
            <el-select clearable
                       v-model="form.productType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in gxCardTypeAllOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="产品类型："
                            prop="productType"
                            v-else>
            <el-select clearable
                       v-model="form.productType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in productTypeOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="申请渠道："
                            prop="applyChannel"
                            v-if="type=='activate'||type=='change'">
            <el-select clearable
                       v-model="form.applyChannel"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateApplyChannel"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="申请渠道："
                            prop="applyChannel"
                            v-else>
            <el-select clearable
                       v-model="form.applyChannel"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in applyChannelOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="订单状态："
                            prop="orderStatus"
                            v-if="type=='newApply'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in applyOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="订单状态："
                            prop="orderStatus"
                            v-if="type=='activate'||type=='change'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="更换业务类型："
                            prop="orderStatus"
                            v-if="type=='change'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="更换设备类型："
                            prop="orderStatus"
                            v-if="type=='change'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="补办设备类型："
                            prop="orderStatus"
                            v-if="type=='remake'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="业务类型："
                            prop="businessType"
                            v-if="type =='newApply'">
            <el-select clearable
                       v-model="form.businessType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="item in businessType"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <div v-if="type=='activate'">
            <dart-search-item label="下单时间："
                              prop="">
              <el-date-picker v-model="OrderSubmitDate"
                              type="datetimerange"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>

            <dart-search-item label="审核时间："
                              prop="">
              <el-date-picker v-model="orderUpdateDate"
                              type="datetimerange"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="激活时间："
                              prop="">
              <el-date-picker v-model="activateDate"
                              type="datetimerange"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>
          </div>
          <div v-else>
            <dart-search-item label="订单申请时间："
                              prop="">
              <el-date-picker v-model="OrderSubmitDate"
                              type="daterange"
                              value-format="yyyy-MM-dd"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>

            <dart-search-item label="订单更新时间："
                              prop="">
              <el-date-picker v-model="orderUpdateDate"
                              type="daterange"
                              value-format="yyyy-MM-dd"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>
          </div>

          <dart-search-item label="绑定渠道："
                            prop="bindChannel"
                            v-if="type=='newApply'">
            <el-select clearable
                       v-model="form.bindChannel"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in applyChannelStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="物流单号："
                            prop="logisticsNo"
                            v-if="type=='newApply'">
            <el-input v-model="form.logisticsNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="OBU发行时间："
                              prop=""
                              v-if="type=='newApply'">
              <el-date-picker v-model="obuSubmitDate"
                              type="daterange"
                              value-format="yyyy-MM-dd"
                              clearable
                              range-separator="至"
                              start-placeholder="开始日期"
                              end-placeholder="结束日期">
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="推广码编号："
                            prop="promotionCodeNumber"
                            v-if="type=='newApply'">
            <el-input v-model="form.promotionCodeNumber"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="是否扫码办理："
                            prop="isPromotion"
                            v-if="type=='newApply'">
            <el-select clearable
                       v-model="form.isPromotion"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in ScanOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="绑定渠道："
                            prop="bindChannel"
                            v-if="type=='activate'||type=='change'">
            <el-select clearable
                       v-model="form.bindChannel"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateChannelStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <div v-if="type!='activate'">
            <dart-search-item label="车辆所有人："
                              prop="business_type"
                              v-if="type=='after'">
              <el-select clearable
                         v-model="form.business_type"
                         placeholder="请选择"
                         collapse-tags>
                <el-option v-for="item in businessType"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item>

            <dart-search-item label="用户类型："
                              prop="net_user_type"
                              v-if="type=='after'">
              <el-select clearable
                         v-model="form.net_user_type"
                         placeholder="请选择"
                         collapse-tags>
                <el-option v-for="item in customerType"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item>
          </div>
        </div>
        <dart-search-item :is-button="true"
                          :colElementNum='isCollapse ?(type=="newApply"?3:3):1'>
          <div class="g-flex g-flex-end">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="batchAudit"
                       v-if="type=='newApply'">批量审核</el-button>
            <el-button type="warning"
                       size="mini"
                       native-type="submit"
                       @click="exportHandle">导出</el-button>
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       v-permisaction="['release:export']"
                       @click="codeExportHandle"
                       v-if="type=='newApply'">推广码订单导出</el-button>
            <el-button type="text"
                       @click="isCollapse=!isCollapse"><span v-if="isCollapse">收起</span><span v-if="!isCollapse">展开</span></el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import {
  businessType,
  licenseColorOptionAll,
  newApplyNodeCodeOptions,
  refundNodeCodeOptions,
  cancelNodeCodeOptions,
  afterSaleStatus,
  vehicleTypeAll,
  customerTypeAll,
  productTypeOptions,
  checkTypeOptions,
  applyChannelOptions,
  mailType,
  activateOrderStatus,
  activateApplyChannel,
  gxCardTypeAllOptions,
  activateCheckOptions,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'

export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      afterSaleStatus,
      newApplyNodeCodeOptions,
      refundNodeCodeOptions,
      cancelNodeCodeOptions,
      vehicleTypeAll,
      licenseColorOptionAll,
      customerTypeAll,
      businessType,
      gxCardTypeAllOptions,
      activateCheckOptions,
      form: {
        accountType: '',
        applyChannel: '',
        bindChannel: '',
        checkType: '',
        id: '',
        installType: '',
        isTrunk: '',
        mobile: '',
        orderStatus: '',
        orderTimeEnd: '',
        orderTimeSta: '',
        productType: '',
        statusTimeEnd: '',
        statusTimeSta: '',
        userName: '',
        vehicleColor: '',
        vehicleNo: '',
        installType: '',
        pageIndex: 1,
        pageSize: 10,
        cardNo: '',
        obuNo: '',
        activateTimeEnd: '',
        activateTimeStart: '',
        businessType: '',
        obuTimesta: '',
        obuTimeEnd: '',
        isPromotion:''
      },
      isCollapse: false,
      OrderSubmitDate: '',
      orderUpdateDate: '',
      activateDate: '',
      obuSubmitDate: '',
      productTypeOptions,
      applyChannelOptions,
      checkTypeOptions,
      mailType,
      orderStatusOptions: [],
      activateOrderStatus,
      activateApplyChannel,
      ScanOptions: [
        { label:'全部', value:''},
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    }
  },
  computed: {
    ...mapGetters([
      'applyOrderStatus',
      'applyChannelStatus',
      'activateChannelStatus',
    ]),
  },
  watch: {
    OrderSubmitDate(val) {
      if (!val) {
        this.form.orderTimeSta = ''
        this.form.orderTimeEnd = ''
      }
    },
    obuSubmitDate(val) {
      if (!val) {
        this.form.obuTimesta = ''
        this.form.obuTimeEnd = ''
      }
    },
    orderUpdateDate(val) {
      if (!val) {
        this.form.statusTimeSta = ''
        this.form.statusTimeEnd = ''
      }
    },
    activateDate(val) {
      if (!val) {
        this.form.activateTimeStart = ''
        this.form.activateTimeEnd = ''
      }
    },
  },
  created() {},
  created() {},
  methods: {
    onSearchHandle() {
      this.formatDate()
      this.$emit('search', this.form)
    },
    formatDate() {
      if (this.OrderSubmitDate) {
        this.form.orderTimeSta = this.OrderSubmitDate[0]
        this.form.orderTimeEnd = this.OrderSubmitDate[1]
      }
      if (this.obuSubmitDate) {
        this.form.obuTimesta = this.obuSubmitDate[0]
        this.form.obuTimeEnd = this.obuSubmitDate[1]
      }
      if (this.orderUpdateDate) {
        this.form.statusTimeSta = this.orderUpdateDate[0]
        this.form.statusTimeEnd = this.orderUpdateDate[1]
      }
      if (this.activateDate) {
        this.form.activateTimeStart = this.activateDate[0]
        this.form.activateTimeEnd = this.activateDate[1]
      }
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
      this.OrderSubmitDate = ''
      this.obuSubmitDate = ''
      this.orderUpdateDate = ''
      this.activateDate = ''
      this.form.orderTimeSta = ''
      this.form.orderTimeEnd = ''
      this.form.obuTimesta = ''
      this.form.obuTimeEnd = ''
      this.form.statusTimeSta = ''
      this.form.statusTimeEnd = ''
      this.form.activateTimeStart = ''
      this.form.activateTimeEnd = ''
      this.$emit('search', this.form)
    },
    //批量审核
    batchAudit() {
      this.$emit('batchAudit')
    },

    //导出
    exportHandle() {
      this.formatDate()

      if (this.type == 'activate') {
        this.$emit('export', this.form)
        return
      }
      if (this.type == 'newApply') {
        this.$emit('export', this.form)
        return
      }
    },
    codeExportHandle() {
      this.formatDate()
      this.$emit('codeExport', this.form)
    },
  },
}
</script>

<style lang='scss' scoped>
</style>