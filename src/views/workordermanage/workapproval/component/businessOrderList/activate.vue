<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:自助激活
  * @author:zhangys
  * @date:2023/03/26 10:19:12
-->
<template>
  <div>
    <search @search="search"
            @export="exportReport"
            type="activate"></search>
    <div class="table">
      <el-table :data="tableData"
                v-loading="tableloading"
                style="width: 100%;"
                height="55vh"
                row-key="id">

        <el-table-column prop="orderNo"
                         label="订单号"
                         align="center"
                         width="170" />
        <el-table-column prop="custName"
                         label="用户名称"
                         align="center"
                         width="120">
          <template slot-scope="scope">
            {{maskUsername(scope.row.custName)}}
          </template>
        </el-table-column>
        <el-table-column prop="carNo"
                         label="车牌号"
                         width="120"
                         align="center" />
        <el-table-column prop="carColor"
                         label="车牌颜色"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.carColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus_str"
                         label="订单状态"
                         width="120"
                         align="center" />

        <el-table-column prop="cardNo"
                         label="ETC卡号"
                         width="180"
                         align="center" />
        <el-table-column prop="obuNo"
                         label="OBU号"
                         width="180"
                         align="center" />

        <el-table-column prop="vehicleType"
                         label="客货类型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleType(scope.row.vehicleType)}}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleTypeKind"
                         label="车型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getCarType(scope.row.vehicleTypeKind)}}
          </template>
        </el-table-column>
        <el-table-column prop="productType_Str"
                         label="产品类型"
                         width="180"
                         align="center">
          <template slot-scope="scope">
            {{getallGxCardType(scope.row.productType)}}
          </template>
        </el-table-column>
        <el-table-column prop="bindChannel"
                         label="绑定渠道"
                         width="120"
                         align="center">
          <!--  -->
          <template slot-scope="scope">
            {{scope.row.bindingChannel}}

          </template>
        </el-table-column>
        <el-table-column prop="applyNumber"
                         label="申请次数"
                         width="120"
                         align="center">
        </el-table-column>
        <el-table-column prop="applyChannel_str"
                         label="申请渠道"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            {{scope.row.applyChannel_str}}
          </template>
        </el-table-column>
        <el-table-column prop="applyTime"
                         label="下单时间"
                         width="180"
                         align="center" />

        <el-table-column prop="auditType_str"
                         label="审核方式"
                         width="100"
                         align="center">
        </el-table-column>
        <el-table-column prop="auditByName"
                         label="审核人"
                         width="100"
                         align="center">
        </el-table-column>
        <el-table-column prop="auditTime"
                         label="审核时间"
                         width="180"
                         align="center" />
        <el-table-column prop="auditRemarks"
                         label="审核备注"
                         width="180"
                         align="center">
        </el-table-column>
        <el-table-column prop="activateStatus_str"
                         label="激活状态"
                         width="100"
                         align="center">
        </el-table-column>
        <el-table-column prop="activateTime"
                         label="激活时间"
                         width="180"
                         align="center">
        </el-table-column>
        <el-table-column label="操作"
                         fixed="right"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            <el-button type="text"
                       @click="toDetail(scope.row,'view')">查看</el-button>
            <el-button type="text"
                       @click="toDetail(scope.row,'deal')">处理</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="form.page"
                     :page-sizes="[10, 20, 50]"
                     :page-size="form.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <dartSlide :visible.sync="slideVisible"
               title="二次激活申请单详情"
               v-transfer-dom
               width="90%"
               :maskClosable="true">
      <detail :applyId="applyId"
              :slideVisible="slideVisible"
              :isView="isView"
              @refreshList="getOrderList"></detail>
    </dartSlide>
  </div>

</template>

<script>
import {
  getbusinessType,
  getVehicleColor,
  getnowstate,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getallGxCardType,
  getCarType,
} from '@/common/method/formatOptions'
import {
  newApplyNodeCodeOptions,
  refundNodeCodeOptions,
  cancelNodeCodeOptions,
  afterSaleStatus,
  vehicleType,
  customerType,
} from '@/common/const/optionsData.js'
import dartSlide from '@/components/dart/Slide/index.vue'
import search from './search'
import detail from '../activate/detail'
import { mapGetters, mapActions } from 'vuex'
import { maskUsername, maskMobile } from '@/utils'

export default {
  components: {
    search,
    dartSlide,
    detail,
  },
  created() {
    console.log(this.activateChannelStatus, '<<---------this.')
    this.getOrderList()
  },
  computed: {
    ...mapGetters(['activateChannelStatus']),
  },

  mounted() {},
  data() {
    return {
      tableloading: false,
      tableData: [],
      form: { page: 1, pageSize: 10 },

      detaildata: {},
      dialogVisible: false,
      auditVisible: false,
      total: 0,
      typearr: ['1000', '2000', '2020', '3000', '4000'],
      nodeCodeOptions: [],
      afterSaleStatus,
      newApplyNodeCodeOptions,
      refundNodeCodeOptions,
      cancelNodeCodeOptions,
      slideVisible: false,
      applyId: '',
      isView: false, //是否查看
      selectedItem: [],
      maskUsername,
      maskMobile
    }
  },
  methods: {
    getbusinessType,
    getVehicleColor,
    getnowstate,
    getVehicleType,
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getallGxCardType,
    closeDartSlide() {},
    getActivateApplyChannel(val) {
      for (let i = 0; i < this.activateChannelStatus.length; i++) {
        if (this.activateChannelStatus[i].value == val) {
          return this.activateChannelStatus[i].label
        }
      }
      return ''
    },
    auditDialog() {
      this.auditVisible = true
    },
    search(val) {
      this.initData(val)
      this.getOrderList()
    },
    toDetail(item, val) {
      if (val == 'view') {
        this.isView = true
      } else {
        this.isView = false
      }
      this.applyId = item.id
      this.slideVisible = true
      return
    },
    initData(val) {
      let data = JSON.parse(JSON.stringify(val))
      this.form = {
        activateTimeEnd: data.activateTimeEnd,
        activateTimeStart: data.activateTimeStart,
        applyChannel: data.applyChannel,
        applyReasonType: '',
        applyTimeEnd: data.orderTimeEnd,
        applyTimeStart: data.orderTimeSta,
        auditTimeEnd: data.statusTimeEnd,
        auditTimeStart: data.statusTimeSta,
        auditType: data.checkType,
        carColor: data.vehicleColor,
        carNo: data.vehicleNo,
        cardNo: data.cardNo,
        custMastId: '',
        netUserNo: '',
        obuNo: data.obuNo,
        orderByField: 'id',
        orderNo: data.id,
        orderStatus: data.orderStatus,
        page: 1,
        pageSize: 20,
        phone: data.mobile,
        productType: data.productType,
        sort: 'desc',
        custName: data.userName,
        userType: data.accountType,
        vehicleType: data.isTrunk,
        bindingChannel: data.bindChannel,
      }
    },
    getOrderList() {
      this.loading = true
      this.$request({
        url: this.$interfaces.activateApplyList,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          this.tableData = res.data.data
          this.total = res.data.total
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log('err', error)
        })
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getOrderList()
    },
    changePage(e) {
      this.form.page = e
      this.getOrderList()
    },

    exportReport(val) {
      this.initData(val)
      this.startLoading()

      this.$request({
        url: this.$interfaces.activateExport,
        method: 'post',
        data: this.form,
        responseType: 'blob',
      })
        .then((res) => {
          this.endLoading()

          this.getBlob(res, 'application/vnd.ms-excel', '自助激活列表')
        })
        .catch(() => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .modular-view-title {
  border-bottom: none !important;
}
</style>