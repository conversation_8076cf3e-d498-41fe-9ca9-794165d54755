<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button
        size="mini"
        type="primary"
        slot="btn"
        @click="importFile('sell')"
        ><i class="el-icon-upload"></i> 导入销售订单</el-button
      >
      <el-button
        size="mini"
        type="primary"
        slot="btn"
        @click="importFile('after')"
        ><i class="el-icon-upload"></i> 导入售后订单</el-button
      >
      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >导出</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="dyListColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="false"
        @changeTableData="changeTableData"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button type="text" @click="handelRow(scope, 'view')"
              >查看</el-button
            >
            <el-button type="text" @click="handelRow(scope, 'edit')"
              >处理</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <DartSlide
      :visible.sync="slideVisible"
      title="订单详情"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
      @close="modelClose"
    >
      <Detail
        v-if="slideVisible"
        :orderId="orderId"
        :applyId="applyId"
        :vehicleColorCode="vehicleColor"
        :queryGroup="queryGroup"
        :slideVisible="slideVisible"
        :isView="isView"
        @refreshList="getTableData"
        @closeDartSlide="closeDartSlide"
      ></Detail>
    </DartSlide>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import DartSlide from '@/components/dart/Slide/index.vue'
import Detail from '../dyOrder/detail.vue'
import { dyListColoumns, dyListForm, tiktokQueryExportConfig } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import {
  queryInitialize,
  orderSearch,
  orderLock,
  orderFree,
  exportList
} from '@/api/workordermanage'
import { mapGetters } from 'vuex'
import { convertFormat, arrayToObject } from '@/utils'
import { decode } from 'js-base64'

export default {
  components: {
    MyTable,
    SearchForm,
    DartSlide,
    Detail
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // dyListColoumns,
      api: orderSearch,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      isView: false,
      slideVisible: false,
      orderId: '',
      applyId: '',
      vehicleColor: '',
      queryGroup: {
        orderStatusObj: {},
        orderStatus: [],
        bindingChannels: [],
        bindingChannelsObj: {},
        nodeCode: [],
        nodeCodeObj: {},
        postStatus: [],
        postStatusObj: {}
      },
      timeField: ['OrderSubmitDate', 'orderUpdateDate']
    }
  },
  computed: {
    ...mapGetters(['applyOrderStatus', 'activateChannelStatus']),
    dyListColoumns() {
      return dyListColoumns(this)
    },
    formConfig() {
      return dyListForm(this)
    }
  },
  methods: {
    async handelRow(row, type) {
      this.orderId = row.orderId
      this.applyId = row.applyId
      this.vehicleColor = row.vehicleColor
      if (type == 'view') {
        this.isView = true
      } else {
        this.isView = false
        // await this.lockOrder()
      }
      this.slideVisible = true
    },
    closeDartSlide() {
      this.slideVisible = false
    },
    modelClose() {
      this.freeOrder()
    },
    async lockOrder() {
      console.log(this.orderId, this.isView)
      if (!this.orderId || this.isView) return
      let params = {
        orderId: this.orderId
      }
      await orderLock(params)
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    async freeOrder() {
      if (!this.orderId || this.isView) return
      let params = {
        orderId: this.orderId
      }
      let res = await orderFree(params)
      this.getTableData()
    },
    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = this.transformData(query, tiktokQueryExportConfig)
      params.name = 'tikTokReleaseOrders'
      // console.log(params, 'exportHandle params')
      this.sendReportRequest(params)
    },
    // 格式转换
    transformData(data, config) {
      const transformedData = {}
      for (const key in config) {
        if (data.hasOwnProperty(key) && data[key]) {
          const newKey = config[key]
          const value = data[key]
          transformedData[newKey] = value
        }
      }
      return transformedData
    },
    async sendReportRequest(data) {
      this.loading = true
      let res = await exportList(data)
      this.loading = false
      if (res.code == 200) {
        let url = res.data
        let decodeUrl = decode(url)
        // console.log(decodeUrl,'地址')
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      }
    },
    handleSizeChange(val) {
      this.changeTableData(val, this.pageNum)
    },
    handleCurrentChange(val) {
      this.changeTableData(this.pageSize, val)
    },
    /**
     * 字典初始化
     */
    async queryInit() {
      let res = await queryInitialize()
      console.log(res)
      let { orderStatus, bindingChannels, nodeCode, postStatus } = res.data
      let bindingChannelsArr = bindingChannels.map(item => {
        return { label: item.channelName, value: item.channelCode }
      })
      let bindingChannelsObj = arrayToObject(
        bindingChannels,
        'channelCode',
        'channelName'
      )
      this.queryGroup = {
        orderStatusObj: orderStatus,
        orderStatus: this.convertFormatVal(orderStatus),
        bindingChannels: bindingChannelsArr,
        bindingChannelsObj: bindingChannelsObj,
        nodeCodeObj: nodeCode,
        nodeCode: convertFormat(nodeCode),
        postStatus: this.convertFormatVal(postStatus),
        postStatusObj: postStatus
      }
      console.log(this.queryGroup, 'queryGroup')
    },
    convertFormatVal(input) {
      const output = []
      const map = new Map(Object.entries(input))
      for (let [key, value] of map) {
        output.push({ label: value, value: value })
      }
      return output
    },
    importFile(type) {
      let fileObj = {
        sell: {
          title: '导入销售订单',
          path:
            '@/views/workordermanage/workapproval/component/importFile/sellOrder-import'
        },
        after: {
          title: '导入售后单',
          path:
            '@/views/workordermanage/workapproval/component/importFile/afterOrder-import'
        }
      }
      this.$openPage(
        fileObj[type].path,
        fileObj[type].title,
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
            this.getTableData()
          }
        },
        {
          area: ['45%', '430px'],
          offset: ['40%', '350']
        }
      )
    }
  },
  created() {
    this.queryInit()
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  // padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>