import {
  getVehicleColor,
  getVehicleType,
  getCarType,
} from '@/common/method/formatOptions'

import {
  licenseColorOptionAll,
  vehicleTypeAll,
  customerTypeAll,
  applyChannel,
} from '@/common/const/optionsData.js'
import { convertFormat,maskUsername,maskMobile } from '@/utils'

//线上注销表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'orderId',
      label: '订单号',
      width: 200,
    },
    {
      prop: 'custName',
      width: 170,
      label: '用户名称',
      formatter: (row) => {
        return maskUsername(row)
      }
    },
    {
      prop: 'mobile',
      width: 120,
      label: '手机号',
      formatter: (row) => {
        return maskMobile(row)
      }
    },
    {
      prop: 'applicantMobile',
      label: '申请人手机号',
      width: 120,
    },
    {
      prop: 'applicantCount',
      label: '申请累计次数',
      width: 120,
    },
    {
      prop: 'accountType',
      width: 120,
      label: '用户类型',
      formatter: (row) => {
        return row == 1 ? '单位' : '个人'
      }
    },
    // {
    //   prop: 'businessType',
    //   width: 100,
    //   label: '业务类型',
    //   formatter:(row) => {
    //     return getbusinessType(row)
    //   }
    // },
    {
      prop: 'nodeCode',
      width: 120,
      label: '订单状态',
      formatter: (row) => {
        return _this.queryGroup.orderStatusObj[row]
      }
    },
    {
      prop: 'vehicleNo',
      width: 120,
      label: '车牌号',
    },
    {
      prop: 'vehicleColor',
      width: 120,
      label: '车辆颜色',
      formatter: (row) => {
        return getVehicleColor(row)
      }
    },
    {
      prop: 'cardNo',
      label: 'ETC卡号',
      width: 200,

    },
    {
      prop: 'obuNo',
      label: 'OBU号',
      width: 200,

    },
    {
      prop: 'isTrunk',
      width: 100,
      label: '客货类型',
      formatter: (row) => {
        return getVehicleType(row)
      }
    },
    {
      prop: 'carType',
      width: 100,
      label: '车型',
      formatter: (row) => {
        return getCarType(row)
      }
    },
    {
      prop: 'productType',
      width: 200,
      label: '产品类型',
      formatter: (row) => {
        return _this.queryGroup.productTypeObj[row]
      }
    },
    {
      prop: 'bindingChannel',
      label: '绑定渠道',
      width: 100,
    },
    {
      prop: 'isCharge',
      label: '是否收费',
      width: 100,
      formatter: (row) => {
        return row == 1 ? '收费' : '免费'
      }
    },
    {
      prop: 'amount',
      label: '注销费用',
      formatter: (row) => {
        return row * 100
      },
      width: 100,
    },
    {
      prop: 'applyChannel',
      label: '申请渠道',
      formatter: (row) => {
        return applyChannel[row]
      },
      width: 120,
    },
    {
      prop: 'createdTime',
      width: 180,
      label: '下单时间',
    },
    {
      prop: 'updatedTime',
      width: 180,
      label: '更新时间',
    },

    {
      prop: 'action',
      fixed: 'right',
      width: 100,
      label: '操作'
    }
  ]
}


//线上注销搜索表单
export const listForm = (_this) => {
  console.log(_this, '_this')
  return [
    {
      type: 'input',
      field: 'orderId',
      label: '订单号：',
      default: '',
    },
    {
      type: 'input',
      field: 'vehicleNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll
    },
    {
      type: 'input',
      field: 'cardNo',
      label: 'ETC号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'obuNo',
      label: 'OBU号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'isTrunk',
      label: '客货类型：',
      isCollapse: true,
      options: vehicleTypeAll
    },
    {
      type: 'input',
      field: 'custName',
      label: '用户名称：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'mobile',
      label: '手机号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'accountType',
      label: '用户类型：',
      isCollapse: true,
      options: customerTypeAll
    },
    {
      type: 'select',
      field: 'productType',
      label: '产品类型：',
      isCollapse: true,
      options: _this.queryGroup.productType
    },
    {
      type: 'select',
      field: 'applyChannel',
      label: '申请渠道：',
      isCollapse: true,
      options: convertFormat(applyChannel)
    },
    {
      type: 'select',
      field: 'nodeCode',
      label: '订单状态：',
      isCollapse: true,
      options: _this.queryGroup.orderStatus
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['createdTimeStart', 'createdTimeEnd'],
      isCollapse: true,
      label: '下单时间：',
      default: []
    },
    {
      type: 'dateRange',
      field: 'orderUpdateDate',
      keys: ['updatedTimeStart', 'updatedTimeEnd'],
      isCollapse: true,
      label: '更新时间：',
      default: []
    },
    {
      type: 'select',
      field: 'bindingChannel',
      label: '绑定渠道：',
      isCollapse: true,
      props:{
        filterable:true
      },
      options: _this.activateChannelStatus
    },
    {
      type: 'select',
      field: 'isCharge',
      label: '注销是否收费',
      isCollapse: true,
      options: [
        {
          label: '否',
          value: 0
        },
        {
          label: '是',
          value: 1
        }
      ]
    },
    {
      type: 'input',
      field: 'applicantMobile',
      label: '申请人手机号：',
      isCollapse: true,
      default: '',
    },
  ]
}


//转换结果
const nodeStatusObj = {
  '501': '初始化',
  '502': '转换成功',
  '503': '转换失败',
}

//产品转换表格
export const productListColoumns = (_this) => {
  return [
    {
      prop: 'userName',
      width: 170,
      label: '用户名称',
      formatter: (row) => {
        return maskUsername(row)
      }
    },
    {
      prop: 'mobile',
      width: 120,
      label: '手机号',
      formatter: (row) => {
        return maskMobile(row)
      }
    },
    {
      prop: 'accountType',
      width: 120,
      label: '用户类型',
      formatter: (row) => {
        return row == 1 ? '单位' : '个人'
      }
    },
    {
      prop: 'vehicleNo',
      width: 120,
      label: '车牌号',
    },
    {
      prop: 'vehicleColor',
      width: 120,
      label: '车辆颜色',
      formatter: (row) => {
        return getVehicleColor(row)
      }
    },
    {
      prop: 'cardNo',
      label: 'ETC卡号',
      width: 200,

    },
    {
      prop: 'obuNo',
      label: 'OBU号',
      width: 200,

    },
    {
      prop: 'isTrunk',
      width: 100,
      label: '客货类型',
      formatter: (row) => {
        return getVehicleType(row)
      }
    },
    {
      prop: 'carType',
      width: 100,
      label: '车型',
      formatter: (row) => {
        return getCarType(row)
      }
    },
    {
      prop: 'froProductType',
      width: 200,
      label: '旧产品类型',
      formatter: (row) => {
        return _this.queryGroup.productTypeObj[row]
      }
    },
    {
      prop: 'bindingChannel',
      label: '绑定渠道',
      width: 100,
      // formatter: (row) => {
      //   let obj = {}
      //   _this.activateChannelStatus.forEach(item => {
      //     if(item.value){
      //       obj[item.value] = item.label
      //     }
      //   })
      //   console.log(obj,'convertFormat(_this.activateChannelStatus)')
      //   return  obj[row]
      // }
    },
    {
      prop: 'aftProductType',
      width: 200,
      label: '新产品类型',
      formatter: (row) => {
        return _this.queryGroup.productTypeObj[row]
      }
    },
    {
      prop: 'applyChannel',
      label: '申请渠道',
      formatter: (row) => {
        return applyChannel[row]
      },
      width: 120,
    },
    {
      prop: 'nodeStatus',
      width: 120,
      label: '转换结果',
      formatter: (row) => {
        return nodeStatusObj[row]
      }
    },
    {
      prop: 'remarks',
      width: 100,
      label: '备注信息',
    },
    {
      prop: 'updatedTime',
      width: 180,
      label: '更新时间',
    },

    // {
    //   prop: 'action',
    //   fixed: 'right',
    //   width: 100,
    //   label: '操作'
    // }
  ]
}

//产品转换搜索表单
export const productListForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'vehicleNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll
    },
    {
      type: 'input',
      field: 'cardNo',
      label: 'ETC号：',
      default: '',
    },
    {
      type: 'input',
      field: 'obuNo',
      label: 'OBU号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'isTrunk',
      label: '客货类型：',
      isCollapse: true,
      options: vehicleTypeAll
    },
    {
      type: 'input',
      field: 'userName',
      label: '用户名称：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'mobile',
      label: '手机号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'accountType',
      label: '用户类型：',
      isCollapse: true,
      options: customerTypeAll
    },
    {
      type: 'select',
      field: 'froProductType',
      label: '旧产品类型：',
      isCollapse: true,
      options: _this.queryGroup.productType
    },
    {
      type: 'select',
      field: 'bindingChannel',
      label: '绑定渠道：',
      isCollapse: true,
      options: _this.activateChannelStatus
    },
    {
      type: 'select',
      field: 'aftProductType',
      label: '新产品类型：',
      isCollapse: true,
      options: _this.queryGroup.productType
    },
    {
      type: 'select',
      field: 'applyChannel',
      label: '申请渠道：',
      isCollapse: true,
      options: convertFormat(applyChannel)
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['transformTimeSta', 'transformTimeEnd'],
      isCollapse: true,
      label: '转换时间：',
      default: []
    },
    {
      type: 'select',
      field: 'nodeStatus',
      label: '转换结果：',
      isCollapse: true,
      options: convertFormat(nodeStatusObj)
    },
  ]
}

// 线上注销导出字段转换
export const queryExportConfig = {
  orderId: 'logoutId',
  vehicleNo: 'logoutVehicleNo',	//车牌号码
  vehicleColor: 'logoutVehicleColor',	//车牌颜色
  cardNo: 'logoutCardNo',	//ETC卡号
  obuNo: 'logoutObuNo',	//OBU号
  isTrunk: 'logoutIsTrunk',	//客货类型
  custName: 'logoutCustName',	//用户名称
  mobile: 'logoutMobile',	//手机号
  accountType: 'logoutAccountType',	//用户类型
  productType: 'logoutProductType',		//产品类型
  applyChannel: 'logoutApplyChannel',	//申请渠道
  nodeCode: 'logoutNodeCode',		//订单状态
  createdTimeStart: 'logoutStartTime',		//下单时间起始
  createdTimeEnd: 'logoutEndTime',		//下单时间截止
  updatedTimeStart: 'logoutUpdateStartTime',	//更新时间起始
  updatedTimeEnd: 'logoutUpdateEndTime',	//更新时间截止
  bindingChannel: 'logoutBindingChannel',	//绑定渠道
  isCharge: 'logoutIsCharge'		//是否收费
}

// 产品转换导出字段转换
export const porductQueryExportConfig = {
  vehicleNo: 'transformVehicleNo',	//车牌号码
  vehicleColor: 'transformVehicleColor',	//车牌颜色
  cardNo: 'transformCardNo',	//ETC卡号
  obuNo: 'transformObuNo',	//OBU号
  isTrunk: 'transformIsTrunk',	//客货类型
  custName: 'transformUserName',	//用户名称
  mobile: 'transformMobile',	//手机号
  accountType: 'transformAccountType',	//用户类型
  froProductType: 'transformFroProductType',		//旧产品类型
  bindingChannel: 'transformBindingChannel',	//绑定渠道
  aftProductType: 'transformAftProductType', // 新产品类型
  applyChannel: 'transformApplyChannel',	//申请渠道
  transformTimeSta: 'transformTimeSta',		//下单时间起始
  transformTimeEnd: 'transformTimeEnd',		//下单时间截止
  nodeStatus: 'transformNodeStatus'		//是否收费
}

//抖音订单表格
export const dyListColoumns = (_this) => {
  return [
    {
      prop: 'orderId',
      label: '订单编号',
      width: 200,
    },
    {
      prop: 'salesChannels',
      label: '销售渠道',
      width: 100,
      formatter: (row) => {
        return row == 0 ? '抖音' : '-'
      }
    },
    {
      prop: 'netName',
      width: 170,
      label: '用户名称',
      formatter: (row) => {
        return maskUsername(row)
      }
    },
    {
      prop: 'netMobile',
      width: 120,
      label: '手机号',
      formatter: (row) => {
        return maskMobile(row)
      }
    },
    {
      prop: 'orderStatus',
      width: 120,
      label: '订单状态',
      // formatter: (row) => {
      //   return _this.queryGroup.orderStatusObj[row]
      // }
    },
    {
      prop: 'afterSaleStatus',
      width: 100,
      label: '售后状态',
      // formatter: (row) => {
      //   return _this.queryGroup.postStatusObj[row]
      // }
    },
    {
      prop: 'productType',
      width: 200,
      label: '产品类型',
      formatter: (row) => {
        return row == 5 ? '日日通' : row == 10 ? '次次顺' : '-'
      }
    },
    {
      prop: 'vehicleNo',
      width: 120,
      label: '车牌号',
    },
    {
      prop: 'vehicleColor',
      width: 120,
      label: '车牌颜色',
      formatter: (row) => {
        return getVehicleColor(row)
      }
    },

    {
      prop: 'isTrunk',
      width: 100,
      label: '客货类型',
      formatter: (row) => {
        return getVehicleType(row)
      }
    },
    {
      prop: 'carType',
      width: 100,
      label: '车型',
      formatter: (row) => {
        return getCarType(row)
      }
    },
    {
      prop: 'bindingChannels',
      label: '绑定渠道',
      width: 120,
      formatter: (row) => {
        return _this.queryGroup.bindingChannelsObj[row]
      }
    },
    {
      prop: 'createTime',
      width: 220,
      label: '订单提交时间',
    },
    {
      prop: 'checkType',
      width: 120,
      label: '审核方式',
      formatter: (row) => {
        return row == 0 ? '系统审核' : '人工审核'
      }
    },
    {
      prop: 'nodeCode',
      width: 120,
      label: '申办状态',
      formatter: (row) => {
        return _this.queryGroup.nodeCodeObj[row]
      }
    },
    {
      prop: 'action',
      fixed: 'right',
      width: 100,
      label: '操作'
    }
  ]
}


//抖音订单搜索表单
export const dyListForm = (_this) => {
  console.log(_this, '_this')
  return [
    {
      type: 'input',
      field: 'orderId',
      label: '订单编号：',
      default: '',
    },
    {
      type: 'input',
      field: 'vehicleNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll
    },
    {
      type: 'input',
      field: 'netName',
      label: '用户名称：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'netMobile',
      label: '手机号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'salesChannels123',
      label: '销售渠道：',
      isCollapse: true,
      default: 0,
      options: [
        {
          label: '抖音',
          value: 0
        }
      ]
    },
    {
      type: 'select',
      field: 'checkType',
      label: '审核方式：',
      isCollapse: true,
      options: [
        {
          label: '系统审核',
          value: 0
        },
        {
          label: '人工审核',
          value: 1
        },
      ]
    },
    {
      type: 'select',
      field: 'orderStatus',
      label: '订单状态：',
      isCollapse: true,
      options: _this.queryGroup.orderStatus
    },
    {
      type: 'select',
      field: 'afterSaleStatus',
      label: '售后状态：',
      isCollapse: true,
      options: _this.queryGroup.postStatus
    },
    {
      type: 'select',
      field: 'nodeCode',
      label: '申办状态：',
      isCollapse: true,
      options: _this.queryGroup.nodeCode
    },
    {
      type: 'select',
      field: 'bindingChannels',
      label: '绑定渠道：',
      isCollapse: true,
      options: _this.queryGroup.bindingChannels
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['createTimeSta', 'createTimeEnd'],
      isCollapse: true,
      label: '订单提交时间：',
      default: []
    },
    {
      type: 'select',
      field: 'productType',
      label: '产品类型：',
      isCollapse: true,
      options: [
        {
          label: '日日通',
          value: 5
        },
        {
          label: '次次顺',
          value: 10
        },
      ]
    },

    {
      type: 'select',
      field: 'vehicleOwner',
      label: '车辆所有人',
      isCollapse: true,
      options: [
        {
          label: '本人',
          value: '0'
        },
        {
          label: '他人',
          value: '1'
        },
        {
          label: '单位',
          value: '2'
        },
      ]
    },
  ]
}

// 电商订单导出转换
export const tiktokQueryExportConfig = {
  orderId: 'tik_orderId',
  vehicleNo: 'tik_vehicleNo',
  vehicleColor: 'tik_vehicleColor',
  netName: 'tik_netName',
  netMobile: 'tik_netMobile',
  salesChannels: 'tik_salesChannels',
  checkType: 'tik_checkType',
  orderStatus: 'tik_orderStatus',
  afterSaleStatus: 'tik_afterSaleStatus',
  nodeCode: 'tik_nodeCode',
  bindingChannels: 'tik_bindingChannels',
  createTimeSta: 'tik_createTimeSta',
  createTimeEnd: 'tik_createTimeEnd',
  productType: 'tik_productType',
  vehicleOwner: 'tik_vehicleOwner',
}