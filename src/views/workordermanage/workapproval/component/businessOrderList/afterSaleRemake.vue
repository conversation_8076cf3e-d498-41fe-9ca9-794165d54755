<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行——售后补办
  * @author:zhang<PERSON>
  * @date:2023/03/07 15:24:12
-->
<template>
  <div>
    <afterSaleSearch @search="search"
                     @exportReport="exportReport"
                     type="remake"></afterSaleSearch>
    <div class="table">
      <el-table :data="tableData"
                style="width: 100%;"
                height="55vh"
                row-key="id">
        <el-table-column prop="orderId"
                         label="订单号"
                         width="190"
                         align="center" />
        <el-table-column prop="custName"
                         label="用户名"
                         align="center"
                         width="120" />
        <el-table-column prop="custMobile"
                         label="手机号"
                         align="center"
                         width="140" />
        <el-table-column prop="custType"
                         label="账户类型"
                         align="center"
                         width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.custType">{{scope.row.custType == 1 ? '单位' : '个人'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus"
                         label="订单状态"
                         width="120"
                         align="center">

          <template slot-scope="scope">
            <span>{{getAfterSaleRemakeStatus(scope.row.orderStatus)}}</span>
          </template>
        </el-table-column>

        <el-table-column prop="carNo"
                         label="车牌号"
                         width="120"
                         align="center" />
        <el-table-column prop="carColor"
                         label="车辆颜色"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.carColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="cardNo"
                         label="ETC卡号"
                         width="180"
                         align="center" />
        <el-table-column prop="obuNo"
                         label="OBU号"
                         width="180"
                         align="center" />
        <el-table-column prop="carType"
                         label="客货类型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleType(scope.row.carType)}}
          </template>
        </el-table-column>
        <el-table-column prop="carType"
                         label="车型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            <!-- {{getCarType(scope.row.carType)}} -->
          </template>
        </el-table-column>
        <el-table-column prop="productType"
                         label="产品类型"
                         width="150"
                         align="center">
          <template slot-scope="scope">
            {{getallGxCardType(scope.row.productType)}}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="vehicleNo"
                         label="更换业务类型"
                         width="120"
                         align="center" />
        <el-table-column prop="vehicleNo"
                         label="更换设备类型"
                         width="120"
                         align="center" />
        <el-table-column prop="benefitServiceFee"
                         label="更换费用(元)"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            {{scope.row.benefitServiceFee}}
          </template>
        </el-table-column> -->

        <el-table-column prop="bindChannel"
                         label="绑定渠道"
                         width="120"
                         align="center" />
        <el-table-column prop="applyChannel"
                         label="申请渠道"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            <!-- {{getApplyChannelOptions(scope.row.applyChannel)}} -->
            八桂行小程序

          </template>
        </el-table-column>
        <el-table-column prop="createTime"
                         label="订单提交时间"
                         width="180"
                         align="center" />
        <el-table-column prop="updateTime"
                         label="状态更新时间"
                         width="180"
                         align="center" />
        <el-table-column label="操作"
                         fixed="right"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            <el-button type="text"
                       @click="toDetail(scope.row,'view')">查看</el-button>
            <el-button type="text"
                       @click="toDetail(scope.row,'deal')"
                       v-if="!(scope.row.orderStatus=='301'||scope.row.orderStatus=='304'||scope.row.orderStatus=='306'||scope.row.orderStatus=='307')">处理</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="form.pageNum"
                     :page-sizes="[10, 20, 50]"
                     :page-size="form.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <dartSlide :visible.sync="slideVisible"
               title="订单详情"
               v-transfer-dom
               width="90%"
               :maskClosable="true">
      <detail :orderId="orderId"
              :slideVisible="slideVisible"
              :detailType="detailType"
              @refreshList="getOrderList"
              @closeDartSlide="closeDartSlide"
              orderType='remake'></detail>
    </dartSlide>
  </div>

</template>

<script>
import {
  getbusinessType,
  getVehicleColor,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
  getAfterSaleRemakeStatus,
  getallGxCardType,
} from '@/common/method/formatOptions'
import dartSlide from '@/components/dart/Slide/index.vue'
import afterSaleSearch from './afterSaleSearch'
import detail from '../afterSale/detail'
import float from '@/common/method/float.js'
import { getToken } from '@/utils/auth'

export default {
  components: {
    afterSaleSearch,
    dartSlide,
    detail,
  },
  created() {
    this.getOrderList()
  },
  mounted() {},
  data() {
    return {
      tableData: [],
      form: { pageNum: 1, pageSize: 10 },
      auditVisible: false,
      total: 0,
      slideVisible: false,
      orderId: '',
      detailType: '',
      vehicleTypeAll: [
        { value: '', label: '全部' },
        { value: '1', label: '客车' },
        { value: '2', label: '货车' },
        { value: '3', label: '专项作业车' },
      ],
    }
  },
  mounted() {
    // 挂在时新增监听器，页面刷新或关闭都会触发beforeunload
    window.addEventListener('beforeunload', (e) => this.beforeunloadFn(e))
  },
  beforeDestroy() {
    // 销毁前移除监听器，同时调用解锁
    window.removeEventListener('beforeunload', (e) => this.beforeunloadFn(e))
  },
  methods: {
    getbusinessType,
    getVehicleColor,
    getVehicleType(value) {
      for (let i = 0; i < this.vehicleTypeAll.length; i++) {
        if (this.vehicleTypeAll[i].value == value) {
          return this.vehicleTypeAll[i].label
        }
      }
      return ''
    },
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getAfterSaleRemakeStatus,
    getCheckTypeOptions,
    getallGxCardType,
    beforeunloadFn(e) {
      let url =
        process.env.VUE_APP_BASE_API +
        '/issue-web' +
        this.$interfaces.afterSaleFreeOrder
      let data = { orderId: this.orderId }
      let headers = {
        'Content-Type': 'application/json',
        Authorization: getToken(),
      }
      //mounted时和预览时不调用解锁操作

      if (!this.orderId || this.detailType == 'view') return
      fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        // 保持连接
        keepalive: true,
      })
        .then((res) => {})
        .catch((err) => {})
    },
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    closeDartSlide() {
      this.slideVisible = false
    },

    search(val) {
      this.form = val
      this.form.pageNum = 1
      this.getOrderList()
    },
    exportReport(val) {
      this.startLoading()
      let params = JSON.parse(JSON.stringify(val))
      params.orderType = '0'
      this.$request({
        url: this.$interfaces.afterSaleListExport,
        method: 'post',
        data: params,
        responseType: 'blob',
      })
        .then((res) => {
          this.endLoading()

          this.getBlob(res, 'application/vnd.ms-excel', '线上补办列表')
        })
        .catch(() => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    toDetail(item, val) {
      this.detailType = val
      this.orderId = item.orderId
      this.slideVisible = true
    },

    getOrderList() {
      this.startLoading()
      let params = JSON.parse(JSON.stringify(this.form))
      params.orderType = '0'
      this.$request({
        url: this.$interfaces.afterSaleList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.endLoading()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((error) => {
          this.endLoading()

          console.log('err', error)
        })
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getOrderList()
    },
    changePage(e) {
      this.form.pageNum = e
      this.getOrderList()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .modular-view-title {
  border-bottom: none !important;
}
</style>