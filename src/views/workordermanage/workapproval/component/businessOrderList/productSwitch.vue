<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >导出</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="false"
        @changeTableData="changeTableData"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button type="text" @click="handelRow(scope, 'view')"
              >查看</el-button
            >
            <el-button type="text" @click="handelRow(scope, 'edit')"
              >处理</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <DartSlide
      :visible.sync="slideVisible"
      title="订单详情"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
      @close="modelClose"
    >
      <Detail
        v-if="slideVisible"
        :orderId="orderId"
        :queryGroup="queryGroup"
        :slideVisible="slideVisible"
        :isView="isView"
        @refreshList="getTableData"
        @closeDartSlide="closeDartSlide"
      ></Detail>
    </DartSlide>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import DartSlide from '@/components/dart/Slide/index.vue'
import Detail from '../cancel/detail.vue'
import {
  productListColoumns,
  productListForm,
  porductQueryExportConfig
} from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import {
  logoutInitData,
  transformList,
  orderLock,
  orderFree,
  exportList
} from '@/api/workordermanage'
import { mapGetters } from 'vuex'
import { convertFormat } from '@/utils'
import { decode } from 'js-base64'

export default {
  components: {
    MyTable,
    SearchForm,
    DartSlide,
    Detail
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: transformList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      isView: false,
      slideVisible: false,
      orderId: '',
      queryGroup: {
        orderStatusObj: {},
        productTypeObj: {}
      },
      timeField: ['OrderSubmitDate', 'orderUpdateDate']
    }
  },
  computed: {
    ...mapGetters(['applyOrderStatus', 'activateChannelStatus']),
    listColoumns() {
      return productListColoumns(this)
    },
    formConfig() {
      return productListForm(this)
    }
  },
  methods: {
    async handelRow(row, type) {
      this.orderId = row.orderId
      if (type == 'view') {
        this.isView = true
      } else {
        this.isView = false
        await this.lockOrder()
      }
      this.slideVisible = true
    },
    closeDartSlide() {
      this.slideVisible = false
    },
    modelClose() {
      this.freeOrder()
    },
    async lockOrder() {
      console.log(this.orderId, this.isView)
      if (!this.orderId || this.isView) return
      let params = {
        orderId: this.orderId
      }
      await orderLock(params)
      return new Promise((resolve, reject) => {
        resolve()
      })
    },
    async freeOrder() {
      if (!this.orderId || this.isView) return
      let params = {
        orderId: this.orderId
      }
      let res = await orderFree(params)
    },
    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = this.transformData(query, porductQueryExportConfig)
      params.name = 'onlineTransformReport'
      // console.log(params, 'exportHandle params')
      this.sendReportRequest(params)
    },
    // 格式转换
    transformData(data, config) {
      const transformedData = {}
      for (const key in config) {
        if (data.hasOwnProperty(key) && data[key]) {
          const newKey = config[key]
          const value = data[key]
          transformedData[newKey] = value
        }
      }
      return transformedData
    },
    async sendReportRequest(data) {
      this.loading = true
      let res = await exportList(data)
      this.loading = false
      if (res.code == 200) {
        let url = res.data
        let decodeUrl = decode(url)
        // console.log(decodeUrl,'地址')
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      }
    },
    handleSizeChange(val) {
      this.changeTableData(val, this.pageNum)
    },
    handleCurrentChange(val) {
      this.changeTableData(this.pageSize, val)
    },
    async queryInit() {
      let res = await logoutInitData()
      let { orderStatus, productType } = res.data

      this.queryGroup = {
        orderStatusObj: orderStatus,
        productTypeObj: productType,
        orderStatus: convertFormat(orderStatus),
        productType: convertFormat(productType)
      }
      console.log(this.queryGroup)
    }
  },
  created() {
    this.queryInit()
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  // padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>