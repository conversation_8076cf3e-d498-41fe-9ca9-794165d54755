<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:通用搜索组件
  * @author:zhangys
  * @date:2023/03/07 16:02:53
-->
<template>
  <div>
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="form"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      class="search"
    >
      <template slot="search-form">
        <dart-search-item label="订单号：" prop="orderId">
          <el-input v-model="form.orderId" clearable placeholder=""></el-input>
        </dart-search-item>

        <dart-search-item label="车牌号：" prop="carNo">
          <el-input v-model="form.carNo" clearable placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色：" prop="carColor">
          <el-select
            clearable
            v-model="form.carColor"
            placeholder="请选择"
            collapse-tags
          >
            <el-option
              v-for="(item, index) in licenseColorOptionAll"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>

        <div class="collapse-wrapper" v-show="isCollapse">
          <dart-search-item label="订单类型：" prop="cardNo">
            <el-select
              clearable
              v-model="form.orderType"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in afterSaleOrderType"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            label="订单状态："
            prop="orderStatus"
            v-if="type == 'remake'"
          >
            <el-select
              clearable
              v-model="form.orderStatus"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in afterSaleRemakeStatus"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            label="订单状态："
            prop="orderStatus"
            v-if="type == 'change'"
          >
            <el-select
              clearable
              v-model="form.orderStatus"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in afterSaleChangeStatus"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="ETC卡号：" prop="cardNo">
            <el-input v-model="form.cardNo" clearable placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="OBU号：" prop="obuNo">
            <el-input v-model="form.obuNo" clearable placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="用户名称：" prop="custName">
            <el-input
              v-model="form.custName"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="手机号：" prop="custMobile">
            <el-input
              v-model="form.custMobile"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          <dart-search-item label="用户类型："
                            prop="custType">
            <el-select clearable
                       v-model="form.custType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in customerTypeAll"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="申请渠道：" prop="applyChannel">
            <el-select
              clearable
              v-model="form.applyChannel"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in applyChannelOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>

          <dart-search-item label="产品类型：" prop="productType">
            <el-select
              clearable
              v-model="form.productType"
              placeholder="请选择"
              collapse-tags
            >
              <el-option
                v-for="(item, index) in productTypeOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>

          <!-- <dart-search-item label="更换业务类型："
                            prop="orderStatus">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="更换设备类型："
                            prop="orderStatus">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="补办设备类型："
                            prop="orderStatus"
                            v-if="type=='remake'">
            <el-select clearable
                       v-model="form.orderStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateOrderStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item> -->

          <div>
            <dart-search-item label="订单提交时间:" prop="">
              <el-date-picker
                v-model="OrderSubmitDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </dart-search-item>

            <dart-search-item label="状态更新时间:" prop="">
              <el-date-picker
                v-model="orderUpdateDate"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </dart-search-item>

            <dart-search-item label="审核方式：" prop="carType">
              <el-select
                clearable
                v-model="form.auditType"
                placeholder="请选择"
                collapse-tags
              >
                <el-option
                  v-for="(item, index) in afterSaleAudit"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="车辆类型：" prop="carType">
              <el-select
                clearable
                v-model="form.carType"
                placeholder="请选择"
                collapse-tags
              >
                <el-option
                  v-for="(item, index) in vehicleTypeAll"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="绑定渠道：" prop="bindChannel">
              <el-select
                clearable
                v-model="form.bindChannel"
                placeholder="请选择"
                collapse-tags
              >
                <el-option
                  v-for="(item, index) in activateChannelStatus"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item label="物流单号：" prop="expressNumber">
            <el-input
              v-model="form.expressNumber"
              clearable
              placeholder=""
            ></el-input>
          </dart-search-item>
          </div>
        </div>
        <dart-search-item :is-button="true" :colElementNum="1">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button
              type="warning"
              size="mini"
              native-type="submit"
              @click="exportHandle"
              >导出</el-button
            >

            <el-button type="text" @click="isCollapse = !isCollapse"
              ><span v-if="isCollapse">收起</span
              ><span v-if="!isCollapse">展开</span></el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import {
  licenseColorOptionAll,
  vehicleTypeAll,
  customerTypeAll,
  productTypeOptions,
  applyChannelOptions,
  activateOrderStatus,
  activateApplyChannel,
  afterSaleOrderType,
  afterSaleRemakeStatus,
  afterSaleChangeStatus,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'

export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      vehicleTypeAll: [
        { value: '', label: '全部' },
        { value: '1', label: '客车' },
        { value: '2', label: '货车' },
        { value: '3', label: '专项作业车' },
      ],
      licenseColorOptionAll,
      customerTypeAll,
      form: {
        orderId: '',
        carNo: '',
        carColor: '',
        orderType: '',
        orderStatus: '',
        cardNo: '',
        obuNo: '',
        custName: '',
        custMobile: '',
        custType: '',
        applyChannel: '',
        productType: '',
        createTimeStart: '',
        createTimeEnd: '',
        updateTimeStart: '',
        updateTimeEnd: '',
        auditType: '',
        carType: '',
        bindChannel: '',
        expressNumber: '', //快递单号
        pageNum: 1,
        pageSize: 10,
      },
      isCollapse: false,
      OrderSubmitDate: '',
      orderUpdateDate: '',
      productTypeOptions,
      applyChannelOptions,
      orderStatusOptions: [],
      activateOrderStatus,
      activateApplyChannel,
      afterSaleOrderType,
      afterSaleAudit: [{ value: '1', label: '人工审核' }],
      afterSaleRemakeStatus,
      afterSaleChangeStatus,
    }
  },
  computed: {
    ...mapGetters([
      'applyOrderStatus',
      'applyChannelStatus',
      'activateChannelStatus',
    ]),
  },
  watch: {
    OrderSubmitDate(val) {
      if (!val) {
        this.form.createTimeStart = ''
        this.form.createTimeEnd = ''
      }
    },
    orderUpdateDate(val) {
      if (!val) {
        this.form.updateTimeEnd = ''
        this.form.updateTimeStart = ''
      }
    },
  },
  created() {},
  methods: {
    onSearchHandle() {
      this.formatDate()
      this.$emit('search', this.form)
    },
    formatDate() {
      if (this.OrderSubmitDate) {
        this.form.createTimeStart = this.OrderSubmitDate[0]
        this.form.createTimeEnd = this.OrderSubmitDate[1]
      }
      if (this.orderUpdateDate) {
        this.form.updateTimeStart = this.orderUpdateDate[0]
        this.form.updateTimeEnd = this.orderUpdateDate[1]
      }
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
      this.OrderSubmitDate = ''
      this.orderUpdateDate = ''
      this.form.createTimeStart = ''
      this.form.createTimeEnd = ''
      this.form.updateTimeStart = ''
      this.form.createTimeEnd = ''
      this.$emit('search', this.form)
    },

    //导出
    exportHandle() {
      this.formatDate()
      this.$emit('exportReport', this.form)
    },
  },
}
</script>

<style lang='scss' scoped>
</style>