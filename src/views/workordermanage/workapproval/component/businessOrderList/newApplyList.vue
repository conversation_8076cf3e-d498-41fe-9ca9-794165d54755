<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行订单
  * @author:zhangys
  * @date:2023/03/07 15:24:12
-->
<template>
  <div>
    <search @auditDialog="auditDialog"
            @search="search"
            @batchAudit="batchAuditConfirm"
            @export="exportReport"
            type="newApply"></search>
    <div class="table">
      <el-table :data="tableData"
                v-loading="tableloading"
                style="width: 100%;"
                height="55vh"
                row-key="id"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection">
        </el-table-column>
        <el-table-column prop="id"
                         label="订单号"
                         align="center" />
        <el-table-column prop="userName"
                         label="用户名"
                         align="center"
                         width="120" />
        <el-table-column prop="mobile"
                         label="手机号"
                         align="center"
                         width="120" />
        <el-table-column prop="accountType"
                         label="账户类型"
                         align="center"
                         width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.accountType">{{scope.row.accountType == 1 ? '单位' : '个人'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="businessType"
                         label="业务类型"
                         align="center">
          <template slot-scope="scope">
            {{getbusinessType(scope.row.businessType)}}
          </template>
        </el-table-column>
        <el-table-column prop="orderStatus"
                         label="订单状态"
                         width="120"
                         align="center" />
        <!--  -->

        <el-table-column prop="vehicleNo"
                         label="车牌号"
                         width="120"
                         align="center" />
        <el-table-column prop="vehicleColor"
                         label="车牌颜色"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.vehicleColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="isTrunk"
                         label="客货类型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleType(scope.row.isTrunk)}}
          </template>
        </el-table-column>
        <el-table-column prop="carType"
                         label="车型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getCarType(scope.row.carType)}}
          </template>
        </el-table-column>
        <el-table-column prop="productType"
                         label="产品类型"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getProductTypeOptions(scope.row.productType)}}
          </template>
        </el-table-column>
        <el-table-column prop="installType"
                         label="取货方式"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{scope.row.installType=='0'?'网点自提':'快递邮寄'}}
          </template>
        </el-table-column>
        <el-table-column prop="benefitServiceFee"
                         label="权益服务费(元)"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.benefitServiceFee)}}
          </template>
        </el-table-column>
        <el-table-column prop="activationDeposit"
                         label="激活保证金(元)"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.activationDeposit)}}
          </template>
        </el-table-column>
        <el-table-column prop="equipmentFee"
                         label="设备费(元)"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.equipmentFee)}}
          </template>
        </el-table-column>
        <el-table-column prop="bindChannel"
                         label="绑定渠道"
                         width="120"
                         align="center" />
        <el-table-column prop="applyChannel"
                         label="申请渠道"
                         width="120"
                         align="center">
          <template slot-scope="scope">
            {{getApplyChannelOptions(scope.row.applyChannel)}}
          </template>
        </el-table-column>
        <el-table-column prop="orderTime"
                         label="下单时间"
                         width="180"
                         align="center" />
        <el-table-column prop="statusTime"
                         label="更新时间"
                         width="180"
                         align="center" />
        <el-table-column prop="checkType"
                         label="审核方式"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            {{getCheckTypeOptions(scope.row.checkType)}}
          </template>
        </el-table-column>

        <el-table-column label="操作"
                         fixed="right"
                         width="100"
                         align="center">
          <template slot-scope="scope">
            <!-- <el-button type="text"
                         @click="toDetail(scope.row)">{{gettype(scope.row.node_code) ? '审批' : '详情' }}</el-button> -->

            <!-- node_code: 
                  2000：退换申请
                  2020：人工审核
               -->
            <!-- <el-button type="text"
                       @click="toDetail(scope.row)">{{(scope.row.node_status=='2'&&gettype(scope.row.node_code)||scope.row.node_code=='2000'||scope.row.node_code=='2020' )? '审批' : '详情' }}</el-button> -->

            <el-button type="text"
                       @click="toDetail(scope.row,'view')">查看</el-button>
            <el-button type="text"
                       @click="toDetail(scope.row,'deal')">处理</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="form.pageIndex"
                     :page-sizes="[10, 20, 50]"
                     :page-size="form.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>

    <dartSlide :visible.sync="slideVisible"
               title="订单详情"
               v-transfer-dom
               width="90%"
               :maskClosable="true">
      <detail :applyId="applyId"
              :slideVisible="slideVisible"
              :isView="isView"
              @refreshList="getOrderList"
              @closeDartSlide="closeDartSlide"></detail>
    </dartSlide>
  </div>

</template>

<script>
import {
  getbusinessType,
  getVehicleColor,
  getnowstate,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
} from '@/common/method/formatOptions'
import {
  newApplyNodeCodeOptions,
  refundNodeCodeOptions,
  cancelNodeCodeOptions,
  afterSaleStatus,
  vehicleType,
  customerType,
} from '@/common/const/optionsData.js'
import dartSlide from '@/components/dart/Slide/index.vue'
import search from './search'
import detail from '../newApply/detail'
import float from '@/common/method/float.js'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'

export default {
  components: {
    search,
    dartSlide,
    detail,
  },
  created() {
    this.getOrderList()
  },
  data() {
    return {
      tableloading: false,
      tableData: [],
      form: { pageIndex: 1, pageSize: 10 },

      detaildata: {},
      dialogVisible: false,
      auditVisible: false,
      total: 0,
      typearr: ['1000', '2000', '2020', '3000', '4000'],
      nodeCodeOptions: [],
      afterSaleStatus,
      newApplyNodeCodeOptions,
      refundNodeCodeOptions,
      cancelNodeCodeOptions,
      slideVisible: false,
      applyId: '',
      isView: false, //是否查看
      selectedItem: [],
    }
  },
  /**
   * 根据业主需求优化
   * 逻辑：防止网页刷新或者关闭导致订单异常关闭被锁,新增监听器，在页面销毁时清除监听器，同时会触发解锁操作
   *
   * 注意：界面刷新或关闭大概在1-3ms之内完成，而普通接口请求一般需要50ms+,所以在页面销毁时异步调用接口会调用失败
   *
   * 一般采用：
   *  # ajax同步请求(不推荐)
   *  # fetch请求采用keepalive: true的形式，界面销毁保持连接
   *  # navigator.sendBeacon() 详情百度
   * author: zys -2023.4.14
   */
  mounted() {
    // 挂在时新增监听器，页面刷新或关闭都会触发beforeunload
    window.addEventListener('beforeunload', (e) => this.beforeunloadFn(e))
  },
  beforeDestroy() {
    // 销毁前移除监听器，同时调用解锁
    window.removeEventListener('beforeunload', (e) => this.beforeunloadFn(e))
  },

  methods: {
    getbusinessType,
    getVehicleColor,
    getnowstate,
    getVehicleType,
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    beforeunloadFn(e) {
      let url =
        process.env.VUE_APP_BASE_API + '/issue-web' + this.$interfaces.orderOpen
      let data = { id: this.applyId }
      let headers = {
        'Content-Type': 'application/json',
        Authorization: getToken(),
      }
      //mounted时和预览时不调用解锁操作
      if (!this.applyId || this.isView) return
      fetch(url, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(data),
        // 保持连接
        keepalive: true,
      })
        .then((res) => {})
        .catch((err) => {})
    },
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    closeDartSlide() {
      this.slideVisible = false
    },
    auditDialog() {
      this.auditVisible = true
    },

    search(val) {
      this.form = val
      this.form.pageIndex = 1
      this.getOrderList()
    },
    toDetail(item, val) {
      if (val == 'view') {
        this.isView = true
      } else {
        this.isView = false
      }
      this.applyId = item.id
      this.slideVisible = true
      return
    },
    gettype(val) {
      if (this.typearr.indexOf(val) == -1) {
        return false
      } else {
        return true
      }
    },
    getOrderList() {
      this.startLoading()
      let params = JSON.parse(JSON.stringify(this.form))
      this.$request({
        url: this.$interfaces.newApplyList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total
          this.endLoading()
        })
        .catch((error) => {
          this.endLoading()

          console.log('err', error)
        })
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getOrderList()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.getOrderList()
    },
    //表格选中
    handleSelectionChange(val) {
      this.selectedItem = val.map((item) => {
        return item.id
      })
      console.log(this.selectedItem, '<<---------this.selectedItem')
    },
    //批量审核
    batchAuditConfirm() {
      if (this.selectedItem.length == 0) {
        this.$message.warning('请至少选中一条记录！')
        return
      }
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '是否对选中的记录进行批量审核操作'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.batchAudit()
          }
        },
      })
    },
    batchAudit() {
      let params = {
        id: this.selectedItem,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.batchAudit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, '<<---------res')
          if (res.code == 200) {
            if (res.data.failed > 0) {
              this.$message.error(
                `批量审核${res.data.total}条，成功${res.data.success}条，失败${res.data.failed}条`
              )
            }
            if (res.data.failed == 0) {
              this.$message.success(
                `批量审核${res.data.total}条，成功${res.data.success}条，失败${res.data.failed}条`
              )
            }
            this.getOrderList()
            this.endLoading()
          } else {
            this.$message.error(res.msg)
            this.endLoading()
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    exportReport(val) {
      let params = {
        name: 'etcOnlineReleaseReport',
        apply_id: val.id,
        vehicle_n: val.vehicleNo,
        vehicle_c: val.vehicleColor,
        check_type: val.checkType,
        install_type: val.installType,
        is_trunk: val.isTrunk,
        cust_name: val.userName,
        net_mobile: val.mobile,
        account_type: val.accountType,
        product_type: val.productType,
        apply_channel: val.applyChannel,
        order_status: val.orderStatus,
        pay_time_sta: val.orderTimeSta,
        pay_time_end: val.orderTimeEnd,
        update_time_sta: val.statusTimeSta,
        update_time_end: val.statusTimeEnd,
        bind_channel: val.bindChannel,
        businessType: val.businessType,
        logisticsNo: val.logisticsNo,
      }
      this.startLoading()

      request({
        url: api.report,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          } else {
            this.endLoading()
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    chengType(value) {
      if (value == '1') {
        this.nodeCodeOptions = this.newApplyNodeCodeOptions
        return
      }
      if (value == '3' || value == '4') {
        this.nodeCodeOptions = this.refundNodeCodeOptions
        return
      }
      if (value == '2') {
        this.nodeCodeOptions = this.cancelNodeCodeOptions
        return
      }
      if (!value) {
        this.nodeCodeOptions = []
        this.form.nodeCode = ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .modular-view-title {
  border-bottom: none !important;
}
</style>