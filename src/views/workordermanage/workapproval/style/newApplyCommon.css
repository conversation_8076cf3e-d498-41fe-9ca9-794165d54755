/* 
  * @projectName:gxetc-issue-manage-web
  * @desc:线上新办售后通用样式
  * @author:zhang<PERSON>
  * @date:2023/03/02 10:18:08
*/
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-top: 20px;
}

.detail-wrap .orderItem:first-of-type {
  margin-top: 0px;
}

.detail-wrap .orderItem::-webkit-scrollbar {
  display: none;
}

.archives {
  padding: 0 20px;
  background-color: #fff;
}

.archives-box {
  display: flex;
  flex-wrap: wrap;
  -moz-box-pack: start;
  -ms-box-pack: start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  justify-content: flex-start;
}

.archives-box .archives-item {
  width: 240px;
  margin-right: 20px;
  margin-bottom: 20px;
}

.archives-box .archives-item .demonstration {
  display: block;
  color: #8492a6;
  width: 100%;
  text-align: center;
  font-size: 15px;
  margin-top: 10px;
}

.archives-box .archives-item .imgbox img {
  width: 240px;
  height: 180px;
}

.descriptions-content {
  padding: 16px !important;
}

.expand-icon {
  font-weight: bold;
  font-size: 18px;
  color: #409eff;
}

.edit-btn {
  padding-bottom: 10px;
  background-color: #fafafa;
}
