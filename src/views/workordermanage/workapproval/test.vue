<template>
  <div class="user">
    <div class="tab-item">
      <el-tabs @tab-click="handleClick" v-model="selectItem" type="border-card">
        <el-tab-pane
          :label="item.label"
          v-for="(item, index) in businessTypeOptions"
          :key="index"
          :name="item.value"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div>
      <!-- <chartInfo v-show="selectItem == '1'"></chartInfo> -->
      <Statistics v-show="selectItem == '1'"></Statistics>
      <Statistics v-show="selectItem == '1'"></Statistics>
      <Statistics v-show="selectItem == '1'"></Statistics>
      <Statistics v-show="selectItem == '1'"></Statistics>
      <Statistics v-show="selectItem == '1'"></Statistics>
      <!-- <newApplyList v-show="selectItem == '2'"></newApplyList>
      <activate v-show="selectItem == '5'"></activate>
      <afterSaleChange v-show="selectItem == '3'"></afterSaleChange>
      <afterSaleRemake v-show="selectItem == '4'"></afterSaleRemake> -->

      <!-- <cancellation v-if="selectItem == '6'"></cancellation>
      <productSwitch v-if="selectItem == '9'"></productSwitch>
      <dyOrder v-if="selectItem == '11'"></dyOrder> -->
    </div>
  </div>
</template>

<script>
import chartInfo from './component/businessOrderList/chartInfo'
import newApplyList from './component/businessOrderList/newApplyList'
import afterSaleChange from './component/businessOrderList/afterSaleChange'
import afterSaleRemake from './component/businessOrderList/afterSaleRemake'
import activate from './component/businessOrderList/activate'
import cancellation from './component/businessOrderList/cancellation'
import productSwitch from './component/businessOrderList/productSwitch.vue'
import dyOrder from './component/businessOrderList/dyOrder.vue'
import { mapGetters, mapActions } from 'vuex'
import Statistics from '../statistics/index.vue'

export default {
  components: {
    chartInfo,
    newApplyList,
    afterSaleChange,
    afterSaleRemake,
    activate,
    cancellation,
    productSwitch,
    Statistics,
    dyOrder
  },
  created() {
    this.getActivateStatusOptions()
    this.getStatusOptions()
    this.getAddress()
  },
  mounted() {},
  data() {
    return {
      businessTypeOptions: [
        { label: '首页', value: '1' },
        { label: '线上发行', value: '2' },
        { label: '线上售后更换', value: '3' },
        { label: '线上售后补办', value: '4' },
        { label: '自助激活', value: '5' },
        { label: '线上注销申请', value: '6' },
        { label: '产品转换', value: '9' },
        { label: '电商平台订单', value: '11' },

      ],
      selectItem: '1'
    }
  },
  methods: {
    ...mapActions([
      'setApplyOrderStatus',
      'setApplyChannelStatus',
      'setActivateChannelStatus',
      'setAddress'
    ]),
    handleClick(val) {
      this.selectItem = this.businessTypeOptions[val.index].value
    },
    getStatusOptions() {
      this.$request({
        url: this.$interfaces.searchInit,
        method: 'post',
        data: {}
      })
        .then(res => {
          if (res.code == 200) {
            let statusOptions = res.data.orderStatus.map(item => {
              return {
                value: item.id,
                label: item.bStatus
              }
            })
            let channelOPtions = res.data.channels.map(item => {
              return {
                value: item.vcOrgId,
                label: item.vcChannelName
              }
            })

            statusOptions.unshift({ value: '', label: '全部' })
            channelOPtions.unshift({ value: '', label: '全部' })

            this.setApplyOrderStatus(statusOptions)
            this.setApplyChannelStatus(channelOPtions)
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    },
    getAddress() {
      this.$request({
        url: this.$interfaces.getAddress,
        method: 'post',
        data: {}
      })
        .then(res => {
          if (res.code == 200) {
            let gxAddress = res.data.filter(item => {
              if (item.value == '450000') {
                return item
              }
            })
            let tmpArr = []
            tmpArr.push(gxAddress)
            this.setAddress(tmpArr)
          }
        })
        .catch(() => {})
    },
    getActivateStatusOptions() {
      this.$request({
        url: this.$interfaces.activateBindChannel,
        method: 'post',
        data: {}
      })
        .then(res => {
          console.log(res, '<<---------res')
          if (res.code == 200) {
            let channelOPtions = res.data.BINDING_BANK_TYPE.map(item => {
              return {
                value: item.fieldValue,
                label: item.fieldNameDisplay
              }
            })
            channelOPtions.unshift({ value: '', label: '全部' })
            this.setActivateChannelStatus(channelOPtions)
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  height: 100%;
  position: relative;
  margin: 15px 20px;
  flex-flow: column;
  display: flex;
  .tab-item {
    ::v-deep .el-tabs--border-card {
      box-shadow: none;
    }
    ::v-deep .el-tabs__content {
      padding: 0;
    }
    ::v-deep .el-tabs--border-card {
      border-bottom: none;
    }
  }
  .table {
    width: 100%;
    flex: 1;
    background-color: #ffffff;
    text-align: center;
    margin: 0;
  }
  ::v-deep.el-table th,
  ::v-deep.el-table td {
    text-align: center;
  }
  .itembox {
    padding: 0 25px;
    line-height: 50px;
    .item {
      margin: auto;
      font-size: 14px;
      span {
        display: inline-block;
        padding-right: 10px;
        font-weight: 600;
        min-width: 75px;
      }
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .foot {
    margin-top: 20px;
    text-align: center;
  }
  .pagination {
    padding: 10px 0;
    background-color: #fff;
    text-align: center;
  }
  ::v-deep .el-dialog {
    min-width: 1150px;
  }
}
</style>
