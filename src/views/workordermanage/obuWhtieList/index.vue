<template>
  <div class="obu-page">
    <SearchForm ref="SearchForm" :formConfig="formConfig" :rules="rules" @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle" :btnSpan="16">
      <el-button type="warning" slot="btn" size="mini" native-type="submit" @click="exportHandle">导出</el-button>
    </SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="add">添加</el-button>
        <el-button size="mini" type="danger" @click="batchDelete">删除</el-button>
      </div>
      <my-table ref="tableRef" v-loading="loading" :cloumns="listColoumns" :tableData="tableData" :total="total"
        :pageSize="pageSize" :pageNum="pageNum" :hasPagination="true" @changeTableData="changeTableData"
        @selectChange="selectChange">
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="danger" @click="handleDelete(scope, 'del')">删除</el-button>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm, queryExportConfig, formRules } from './model'
import { queryObuList, addObuWhite, deleteObuWhite, whiteListExport } from '@/api/workordermanage' // 暂时使用现有API

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data () {
    return {
      tableData: [],
      api: queryObuList, //
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      dataKey: 'data',
      timeField: ['OrderSubmitDate'],
      queryGroup: {
        plateColorObj: {},
        plateColorArr: [],
        obuStatusObj: {},
        obuStatusArr: []
      }
    }
  },
  computed: {
    listColoumns () {
      return listColoumns(this)
    },
    formConfig () {
      return listForm(this)
    },
    rules () {
      return formRules(this)
    }
  },
  methods: {
    add () {
      this.$openPage(
        '@/views/workordermanage/obuWhtieList/components/form-layer',
        '添加蓝牙OBU',
        {
          formData: {},
          callBack: (res, lid) => {
            this.addSubmit(res, lid)
          }
        },
        {
          area: ['60%', '500px']
        }
      )
    },

    async addSubmit (params, lid) {
      try {
        const res = await addObuWhite(params)
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.$layer.close(lid)
          this.getTableData()
        } else {
          this.$confirm(`添加失败\n失败原因：${res.message || '添加失败'}`, '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'error'
          })
        }
      } catch (error) {
        console.error(error)
      }
    },
    // 批量删除
    batchDelete () {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let params = {
        ids: this.selectArr.map(item => item.id)
      }
      this.handleDelete(params)
      // console.log(params)
    },
    async handleDelete (params) {
      this.$confirm('确定删除OBU白名单吗？', '提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        type: 'warning'
      }).then(async () => {
        let res = await deleteObuWhite(params)
        if (res.code === 200) {
          this.$message.success('删除成功')
          this.getTableData()
        } else {
          this.$message.error('删除失败')
        }
      })
    },
    exportHandle () {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = this.transformData(query, queryExportConfig)
      let fileObj = {
        fileName: 'OBU白名单数据.xlsx'
      }
      this.exportFile(params, whiteListExport, fileObj)
    },
    // 格式转换
    transformData (data, config) {
      const transformedData = {}
      for (const key in config) {
        if (data.hasOwnProperty(key) && data[key]) {
          const newKey = config[key]
          const value = data[key]
          transformedData[newKey] = value
        }
      }
      return transformedData
    },
  },
  created () {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.obu-page {
  height: 100%;
  position: relative;
  flex-flow: column;
  display: flex;

  .top-btn {
    margin-bottom: 20px;
  }
}
</style>
