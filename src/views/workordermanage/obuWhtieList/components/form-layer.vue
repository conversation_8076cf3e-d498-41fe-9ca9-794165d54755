<template>
  <div class="form-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px" size="small">
      <!-- 查询区域 -->
      <div class="search-section">
        <el-row :gutter="24">
          <el-col :span="7">
            <el-form-item label="车牌号" prop="carNo">
              <el-input v-model="form.carNo" placeholder="请输入车牌号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="车牌颜色" prop="carColor">
              <el-select v-model="form.carColor" class="w100-select" placeholder="请选择车牌颜色">
                <el-option class="w100-select" v-for="item in licenseColorOption" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="OBU号" prop="obuNo">
              <el-input v-model="form.obuNo" placeholder="请输入OBU号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item class="custom-form-item">
              <el-button type="primary" @click="handleQuery" size="small">查询</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息区域 -->
      <div class="detail-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="label">车牌号：</span>
              <span class="value">{{ detailInfo.carNo || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="label">车牌颜色：</span>
              <span class="value">{{ getVehicleColor(detailInfo.carColor) || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="label">OBU号：</span>
              <span class="value">{{ detailInfo.contractNo || '-' }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <span class="label">OBU品牌：</span>
              <span class="value">{{ detailInfo.vendorMastId  || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <span class="label">OBU发行时间：</span>
              <span class="value">{{ detailInfo.releaseDate2 || '-' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 备注区域 -->
      <div class="remark-section">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :rows="3" :maxlength="500" placeholder="请输入备注"></el-input>
        </el-form-item>
      </div>
    </el-form>

    <div class="form-footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="loading">
        确认添加
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  licenseColorOption,
} from '@/common/const/optionsData.js'
import layerMix from '@/utils/layerMixins'
import { queryObu } from '@/api/workordermanage'
import { getVehicleColor } from '@/common/method/formatOptions'

export default {
  mixins: [layerMix],
  name: 'FormLayer',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    callBack: {
      type: Function,
      default: () => { }
    }
  },
  data () {
    return {
      loading: false,
      form: {
        carNo: '',
        carColor: '',
        obuNo: '',
        remark: ''
      },
      detailInfo: {
        carNo: '',
        plateColorName: '',
        obuNo: '',
        obuBrand: '',
        obuIssueTime: ''
      },
      rules: {
        carNo: [
          { 
            validator: (rule, value, callback) => {
              if (!value && !this.form.obuNo) {
                callback(new Error('请输入车牌号或OBU号'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        carColor: [
          { 
            validator: (rule, value, callback) => {
              if (this.form.carNo && !value) {
                callback(new Error('输入车牌号时必须选择车牌颜色'))
              } else {
                callback()
              }
            },
            trigger: 'change'
          }
        ],
        obuNo: [
          {
            validator: (rule, value, callback) => {
              if (!value && !this.form.carNo) {
                callback(new Error('请输入OBU号或车牌号'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      licenseColorOption
    }
  },
  methods: {
    getVehicleColor,
    async handleQuery () {
      try {
        // 修改验证逻辑
        if (this.form.carNo) {
          // 如果输入了车牌号，则同时验证车牌号和车牌颜色
          await this.$refs.form.validate(['carNo', 'carColor'])
        } else {
          // 否则只验证 OBU 号
          await this.$refs.form.validate(['obuNo'])
        }
        
        // 这里添加查询逻辑
        const result = await queryObu(this.form)
        if (result.code === 200) {
          this.detailInfo = result.data
          if (!this.detailInfo.obuMastId) {
            this.$message.error('查询的OBU信息不存在')
          }
        } else {
          this.$message.error('查询失败：' + result.message)
        }
      } catch (error) {
        if (error.message) {
          this.$message.error('查询失败：' + error.message)
        }
      }
    },
    handleSubmit () {
      // 检查是否已查询到OBU信息
      if (!this.detailInfo.obuMastId) {
        this.$message.error('请先查询OBU信息或者查询的OBU信息不存在')
        return
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          let params = { 
            obuMastId: this.detailInfo.obuMastId,
            remark: this.form.remark
          }
          // this.$emit('submit', this.formData)
          this.getParam('callBack')(params, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    cancel () {
      this.closeDialog()
    },
    async initDict () {
      // 获取字典数据
      // this.licenseColorOption = ...
    }
  },
  created () {
    this.initDict()
    // 如果是编辑，需要填充表单数据
    if (this.formData) {
      this.form = { ...this.formData }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: 20px;

  .search-section {
    padding: 20px;
    border-bottom: 1px solid #EBEEF5;
  }

  .detail-section {
    padding: 20px;
    border-bottom: 1px solid #EBEEF5;

    .detail-item {
      margin-bottom: 15px;

      .label {
        color: #606266;
        margin-right: 8px;
      }

      .value {
        color: #303133;
      }
    }
  }

  .remark-section {
    padding: 20px;
  }

  .form-footer {
    text-align: center;
    margin-top: 20px;
  }

  :deep(.custom-form-item) {
    .el-form-item__content {
      margin-left: 0 !important;
    }
  }

}
</style>

<style lang="scss">
.w100-select {
  width: 170px;
}
</style>