import {
  licenseColorOption,
} from '@/common/const/optionsData.js'
import { getVehicleColor } from '@/common/method/formatOptions'
// 表格列配置
export const listColoumns = (_this) => {
  return [
    {
      prop: 'carNo',
      label: '车牌号',
    },
    {
      prop: 'carColor',
      label: '车牌颜色',
      formatter: (row) => {
        return getVehicleColor(row)
      }
    },
    {
      prop: 'contractNo',
      label: 'OBU号',
    },
    {
      prop: 'vendorMastId',
      label: 'OBU品牌',
    },
    {
      prop: 'releaseDate2',
      label: 'OBU发行时间',
    },
    {
      prop: 'obuStatus',
      label: 'OBU状态',
    },
    {
      prop: 'remark',
      label: '备注',
    },
    {
      prop: 'createdTime',
      label: '添加时间',
    },
    {
      prop: 'createdBy',
      label: '添加人员',
    },
    // {
    //   prop: 'action',
    //   width: 120,
    //   label: '操作'
    // }
  ]
}

// 搜索表单配置
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'carNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'carColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOption,
    },
    {
      type: 'input',
      field: 'obuNo',
      label: 'OBU号：',
      default: '',
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['startTime', 'endTime'],
      label: '添加时间：',
      format:'YYYY-MM-DD HH:mm:ss',
      // defaultTime:['00:00:00', '00:00:00'],
      default: []
    }
  ]
}

// 添加表单验证规则
export const formRules = (_this) => ({
  carColor: [
    {
      validator: (rule, value, callback) => {
        // 从验证规则的父组件（search-form）中获取表单数据
        const carNo = _this.$refs.SearchForm.search.carNo
        if (carNo && carNo.trim() !== '') {
          // 如果输入了车牌号，检查是否选择了车牌颜色
          if (!value) {
            callback(new Error('请选择车牌颜色'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: ['blur', 'change', 'submit']
    }
  ]
})

// 导出字段映射配置
export const queryExportConfig = {
  carNo: 'carNo',
  carColor: 'carColor',
  obuNo: 'obuNo',
  createTime: 'CreateTime'
}