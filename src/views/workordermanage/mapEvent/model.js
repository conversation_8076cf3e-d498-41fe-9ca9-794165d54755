import {
  emergencyEventsQuery, constructionRoadQuery
} from '@/api/workordermanage'

const statusOptions = {
  '0': '刚上报',
  '1': '研判否',
  '2': '研判确认',
  '3': '处理中',
  '8': '处置完离场状态',
  '10': '处理完成',
}

// 表格列配置
export const listColoumns = (_this) => {
  return [
    {
      prop: 'emergencyId',
      label: '事件ID',
      width: 140
    },
    {
      prop: 'loadName',
      label: '路段名称',
      wordWrap: true,
      width: 160
    },
    {
      prop: 'directionTraffic',
      label: '通行方向',
      width: 150
    },
    {
      prop: 'description',
      label: '事件描述',
      // wordWrap: true,
      width: 300
    },
    {
      prop: 'reportTime',
      label: '上报时间',
      width: 170
    },
    {
      prop: 'eventStatus',
      label: '状态',
      width: 140,
      formatter: (row) => {
        return statusOptions[row] || row
      }
    },
    {
      prop: 'updatedTime',
      label: '更新时间(同步时间)',
      width: 170
    },
    {
      prop: 'handlingStartTime',
      label: '处理时间',
      width: 170
    },
    {
      prop: 'lon',
      label: '经度',
      width: 140
    },
    {
      prop: 'lat',
      label: '维度',
      width: 140
    }
  ]
}

// 搜索表单配置
export const listForm = (_this) => {
  console.log(_this, '_this')
  return [
    {
      type: 'select',
      field: 'eventType',
      label: '事件类型：',
      clearable: 1,
      options: [
        { label: '道路事件', value: '1' },
        { label: '涉路施工', value: '2' },
        // { label: '事件拥堵', value: '3' },
      ],
      default: '1',
      callBack: (val) => {
        _this.formVal = val
        _this.api = val == '1' ? emergencyEventsQuery : constructionRoadQuery
        _this.tableData = []
      }
    },
    {
      type: 'select',
      field: 'eventStatus',
      label: '处理状态：',
      isCollapse: true,
      options: [
        { label: '刚上报', value: 0 },
        { label: '研判否', value: 1 },
        { label: '研判确认', value: 2 },
        { label: '处理中', value: 3 },
        { label: '处置完离场状态', value: 8 },
        { label: '处理完成', value: 10 },
      ]
    },
    {
      type: 'dateRange',
      field: 'reportTime',
      label: '上报时间：',
      isCollapse: true,
      keys: ['startTime', 'endTime'],
      format: 'YYYY-MM-DD HH:mm:ss',
      default: []
    },
    {
      type: 'input',
      field: 'loadName',
      label: '路段名称：',
      isCollapse: true,
      placeholder: '单行输入'
    }
  ]
}

// 表格列配置
export const listColoumns2 = (_this) => {
  return [
    {
      prop: 'constructionId',
      label: '事件ID'
    },
    {
      prop: 'highspeedName',
      label: '路段名称',
      width: 200
    },
    {
      prop: 'startZh',
      label: '桩号',
      width: 180,
      formatter: (val, row) => {
        return row.startZh + ' - ' + row.endZh
      }
    },
    {
      prop: 'direction',
      label: '通行方向',
      width: 160
    },
    {
      prop: 'description',
      label: '施工描述',
      width: 300
    },
    {
      prop: 'startTime',
      label: '施工开始时间',
      width: 140
    },
    {
      prop: 'endTime',
      label: '施工结束时间',
      width: 140
    },
    {
      prop: 'updatedTime',
      label: '更新时间(同步时间)',
      width: 160
    },
    {
      prop: 'startLatitude',
      label: '经度',
      width: 130
    },
    {
      prop: 'startLongitude',
      label: '维度',
      width: 130
    }
  ]
}

// 导出配置
export const queryExportConfig = {
  eventType: 'eventType',
  status: 'status',
  startTime: 'startTime',
  endTime: 'endTime',
  roadName: 'roadName'
} 