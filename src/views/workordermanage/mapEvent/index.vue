<template>
  <div class="map-event" :class="{'collapse': formVal === '1'}">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      :btnSpan="16"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        @changeTableData="changeTableData"
      >
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import {
  emergencyEventsQuery,constructionRoadQuery
} from '@/api/workordermanage'
import { listColoumns, listForm, listColoumns2 } from './model'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      loading: false,
      api: emergencyEventsQuery,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageNo',
      timeField: ['reportTime','eventType'],
      formVal:'1'
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    },
    listColoumns() {
      return this.formVal === '1' ? listColoumns(this) : listColoumns2(this)
    }
  },
  methods: {
        // 重置的回调
    onReSetHandle (formData) {
      this.formVal = '1'
      this.currentFormData = formData
      this.api = emergencyEventsQuery
      this.tableData = []
      this.getTableData()
    },
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.map-event {
  height: 100%;
  position: relative;
  flex-flow: column;
  display: flex;
  
  .pagination {
    margin: 10px 0;
  }
}
.collapse{
    ::v-deep .search{
      .isCollapse{
        display: block;
      }
    }
  }
</style>
