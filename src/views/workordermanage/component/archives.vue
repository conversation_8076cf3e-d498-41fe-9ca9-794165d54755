<template>
  <div class="archives-viewer" v-viewer="viewerOptions">
    <template v-for="(item, index) in options">
      <div class="archives-item" :key="index" v-if="item['dataSource']">
        <div class="archives-item__img-area" :style="imgAreaStyle">
          <img
            :src="item['dataSource']"
            :dataSource="item['dataSource']"
            alt=""
            class="img"
          />
        </div>
        <div class="archives-item__text-area" v-if="item.title">
          <div class="archives-item__title">
            {{ item.title }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    viewerOptions: {
      type: Object,
      default() {
        return {
          toolbar: true,
          navbar: false,
          title: false,
          url: 'dataSource'
        }
      }
    },
    source: {},
    imgAreaStyle: {
      type: Object,
      default() {
        return {}
      }
    }
  },

  data() {
    return {
      options: []
    }
  },
  watch: {
    source(val) {
      if (val && val.length) {
        this.options = val
      }
    }
  },
  created() {
    if (this.source && this.source.length) {
      this.options = this.source
    }
  },
  components: {},

  computed: {},

  methods: {}
}
</script>
<style>
.archives-viewer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.archives-viewer .archives-item {
  margin-right: 20px;
  margin-bottom: 20px;
}
.archives-viewer .archives-item .archives-item__img-area {
  height: 180px;
  width: 240px;
}
.archives-viewer .archives-item .archives-item__img-area .img {
  display: block;
  width: 100%;
  height: 100%;
}
.archives-viewer .archives-item .archives-item__text-area {
  width: 100%;
  margin-top: 10px;
}
.archives-viewer
  .archives-item
  .archives-item__text-area
  .archives-item__title {
  width: 100%;
  text-align: center;
  font-size: 15px;
  font-weight: 600;
  color: #999;
}
</style>
