<template>
  <div class="data-one" v-loading.fullscreen.lock="loading">
    <div class="top">
      <div class="btn-wrap">
        <div class="dimension-buttons">
          <el-radio-group v-model="tabPosition" @change="getChartData">
            <el-radio-button label="0">今日</el-radio-button>
            <el-radio-button label="1">近一周</el-radio-button>
            <el-radio-button label="2">近一月</el-radio-button>
            <el-radio-button label="3">全部</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="count-wrapper">
        <div class="counter">
          <!-- 标签行 -->
          <div class="unit-labels">
            <div class="unit-label">百万</div>
            <div class="unit-label">十万</div>
            <div class="unit-label">万</div>
            <div class="unit-label">千</div>
          </div>

          <!-- 数字显示 -->
          <div class="digits">
            <div
              class="digit-box"
              v-for="(item, index) in charArray"
              :key="index"
            >
              {{ item }}
            </div>
          </div>
        </div>
        <div class="count-title">累计交易额</div>
      </div>
      <div class="card-wrapper">
        <card v-for="(item, idx) in list" :key="idx" :itemObj="item" />
      </div>
    </div>
  </div>
</template>

<script>
import card from '../components/static-card.vue'
export default {
  components: { card },
  data() {
    return {
      loading: false,
      tabPosition: '3',
      charArray: [],
      list: [
        {
          id: 1,
          icon: 'icon-dingdan',
          key: 'addCount',
          count: 0,
          unit: '笔',
          title: '新增订单',
        },
        {
          id: 2,
          icon: 'icon-yiwanchengdingdan',
          count: 0,
          key: 'endCount',
          unit: '笔',
          title: '已完结订单',
        },
      ],
    }
  },
  methods: {
    getChartData() {
      this.loading = true
      let params = {
        dateFlage: this.tabPosition,
      }
      this.$request({
        url: this.$interfaces.dataOne,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            let data = res.data
            let str = res.data.amount
            // 补零至7位
            const paddedStr = str.padStart(7, '0') // 输出 "0001048"
            const charArray = paddedStr.split('')
            this.charArray = charArray

            this.list = this.list.map((item) => {
              return {
                ...item,
                count: data[item.key],
              }
            })
          }
        })
        .catch((error) => {
          this.loading = false
        })
      // this.loading = true
      // try {
      //   let params = {
      //     type: this.tabPosition,
      //   }
      //   let { data } = await orderSpecific(params)
      //   this.barData = this.originalData.map((item) => {
      //     return [`${item.name}`, data[item.key]]
      //   })
      //   this.loading = false
      // } catch (err) {
      //   console.log(err)
      //   this.loading = false
      // }
    },
  },
  created() {
    this.getChartData()
  },
}
</script>

<style lang="scss" scoped>
.data-one {
  // background: #0a1632;
  // margin: 10px;
  // padding: 20px;
}
.btn-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px;
  ::v-deep .el-radio-button__inner {
    padding: 7px 12px;
    background: rgb(30, 34, 105);
    color: #ffffff;
    border-color: #fff;
    box-shadow: none;
  }
  ::v-deep .is-active {
    .el-radio-button__inner {
      background: rgb(56, 50, 156);
    }
  }
  .compare-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      padding: 7px 12px;
      border-radius: 3px;
      background: rgb(30, 34, 105);
      border: 1px solid rgba(0, 0, 0, 0.15);
      cursor: pointer;
      font-size: 12px;
      &.active {
        background: #fff;
      }
      &:first-child {
        border-right: 0;
        border-radius: 3px 0 0 3px;
      }
      &:last-child {
        border-radius: 0 3px 3px 0;
      }
    }
    div {
      margin-left: 10px;
      font-size: 14px;
      font-weight: 400;
      i {
        font-size: 14px;
      }
    }
  }
}

.content {
  display: flex;
  // background: rgb(15, 19, 77);
  background: rgb(15, 28, 55);
  border: 1px solid #51b3a9;
  padding: 20px 0;
  margin: 20px 20px 20px 0;
  border-radius: 6px;
}

.counter {
  padding: 20px;
  border-radius: 8px;
  font-family: Arial, sans-serif;
}
.count-title {
  font-size: 16px;
  color: #ffffff;
  text-align: center;
}

/* 标签行 */
.unit-labels {
  display: flex;
  gap: 12px;
  margin-bottom: 10px;
}
.unit-label {
  color: #ffffff;
  width: 60px;
  text-align: center;
  position: relative;
}
// .unit-label:nth-child(4)::after {
//   /* 千位标签后的逗号 */
//   content: ',';
//   position: absolute;
//   left: 62px; /* 精准定位 */
//   top: -2px; /* 垂直微调 */
//   font-size: 24px;
// }

/* 数字行 */
.digits {
  display: flex;
  gap: 12px;
}
.digit-box {
  width: 50px;
  height: 80px;
  background: rgb(8, 38, 111);
  border-radius: 8px;
  color: white;
  font-size: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.digit-box:nth-child(1)::after,
.digit-box:nth-child(4)::after {
  /* 第四个数字后的逗号 */
  content: ',';
  color: #ffffff;
  position: absolute;
  left: 50px; /* 精准偏移 */
  top: 20px; /* 垂直居中 */
  font-size: 50px;
}

.card-wrapper {
  flex: 1;
  margin-left: 20px;
  display: flex;
  align-items: center;
}
</style>