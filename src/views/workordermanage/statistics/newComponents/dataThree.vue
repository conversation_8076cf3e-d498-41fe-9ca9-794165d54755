<template>
  <div class="data-three" v-loading.fullscreen.lock="loading">
    <div class="section">
      <div class="chart">
        <div class="title-wrapper">
          <div class="title">
            <div class="top">业务数据统计</div>
          </div>
          <div class="time-picker">
            <el-date-picker
              v-model="businessDateRange"
              type="daterange"
              size="small"
              :value-format="'yyyy-MM-dd'"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>
        </div>
        <div class="chart-ref" ref="businessChart"></div>
      </div>
      <div class="chart">
        <div class="title-wrapper">
          <div class="title">
            <div class="top">订单状态数量统计</div>
          </div>
          <div class="time-picker">
            <el-date-picker
              v-model="statusDateRange"
              type="daterange"
              size="small"
              :value-format="'yyyy-MM-dd'"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>
        </div>
        <div class="chart-ref" ref="statusChart"></div>
      </div>
    </div>
    <div class="section">
      <div
        class="chart"
        style="
          width: 490px;
          background-color: #081c36;
          border: 1px solid #51b3a9;
          padding: 0;
        "
      >
        <div
          class="title-wrapper"
          style="
            padding: 0 20px;
            border-bottom: 1px solid #51b3a9;
            background-color: #092c47;
            padding: 10px 60px 10px 40px;
          "
        >
          <div class="title">
            <div class="top" style="font-size: 18px">累积订单量</div>
          </div>
          <div class="num">
            <span
              style="font-size: 20px; font-weight: bold; margin-right: 4px"
              >{{ num }}</span
            >
            <span style="font-size: 12px">笔</span>
          </div>
        </div>
        <el-table
          class="el-table"
          :data="tableData"
          :header-row-class-name="'custom-header'"
          :row-class-name="'custom-row'"
        >
          <el-table-column
            prop="name"
            align="center"
            label="业务类型"
            min-width="150px"
          />
          <el-table-column prop="now" align="center" label="未完结" />
          <el-table-column prop="end" align="center" label="已完结" />
          <el-table-column prop="total" align="center" label="总量" />
        </el-table>
      </div>
      <div class="chart">
        <div class="title-wrapper">
          <div class="title">
            <div class="top">业务增长趋势</div>
          </div>
          <div class="time-picker">
            <el-date-picker
              v-model="increaseDateRange"
              type="daterange"
              size="small"
              :value-format="'yyyy-MM-dd'"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>
        </div>
        <div class="chart-ref" ref="increaseChart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import businessMixins from '../mixins/businessMixins.js'
import statusMixins from '../mixins/statusMixins.js'
import increaseMixins from '../mixins/increaseMixins.js'
var _ = require('lodash')
export default {
  components: {},
  mixins: [businessMixins, statusMixins, increaseMixins],
  data() {
    return {
      loading: false,
      businessChart: null,
      statusChart: null,
      increaseChart: null,
      startDate: null,
      endDate: null,
      num: 0,
      tableData: [
        { name: '新办订单', type: 'apply', end: null, now: null, total: null },
        {
          name: '更换/补办',
          type: 'exchange',
          end: null,
          now: null,
          total: null,
        },
        { name: '注销申请', type: 'cancel', end: null, now: null, total: null },
        {
          name: '自助激活',
          type: 'activate',
          end: null,
          now: null,
          total: null,
        },
        { name: '抖音订单', type: 'douYin', end: null, now: null, total: null },
        { name: '其他售后', type: 'sale', end: null, now: null, total: null },
      ],
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (!maxDate && minDate) {
            // 标准化到当日0点
            this.startDate = new Date(minDate).setHours(0, 0, 0, 0)
          }
        },
        disabledDate: (time) => {
          const now = new Date().setHours(0, 0, 0, 0)
          const t = new Date(time).setHours(0, 0, 0, 0) // 标准化校验时间

          return (
            t > now ||
            (this.startDate &&
              (t < this.startDate - 6 * 8.64e7 ||
                t > this.startDate + 6 * 8.64e7))
          )
        },
      },
    }
  },
  methods: {
    getChartData() {
      this.$request({
        url: this.$interfaces.dataThreeCount,
        method: 'post',
      })
        .then((res) => {
          console.log('获取累积量========》》》》》》》》》》》》', res)
          // this.loading = false
          if (res.code == 200) {
            this.num = res.data.num
            this.changeData(res.data)
          }
        })
        .catch((error) => {
          // this.loading = false
        })
    },
    getDefaultDateRange() {
      const end = new Date()
      const start = new Date(end)
      start.setDate(start.getDate() - 6)
      return [
        start.toISOString().split('T')[0], // 格式化为yyyy-MM-dd
        end.toISOString().split('T')[0],
      ]
    },
    changeData(responseData) {
      // 映射处理器
      const mappedData = this.tableData.map((item) => {
        const typePrefix = item.type === 'douYin' ? 'douYinOrder' : item.type

        return {
          ...item,
          end:
            responseData[`${item.type}EndCount`] ??
            responseData[`${typePrefix}EndCount`], // 处理抖音字段差异
          now:
            responseData[`${item.type}AddCount`] ??
            responseData[`${typePrefix}AddCount`], // 处理抖音字段差异
          total: responseData[`${item.type}Count`],
        }
      })
      this.tableData = mappedData
      console.log('获取累积量========》》》》》》》》》》》》', this.tableData)
    },
  },
  created() {
    this.getChartData()
  },
}
</script>

<style lang="scss" scoped>
.data-three {
  // padding: 20px;
  margin-top: 10px;
  .section {
    display: flex;
    // justify-content: space-between;
    margin-bottom: 40px;
    .chart {
      // background: rgb(15, 28, 55);
      // margin: 10px 20px 0px 20px;
      // padding: 20px;
      // min-width: 0; // 防止flex压缩
      // width: 500px;
      // height: 206px;
      background: rgb(15, 28, 55);
      // background-color: #081c36;
      border: 1px solid #51b3a9;
      padding: 0;
      // margin: 30px 30px 30px 20px;
      // padding: 20px;
      min-width: 0; // 防止flex压缩

      &:first-child {
        margin-right: 50px;
      }
      .chart-ref {
        width: 490px;
        height: 290px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
      }
      .title-wrapper {
        display: flex;
        justify-content: space-between;
        height: 52px;
        padding: 10px 20px;
        .title {
          display: flex;
          flex-direction: column;
          justify-content: center;
          // text-align: center;
          color: #0ab0c7;
          font-size: 18px;
          font-weight: bold;
          .desc {
            margin-top: 10px;
            color: #ffffff;
            font-size: 14px;
          }
        }
        .time-picker {
          margin-bottom: 20px;
        }
        ::v-deep .el-date-editor--daterange.el-input__inner {
          width: 250px;
        }
      }
    }
  }
  ::v-deep .el-table {
    width: 450px;
    height: 280px;
    &::before {
      display: none !important;
    }
  }
  // 深度覆盖表头样式
  ::v-deep .custom-header {
    th {
      background: #081c36 !important; // 深蓝背景
      color: white !important; // 白色文字
      font-size: 18px;
      font-weight: bold;
      border-bottom: none !important; // 去除底部边框
    }
  }

  // 行样式
  ::v-deep .custom-row {
    td {
      background: #081c36 !important; // 白色背景
      color: #ffffff !important; // 黑色文字
      font-size: 16px;
      // border-bottom: 1px solid #f0f0f0 !important; // 浅灰分割线
      border: none !important;
      padding: 8px 0;
    }

    // &:hover td {
    //   background: #f5f7fa !important; // 悬停浅灰
    // }
  }

  // 全局去除表格外边框
  ::v-deep .el-table--border {
    border: none !important;
    // border-color: #094d7c;
    &::after {
      display: none !important;
    }
  }
  .num {
    color: #ffffff;
  }
}
</style>