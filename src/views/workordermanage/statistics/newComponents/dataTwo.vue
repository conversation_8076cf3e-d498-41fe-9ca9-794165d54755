<template>
  <div class="data-two">
    <div class="bg">
      <div class="num">
        <div class="title">当前待处理订单</div>
        <div>{{ num }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      num: 0,
    }
  },
  methods: {
    getChartData() {
      this.$request({
        url: this.$interfaces.dataTwo,
        method: 'post',
      })
        .then((res) => {
          // this.loading = false
          if (res.code == 200) {
            this.num = res.data.num
          }
        })
        .catch((error) => {
          // this.loading = false
        })
    },
  },
  created() {
    this.getChartData()
  },
}
</script>

<style lang="scss" scoped>
.data-two {
  background: rgb(15, 28, 55);
  // height: 206px;
  // width: 350px;
  width: 490px;
  height: 208px;
  border: 1px solid #51b3a9;
  // margin: 30px 30px 20px 30px;
  margin-bottom: 21px;
  margin-right: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  .bg {
    position: relative;
    width: 330px;
    height: 100px;
    background-image: url(data:image/png;base64,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);
    background-repeat: no-repeat;
  }
  .title {
    color: #0ab0c7;
    font-size: 24px;
    font-weight: bold;
  }
  .num {
    position: absolute;
    top: 20px;
    left: 120px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #ffffff;
    font-size: 26px;
  }
}
</style>