<template>
  <div class="data-four" v-loading.fullscreen.lock="loading">
    <div class="section">
      <div
        class="chart"
        style="background-color: #081c36; border: 1px solid #51b3a9; padding: 0"
      >
        <div class="title-wrapper" style="padding: 10px 20px">
          <div class="title">
            <div class="top">销售金额统计</div>
          </div>
          <div class="time-picker">
            <el-date-picker
              v-model="saleDateRange"
              type="daterange"
              size="small"
              :value-format="'yyyy-MM-dd'"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              :picker-options="pickerOptions"
            ></el-date-picker>
          </div>
        </div>
        <div
          class="chart-ref"
          ref="saleChart"
          style="width: 490px; height: 290px"
        ></div>
      </div>
      <div
        class="chart"
        style="background-color: #081c36; border: 1px solid #51b3a9; padding: 0"
      >
        <div
          class="title-wrapper"
          style="
            border-bottom: 1px solid #51b3a9;
            background-color: #092c47;
            padding: 10px 20px;
            height: 52px;
          "
        >
          <div class="title">
            <div class="top" style="font-size: 18px; color: #0ab0c7">
              业务地图展示
            </div>
          </div>
        </div>
        <div
          class="chart-ref"
          ref="mapChart"
          style="width: 490px; height: 290px"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import saleMixins from '../mixins/saleMixins.js'
import mapMixins from '../mixins/mapMixins.js'
var _ = require('lodash')
export default {
  components: {},
  mixins: [saleMixins, mapMixins],
  data() {
    return {
      loading: false,
      saleChart: null,
      mapChart: null,
      startDate: null,
      endDate: null,
      pickerOptions: {
        onPick: ({ maxDate, minDate }) => {
          if (!maxDate && minDate) {
            // 标准化到当日0点
            this.startDate = new Date(minDate).setHours(0, 0, 0, 0)
          }
        },
        disabledDate: (time) => {
          const now = new Date().setHours(0, 0, 0, 0)
          const t = new Date(time).setHours(0, 0, 0, 0) // 标准化校验时间

          return (
            t > now ||
            (this.startDate &&
              (t < this.startDate - 13 * 8.64e7 ||
                t > this.startDate + 13 * 8.64e7))
          )
        },
      },
    }
  },
  methods: {
    getDefaultDateRange() {
      const end = new Date()
      const start = new Date(end)
      start.setDate(start.getDate() - 13)
      return [
        start.toISOString().split('T')[0], // 格式化为yyyy-MM-dd
        end.toISOString().split('T')[0],
      ]
    },
  },
  created() {},
}
</script>

<style lang="scss" scoped>
.data-four {
  // padding: 20px;
  .section {
    display: flex;
    flex-direction: column;
    // justify-content: space-between;
    margin-bottom: 30px;
    .chart {
      // width: 500px;
      // height: 206px;
      background: rgb(15, 28, 55);
      // margin: 30px 30px 30px 20px;
      // padding: 20px;
      min-width: 0; // 防止flex压缩
      &:first-child {
        // margin-right: 100px;
        margin-bottom: 40px;
      }
      .chart-ref {
        width: 450px;
        height: 250px;
        // padding: 20px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 6px;
      }
      .title-wrapper {
        display: flex;
        justify-content: space-between;
        .title {
          display: flex;
          flex-direction: column;
          justify-content: center;
          // text-align: center;
          color: #0ab0c7;
          font-size: 18px;
          font-weight: bold;
          .desc {
            margin-top: 10px;
            color: #ffffff;
            font-size: 14px;
          }
        }
        .time-picker {
          // margin-bottom: 20px;
        }
        ::v-deep .el-date-editor--daterange.el-input__inner {
          width: 250px;
        }
      }
    }
  }
}
</style>