<template>
  <div>
    <div class="top-wrap">
      <Card v-for="(item,idx) in list" :key="idx" :itemObj="item" />
    </div>

    <div class="content-wrap">
      <el-card class="content-card">
        <barChart class="bar-chart-wrap"></barChart>
      </el-card>
      <el-card class="content-card">
        <spChart class="bar-chart-wrap"></spChart>
      </el-card>
    </div>
  </div>
</template>

<script>
import Card from './components/static-card.vue'
import barChart from './components/barChart.vue'
import spChart from './components/specific-barChart.vue'
import { collectOrder } from '@/api/workordermanage'

export default {
  components: {
    barChart,
    Card,
    spChart
  },
  data() {
    return {
      list: [
        {
          id: 1,
          icon: 'icon-dingdan',
          key: 'newlyOrder',
          count: 0,
          unit: '笔',
          title: '今日新增订单'
        },
        {
          id: 2,
          icon: 'icon-shouru',
          count: 0,
          key: 'orderRevenue',
          unit: '元',
          title: '线上订单收入'
        },
        {
          id: 3,
          icon: 'icon-daichulidingdan1',
          count: 0,
          key: 'orderPending',
          unit: '笔',
          title: '累计待处理订单'
        },
        {
          id: 4,
          icon: 'icon-yiwanchengdingdan',
          count: 0,
          key: 'orderHandled',
          unit: '笔',
          title: '今日已处理订单'
        }
      ]
    }
  },
  methods: {
    async getCollect() {
      let { data } = await collectOrder()
      this.list = this.list.map(item => {
        return {
          ...item,
          count: data[item.key]
        }
      })
    }
  },
  created() {
    this.getCollect()
  }
}
</script>

<style lang="scss" scoped>
.top-wrap {
  width: 100%;
  display: flex;
}
.content-wrap {
  display: flex;
  .content-card {
    flex: 1;
    margin-right: 30px;
  }
}
</style>