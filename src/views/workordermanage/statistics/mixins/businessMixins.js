import * as echarts from 'echarts'
export default {
  data() {
    return {
      businessDateRange: this.getDefaultDateRange(), // 初始化默认时间,
      businessOption: {
        backgroundColor: '#0F1C37', // 深蓝背景
        // grid: {
        //   left: '12%',
        //   right: '12%',
        //   bottom: '20%',
        //   containLabel: true,
        // },
        xAxis: {
          type: 'category',
          data: ['新办', '更换', '补办', '注销', '二次激活', '产品转换'],
          axisLine: {
            lineStyle: {
              color: 'rgb(17,204,255)', // 轴线颜色
            },
          },
          axisLabel: {
            color: '#fff', // 标签颜色
            fontSize: 14,
            rotate: 0, // 标签不旋转
            interval: 0, // 强制显示所有标签
            width: 80, // 标签容器宽度
            // rotate: 30, // 标签旋转防重叠（可选）
          },
          axisTick: {
            show: false, // 隐藏刻度线
          },
        },
        yAxis: {
          type: 'value',
          name: '单位：笔',
          nameTextStyle: { // 新增/修改此配置项
            color: '#fff',   // 文字颜色改为白色
            fontSize: 14,    // 字体大小（建议值）
          },
          max: 20,
          splitNumber: 5,
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#ffffff', // 刻度值颜色
            fontSize: 14,
          },
          splitLine: {
            // interval: 5,// 每5个单位显示一条分割线
            lineStyle: {
              color: 'rgba(255,255,255,0.1)', // 网格线颜色
            },
          },
        },
        series: [
          {
            label: { show: true }, // 必须显式声明
            data: [0, 0, 0, 0, 0, 0],
            type: 'bar',
            barWidth: 28, // 柱宽
            itemStyle: {
              // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              //   { offset: 0, color: 'rgb(17,204,255)' }, // 亮绿色渐变
              //   { offset: 1, color: '#0d8a4a' },
              // ]),
              color: 'rgb(17,204,255)',
            },
            label: {
              show: true,
              position: 'top',
              color: '#fff', // 数据标签颜色
              fontSize: 14,
            },
          },
        ],
      },
    }
  },
  watch: {
    businessDateRange: {
      handler(newVal) {
        console.log('businessDateRangenewVal', newVal)
        // console.log('this.debouncedBusinessGetData', this.debouncedBusinessGetData)
        if (!newVal) {
          this.businessDateRange = this.getDefaultDateRange()
        }
        if (newVal?.length === 2 && this.debouncedBusinessGetData) {
          this.debouncedBusinessGetData()
        }
      },
      deep: true, // 深度监听数组元素变化
      immediate: true, // 组件初始化时立即执行一次
    },
  },
  methods: {
    debouncedBusinessGetData: _.debounce(function () {
      this.getBusinessChart()
    }, 500), // 500ms内无新操作才触发,
    getBusinessChart() {
      console.log('业务数据统计1111========>>>', this.businessDateRange)
      this.loading = true
      let param = {
        staTime: this.businessDateRange[0],
        endTime: this.businessDateRange[1],
      }
      console.log('业务数据统计1111========>>>', param)
      this.$request({
        url: this.$interfaces.dataThreeBusiness,
        method: 'post',
        data: param,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            // this.num = res.data.num
            console.log('业务数据统计1111========>>>', res.data)
            let data = res.data
            // let dataArr = Object.keys(data).map((key) => data[key])
            let dataArr = [data.applyCount, data.replaceCount, data.reissueCount, data.cancelCount, data.activateCount, data.productCount]
            //计算max值
            let max = Math.max(...dataArr)
            console.log('dataArr', dataArr)
            // console.log('max', max)
            // 使用响应式更新
            if (max != 0) {
              this.$set(this.businessOption.yAxis, 'max', Math.ceil(max / 10) * 15)
            }
            this.$set(this.businessOption.series[0], 'data', dataArr)

            // console.log('businessOption', this.businessOption)
            // 重新渲染图表
            this.$nextTick(() => {
              this.businessChart.setOption(this.businessOption, true)
            })
          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
  },
  mounted() {
    console.log('businessChart 是否挂载:', typeof this.debouncedBusinessGetData) // 应为 "function"
    this.$nextTick(() => {
      this.businessChart = echarts.init(this.$refs.businessChart)
      window.addEventListener('resize', () => this.businessChart.resize())
      // this.getBusinessChart()
    })
  },
}