import * as echarts from 'echarts'
export default {
  data() {
    return {
      saleDateRange: this.getDefaultDateRange(), // 初始化默认时间,
      saleOption: {
        backgroundColor: '#0F1C37', // 深蓝背景
        legend: {
          data: ['自营小程序', '抖音平台'],
          // right: '10%',
          textStyle: { color: '#fff' }
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a0}: {c0}元<br/>{a1}: {c1}元'
        },
        grid: {
          top: '25%',
          bottom: '15%'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07', '01/08', '01/09', '01/10', '01/11', '01/12'],
          axisLine: { lineStyle: { color: '#19C163' } },
          axisLabel: {
            color: '#fff',
            interval: 0, // 强制显示所有标签
            rotate: 45   // 标签倾斜防重叠
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：元',
          max: 100,
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,0.1)',
              type: 'dotted'  // 虚线网格
            }
          },
          axisLabel: { color: '#fff' },
          nameTextStyle: { // 新增/修改此配置项
            color: '#fff',   // 文字颜色改为白色
            fontSize: 14,    // 字体大小（建议值）
          },
        },
        series: [
          {  // 自营小程序（绿色）
            name: '自营小程序',
            type: 'line',
            data: [80, 78, 85, 82, 88, 83, 90, 86, 92, 89, 95, 98],
            itemStyle: { color: 'rgb(97,159,135)' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 3 }
          },
          {  // 抖音平台（红色）
            name: '抖音平台',
            type: 'line',
            data: [50, 55, 52, 58, 60, 56, 62, 65, 68, 70, 75, 78],
            itemStyle: { color: '#FF0000' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 3 }
          }
        ]
      }
    }
  },
  watch: {
    saleDateRange: {
      handler(newVal) {
        // console.log('newVal', newVal)
        // console.log('this.debouncedSaleGetData', this.debouncedSaleGetData)
        if (newVal?.length === 2 && this.debouncedSaleGetData) {
          this.debouncedSaleGetData()
        }
      },
      deep: true, // 深度监听数组元素变化
      immediate: true, // 组件初始化时立即执行一次
    },
  },
  methods: {
    debouncedSaleGetData: _.debounce(function () {
      this.getSaleChart()
    }, 500), // 500ms内无新操作才触发,
    getSaleChart() {
      this.loading = true
      let param = {
        staTime: this.saleDateRange[0],
        endTime: this.saleDateRange[1],
      }
      this.$request({
        url: this.$interfaces.dataFour,
        method: 'post',
        data: param,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            console.log('销售统计图图========>>>>>>>>>', res.data)
            let data = res.data
            // 重新渲染图表
            let dateList = data.dateList
            let max = data.maxNum

            // 更新 X 轴配置
            this.$set(this.saleOption.xAxis, 'data', dateList);
            if (max != 0) {
              this.$set(this.saleOption.yAxis, 'max', Math.ceil(max / 10) * 15)
            }
            this.$set(this.saleOption.series[0], 'data', data.applyNum)
            this.$set(this.saleOption.series[1], 'data', data.douYinNum)
            // this.$set(this.saleOption.series[2], 'data', data.cancelList)
            // this.$set(this.saleOption.series[3], 'data', data.activateList)
            // this.$set(this.saleOption.series[4], 'data', data.saleList)
            this.$nextTick(() => {
              this.saleChart.setOption(this.saleOption, true)
            })

          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
  },
  mounted() {
    // console.log('debouncedSaleGetData4444444是否挂载:', typeof this.debouncedSaleGetData) // 应为 "function"
    this.$nextTick(() => {
      this.saleChart = echarts.init(this.$refs.saleChart)
      window.addEventListener('resize', () => this.saleChart.resize())
      // this.getSaleChart()
    })
  },
}