import * as echarts from 'echarts'
export default {
  data() {
    return {
      statusDateRange: this.getDefaultDateRange(), // 初始化默认时间
      statusOption: {
        backgroundColor: '#0F1C37', // 深蓝背景
        legend: {
          data: ['已完结订单', '未完结订单'],
          textStyle: { color: '#fff' }, // 图例文字颜色
          itemGap: 20 // 图例间距
        },
        tooltip: {
          trigger: 'axis',
          formatter: '{b}<br/>{a0}: {c0}笔<br/>{a1}: {c1}笔'
        },
        xAxis: {
          type: 'category',
          data: ['01/01', '01/02', '01/03', '01/04', '01/05'], // 日期轴
          axisLabel: { color: '#fff', fontSize: 12 },
          axisLine: {
            lineStyle: {
              color: 'rgb(17,204,255)', // 轴线颜色
            },
          },
          axisTick: { show: false } // 隐藏刻度线
        },
        yAxis: {
          type: 'value',
          name: '单位：笔',
          nameTextStyle: { // 新增/修改此配置项
            color: '#fff',   // 文字颜色改为白色
            fontSize: 14,    // 字体大小（建议值）
          },
          axisLine: {
            show: false,
          },
          max: 20,
          // splitNumber: 1,
          // interval: 5,  // 
          splitLine: { lineStyle: { color: 'rgba(255,255,255,0.1)' } },
          axisLabel: { color: '#fff' }
        },
        series: [
          { // 已完结订单
            label: {
              show: true,
              position: 'top',
              color: '#fff', // 数据标签颜色
              fontSize: 14,
            },
            name: '已完结订单',
            type: 'bar',
            barWidth: 18, // 柱子宽度
            itemStyle: { color: '#00FFFF' }, // 深灰色
            data: [40, 50, 65, 80, 70, 50, 33] // 数据趋势：逐步上升后回落
          },
          { // 未完结订单
            label: {
              show: true,
              position: 'top',
              color: '#fff', // 数据标签颜色
              fontSize: 14,
            },
            name: '未完结订单',
            type: 'bar',
            barWidth: 18,
            itemStyle: { color: '#FFD700' }, // 浅蓝色
            data: [50, 55, 60, 60, 45, 66, 99] // 数据趋势：稳定后轻微下降
          }
        ],
        // 双柱并排显示配置
        barGap: '10%',    // 系列间间距
        barCategoryGap: '30%' // 类目间间距
      }
    }
  },
  watch: {
    statusDateRange: {
      handler(newVal) {
        // console.log('日期范围变更:', newVal); // 检查是否触发
        if (!newVal) {
          this.statusDateRange = this.getDefaultDateRange()
        }
        if (newVal?.length === 2 && this.debouncedStatusGetData) {
          this.debouncedStatusGetData()
        }
      },
      deep: true, // 深度监听数组元素变化
      immediate: true, // 组件初始化时立即执行一次
    },
  },
  methods: {
    debouncedStatusGetData: _.debounce(function () {
      this.getStatusChart();
    }, 500, { leading: true }), // 500ms内无新操作才触发,
    // 计算默认时间范围（当前时间作为结束时间，前7天为开始时间）
    getStatusChart() {
      // console.log('订单状态数量统计')
      this.loading = true
      let param = {
        staTime: this.statusDateRange[0],
        endTime: this.statusDateRange[1],
      }
      this.$request({
        url: this.$interfaces.dataThreeStatus,
        method: 'post',
        data: param,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            // this.num = res.data.num
            console.log('业务数据统计status', res.data)
            let data = res.data
            // 重新渲染图表
            let dateList = data.date
            let max = data.maxNum
            let add = data.add
            let end = data.end
            // 更新 X 轴配置
            this.$set(this.statusOption.xAxis, 'data', dateList);
            if (max != 0) {
              this.$set(this.statusOption.yAxis, 'max', Math.ceil(max / 10) * 15)
            }
            this.$set(this.statusOption.series[0], 'data', end)
            this.$set(this.statusOption.series[1], 'data', add)

            this.$nextTick(() => {
              this.statusChart.setOption(this.statusOption, true)
            })
          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
  },
  mounted() {
    // console.log('debouncedStatusGetData 是否挂载:', typeof this.debouncedStatusGetData) // 应为 "function"
    this.$nextTick(() => {
      this.statusChart = echarts.init(this.$refs.statusChart)
      window.addEventListener('resize', () => this.statusChart.resize())
      // this.getStatusChart()
    })
  },
}