import * as echarts from 'echarts'
export default {
  data() {
    return {
      increaseDateRange: this.getDefaultDateRange(), // 初始化默认时间,
      increaseOption: {
        backgroundColor: '#0F1C37', // 深蓝背景
        tooltip: {  // 核心配置部分
          trigger: 'item', // 关键配置：基于数据项触发[1,6](@ref)
          formatter: function (params) {
            // 直接提取当前数据点的 X 值
            return params.value; // 仅显示 X 轴标签[1,6](@ref)
          },
          backgroundColor: 'rgba(15,28,55,0.9)',
          borderColor: '#19C163',
          textStyle: { color: '#fff' }
        },
        legend: {
          data: ['新办', '更换补办', '注销', '激活', '其他'],
          textStyle: { color: '#fff' }, // 白色图例文字
          itemGap: 20, // 图例间距
          top: 30 // 距离顶部间距
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['01/01', '01/02', '01/03', '01/04', '01/05', '01/06', '01/07'],
          axisLine: {
            lineStyle: {
              color: 'rgb(17,204,255)', // 轴线颜色
            },
          },
          axisLabel: {
            color: '#fff',
            rotate: 40 // 日期标签旋转40度防重叠
          }
        },
        yAxis: {
          type: 'value',
          max: 2500,
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,0.1)' } // 灰色网格线
          },
          axisLabel: {
            color: '#fff',
            fontSize: 12,
            formatter: function (value) {
              return value === 2500 ? '2500' : value; // 仅显示最大值标签
            }
          }
        },
        series: [
          { // 新办 - 浅蓝色线
            name: '新办',
            type: 'line',
            data: [],
            itemStyle: { color: 'rgb(97,159,135)' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 2 }
          },
          { // 更换补办 - 粉色线
            name: '更换补办',
            type: 'line',
            data: [],
            itemStyle: { color: '#FF69B4' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 2 }
          },
          { // 注销 - 黄色线
            name: '注销',
            type: 'line',
            data: [],
            itemStyle: { color: '#FFD700' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 2 }
          },
          { // 激活 - 青色线
            name: '激活',
            type: 'line',
            data: [],
            itemStyle: { color: '#00FFFF' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 2 }
          },
          { // 其他 - 红色线
            name: '其他',
            type: 'line',
            data: [],
            itemStyle: { color: '#FF0000' },
            symbol: 'circle',
            symbolSize: 8,
            lineStyle: { width: 2 }
          }
        ]
      }
    }
  },
  watch: {
    increaseDateRange: {
      handler(newVal) {
        console.log('increaseDateRangenewVal', newVal)
        // console.log('this.debouncedGetData', this.debouncedGetData)
        if (!newVal) {
          this.increaseDateRange = this.getDefaultDateRange()
        }
        if (newVal?.length === 2 && this.debouncedGetData) {
          this.debouncedGetData()
        }
      },
      deep: true, // 深度监听数组元素变化
      immediate: true, // 组件初始化时立即执行一次
    },
  },
  methods: {
    debouncedGetData: _.debounce(function () {
      this.getIncreaseChart()
    }, 500), // 500ms内无新操作才触发,
    getIncreaseChart() {
      this.loading = true
      let param = {
        staTime: this.increaseDateRange[0],
        endTime: this.increaseDateRange[1],
      }
      this.$request({
        url: this.$interfaces.dataThreeIncrease,
        method: 'post',
        data: param,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            console.log('业务增长趋势图===》》》', res.data)
            let data = res.data
            // 重新渲染图表
            let dateList = data.dateList
            let max = data.maxNum || 20
            // 更新 X 轴配置
            this.$set(this.increaseOption.xAxis, 'data', dateList);
            this.$set(this.increaseOption.yAxis, 'max', Math.ceil(max / 10) * 15)
            this.$set(this.increaseOption.series[0], 'data', data.applyList)
            this.$set(this.increaseOption.series[1], 'data', data.exchangeTableInfoDTOList)
            this.$set(this.increaseOption.series[2], 'data', data.cancelList)
            this.$set(this.increaseOption.series[3], 'data', data.activateList)
            this.$set(this.increaseOption.series[4], 'data', data.saleList)
            this.$nextTick(() => {
              this.increaseChart.setOption(this.increaseOption, true)
            })
          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
  },
  mounted() {
    console.log('increaseChart 是否挂载:', typeof this.debouncedGetData) // 应为 "function"
    this.$nextTick(() => {
      this.increaseChart = echarts.init(this.$refs.increaseChart)
      window.addEventListener('resize', () => this.increaseChart.resize())
      // this.getIncreaseChart()
    })
  },
}