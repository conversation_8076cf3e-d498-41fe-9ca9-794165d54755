import * as echarts from 'echarts'
import { color } from 'echarts/lib/export';
import guangxiJson from 'echarts/map/json/province/guangxi.json';
export default {
  data() {
    return {
      itemStyles: [{
        normal: { areaColor: '#EA808D' },
        emphasis: { areaColor: '#EA808D' }
      }, {
        normal: { areaColor: '#81D3F8' },
        emphasis: { areaColor: '#81D3F8' }
      }, {
        normal: { areaColor: '#CAF982' },
        emphasis: { areaColor: '#CAF982' }
      }],//默认地图前三配置颜色
      mapOption: {
        // 基础地图容器配置
        geo: {
          map: '广西',
          aspectScale: 0.85,// 地图长宽比例（默认0.75）
          itemStyle: {
            areaColor: '#2c3544', // 无数据区域颜色（深灰）
            borderColor: '#19C163', // 边界线颜色
            borderWidth: 1
          },
          emphasis: { // 悬停交互样式
            itemStyle: {
              areaColor: '#FFD700', // 悬停时金色高亮
              color: '#ffffff',
              borderWidth: 1
            }
          }
        },

        // 数据驱动层
        series: [{
          type: 'map',
          mapType: '广西',
          aspectScale: 0.85,// 地图长宽比例（默认0.75）
          data: [
            {
              name: '南宁市',
              value: 95,
              // 强制定义层级样式
              itemStyle: {
                normal: { areaColor: '#EA808D' }, // 常态红色
                emphasis: { areaColor: '#EA808D' } // 悬停深红
              }
            },
            {
              name: '柳州市', value: 80, itemStyle: {
                normal: { areaColor: '#81D3F8' }, // 常态蓝色
                emphasis: { areaColor: '#81D3F8' } // 悬停蓝色
              }
            },
            {
              name: '桂林市', value: 70, itemStyle: {
                normal: { areaColor: '#CAF982' }, // 常态绿色
                emphasis: { areaColor: '#CAF982' } // 悬停绿色
              }
            },
          ],
          // // 视觉映射配置
          // visualMap: {
          //   pieces: [ { min: 0, color: '#FF4757' } ]
          // },
          // 标签显示规则
          // label: {
          //   show: true,
          //   color: '#FFF',
          //   formatter: '{b}', // 仅显示区域名称
          //   textBorderColor: '#000',
          //   textBorderWidth: 2
          // },
          // 提示框配置
          // tooltip: {
          //   trigger: 'item',
          //   formatter: '{b}: {c}'
          // }
        }]
      }
    }
  },
  methods: {
    // debouncedMapGetData: _.debounce(function () {
    //   this.getMapChart()
    // }, 500), // 500ms内无新操作才触发,
    getMapChart() {
      this.loading = true
      this.$request({
        url: this.$interfaces.dataFive,
        method: 'post',
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            console.log('地图展示', res.data)
            let data = res.data
            const newArr = data.map(({ cityName: name, num: value }, index) => ({
              name,
              value,
              itemStyle: this.itemStyles[index % this.itemStyles.length] // 循环取颜色
            }));
            // console.log('地图数据数组', newArr);
            // 更新地图配置
            this.$set(this.mapOption.series[0], 'data', newArr)

            this.$nextTick(() => {
              this.mapChart.setOption(this.mapOption, true)
            })

          }
        })
        .catch((error) => {
          this.loading = false
        })
    },
  },
  mounted() {
    this.$nextTick(() => {
      // console.log('地图加载======================>>>>>>>>>>>>>')
      echarts.registerMap('广西', guangxiJson);
      this.mapChart = echarts.init(this.$refs.mapChart)
      window.addEventListener('resize', () => this.mapChart.resize())
      this.getMapChart()
    })
  },
}