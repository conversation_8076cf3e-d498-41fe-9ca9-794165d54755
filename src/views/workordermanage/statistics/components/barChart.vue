<template>
  <div class="page-wrap" v-loading="loading">
    <div class="title-warpper">
      <div class="title">{{title}}</div>
    </div>
    <div class="btn-wrap">
      <div class="dimension-buttons">
        <el-radio-group v-model="tabPosition" @change="generateChartTitle">
          <el-radio-button label="1">本周</el-radio-button>
          <el-radio-button label="2">本月</el-radio-button>
          <el-radio-button label="3">三月</el-radio-button>
        </el-radio-group>
      </div>
      <div class="compare-buttons">
        <span :class="{active:currentCompare == 'year'}" @click="changeCompare('year')">同比</span>
        <span :class="{active:currentCompare == 'ring'}" @click="changeCompare('ring')">环比</span>
        <div>
          {{percent}}
          <i class="el-icon-bottom" v-if="percent.includes('-')"></i>
          <i class="el-icon-top" v-else></i>
        </div>
      </div>
    </div>

    <ChartBar class="chart-bar" :options="barOptions" :chart-data="barData" />
  </div>
</template>

<script>
import ChartBar from '@/components/echart/bar.vue'
import { orderRing, orderYear, orderSummary } from '@/api/workordermanage'

let unitObj = {
  1: '日',
  2: '',
  3: '月'
}
export default {
  components: {
    ChartBar
  },
  data() {
    return {
      barData: [],
      tabPosition: '1',
      // originalData: [],
      loading: false,
      currentCompare: 'year',
      title: '业务量统计',
      percent: ''
    }
  },
  computed: {
    barOptions() {
      return {
        backgroundColor: 'white',
        //图表离容器的距离
        grid: {
          left: '7%',
          top: '10%',
          right: '7%',
          bottom: '7%'
        },
        xAxis: {
          type: 'category',
          // data: ['库里', '科比', '詹姆斯', '乔丹', '杜兰特', '欧文', '威少'],
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.45)'
          },
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.15)',
              width: 0
            }
          }
        },
        //y轴
        yAxis: [
          {
            type: 'value',
            // splitNumber: 10,
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.45)'
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 0, 0, 0.15)'
              }
            }
          }
        ],
        series: [
          {
            // data: [2, 1, 4, 6, 1, 0, 1],
            label: {
              show: true,
              position: 'top',
              distance: 5,
              color: '#0E6ED3',
              formatter: val => {
                // '{c}' + 'MVP'
                return val[1]
              }
            },
            //折线图line,饼图pie，散点图scatter等等
            type: 'bar',
            barWidth: this.tabPosition == 2 ? 15 : 20,
            itemStyle: {
              barBorderRadius: [20, 20, 0, 0],
              color: {
                x: 0, //右
                y: 0, //下
                x2: 0, //左
                y2: 1, //上
                colorStops: [
                  {
                    offset: 0.1,
                    color: '#0E6ED3' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#409EFF' // 100% 处的颜色
                  }
                ]
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    changeCompare(compare) {
      this.currentCompare = compare
      this.getRate()
    },
    generateChartTitle() {
      let titleConfig = {
        1: '业务量统计',
        2: '本月拼团趋势',
        3: '近三个月拼团趋势'
      }
      this.title = titleConfig[this.tabPosition]
      this.getChartData()
      this.getRate()
    },
    async getChartData() {
      this.loading = true
      try {
        let params = {
          type: this.tabPosition
        }
        let unit = unitObj[this.tabPosition]
        let { data } = await orderSummary(params)
        this.barData = data.map(item => {
          return [`${item.statDate}${unit}`, item.statNum]
        })
        this.loading = false
        // console.log(data, 666)
      } catch (err) {
        this.loading = false
      }
    },
    async getRate() {
      if (this.currentCompare == 'year' && this.tabPosition == 1) {
        //环比没有周
        this.percent = '0%'
        return
      }
      let fn = {
        ring: orderRing,
        year: orderYear
      }
      let params = {
        type: this.tabPosition
      }
      let { data } = await fn[this.currentCompare](params)
      this.percent = data
    }
  },
  created() {
    this.getChartData()
    this.getRate()
  }
}
</script>

<style lang="scss" scoped>
.page-wrap {
  width: 100%;
  height: 100%;
  .chart-bar {
    width: 100%;
    height: 470px;
  }
}
.btn-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  ::v-deep .el-radio-button__inner {
    padding: 7px 12px;
  }
  .compare-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      padding: 7px 12px;
      border-radius: 3px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      cursor: pointer;
      font-size: 12px;
      &.active {
        background: #f0f2f5;
      }
      &:first-child {
        border-right: 0;
        border-radius: 3px 0 0 3px;
      }
      &:last-child {
        border-radius: 0 3px 3px 0;
      }
    }
    div {
      margin-left: 10px;
      font-size: 14px;
      font-weight: 400;
      i {
        font-size: 14px;
      }
    }
  }
}
.title-warpper {
  margin: 10px 0;
  .title {
    font-size: 16px;
    border-left: 4px solid #09aff7;
    padding-left: 5px;
  }
}
</style>