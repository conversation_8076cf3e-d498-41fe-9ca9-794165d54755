<template>
  <div class="page-wrap" v-loading="loading">
    <div class="title-warpper">
      <div class="title">{{title}}</div>
    </div>
    <div class="btn-wrap">
      <div class="dimension-buttons">
        <el-radio-group v-model="tabPosition" @change="generateChartTitle">
          <el-radio-button label="1">本周</el-radio-button>
          <el-radio-button label="2">本月</el-radio-button>
          <el-radio-button label="3">三月</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <ChartBar class="chart-bar" :options="barOptions" :chart-data="barData" />
  </div>
</template>

<script>
import ChartBar from '@/components/echart/bar.vue'
import { orderSpecific } from '@/api/workordermanage'

export default {
  components: {
    ChartBar
  },
  data() {
    return {
      barData: [],
      tabPosition: '1',
      loading: false,
      originalData: [
        {
          key: 'release',
          name: '线上发行'
        },
        {
          key: 'tiktok',
          name: '电商平台发售'
        },
        {
          key: 'replace',
          name: '设备更换'
        },
        {
          key: 'reissue',
          name: '设备补办'
        },
        {
          key: 'logout',
          name: '注销'
        },
        {
          key: 'activate',
          name: '设备激活'
        }
      ],
      title: '具体业务量统计'
    }
  },
  computed: {
    barOptions() {
      return {
        backgroundColor: 'white',
        //图表离容器的距离
        grid: {
          left: '7%',
          top: '10%',
          right: '7%',
          bottom: '7%'
        },
        xAxis: {
          type: 'category',
          // data: [],
          axisLabel: {
            color: 'rgba(0, 0, 0, 0.45)'
          },
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(0, 0, 0, 0.15)',
              width: 0
            }
          }
        },
        //y轴
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.45)'
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0, 0, 0, 0.15)'
              }
            }
          }
        ],
        series: [
          {
            // data: [],
            label: {
              show: true,
              position: 'top',
              distance: 5,
              color: '#0E6ED3',
              formatter: val => {
                return val[1]
              }
            },
            //折线图line,饼图pie，散点图scatter等等
            type: 'bar',
            barWidth: 20,
            itemStyle: {
              barBorderRadius: [20, 20, 0, 0],
              color: {
                x: 0, //右
                y: 0, //下
                x2: 0, //左
                y2: 1, //上
                colorStops: [
                  {
                    offset: 0.1,
                    color: '#0E6ED3' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#409EFF' // 100% 处的颜色
                  }
                ]
              }
            }
          }
        ]
      }
    }
  },
  methods: {
    generateChartTitle() {
      let titleConfig = {
        1: '具体业务量统计',
        2: '本月业务量统计',
        3: '近三个月业务量统计'
      }
      this.title = titleConfig[this.tabPosition]
      this.getChartData()
    },
    async getChartData() {
      this.loading = true
      try {
        let params = {
          type: this.tabPosition
        }
        let { data } = await orderSpecific(params)
        this.barData = this.originalData.map(item => {
          return [`${item.name}`, data[item.key]]
        })
        this.loading = false
      } catch (err) {
        console.log(err)
        this.loading = false
      }
    }
  },
  created() {
    this.getChartData()
  }
}
</script>

<style lang="scss" scoped>
.page-wrap {
  width: 100%;
  height: 100%;
  .chart-bar {
    width: 100%;
    height: 470px;
  }
}
.btn-wrap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
  ::v-deep .el-radio-button__inner {
    padding: 7px 12px;
  }
  .compare-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      padding: 7px 12px;
      border-radius: 3px;
      border: 1px solid rgba(0, 0, 0, 0.15);
      cursor: pointer;
      font-size: 12px;
      &.active {
        background: #fff;
      }
      &:first-child {
        border-right: 0;
        border-radius: 3px 0 0 3px;
      }
      &:last-child {
        border-radius: 0 3px 3px 0;
      }
    }
    div {
      margin-left: 10px;
      font-size: 14px;
      font-weight: 400;
      i {
        font-size: 14px;
      }
    }
  }
}
.title-warpper {
  margin: 10px 0;
  .title {
    font-size: 16px;
    border-left: 4px solid #09aff7;
    padding-left: 5px;
  }
}
</style>