<template>
  <div class="box-card" :body-style="{ padding: '20px' }">
    <div class="card-wrap">
      <div class="img-box">
        <!-- <img src="@/image/bg-left.png" /> -->
        <i class="icon iconfont" :class="itemObj.icon"></i>
      </div>
      <div class="card-content">
        <div class="count">
          {{ itemObj.count }}
          <i>{{ itemObj.unit }}</i>
        </div>
        <div class="card-title">{{ itemObj.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    itemObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      count: 1234,
      unit: '笔',
      title: '今日新增订单',
    }
  },
}
</script>

<style lang="scss" scoped>
.box-card {
  // width: 320px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 10px 0;
  margin-right: 32px;
  background: transparent;
  border: 2px solid rgb(30, 38, 98);
  border-radius: 12px;
  padding: 10px 20px;
  .card-wrap {
    display: flex;
    align-items: center;
    .img-box {
      width: 80px;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
      }
      i {
        font-size: 60px;
        color: #409eff;
      }
    }
  }
  .card-content {
    margin-left: 10px;
    .count {
      width: 120px;
      font-size: 26px;
      margin-bottom: 8px;
      color: #ffffff;
      i {
        font-style: inherit;
        font-size: 18px;
      }
    }
    .card-title {
      width: 120px;
      color: rgb(64, 158, 255);
      font-weight: 500;
    }
  }
}
</style>