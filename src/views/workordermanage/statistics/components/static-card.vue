<template>
  <el-card class="box-card" :body-style="{ padding: '20px' }">
    <div class="card-wrap">
      <div class="img-box">
        <!-- <img src="@/image/bg-left.png" /> -->
        <i class="icon iconfont" :class="itemObj.icon"></i>
      </div>
      <div class="card-content">
        <div class="count">
          {{itemObj.count}}
          <i>{{itemObj.unit}}</i>
        </div>
        <div class="card-title">{{itemObj.title}}</div>
      </div>
    </div>
  </el-card>
</template>

<script>
export default {
  props: {
    itemObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      count: 1234,
      unit: '笔',
      title: '今日新增订单'
    }
  }
}
</script>

<style lang="scss" scoped>
.box-card {
  // width: 320px;
  flex: 1;
  margin: 10px 0;
  margin-right: 32px;
  .card-wrap {
    display: flex;
    align-items: center;
    .img-box {
      width: 80px;
      height: 80px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 100%;
        height: 100%;
      }
      i {
        font-size: 60px;
        color: #409eff;
      }
    }
  }
  .card-content {
    margin-left: 20px;
    .count {
      font-size: 32px;
      margin-bottom: 8px;
      i {
        font-style: inherit;
        font-size: 18px;
      }
    }
    .card-title {
      color: rgb(64, 158, 255);
      font-weight: 500;
    }
  }
}
</style>