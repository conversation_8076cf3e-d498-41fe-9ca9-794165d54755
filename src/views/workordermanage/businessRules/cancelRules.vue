<template>
  <div class="business-container">
    <!-- 业务办理须知 （免费注销）-->
    <div class="g-flex item-content">
      <div class="label">
        业务办理须知：
        <br />（免费注销）
      </div>
      <div class="g-flex g-flex-column">
        <quillEditor :notice="formData.freeInstructions" @editorChange="freeEditorChange"></quillEditor>
      </div>
    </div>

    <!-- 业务办理须知 （收费注销）-->
    <div class="g-flex item-content">
      <div class="label">
        业务办理须知：
        <br />（收费注销）
      </div>
      <div class="g-flex g-flex-column">
        <quillEditor :notice="formData.feesInstructions" @editorChange="editorChange"></quillEditor>
      </div>
    </div>

    <div class="g-flex item-content">
      <div class="label g-flex">支持的绑定渠道:</div>
      <div class="g-flex g-flex-column" style="margin-left:50px">
        <!-- 分对分银行 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="divisionListIndeterminate"
            v-model="divisionCheckAll"
            @change="divisionCheckAllChange"
            :disabled="!formData.productConfig.supportPostpaidBindingCard"
          >
            <span class="checkbox-label">分对分银行</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="divisionChecked" @change="divisionCheckItemChange">
              <el-checkbox
                v-for="(item,index) in divisionType"
                :label="item.label"
                :key="index"
                :disabled="!formData.productConfig.supportPostpaidBindingCard"
              >{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- 总对总银行 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="totalListIndeterminate"
            v-model="totalCheckAll"
            @change="totalCheckAllChange"
            :disabled="!formData.productConfig.supportPostpaidBindingCard"
          >
            <span class="checkbox-label">总对总银行</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="totalChecked" @change="totalCheckItemChange">
              <el-checkbox
                v-for="(item,index) in totalType"
                :label="item.label"
                :key="index"
                :disabled="!formData.productConfig.supportPostpaidBindingCard"
              >{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 其他合作渠道 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="otherListIndeterminate"
            v-model="otherCheckAll"
            @change="otherCheckAllChange"
            :disabled="!formData.productConfig.supportPostpaidBindingCard"
          >
            <span class="checkbox-label">其他合作渠道</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="otherChecked" @change="otherCheckItemChange">
              <el-checkbox
                v-for="(item,index) in otherType"
                :label="item.label"
                :key="index"
                :disabled="!formData.productConfig.supportPostpaidBindingCard"
              >{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>
    <div class="g-flex g-flex-horizontal-vertical">
      <el-button type="primary" @click="updateBusinessRules">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  bindChannelDivision,
  bindChannelTotal,
  bindChannelOther
} from '@/common/const/optionsData.js'
import quillEditor from './components/quillEditor'
import { cancelConfigDetail, postCancelConfig } from '@/api/workordermanage'

export default {
  components: { quillEditor },
  data() {
    return {
      formData: {
        feesInstructions: '',
        freeInstructions: '',
        productConfig: {
          supportPostpaidBindingCard: true // 是否支持银行
        }
      },
      //分对分
      divisionListIndeterminate: null,
      divisionCheckAll: '',
      divisionChecked: [],
      divisionType: bindChannelDivision,

      //总对总
      totalListIndeterminate: null,
      totalCheckAll: '',
      totalChecked: [],
      totalType: bindChannelTotal,

      //其他渠道
      otherListIndeterminate: null,
      otherCheckAll: '',
      otherChecked: [],
      otherType: bindChannelOther
    }
  },
  methods: {
    freeEditorChange(val) {
      this.formData.freeInstructions = val.content
    },
    editorChange(val) {
      this.formData.feesInstructions = val.content
    },
    async updateBusinessRules() {
      let params = JSON.parse(JSON.stringify(this.formData))
      if (!params.productConfig.supportPostpaidBindingCard) {
        params.bindingChannels = {
          bisectionBankConfig: {
            supportABC: false,
            supportGBGB: false,
            supportBOC: false,
            supportCCB: false,
            supportCEB: false,
            supportCOMM: false,
            supportGXNX: false,
            supportHXB: false,
            supportICBC: false,
            supportLZCCB: false,
            supportNAB: false,
            supportPSBC: false
          },
          totalBankConfig: {
            supportABC: false,
            supportBOC: false,
            supportCCB: false,
            supportCMB: false,
            supportCOMM: false,
            supportHXB: false,
            supportICBC: false,
            supportINDUSTRIAL: false,
            supportPSBC: false,
            supportSPD: false
          },
          partnerChannelConfig: {
            supportALIPAY: false,
            supportZSINFO: false,
           supportFMGS: false,
            supportHCB: false,
            supportHLY: false,
            supportSDXL: false,
            supportWXGP: false,
            supportJIET: false
          }
        }
      }
      console.log(params, 'params')
      this.startLoading()
      let res = await postCancelConfig(params)
      if (res.code == 200) {
        this.endLoading()
        this.$message.success('更新成功！')
        this.getDetail()
      } else {
        this.endLoading()
        this.$message.error(res.msg)
      }
    },
    //分对分
    divisionCheckAllChange(val) {
      this.divisionChecked = val ? this.divisionType : []
      this.divisionChecked = this.divisionChecked.map(item => {
        return item.label
      })
      this.divisionListIndeterminate = false
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },
    divisionCheckItemChange(value) {
      let checkedCount = value.length
      this.divisionCheckAll = checkedCount === this.divisionType.length
      this.divisionListIndeterminate =
        checkedCount > 0 && checkedCount < this.divisionType.length
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },
    //总对总
    totalCheckAllChange(val) {
      this.totalChecked = val ? this.totalType : []
      this.totalChecked = this.totalChecked.map(item => {
        return item.label
      })
      this.totalListIndeterminate = false
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },
    totalCheckItemChange(value) {
      let checkedCount = value.length
      this.totalCheckAll = checkedCount === this.totalType.length
      this.totalListIndeterminate =
        checkedCount > 0 && checkedCount < this.totalType.length
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },
    //其他渠道
    otherCheckAllChange(val) {
      this.otherChecked = val ? this.otherType : []
      this.otherChecked = this.otherChecked.map(item => {
        return item.label
      })
      this.otherListIndeterminate = false
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    otherCheckItemChange(value) {
      let checkedCount = value.length
      this.otherCheckAll = checkedCount === this.otherType.length
      this.otherListIndeterminate =
        checkedCount > 0 && checkedCount < this.otherType.length
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    //通过选中数组中的label匹配出对象中的value值
    formatData(obj, selected) {
      let arr = []
      for (let i = 0; i < selected.length; i++) {
        for (let j = 0; j < obj.length; j++) {
          if (selected[i] == obj[j].label) {
            arr.push(obj[j].field)
          }
        }
      }
      return arr
    },
    matchData(selected, type) {
      console.log(type)
      //分对分
      if (type == 'division') {
        for (let key in this.formData.bindingChannels.bisectionBankConfig) {
          this.formData.bindingChannels.bisectionBankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannels.bisectionBankConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannels.bisectionBankConfig[key] = true
            }
          }
        }
      }
      //总对总
      if (type == 'total') {
        for (let key in this.formData.bindingChannels.totalBankConfig) {
          this.formData.bindingChannels.totalBankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannels.totalBankConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannels.totalBankConfig[key] = true
            }
          }
        }
      }
      //其他渠道
      if (type == 'other') {
        for (let key in this.formData.bindingChannels.partnerChannelConfig) {
          this.formData.bindingChannels.partnerChannelConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannels.partnerChannelConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannels.partnerChannelConfig[key] = true
            }
          }
        }
      }
    },
    //匹配详情查询出的数据
    matchDetailData() {
      //分对分
      for (let key in this.formData.bindingChannels.bisectionBankConfig) {
        for (let i = 0; i < this.divisionType.length; i++) {
          if (
            this.formData.bindingChannels.bisectionBankConfig[key] &&
            key == this.divisionType[i].field
          ) {
            this.divisionChecked.push(this.divisionType[i].label)
          }
        }
      }
      this.divisionCheckItemChange(this.divisionChecked)

      //总对总
      for (let key in this.formData.bindingChannels.totalBankConfig) {
        for (let i = 0; i < this.totalType.length; i++) {
          if (
            this.formData.bindingChannels.totalBankConfig[key] &&
            key == this.totalType[i].field
          ) {
            this.totalChecked.push(this.totalType[i].label)
          }
        }
      }
      this.totalCheckItemChange(this.totalChecked)

      //其他
      for (let key in this.formData.bindingChannels.partnerChannelConfig) {
        for (let i = 0; i < this.otherType.length; i++) {
          if (
            this.formData.bindingChannels.partnerChannelConfig[key] &&
            key == this.otherType[i].field
          ) {
            this.otherChecked.push(this.otherType[i].label)
          }
        }
      }
      this.otherCheckItemChange(this.otherChecked)
    },
    async getDetail() {
      this.startLoading()
      let res = await cancelConfigDetail()
      if (res.code == 200) {
        this.formData = {
          ...res.data,
          productConfig: {
            supportPostpaidBindingCard: true // 是否支持银行
          }
        }
        this.totalChecked = []
        this.otherChecked = []
        this.divisionChecked = []
        console.log(this.formData, 'this.formData')
        this.matchDetailData()
        this.endLoading()
      } else {
        this.endLoading()
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.getDetail()
  }
}
</script>

<style lang="scss" scoped>
.business-container {
  .checkbox-item {
    margin: 0px 20px 20px 20px;
  }
  .checkbox-label {
    font-weight: bold;
  }
  .label {
    width: 200px;
  }
  .business-desc {
    margin: 10px;
  }
  .deposit-money {
    width: 150px;
    margin-left: 10px;
    &:before {
      content: '*';
      color: red;
    }
  }
}
.item-label-style {
  margin-right: 4px;
}
.item-content {
  border-top: 0.5px solid #f2f2f2;
  border-bottom: 0.5px solid #f2f2f2;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
  padding: 20px;
}
.el-checkbox__label {
  width: 100px;
}
</style>