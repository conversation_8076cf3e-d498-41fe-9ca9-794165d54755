<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:
  * @author:zhangys
  * @date:2023/03/26 10:36:34
-->
<template>
  <div class="business-container">
    <!-- 业务办理须知 -->
    <div class="g-flex item-content">
      <div class="label">业务办理须知：</div>
      <div class="g-flex g-flex-column">
        <quillEditor :notice="formData.handlingInstructions"
                     @editorChange="editorChange"></quillEditor>

      </div>
    </div>

    <!-- 系统审核配置 -->
    <div class="g-flex item-content">
      <div class="label g-flex g-flex-align-center">系统审核配置:</div>
      <div class="g-flex g-flex-align-center g-flex-nowrap">
        <div class="item-label-style">系统延时审核时间</div>
        <el-input style="width:100px;margin-right:10px"
                  v-model="formData.systemAuditConfig.time"></el-input>
        <el-select placeholder="请选择"
                   v-model="formData.systemAuditConfig.timeUnit">
          <el-option v-for="(value, key) in activateSystemAuditOptions"
                     :label="value.label"
                     :value="value.value"
                     :key="key"></el-option>
        </el-select>
      </div>
    </div>

    <!-- 人工审核条件 -->
    <div class="g-flex item-content">
      <div class="label g-flex ">人工审核条件:</div>
      <div class="g-flex  g-flex-column">
        <div class="g-flex g-flex-align-center g-flex-nowrap">
          <div class="item-label-style">自然月激活次数超</div>
          <el-input style="width:100px;margin-right:10px"
                    v-model="formData.manualReviewConfig.monthlyActivationTimes"></el-input> <span>次</span>
        </div>

        <div class="g-flex g-flex-align-center g-flex-nowrap"
             style="margin-top:10px">
          <div class="item-label-style">自然年激活次数超</div>
          <el-input style="width:100px;margin-right:10px"
                    v-model="formData.manualReviewConfig.annualActivationTimes"></el-input> <span>次</span>
        </div>
      </div>
    </div>

    <!-- 超频激活限制 -->
    <div class="g-flex item-content">
      <div class="label g-flex g-flex-align-center">超频激活限制:</div>
      <div class="g-flex g-flex-align-center g-flex-nowrap">
        <div class="item-label-style">在</div>
        <el-input v-model="formData.overfrequencyActLimit.time"
                  style="width:100px;margin-right:10px"></el-input>
        <el-select placeholder="请选择"
                   v-model="formData.overfrequencyActLimit.timeUnit">
          <el-option v-for="(value, key) in activateSystemAuditOptions"
                     :label="value.label"
                     :value="value.value"
                     :key="key"></el-option>
        </el-select>
        <span style="margin:0 2px;">内自助激活次数达</span>
        <el-input v-model="formData.overfrequencyActLimit.activateNumber"
                  style="width:100px;margin-right:10px"></el-input>
        <span>次，即限制用户再次申请</span>

      </div>
    </div>

    <!-- 重点稽核列表加入条件： -->
    <div class="g-flex item-content">
      <div class="label g-flex ">重点稽核列表加入条件:</div>
      <div class="g-flex  g-flex-column">
        <div class="g-flex g-flex-align-center g-flex-nowrap">
          <el-checkbox v-model="formData.auditListConfig.setActivateUpperLimit"
                       style="margin-right:10px"></el-checkbox>
          <div class="item-label-style">激活次数上限:自助激活次数达</div>
          <el-input style="width:100px;margin-right:10px"
                    v-model="formData.auditListConfig.activationTimes"></el-input> <span>次</span>
        </div>

        <div class="g-flex g-flex-align-center g-flex-nowrap"
             style="margin-top:10px">
          <el-checkbox v-model="formData.auditListConfig.setPassesLimit"
                       style="margin-right:10px"></el-checkbox>
          <div class="item-label-style">激活次数上限:最近一次自助激活后通行次数达</div>
          <el-input style="width:100px;margin-right:10px"
                    v-model="formData.auditListConfig.passesTimes"> </el-input> <span>次</span>
        </div>
      </div>
    </div>
    <div class="g-flex item-content">
      <div class="label g-flex">支持客货类型：</div>
      <div class="g-flex g-flex-column">

        <el-checkbox-group v-model="carChecked"
                           @change="carCheckItemChange"
                           style="margin-bottom:10px">
          <el-checkbox v-for="(item,index) in carType"
                       :label="item.label"
                       :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>

        <el-checkbox-group v-model="truckChecked"
                           @change="truckCheckItemChange"
                           style="margin-bottom:10px">
          <el-checkbox v-for="(item,index) in truckType"
                       :label="item.label"
                       :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>
        <el-checkbox-group v-model="privateChecked"
                           @change="privateCheckItemChange"
                           style="margin-bottom:10px">
          <el-checkbox v-for="(item,index) in specialVehicleType"
                       :label="item.label"
                       :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </div>

    </div>
    <div class="g-flex item-content">
      <div class="label g-flex">支持的产品:</div>
      <div class="g-flex g-flex-column"
           style="margin-left:-20px">

        <!-- 客车 -->
        <div class="checkbox-item">
          <el-checkbox :indeterminate="productListIndeterminate"
                       v-model="productCheckAll"
                       @change="productCheckAllChange"><span class="checkbox-label">支持的产品</span></el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="productChecked"
                               @change="productCheckItemChange">
              <el-checkbox v-for="(item,index) in productType"
                           :label="item.label"
                           :key="index">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>

    <div class="g-flex item-content">
      <div class="label g-flex">支持的绑定渠道:</div>
      <div class="g-flex g-flex-column"
           style="margin-left:50px">

        <!-- 分对分银行 -->
        <div class="checkbox-item">
          <el-checkbox :indeterminate="divisionListIndeterminate"
                       v-model="divisionCheckAll"
                       @change="divisionCheckAllChange"
                       :disabled="!formData.productConfig.supportPostpaidBindingCard"><span class="checkbox-label">分对分银行</span></el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="divisionChecked"
                               @change="divisionCheckItemChange">
              <el-checkbox v-for="(item,index) in divisionType"
                           :label="item.label"
                           :key="index"
                           :disabled="!formData.productConfig.supportPostpaidBindingCard">{{item.label}} </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <!-- 总对总银行 -->
        <div class="checkbox-item">
          <el-checkbox :indeterminate="totalListIndeterminate"
                       v-model="totalCheckAll"
                       @change="totalCheckAllChange"
                       :disabled="!formData.productConfig.supportPostpaidBindingCard"><span class="checkbox-label">总对总银行</span></el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="totalChecked"
                               @change="totalCheckItemChange">
              <el-checkbox v-for="(item,index) in totalType"
                           :label="item.label"
                           :key="index"
                           :disabled="!formData.productConfig.supportPostpaidBindingCard">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 其他合作渠道 -->
        <div class="checkbox-item">
          <el-checkbox :indeterminate="otherListIndeterminate"
                       v-model="otherCheckAll"
                       @change="otherCheckAllChange"
                       :disabled="!formData.productConfig.supportPostpaidBindingCard"><span class="checkbox-label">其他合作渠道</span></el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group v-model="otherChecked"
                               @change="otherCheckItemChange">
              <el-checkbox v-for="(item,index) in otherType"
                           :label="item.label"
                           :key="index"
                           :disabled="!formData.productConfig.supportPostpaidBindingCard">{{item.label}}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
      </div>

    </div>
    <div class="g-flex g-flex-horizontal-vertical">
      <el-button type="primary"
                 @click="updateBusinessRules">修改</el-button>
    </div>
  </div>

</template>

<script>
import {
  vehicleType,
  truckVehicleType,
  passengerVehicleType,
  businessRuleProduct,
  bindChannelDivision,
  bindChannelTotal,
  bindChannelOther,
  activateSystemAuditOptions,
  specialVehicleType,
} from '@/common/const/optionsData.js'
import quillEditor from './components/quillEditor'

export default {
  name: '',
  props: {},
  components: { quillEditor },
  data() {
    return {
      activateSystemAuditOptions,
      specialVehicleType,
      // 客货类型
      vehicleListIndeterminate: null,
      vehicleCheckAll: '',
      vehicleChecked: [],
      vehicleList: vehicleType,
      //客车
      carListIndeterminate: null,
      carCheckAll: '',
      carChecked: [],
      carType: passengerVehicleType,

      //货车
      truckListIndeterminate: null,
      truckCheckAll: '',
      truckChecked: [],
      truckType: truckVehicleType,

      //专车
      privateListIndeterminate: null,
      privateCheckAll: '',
      privateChecked: [],

      //产品类型
      productListIndeterminate: null,
      productCheckAll: '',
      productChecked: [],
      productType: businessRuleProduct,

      //分对分
      divisionListIndeterminate: null,
      divisionCheckAll: '',
      divisionChecked: [],
      divisionType: bindChannelDivision,

      //总对总
      totalListIndeterminate: null,
      totalCheckAll: '',
      totalChecked: [],
      totalType: bindChannelTotal,

      //其他渠道
      otherListIndeterminate: null,
      otherCheckAll: '',
      otherChecked: [],
      otherType: bindChannelOther,
      ddpRemark: '',
      ttpRemark: '',
      //   formData: {
      //     activateFee: '',
      //     carOwner: '',
      //     ddpRemark: '',
      //     goodsCarType: '',
      //     installType: 0,
      //     isActivate: 0,
      //     isTrunk: '',
      //     passengerCarType: '',
      //     productType: '',
      //     specialCarType: '',
      //     ttpRemark: '',
      //   },

      formData: {
        auditListConfig: {
          activationTimes: '',
          passesTimes: '',
          setActivateUpperLimit: false,
          setPassesLimit: false,
        },
        bindingChannelsConfig: {
          bankConfig: {
            supportABC: false,
            supportGBGB: false,
            supportBOC: false,
            supportCCB: false,
            supportCEB: false,
            supportCOMM: false,
            supportGXNX: false,
            supportHXB: false,
            supportICBC: false,
            supportLZCCB: false,
            supportNAB: false,
            supportPSBC: false,
            supportCZBANK: false
          },
          etcChinaBankConfig: {
            supportABC: false,
            supportBOC: false,
            supportCCB: false,
            supportCMB: false,
            supportCOMM: false,
            supportHXB: false,
            supportICBC: false,
            supportINDUSTRIAL: false,
            supportPSBC: false,
            supportSPD: false,
          },
          otherChannelsConfig: {
            supportALIPAY: false,
            supportZSINFO: false,
           supportFMGS: false,
            supportHCB: false,
            supportHLY: false,
            supportSDXL: false,
            supportWXGP: false,
            supportJIET: false,
          },
        },
        carTypeConfig: {
          passengerCarConfig: {
            supportPassengerCar: false,
            supportVehicleType1: false,
            supportVehicleType2: false,
            supportVehicleType3: false,
            supportVehicleType4: false,
          },
          specialVehicleConfig: {
            supportSpecialVehicle: false,
            supportVehicleType1: false,
            supportVehicleType2: false,
            supportVehicleType3: false,
            supportVehicleType4: false,
            supportVehicleType5: false,
            supportVehicleType6: false,
          },
          truckConfig: {
            supportTruck: false,
            supportVehicleType1: false,
            supportVehicleType2: false,
            supportVehicleType3: false,
            supportVehicleType4: false,
            supportVehicleType5: false,
            supportVehicleType6: false,
          },
        },
        handlingInstructions: '',
        manualReviewConfig: {
          annualActivationTimes: '',
          monthlyActivationTimes: '',
        },
        overfrequencyActLimit: {
          activateNumber: '',
          time: 0,
          timeUnit: '',
        },
        productConfig: {
          supportBindingCard: false,
          supportDayPassCard: false,
          supportMonthCard: false,
          supportPostpaidBindingCard: false,
          supportPrePaidCard: false,
          supportSilkyCard: false,
          supportStoredValueCard: false,
        },
        revision: '',
        systemAuditConfig: {
          time: '',
          timeUnit: '',
        },
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDetail()
  },
  methods: {
    //客车
    carCheckItemChange(value) {
      let checkedCount = value.length
      this.carCheckAll = checkedCount === this.carType.length
      this.carListIndeterminate =
        checkedCount > 0 && checkedCount < this.carType.length
      let tmp = this.formatData(this.carType, this.carChecked)
      this.matchData(tmp, 'car')
    },

    //货车
    truckCheckItemChange(value) {
      let checkedCount = value.length
      this.truckCheckAll = checkedCount === this.truckType.length
      this.truckListIndeterminate =
        checkedCount > 0 && checkedCount < this.truckType.length
      let tmp = this.formatData(this.truckType, this.truckChecked)
      this.matchData(tmp, 'truck')
    },

    //专项作业车

    privateCheckItemChange(value) {
      let checkedCount = value.length
      this.privateCheckAll = checkedCount === this.specialVehicleType.length
      this.privateListIndeterminate =
        checkedCount > 0 && checkedCount < this.specialVehicleType.length
      let tmp = this.formatData(this.specialVehicleType, this.privateChecked)
      this.matchData(tmp, 'specialCar')
    },

    //产品
    productCheckAllChange(val) {
      this.productChecked = val ? this.productType : []
      this.productChecked = this.productChecked.map((item) => {
        return item.label
      })
      this.productListIndeterminate = false
      let tmp = this.formatData(this.productType, this.productChecked)
      this.matchData(tmp, 'product')
    },
    productCheckItemChange(value) {
      let checkedCount = value.length
      console.log(checkedCount, value, '<<---------check')
      this.productCheckAll = checkedCount === this.productType.length
      this.productListIndeterminate =
        checkedCount > 0 && checkedCount < this.productType.length
      let tmp = this.formatData(this.productType, this.productChecked)
      this.matchData(tmp, 'product')
    },
    //分对分
    divisionCheckAllChange(val) {
      this.divisionChecked = val ? this.divisionType : []
      this.divisionChecked = this.divisionChecked.map((item) => {
        return item.label
      })
      this.divisionListIndeterminate = false
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },
    divisionCheckItemChange(value) {
      let checkedCount = value.length
      this.divisionCheckAll = checkedCount === this.divisionType.length
      this.divisionListIndeterminate =
        checkedCount > 0 && checkedCount < this.divisionType.length
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },

    //总对总
    totalCheckAllChange(val) {
      this.totalChecked = val ? this.totalType : []
      this.totalChecked = this.totalChecked.map((item) => {
        return item.label
      })
      this.totalListIndeterminate = false
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },
    totalCheckItemChange(value) {
      let checkedCount = value.length
      this.totalCheckAll = checkedCount === this.totalType.length
      this.totalListIndeterminate =
        checkedCount > 0 && checkedCount < this.totalType.length
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },

    //其他渠道
    otherCheckAllChange(val) {
      this.otherChecked = val ? this.otherType : []
      this.otherChecked = this.otherChecked.map((item) => {
        return item.label
      })
      this.otherListIndeterminate = false
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    otherCheckItemChange(value) {
      let checkedCount = value.length
      this.otherCheckAll = checkedCount === this.otherType.length
      this.otherListIndeterminate =
        checkedCount > 0 && checkedCount < this.otherType.length
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    //通过选中数组中的label匹配出对象中的value值
    formatData(obj, selected) {
      let arr = []
      for (let i = 0; i < selected.length; i++) {
        for (let j = 0; j < obj.length; j++) {
          if (selected[i] == obj[j].label) {
            arr.push(obj[j].field)
          }
        }
      }
      return arr
    },

    //格式化入参数据
    matchData(selected, type) {
      // 产品类型
      if (type == 'product') {
        for (let key in this.formData.productConfig) {
          this.formData.productConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.productConfig) {
            if (selected[i] == key) {
              this.formData.productConfig[key] = true
            }
          }
        }
      }
      //客车配置
      if (type == 'car') {
        for (let key in this.formData.carTypeConfig.passengerCarConfig) {
          this.formData.carTypeConfig.passengerCarConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.carTypeConfig.passengerCarConfig) {
            if (selected[i] == key) {
              this.formData.carTypeConfig.passengerCarConfig[key] = true
            }
          }
        }
      }
      //货车配置
      if (type == 'truck') {
        for (let key in this.formData.carTypeConfig.truckConfig) {
          this.formData.carTypeConfig.truckConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.carTypeConfig.truckConfig) {
            if (selected[i] == key) {
              this.formData.carTypeConfig.truckConfig[key] = true
            }
          }
        }
      }
      //专项车配置
      if (type == 'specialCar') {
        for (let key in this.formData.carTypeConfig.specialVehicleConfig) {
          this.formData.carTypeConfig.specialVehicleConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.carTypeConfig.specialVehicleConfig) {
            if (selected[i] == key) {
              this.formData.carTypeConfig.specialVehicleConfig[key] = true
            }
          }
        }
      }
      //分对分
      if (type == 'division') {
        for (let key in this.formData.bindingChannelsConfig.bankConfig) {
          this.formData.bindingChannelsConfig.bankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannelsConfig.bankConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannelsConfig.bankConfig[key] = true
            }
          }
        }
      }
      //总对总
      if (type == 'total') {
        for (let key in this.formData.bindingChannelsConfig
          .etcChinaBankConfig) {
          this.formData.bindingChannelsConfig.etcChinaBankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannelsConfig
            .etcChinaBankConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannelsConfig.etcChinaBankConfig[key] = true
            }
          }
        }
      }
      //其他渠道
      if (type == 'other') {
        for (let key in this.formData.bindingChannelsConfig
          .otherChannelsConfig) {
          this.formData.bindingChannelsConfig.otherChannelsConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.bindingChannelsConfig
            .otherChannelsConfig) {
            if (selected[i] == key) {
              this.formData.bindingChannelsConfig.otherChannelsConfig[
                key
              ] = true
            }
          }
        }
      }
    },
    //更新业务规则
    updateBusinessRules() {
      let params = JSON.parse(JSON.stringify(this.formData))
      params.manualReviewConfig.annualActivationTimes =
        params.manualReviewConfig.annualActivationTimes * 1
      params.manualReviewConfig.monthlyActivationTimes =
        params.manualReviewConfig.monthlyActivationTimes * 1
      if (!params.productConfig.supportPostpaidBindingCard) {
        params.bindingChannelsConfig = {
          bankConfig: {
            supportABC: false,
            supportGBGB: false,
            supportBOC: false,
            supportCCB: false,
            supportCEB: false,
            supportCOMM: false,
            supportGXNX: false,
            supportHXB: false,
            supportICBC: false,
            supportLZCCB: false,
            supportNAB: false,
            supportPSBC: false,
            supportCZBANK: false
          },
          etcChinaBankConfig: {
            supportABC: false,
            supportBOC: false,
            supportCCB: false,
            supportCMB: false,
            supportCOMM: false,
            supportHXB: false,
            supportICBC: false,
            supportINDUSTRIAL: false,
            supportPSBC: false,
            supportSPD: false,
          },
          otherChannelsConfig: {
            supportALIPAY: false,
            supportZSINFO: false,
           supportFMGS: false,
            supportHCB: false,
            supportHLY: false,
            supportSDXL: false,
            supportWXGP: false,
            supportJIET: false,
          },
        }
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.activateConfig,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.$message.success('更新成功！')
            this.getDetail()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //匹配详情查询出的数据
    matchDetailData() {
      //客车
      if (!this.formData.carTypeConfig.passengerCarConfig) {
        this.formData.carTypeConfig.passengerCarConfig = {
          supportPassengerCar: false,
          supportVehicleType1: false,
          supportVehicleType2: false,
          supportVehicleType3: false,
          supportVehicleType4: false,
        }
      }
      for (let key in this.formData.carTypeConfig.passengerCarConfig) {
        for (let i = 0; i < this.carType.length; i++) {
          if (
            this.formData.carTypeConfig.passengerCarConfig[key] &&
            key == this.carType[i].field
          ) {
            this.carChecked.push(this.carType[i].label)
          }
        }
      }
      //货车
      if (!this.formData.carTypeConfig.truckConfig) {
        this.formData.carTypeConfig.truckConfig = {
        supportTruck: false,
            supportVehicleType1: false,
            supportVehicleType2: false,
            supportVehicleType3: false,
            supportVehicleType4: false,
            supportVehicleType5: false,
            supportVehicleType6: false,
        }
      }
      for (let key in this.formData.carTypeConfig.truckConfig) {
        for (let i = 0; i < this.truckType.length; i++) {
          if (
            this.formData.carTypeConfig.truckConfig[key] &&
            key == this.truckType[i].field
          ) {
            this.truckChecked.push(this.truckType[i].label)
          }
        }
      }
      //专车
       if (!this.formData.carTypeConfig.specialVehicleConfig) {
        this.formData.carTypeConfig.specialVehicleConfig = {
        supportTruck: false,
           supportSpecialVehicle: false,
            supportVehicleType1: false,
            supportVehicleType2: false,
            supportVehicleType3: false,
            supportVehicleType4: false,
            supportVehicleType5: false,
            supportVehicleType6: false,
        }
      }
      for (let key in this.formData.carTypeConfig.specialVehicleConfig) {
        for (let i = 0; i < this.specialVehicleType.length; i++) {
          if (
            this.formData.carTypeConfig.specialVehicleConfig[key] &&
            key == this.specialVehicleType[i].field
          ) {
            this.privateChecked.push(this.specialVehicleType[i].label)
          }
        }
      }
      this.privateCheckItemChange(this.privateChecked)
      // 产品类型
      this.productChecked = []
      for (let key in this.formData.productConfig) {
        for (let i = 0; i < this.productType.length; i++) {
          if (
            this.formData.productConfig[key] &&
            key == this.productType[i].field
          ) {
            this.productChecked.push(this.productType[i].label)
          }
        }
      }
      this.productCheckItemChange(this.productChecked)

      //分对分
      this.divisionChecked = []
      if (!this.formData.bindingChannelsConfig.bankConfig) {
        this.formData.bindingChannelsConfig.bankConfig = {
          supportABC: false,
          supportGBGB: false,
          supportBOC: false,
          supportCCB: false,
          supportCEB: false,
          supportCOMM: false,
          supportGXNX: false,
          supportHXB: false,
          supportICBC: false,
          supportLZCCB: false,
          supportNAB: false,
          supportPSBC: false,
          supportCZBANK: false
        }
      }
      for (let key in this.formData.bindingChannelsConfig.bankConfig) {
        for (let i = 0; i < this.divisionType.length; i++) {
          if (
            this.formData.bindingChannelsConfig.bankConfig[key] &&
            key == this.divisionType[i].field
          ) {
            this.divisionChecked.push(this.divisionType[i].label)
          }
        }
      }
      this.divisionCheckItemChange(this.divisionChecked)

      //总对总
      this.totalChecked = []
      if (!this.formData.bindingChannelsConfig.etcChinaBankConfig) {
        this.formData.bindingChannelsConfig.etcChinaBankConfig = {
          supportABC: false,
          supportBOC: false,
          supportCCB: false,
          supportCMB: false,
          supportCOMM: false,
          supportHXB: false,
          supportICBC: false,
          supportINDUSTRIAL: false,
          supportPSBC: false,
          supportSPD: false,
        }
      }
      for (let key in this.formData.bindingChannelsConfig.etcChinaBankConfig) {
        for (let i = 0; i < this.totalType.length; i++) {
          if (
            this.formData.bindingChannelsConfig.etcChinaBankConfig[key] &&
            key == this.totalType[i].field
          ) {
            this.totalChecked.push(this.totalType[i].label)
          }
        }
      }
      this.totalCheckItemChange(this.totalChecked)

      //其他
      this.otherChecked = []
      if (!this.formData.bindingChannelsConfig.otherChannelsConfig) {
        this.formData.bindingChannelsConfig.otherChannelsConfig = {
          supportAlipayTW: false,
          supportAlipayZSINFO: false,
          supportFM: false,
          supportHCB: false,
          supportHLY: false,
          supportSDXL: false,
          supportWXGP: false,
          supportZDJW: false,
        }
      }
      for (let key in this.formData.bindingChannelsConfig.otherChannelsConfig) {
        for (let i = 0; i < this.otherType.length; i++) {
          if (
            this.formData.bindingChannelsConfig.otherChannelsConfig[key] &&
            key == this.otherType[i].field
          ) {
            this.otherChecked.push(this.otherType[i].label)
          }
        }
      }
      this.otherCheckItemChange(this.otherChecked)
    },
    getDetail() {
      let params = {}
      this.startLoading()
      this.$request({
        url: this.$interfaces.activateConfigDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.formData = res.data
            this.matchDetailData()
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    editorChange(val) {
      this.formData.handlingInstructions = val.content
    },
  },
}
</script>

<style lang='scss' scoped>
.business-container {
  .checkbox-item {
    margin: 0px 20px 20px 20px;
  }
  .checkbox-label {
    font-weight: bold;
  }
  .label {
    width: 200px;
  }
  .business-desc {
    margin: 10px;
  }
  .deposit-money {
    width: 150px;
    margin-left: 10px;
    &:before {
      content: '*';
      color: red;
    }
  }
}
.item-label-style {
  margin-right: 4px;
}
.item-content {
  border-top: 0.5px solid #f2f2f2;
  border-bottom: 0.5px solid #f2f2f2;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
  padding: 20px;
}
.el-checkbox__label {
  width: 100px;
}
</style>
