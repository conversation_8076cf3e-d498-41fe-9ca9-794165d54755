<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:产品转换列表
  * @author:dwz
  * @date:2024/10/28 15:24:12
-->
<template>
  <div>
    <!-- 搜索栏 -->
    <dart-search
      :formSpan="24"
      :gutter="20"
      ref="searchForm1"
      label-position="right"
      :model="search"
      :fontWidth="2"
    >
      <template slot="search-form">
        <dart-search-item label="转换前产品类型：" prop="productType">
          <el-select
            v-model="search.productType"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in productTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>

        <dart-search-item :is-button="true" :colElementNum="2">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table
        :data="tableData"
        v-loading="tableloading"
        style="width: 100%"
        height="55vh"
        row-key="id"
        stripe
      >
        <el-table-column prop="id" label="序号" align="center" />
        <el-table-column
          prop="carType"
          label="支持的客货类型"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            {{ getVehicleType(scope.row.carType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="froProductName"
          label="转换前产品类型"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ getallGxCardType(scope.row.froProductName)
            }}{{ getDeviceType(scope.row.deviceType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="aftProductName"
          label="转换后产品类型"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            {{ getallGxCardType(scope.row.aftProductName)
            }}{{ getDeviceType(scope.row.deviceType) }}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleColor" label="车辆类型" align="center">
          <template slot-scope="scope">
            {{ getCarType(scope.row.carType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="fee"
          label="转换费用(元)"
          width="120"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.isEdit">
              <el-input size="small" v-model="scope.row.fee" />
            </span>
            <span v-else>{{ scope.row.fee }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="!scope.row.isEdit"
              type="primary"
              size="mini"
              @click="handelRow(scope, 'edit')"
              >修改金额</el-button
            >
            <el-button
              v-else
              type="primary"
              size="mini"
              @click="handelRow(scope, 'change')"
              >保存</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="form.pageIndex"
        :page-sizes="[10, 20, 50]"
        :page-size="form.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div> -->

    <!-- <dartSlide :visible.sync="slideVisible"
               title="订单详情"
               v-transfer-dom
               width="90%"
               :maskClosable="true">
      <detail :applyId="applyId"
              :slideVisible="slideVisible"
              :isView="isView"
              @refreshList="getOrderList"
              @closeDartSlide="closeDartSlide"></detail>
    </dartSlide> -->
  </div>
</template>

<script>
import {
  getbusinessType,
  getVehicleColor,
  getnowstate,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
  getallGxCardType,
} from '@/common/method/formatOptions'
import {
  newApplyNodeCodeOptions,
  refundNodeCodeOptions,
  cancelNodeCodeOptions,
  afterSaleStatus,
  vehicleType,
  customerType,
} from '@/common/const/optionsData.js'
// import dartSlide from '@/components/dart/Slide/index.vue'
// import search from './search'
// import detail from '../newApply/detail'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import float from '@/common/method/float.js'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'

export default {
  components: {
    dartSearch,
    dartSearchItem,
    // search,
    // dartSlide,
    // detail,
  },
  created() {
    this.getOrderList()
  },
  data() {
    return {
      tableloading: false,
      tableData: [],
      phone: '',
      form: { pageIndex: 1, pageSize: 10 },
      search: {
        productType: '',
        pageIndex: 1,
        pageSize: 99,
      },
      productTypeList: [
        { label: '预付费绑定记账卡', value: '3' },
        { label: '后付费绑定记账卡', value: '4' },
        { label: '捷通日日通记账卡', value: '5' },
        { label: '捷通次次顺记账卡', value: '10' },
      ],
      deviceTypeList: [
        { label: '(基础版)', value: '1' },
        { label: '(进阶版)', value: '2' },
      ],
    }
  },
  methods: {
    getbusinessType,
    getVehicleColor,
    getnowstate,
    getVehicleType,
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getallGxCardType,
    getDeviceType(value) {
      for (let i = 0; i < this.deviceTypeList.length; i++) {
        if (this.deviceTypeList[i].value == value) {
          return this.deviceTypeList[i].label
        }
      }
      return ''
    },
    handelRow(scope, type) {
      if (type == 'edit') {
        console.log('isEdit00', scope.row.isEdit)
        scope.row.isEdit = true
        console.log('isEdit111', scope.row.isEdit)
      } else {
        console.log(scope)
        this.updateHandle(scope)
      }
      // console.log(scope.row)
      // this.manualLateFeeHandle(row, type)
    },
    updateHandle(scope) {
      this.startLoading()
      // let params = JSON.parse(JSON.stringify(this.form))
      let params = {
        id: scope.row.id,
        newFee: float.mul(scope.row.fee, 100),
      }
      this.$request({
        url: this.$interfaces.converPriceUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log('产品价格修改--->>>>', res)
          this.$message.success('修改' + res.msg)
          scope.row.isEdit = false
          // this.tableData = res.data.records
          // this.tableData.sort((a, b) => {
          //   return parseInt(a.id) - parseInt(b.id)
          // })
          this.endLoading()
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    onSearchHandle() {
      this.getOrderList()
    },
    getOrderList() {
      this.startLoading()
      // let params = JSON.parse(JSON.stringify(this.form))
      this.$request({
        url: this.$interfaces.converPriceList,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          console.log('产品价格配置表--->>>>', res)
          this.tableData = res.data.records
          this.tableData.sort((a, b) => {
            return parseInt(a.id) - parseInt(b.id)
          })
          for (let i = 0; i < this.tableData.length; i++) {
            this.tableData[i].fee = this.moneyFilter(this.tableData[i].fee)
            this.$set(this.tableData[i], 'isEdit', false)
          }
          console.log('table', this.tableData)
          // this.total = res.data.total
          this.endLoading()
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getOrderList()
    },
    changePage(e) {
      this.form.pageIndex = e
      this.getOrderList()
    },
    onResultHandle() {
      this.search.productType = ''
    },
    //表格选中
    handleSelectionChange(val) {
      this.selectedItem = val.map((item) => {
        return item.id
      })
      console.log(this.selectedItem, '<<---------this.selectedItem')
    },
    //批量审核
    batchAuditConfirm() {
      if (this.selectedItem.length == 0) {
        this.$message.warning('请至少选中一条记录！')
        return
      }
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '是否对选中的记录进行批量审核操作'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.batchAudit()
          }
        },
      })
    },
    batchAudit() {
      let params = {
        id: this.selectedItem,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.batchAudit,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, '<<---------res')
          if (res.code == 200) {
            if (res.data.failed > 0) {
              this.$message.error(
                `批量审核${res.data.total}条，成功${res.data.success}条，失败${res.data.failed}条`
              )
            }
            if (res.data.failed == 0) {
              this.$message.success(
                `批量审核${res.data.total}条，成功${res.data.success}条，失败${res.data.failed}条`
              )
            }
            this.getOrderList()
            this.endLoading()
          } else {
            this.$message.error(res.msg)
            this.endLoading()
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .modular-view-title {
  border-bottom: none !important;
}
::v-deep .dart-search-wrapper .dart-search-container .el-form-item__label {
  width: 130px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  width: calc(100% - 130px);
}
</style>