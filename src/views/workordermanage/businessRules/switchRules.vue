<template>
  <div class="business-container">
    <!-- 业务办理须知 （免费注销）-->
    <div class="g-flex item-content">
      <div class="label">
        业务办理须知：
      </div>
      <div class="g-flex g-flex-column">
        <quillEditor
          :notice="formData.handleInstructions"
          @editorChange="freeEditorChange"
        ></quillEditor>
      </div>
    </div>

    <div class="g-flex  item-content">
      <div class="label">转换时可选择的自营产品：</div>
      <div class="checkbox-item ">
        <el-checkbox
          :indeterminate="switchProductIndeterminate"
          v-model="switchProductCheckAll"
          @change="switchProductCheckAllChange"
          ><span class="checkbox-label">转换时可选择的自营产品</span>
        </el-checkbox>
        <div style="margin: 15px 0;">
          <el-checkbox-group
            v-model="switchProductChecked"
            @change="switchProductCheckItemChange"
            class="g-flex g-flex-column"
          >
            <div
              v-for="(item, index) in switchProductType"
              :key="index"
              class="g-flex g-flex-column"
            >
              <el-checkbox :label="item.label">{{ item.label }} </el-checkbox>
              <div class=" business-desc g-flex g-flex-column">
                <span
                  style="font-size:13px;color:#606266;"
                  class="g-flex g-flex-align-start "
                  >产品明细：</span
                >
                <quillEditor
                  :notice="formData[item.key]"
                  :type="switchProductType[index].value"
                  @editorChange="switchEditorChange"
                ></quillEditor>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>

    <div class="g-flex item-content">
      <div class="label g-flex">支持转换的产品类型:</div>
      <div class="g-flex g-flex-column" style="margin-left:-20px">
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="productIndeterminate"
            v-model="productCheckAll"
            @change="productCheckAllChange"
            ><span class="checkbox-label">支持转换的产品类型</span></el-checkbox
          >
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="productChecked"
              @change="productCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in productType"
                :label="item.label"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>

    <div class="g-flex item-content">
      <div class="label g-flex">支持转换的合作渠道:</div>
      <div class="g-flex g-flex-column" style="margin-left:50px">
        <!-- 分对分银行 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="divisionListIndeterminate"
            v-model="divisionCheckAll"
            @change="divisionCheckAllChange"
            :disabled="
              !formData.transformChannelConfig.supportPostpaidBindingCard
            "
          >
            <span class="checkbox-label">分对分银行</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="divisionChecked"
              @change="divisionCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in divisionType"
                :label="item.label"
                :key="index"
                :disabled="
                  !formData.transformChannelConfig.supportPostpaidBindingCard
                "
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>
        <!-- 总对总银行 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="totalListIndeterminate"
            v-model="totalCheckAll"
            @change="totalCheckAllChange"
            :disabled="
              !formData.transformChannelConfig.supportPostpaidBindingCard
            "
          >
            <span class="checkbox-label">总对总银行</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="totalChecked"
              @change="totalCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in totalType"
                :label="item.label"
                :key="index"
                :disabled="
                  !formData.transformChannelConfig.supportPostpaidBindingCard
                "
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>

        <!-- 其他合作渠道 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="otherListIndeterminate"
            v-model="otherCheckAll"
            @change="otherCheckAllChange"
            :disabled="
              !formData.transformChannelConfig.supportPostpaidBindingCard
            "
          >
            <span class="checkbox-label">其他合作渠道</span>
          </el-checkbox>
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="otherChecked"
              @change="otherCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in otherType"
                :label="item.label"
                :key="index"
                :disabled="
                  !formData.transformChannelConfig.supportPostpaidBindingCard
                "
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>
    <div class="g-flex g-flex-horizontal-vertical">
      <el-button type="primary" @click="updateBusinessRules">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  bindChannelDivision,
  bindChannelTotal,
  bindChannelOther,
  businessRuleProduct
} from '@/common/const/optionsData.js'
import quillEditor from './components/quillEditor'
import { transformConfigDetail, transformConfig } from '@/api/workordermanage'

export default {
  components: { quillEditor },
  data() {
    return {
      formData: {
        handleInstructions: '',
        transformChannelConfig: {
          supportPostpaidBindingCard: true // 是否支持银行
        },
        transformProductConfig: {
          supportBindingCard: false,
          supportDayPassCard: false,
          supportMonthCard: false,
          supportPostpaidBindingCard: false,
          supportPrePaidCard: false,
          supportSilkyCard: false,
          supportStoredValueCard: false
        }
      },
      // 转换时可选择的自营产品
      switchProductIndeterminate: null,
      switchProductCheckAll: '',
      switchProductChecked: [],
      switchProductType: [
        {
          label: '日日通记账卡',
          value: '5',
          field: '5',
          key: 'ddpInstructions',
          data: ''
        },
        {
          label: '次次顺记账卡',
          value: '10',
          field: '10',
          key: 'ttpInstructions',
          data: ''
        }
      ],

      //产品类型
      productIndeterminate: null,
      productCheckAll: '',
      productChecked: [],
      productType: [
        { label: '日日通记账卡', value: '2', field: 'supportDayPassCard' },
        { label: '月月行记账卡', value: '3', field: 'supportMonthCard' },
        { label: '次次顺记账卡', value: '4', field: 'supportSilkyCard' },
        { label: '预付费绑定记账卡', value: '5', field: 'supportBindingCard' },
        { label: '后付费绑定记账卡', value: '6',field: 'supportPostpaidBindingCard'},
        { label: '预付费记账卡', value: '7', field: 'supportPrePaidCard' }
      ],

      //分对分
      divisionListIndeterminate: null,
      divisionCheckAll: '',
      divisionChecked: [],
      divisionType: bindChannelDivision,

      //总对总
      totalListIndeterminate: null,
      totalCheckAll: '',
      totalChecked: [],
      totalType: bindChannelTotal,

      //其他渠道
      otherListIndeterminate: null,
      otherCheckAll: '',
      otherChecked: [],
      otherType: bindChannelOther
    }
  },
  methods: {
    freeEditorChange(val) {
      this.formData.handleInstructions = val.content
    },
    editorChange(val) {
      this.formData.feesInstructions = val.content
    },
    async updateBusinessRules() {
      let params = JSON.parse(JSON.stringify(this.formData))
      if (!params.transformChannelConfig.supportPostpaidBindingCard) {
        params.transformChannelConfig = {
          partBankConfig: {
            supportABC: false,
            supportGBGB: false,
            supportBOC: false,
            supportCCB: false,
            supportCEB: false,
            supportCOMM: false,
            supportGXNX: false,
            supportHXB: false,
            supportICBC: false,
            supportLZCCB: false,
            supportNAB: false,
            supportPSBC: false
          },
          etcBankConfig: {
            supportABC: false,
            supportBOC: false,
            supportCCB: false,
            supportCMB: false,
            supportCOMM: false,
            supportHXB: false,
            supportICBC: false,
            supportINDUSTRIAL: false,
            supportPSBC: false,
            supportSPD: false
          },
          otherChannelConfig: {
            supportALIPAY: false,
            supportZSINFO: false,
           supportFMGS: false,
            supportHCB: false,
            supportHLY: false,
            supportSDXL: false,
            supportWXGP: false,
            supportJIET: false
          }
        }
      }
      // console.log(params, 'params')
      // return
      this.startLoading()
      let res = await transformConfig(params)
      if (res.code == 200) {
        this.endLoading()
        this.$message.success('更新成功！')
        this.getDetail()
      } else {
        this.endLoading()
        this.$message.error(res.msg)
      }
    },
    //自营产品
    switchProductCheckAllChange(val) {
      this.switchProductChecked = val ? this.switchProductType : []
      this.switchProductChecked = this.switchProductChecked.map(
        item => item.label
      )
      this.switchProductIndeterminate = false
      let tmp = [
        ...new Set(
          this.formatData(this.switchProductType, this.switchProductChecked)
        )
      ]
      // 改
      if (tmp.length == 2) {
        this.formData.gxCardType = '99'
      } else {
        this.formData.gxCardType = tmp[0]
      }
    },
    switchProductCheckItemChange(value) {
      let checkedCount = value.length
      this.switchProductCheckAll =
        checkedCount === this.switchProductType.length
      this.switchProductIndeterminate =
        checkedCount > 0 && checkedCount < this.switchProductType.length
      console.log(value, checkedCount, this.switchProductType.length)
      let tmp = [
        ...new Set(
          this.formatData(this.switchProductType, this.switchProductChecked)
        )
      ]

      if (tmp.length == 2) {
        this.formData.gxCardType = '99'
      } else {
        this.formData.gxCardType = tmp[0]
      }
    },
    switchEditorChange(val) {
      for (let i = 0; i < this.switchProductType.length; i++) {
        // console.log(val, '<<---------val')

        if (this.switchProductType[i].value == '5' && val.type == '5') {
          //   this.formData.ddpInstructions = val.content
          // this.$set(this.switchProductType[i], 'data', val.content)
          this.$set(this.formData, 'ddpInstructions', val.content)
        }
        if (this.switchProductType[i].value == '10' && val.type == '10') {
          //   this.formData.ttpInstructions = val.content
          // this.$set(this.switchProductType[i], 'data', val.content)
          this.$set(this.formData, 'ttpInstructions', val.content)
        }
      }
    },
    //产品
    productCheckAllChange(val) {
      this.productChecked = val ? this.productType : []
      this.productChecked = this.productChecked.map(item => {
        return item.label
      })
      this.productIndeterminate = false
      let tmp = this.formatData(this.productType, this.productChecked)
      this.matchData(tmp, 'product')
    },
    productCheckItemChange(value) {
      let checkedCount = value.length
      this.productCheckAll = checkedCount === this.productType.length
      this.productIndeterminate =
        checkedCount > 0 && checkedCount < this.productType.length
      let tmp = this.formatData(this.productType, this.productChecked)
      this.matchData(tmp, 'product')
    },
    //分对分
    divisionCheckAllChange(val) {
      this.divisionChecked = val ? this.divisionType : []
      this.divisionChecked = this.divisionChecked.map(item => {
        return item.label
      })
      this.divisionListIndeterminate = false
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },
    divisionCheckItemChange(value) {
      let checkedCount = value.length
      this.divisionCheckAll = checkedCount === this.divisionType.length
      this.divisionListIndeterminate =
        checkedCount > 0 && checkedCount < this.divisionType.length
      let tmp = this.formatData(this.divisionType, this.divisionChecked)
      this.matchData(tmp, 'division')
    },
    //总对总
    totalCheckAllChange(val) {
      this.totalChecked = val ? this.totalType : []
      this.totalChecked = this.totalChecked.map(item => {
        return item.label
      })
      this.totalListIndeterminate = false
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },
    totalCheckItemChange(value) {
      let checkedCount = value.length
      this.totalCheckAll = checkedCount === this.totalType.length
      this.totalListIndeterminate =
        checkedCount > 0 && checkedCount < this.totalType.length
      let tmp = this.formatData(this.totalType, this.totalChecked)
      this.matchData(tmp, 'total')
    },
    //其他渠道
    otherCheckAllChange(val) {
      this.otherChecked = val ? this.otherType : []
      this.otherChecked = this.otherChecked.map(item => {
        return item.label
      })
      this.otherListIndeterminate = false
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    otherCheckItemChange(value) {
      let checkedCount = value.length
      this.otherCheckAll = checkedCount === this.otherType.length
      this.otherListIndeterminate =
        checkedCount > 0 && checkedCount < this.otherType.length
      let tmp = this.formatData(this.otherType, this.otherChecked)
      this.matchData(tmp, 'other')
    },
    //通过选中数组中的label匹配出对象中的value值
    formatData(obj, selected) {
      let arr = []
      for (let i = 0; i < selected.length; i++) {
        for (let j = 0; j < obj.length; j++) {
          if (selected[i] == obj[j].label) {
            arr.push(obj[j].field)
          }
        }
      }
      return arr
    },
    matchData(selected, type) {
      //分对分
      if (type == 'division') {
        for (let key in this.formData.transformChannelConfig.partBankConfig) {
          this.formData.transformChannelConfig.partBankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.transformChannelConfig.partBankConfig) {
            if (selected[i] == key) {
              this.formData.transformChannelConfig.partBankConfig[key] = true
            }
          }
        }
      }
      //总对总
      if (type == 'total') {
        for (let key in this.formData.transformChannelConfig.etcBankConfig) {
          this.formData.transformChannelConfig.etcBankConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.transformChannelConfig.etcBankConfig) {
            if (selected[i] == key) {
              this.formData.transformChannelConfig.etcBankConfig[key] = true
            }
          }
        }
      }
      //其他渠道
      if (type == 'other') {
        for (let key in this.formData.transformChannelConfig
          .otherChannelConfig) {
          this.formData.transformChannelConfig.otherChannelConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.transformChannelConfig
            .otherChannelConfig) {
            if (selected[i] == key) {
              this.formData.transformChannelConfig.otherChannelConfig[
                key
              ] = true
            }
          }
        }
      }
      // 产品类型
      if (type == 'product') {
        for (let key in this.formData.transformProductConfig) {
          this.formData.transformProductConfig[key] = false
        }
        for (let i = 0; i < selected.length; i++) {
          for (let key in this.formData.transformProductConfig) {
            if (selected[i] == key) {
              console.log(selected, 'transformProductConfig')
              this.formData.transformProductConfig[key] = true
            }
          }
        }
      }
    },
    //匹配详情查询出的数据
    matchDetailData() {
      //分对分
      for (let key in this.formData.transformChannelConfig.partBankConfig) {
        for (let i = 0; i < this.divisionType.length; i++) {
          if (
            this.formData.transformChannelConfig.partBankConfig[key] &&
            key == this.divisionType[i].field
          ) {
            this.divisionChecked.push(this.divisionType[i].label)
          }
        }
      }
      this.divisionCheckItemChange(this.divisionChecked)

      //总对总
      for (let key in this.formData.transformChannelConfig.etcBankConfig) {
        for (let i = 0; i < this.totalType.length; i++) {
          if (
            this.formData.transformChannelConfig.etcBankConfig[key] &&
            key == this.totalType[i].field
          ) {
            this.totalChecked.push(this.totalType[i].label)
          }
        }
      }
      this.totalCheckItemChange(this.totalChecked)

      //其他
      for (let key in this.formData.transformChannelConfig.otherChannelConfig) {
        for (let i = 0; i < this.otherType.length; i++) {
          if (
            this.formData.transformChannelConfig.otherChannelConfig[key] &&
            key == this.otherType[i].field
          ) {
            this.otherChecked.push(this.otherType[i].label)
          }
        }
      }
      this.otherCheckItemChange(this.otherChecked)

      //产品
      for (let key in this.formData.transformProductConfig) {
        for (let i = 0; i < this.productType.length; i++) {
          if (
            this.formData.transformProductConfig[key] &&
            key == this.productType[i].field
          ) {
            this.productChecked.push(this.productType[i].label)
          }
        }
      }
      this.productCheckItemChange(this.productChecked)

      // 自营
      if (this.formData.gxCardType == '99') {
        this.switchProductChecked = ['日日通记账卡', '次次顺记账卡']
        this.switchProductCheckItemChange(this.switchProductChecked)
      } else {
        let obj = {
          '5': '日日通记账卡',
          '10': '次次顺记账卡'
        }
        this.switchProductChecked.push(obj[this.formData.gxCardType])
        this.switchProductCheckItemChange(this.switchProductChecked)
      }
    },
    async getDetail() {
      this.startLoading()
      let res = await transformConfigDetail()
      if (res.code == 200) {
        this.formData = {
          ...res.data
          // transformChannelConfig: {
          //   supportPostpaidBindingCard: true
          // },
          // transformProductConfig: {
          //   supportBindingCard: false,
          //   supportDayPassCard: false,
          //   supportMonthCard: false,
          //   supportPostpaidBindingCard: false,
          //   supportPrePaidCard: false,
          //   supportSilkyCard: false,
          //   supportStoredValueCard: false
          // }
        }
        this.formData.transformChannelConfig.supportPostpaidBindingCard = true
        this.totalChecked = []
        this.otherChecked = []
        this.divisionChecked = []
        this.productChecked = []
        // console.log(this.formData, 'this.formData')
        this.matchDetailData()
        this.endLoading()
      } else {
        this.endLoading()
        this.$message.error(res.msg)
      }
    }
  },
  created() {
    this.getDetail()
  }
}
</script>

<style lang="scss" scoped>
.business-container {
  .checkbox-item {
    margin: 0px 20px 20px 20px;
  }
  .checkbox-label {
    font-weight: bold;
  }
  .label {
    width: 200px;
  }
  .business-desc {
    margin: 10px;
  }
  .deposit-money {
    width: 150px;
    margin-left: 10px;
    &:before {
      content: '*';
      color: red;
    }
  }
}
.item-label-style {
  margin-right: 4px;
}
.item-content {
  border-top: 0.5px solid #f2f2f2;
  border-bottom: 0.5px solid #f2f2f2;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
  padding: 20px;
}
.el-checkbox__label {
  width: 100px;
}
</style>