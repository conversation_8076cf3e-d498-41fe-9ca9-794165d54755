<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:车辆权限配置
  * @author:dwz
  * @date:2024/10/28 15:24:12
-->
<template>
  <div>
    <!-- 搜索栏 -->
    <dart-search
      :formSpan="24"
      :gutter="20"
      ref="searchForm1"
      label-position="right"
      :model="search"
      :fontWidth="2"
    >
      <template slot="search-form">
        <dart-search-item label="车牌颜色：" prop="carColor">
          <el-select
            v-model="search.vehicleColor"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in licenseColorOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="车牌号码：" prop="vehicleNo">
          <el-input
            v-model="search.vehicleNo"
            placeholder="请输入车牌号码"
            clearable
          ></el-input>
        </dart-search-item>
        <dart-search-item label="ETC卡号：" prop="cardNo">
          <el-input
            v-model="search.cardNo"
            placeholder="请输入ETC卡号"
            clearable
          ></el-input>
        </dart-search-item>
        <dart-search-item label="OBU编号：" prop="obuNo">
          <el-input
            v-model="search.obuNo"
            placeholder="请输入OBU编号"
            clearable
          ></el-input>
        </dart-search-item>
        <dart-search-item label="用户名称：" prop="custName">
          <el-input
            v-model="search.custName"
            placeholder="请输入用户名称"
            clearable
          ></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true" :colElementNum="4">
          <div class="g-flex g-flex-end">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button
              size="mini"
              type="primary"
              @click="importDialogVisible = true"
            >
              <i class="el-icon-upload"></i> 批量导入</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="updateHandle"
              :disabled="multipleSelection.length == 0"
            >
              确定修改</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <div
        style="
          background-color: #e6f3fc;
          color: #777777;
          padding: 15px;
          font-size: 12px;
        "
      >
        已选择 {{ multipleSelection.length }} 项
      </div>
      <el-table
        :data="tableData"
        v-loading="tableloading"
        style="width: 100%"
        height="55vh"
        row-key="id"
        stripe
        :row-class-name="tableRowClassName"
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column prop="id" label="序号" align="center" /> -->
        <el-table-column
          type="selection"
          width="50"
          label="全选"
          :selectable="selectableStatus"
        >
        </el-table-column>
        <el-table-column prop="userName" label="用户名称" align="center" />
        <el-table-column
          prop="vehicleColor"
          label="车牌颜色"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.vehicleColor) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="vehicleNo"
          label="车牌号"
          align="center"
          width="130"
        />
        <el-table-column
          prop="froProductName"
          label="用户类型"
          align="center"
          width="120"
        >
          <template slot-scope="scope">
            {{ scope.row.custType == '0' ? '个人' : '企业' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="froProductType"
          label="产品类型"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ getallGxCardType(scope.row.froProductType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="froProductName"
          label="客货类型"
          align="center"
          width="180"
        >
          <template slot-scope="scope">
            {{ getVehicleType(scope.row.carType) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="aftProductName"
          label="可选自营产品配置"
          width="280"
          align="center"
        >
          <template slot-scope="scope">
            <el-checkbox-group v-model="scope.row.aftProductType">
              <el-checkbox
                :disabled="
                  scope.row.froProductType == '5' ||
                  setTableDisabled(scope.row.froProductType)
                "
                label="5"
                >日日通记账卡</el-checkbox
              >
              <el-checkbox
                :disabled="
                  scope.row.froProductType == '10' ||
                  setTableDisabled(scope.row.froProductType)
                "
                label="10"
                >次次顺记账卡</el-checkbox
              >
            </el-checkbox-group>
          </template>
        </el-table-column>
        <el-table-column
          prop="vehicleColor"
          label="有偿转换价格配置"
          align="center"
          width="160"
        >
          <template slot-scope="scope">
            <el-radio
              :disabled="
                setRadioDisabled(scope.row.aftProductType) ||
                setTableDisabled(scope.row.froProductType)
              "
              v-model="scope.row.isFree"
              :label="0"
              >付费</el-radio
            >
            <el-radio
              :disabled="
                setRadioDisabled(scope.row.aftProductType) ||
                setTableDisabled(scope.row.froProductType)
              "
              v-model="scope.row.isFree"
              :label="1"
              >免费</el-radio
            >
          </template>
        </el-table-column>
        <el-table-column prop="userName" label="配置结果" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '1'" type="success"
              >配置成功</el-tag
            >
            <el-tag v-else-if="scope.row.status == null" type="info"
              >未配置</el-tag
            >
            <el-tag v-else type="danger">失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="最近修改"
          align="center"
          width="160"
        />
      </el-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageIndex"
        :page-sizes="[20, 50, 100]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <import-dialog
      :visible.sync="importDialogVisible"
      @uploadSuccess="uploadSuccess"
    >
    </import-dialog>
    <result-dialog
      :visible.sync="resultDialogVisible"
      :result="result"
    ></result-dialog>
  </div>
</template>

<script>
import {
  getbusinessType,
  getVehicleColor,
  getnowstate,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
  getallGxCardType,
} from '@/common/method/formatOptions'
import {
  newApplyNodeCodeOptions,
  refundNodeCodeOptions,
  cancelNodeCodeOptions,
  afterSaleStatus,
  vehicleType,
  customerType,
  licenseColorOption,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import importDialog from './components/importDialog'
import resultDialog from './components/resultDialog'
import float from '@/common/method/float.js'
import { getToken } from '@/utils/auth'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'

export default {
  components: {
    dartSearch,
    dartSearchItem,
    importDialog,
    resultDialog,
  },
  created() {
    // this.getOrderList()
  },
  data() {
    return {
      licenseColorOption,
      importDialogVisible: false,
      resultDialogVisible: false,
      tableloading: false,
      firstLine: false,
      tableData: [],
      phone: '',
      // form: { pageIndex: 1, pageSize: 10 },
      search: {
        vehicleNo: '',
        vehicleColor: '',
        // vehicleNo: '桂Z10010',
        // vehicleColor: '0',
        cardNo: '',
        obuNo: '',
        custName: '',
        pageIndex: 1,
        pageSize: 20,
      },
      total: 0,
      productTypeList: [
        { label: '后付费绑定记账卡', value: '4' },
        { label: '捷通日日通记账卡', value: '5' },
        { label: '捷通次次顺记账卡', value: '10' },
      ],
      deviceTypeList: [
        { label: '(基础版)', value: '1' },
        { label: '(进阶版)', value: '2' },
      ],
      multipleSelection: [], //多选
      idsStr: '',
      typeStr: '',
      freeStr: '',
      result: null, //导入结果
    }
  },
  methods: {
    getbusinessType,
    getVehicleColor,
    getnowstate,
    getVehicleType,
    getCarType,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getallGxCardType,
    getDeviceType(value) {
      for (let i = 0; i < this.deviceTypeList.length; i++) {
        if (this.deviceTypeList[i].value == value) {
          return this.deviceTypeList[i].label
        }
      }
      return ''
    },
    selectableStatus(row) {
      // 如果行数据中有disabled属性，则返回false禁用多选
      console.log('row', row)
      return (
        (row.froProductType == '3' ||
          row.froProductType == '4' ||
          row.froProductType == '5' ||
          row.froProductType == '9' ||
          row.froProductType == '10') &&
        row.aftProductType
      )
    },
    setTableDisabled(row) {
      // console.log('设置dis', val.includes('10'))
      return (
        row.froProductType == '3' ||
        row.froProductType == '4' ||
        row.froProductType == '5' ||
        row.froProductType == '9' ||
        row.froProductType == '10'
      )
    },
    tableRowClassName({ row, rowIndex }) {
      // console.log('row=========>>>>>>>>>', row)
      if (rowIndex === 0 && row.firstLine) {
        return 'success-row'
      }
      return ''
    },
    setRadioDisabled(val) {
      // console.log('设置dis', val.includes('10'))
      if (val) {
        if (val.includes('10')) {
          return false
        } else {
          return true
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    uploadSuccess(result) {
      // this.checkAll = false
      // this.radio = []
      this.importDialogVisible = false
      this.resultDialogVisible = true
      this.result = result
      // this.getRefundOrderList()
    },
    updateHandle() {
      console.log('this.select', this.multipleSelection)
      //先重置信息
      this.idsStr = ''
      this.typeStr = ''
      this.freeStr = ''
      for (let i = 0; i < this.multipleSelection.length; i++) {
        let typeJoin = this.multipleSelection[i].aftProductType
        if (i == this.multipleSelection.length - 1) {
          //最后一个不加，
          this.idsStr += this.multipleSelection[i].id
          this.typeStr += typeJoin
          this.freeStr += this.multipleSelection[i].isFree
        } else {
          this.idsStr += this.multipleSelection[i].id + '|'
          this.typeStr += typeJoin + '|'
          this.freeStr += this.multipleSelection[i].isFree + '|'
        }
      }

      this.updateAuth()
    },
    updateAuth() {
      this.startLoading()
      // let params = JSON.parse(JSON.stringify(this.form))
      let params = {
        ids: this.idsStr,
        aftProductType: this.typeStr,
        free: this.freeStr,
      }
      this.$request({
        url: this.$interfaces.updateCarsAuth,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log('车辆权限修改--->>>>', res)
          this.$message.success('修改' + res.msg)
          let filter = this.multipleSelection.filter((item) => {
            return item.firstLine
          })
          if (filter.length > 0) {
            this.getOrderList('noLight')
          } else {
            this.getOrderList()
          }

          // this.tableData = res.data.records
          // this.tableData.sort((a, b) => {
          //   return parseInt(a.id) - parseInt(b.id)
          // })
          this.endLoading()
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    onSearchHandle() {
      // if (
      //   !this.search.vehicleColor &&
      //   !this.search.vehicleNo &&
      //   !this.search.cardNo &&
      //   !this.search.obuNo &&
      //   !this.search.custName
      // ) {
      //   this.$message.warning('请先输入查询条件！')
      //   return
      // } else
      if (
        (!this.search.cardNo &&
          !this.search.obuNo &&
          !this.search.custName &&
          this.search.vehicleColor &&
          !this.search.vehicleNo) ||
        (!this.search.vehicleColor && this.search.vehicleNo)
      ) {
        this.$message.warning('车牌颜色与车牌号需要同时输入！')
        return
      }
      this.getOrderList()
    },
    getOrderList(type) {
      this.startLoading()
      // let params = JSON.parse(JSON.stringify(this.form))
      this.$request({
        url: this.$interfaces.converCarAuthList,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          console.log('产品价格配置表--->>>>', res)
          this.tableData = res.data.records
          this.total = res.data.total
          for (let i = 0; i < this.tableData.length; i++) {
            if (!type) {
              this.tableData[0].firstLine = true
            }
            let str = this.tableData[i].aftProductType
            if (str) {
              let strArr = str.split('|')
              console.log('strArr', strArr)
              this.tableData[i].aftProductType = strArr
            } else {
              this.tableData[i].aftProductType = []
            }
          }
          console.log('table', this.tableData)
          this.endLoading()
        })
        .catch((error) => {
          this.endLoading()
          console.log('err', error)
        })
    },
    handleSizeChange(e) {
      this.search.pageSize = e
      this.getOrderList()
    },
    changePage(e) {
      this.search.pageIndex = e
      this.getOrderList()
    },
    onResultHandle() {
      this.getOrderList('reset')
      this.search.vehicleNo = ''
      this.search.vehicleColor = ''
      this.search.cardNo = ''
      this.search.obuNo = ''
      this.search.custName = ''
      this.search.pageIndex = 1
      this.search.pageSize = 20
      if (this.tableData.length > 0) {
        this.tableData[0].firstLine = false
      }
      this.multipleSelection = []
      console.log('重置按钮', this.search, this.tableData)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-table tbody tr.success-row {
  background: rgb(234, 255, 255) !important;
}
::v-deep .modular-view-title {
  border-bottom: none !important;
}
::v-deep .dart-search-wrapper .dart-search-container .el-form-item__label {
  width: 130px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  width: calc(100% - 130px);
}
</style>