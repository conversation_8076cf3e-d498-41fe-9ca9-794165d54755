<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="4"
    >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :spanMethod="objectSpanMethod"
        :pageSize="pageSize"
        :pageNum="pageNum"
        showBorder
        :hasPagination="false"
        @changeTableData="changeTableData"
      >
        <!-- 权益服务费 -->
        <template slot="benefitServiceFee" slot-scope="{ scope }">
          <div class="operator-td">
            <span v-if="scope.isEdit">
              <el-input size="small" v-model="scope.benefitServiceFee" />
            </span>
            <span v-else>{{ scope.benefitServiceFee }}</span>
          </div>
        </template>

        <!-- 激活保证金 -->
        <template slot="activationDeposit" slot-scope="{ scope }">
          <div class="operator-td">
            <span v-if="scope.isEdit">
              <el-input size="small" v-model="scope.activationDeposit" />
            </span>
            <span v-else>{{ scope.activationDeposit }}</span>
            <!-- <el-button type="text" @click="handelRow(scope, 'activationDeposit')"
              >修改金额</el-button
            > -->
          </div>
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            v-if="scope.isEdit"
            v-permisaction="['release:productConfigEdit']"
            type="primary"
            size="mini"
            @click="handelRow(scope, 'changge')"
            >保存修改</el-button
          >
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="handelRow(scope, 'edit')"
            >修改金额</el-button
          >
        </template>
      </my-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import { listColoumns, listForm } from './components/model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { productConfigQuery, productConfigEdit } from '@/api/workordermanage'
import float from '@/common/method/float.js'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: productConfigQuery,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex'
      // timeField: []
    }
  },
  watch: {
    tableData: {
      immediate: true,
      deep: true,
      handler(val) {
        console.log(val, 'watch')
        if (val.length > 0) {
          this.handleTableData(val)
        }
      }
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    async handelRow(row, type) {
      if (type == 'edit') {
        row.isEdit = true
      } else {
        this.manualLateFeeHandle(row)
      }
      console.log(row)
      // this.manualLateFeeHandle(row, type)
    },
    handleSizeChange(val) {
      this.changeTableData(val, this.pageNum)
    },
    handleCurrentChange(val) {
      this.changeTableData(this.pageSize, val)
    },
    //格式化参数，合并单元格使用
    handleTableData(tableData) {
      let rowSpanArr = [],
        position = 0,
        rowSpanArr2 = [],
        position2 = 0

      for (let [index, item] of tableData.entries()) {
        console.log(index, item)
        if (index == 0) {
          rowSpanArr.push(1)
          position = 0
          rowSpanArr2.push(1)
          position2 = 0
          console.log(123123123)
        } else {
          if (item.productName == tableData[index - 1].productName) {
            rowSpanArr[position] += 1 //项目名称相同，合并到同一个数组中
            rowSpanArr.push(0)
          } else {
            rowSpanArr.push(1)
            position = index
          }

          if (item.productType == tableData[index - 1].productType) {
            rowSpanArr2[position2] += 1 //项目名称相同，合并到同一个数组中
            rowSpanArr2.push(0)
          } else {
            rowSpanArr2.push(1)
            position2 = index
          }
        }
      }
      this.rowSpanArr = rowSpanArr
      this.rowSpanArr2 = rowSpanArr2
      console.log(this.rowSpanArr, this.rowSpanArr2)
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let rowSpan = this.rowSpanArr[rowIndex]
        return {
          rowspan: rowSpan, //行
          colspan: 1 //列
        }
      }
      // if (columnIndex === 5) {
      //   let rowSpan = this.rowSpanArr2[rowIndex]
      //   return {
      //     rowspan: rowSpan, //行
      //     colspan: 1 //列
      //   }
      // }
    },

    /**
     * 调差方法
     */
    async manualLateFeeHandle(row) {
      let { id,activationDeposit, benefitServiceFee,revision } = row
      let params = {
        id,
        revision,
        activationDeposit: float.mul(activationDeposit, 100),
        benefitServiceFee: float.mul(benefitServiceFee, 100)
      }
      let res = await productConfigEdit(params)
      if (res.code == 200) {
        row.isEdit = false
        this.$message.success('操作成功')
        // this.getTableData()
      }
    },
    // manualLateFeeHandle(row) {
    //   this.$prompt('请输入修改金额', '提示', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     inputPattern: /^\d+(?=\.{0,1}\d+$|$)/,
    //     inputErrorMessage: '请输入正确金额（数字）'
    //   })
    //     .then(async ({ value }) => {
    //       let params = {
    //         amount: Number(value),
    //         sern: row.sern
    //       }
    //       console.log(params)
    //       // let res = await productConfigEdit(params)
    //       // if (res.code == 200) {
    //       //   this.$message.success('操作成功')
    //       //   this.getTableData()
    //       // }
    //     })
    //     .catch(() => {
    //       this.$message({
    //         type: 'info',
    //         message: '取消输入'
    //       })
    //     })
    // },
    tableCallBack(res) {
      let data = res.data.records.map(item => {
        return {
          ...item,
          isEdit: false,
          benefitServiceFee: this.moneyFilter(item.benefitServiceFee),
          activationDeposit: this.moneyFilter(item.activationDeposit)
        }
      })
      this.tableData = data
      this.total = res.data.total
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  // padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
  .table {
    margin-top: 0;
    padding-top: 0;
  }
  .operator-td {
    span {
      margin-right: 20px;
    }
  }
}
</style>