<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:业务规则配置
  * @author:zhangys
  * @date:2023/03/07 15:07:48
-->
<template>
  <div class="user">
    <div class="tab-item">
      <el-tabs v-model="activeName" @tab-click="handleClick" type="border-card">
        <el-tab-pane
          :label="item.label"
          v-for="(item, index) in businessTypeOptions"
          :key="index"
          :name="item.label"
        >
          <div v-if="item.label == '产品转换'" slot="label">
            <el-dropdown>
              <span class="el-dropdown-link"> 产品转换 <i class="el-icon-arrow-down el-icon--right"></i></span>
              <!-- <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click="productClick('9')"
                  >规则配置1</el-dropdown-item
                >
                <el-dropdown-item @click="productClick('10')"
                  >车辆权限配置2</el-dropdown-item
                >
              </el-dropdown-menu> -->
              <el-dropdown-menu
                trigger="click"
                :hide-on-click="true"
                slot="dropdown"
                style="padding: 5px 20px"
              >
                <div style="display: flex; flex-direction: column">
                  <el-button
                    style="color: #999999"
                    type="text"
                    @click="productClick('9')"
                  >
                    规则配置</el-button
                  >
                  <el-button
                    style="color: #999999"
                    type="text"
                    @click="productClick('12')"
                  >
                    产品转换价格配置</el-button
                  >
                  <el-button
                    style="color: #999999"
                    type="text"
                    @click="productClick('13')"
                  >
                    车辆权限配置</el-button
                  >
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-tab-pane>
        <div>
          <applyRules v-if="selectItem == '2'"></applyRules>
          <afterSaleChange v-if="selectItem == '3'"></afterSaleChange>
          <afterSaleRemake v-if="selectItem == '4'"></afterSaleRemake>

          <afterSaleAmountConfig
            v-if="selectItem == '5'"
          ></afterSaleAmountConfig>

          <activateRules v-if="selectItem == '6'"></activateRules>
          <cancelRules v-if="selectItem == '7'"></cancelRules>
          <switchRules v-if="selectItem == '9'"></switchRules>
          <priceUpdate v-if="selectItem == '10'"></priceUpdate>
          <dyOrderRules v-if="selectItem == '11'"></dyOrderRules>

          <productConverPrice v-if="selectItem == '12'"></productConverPrice>
          <carAuthRules v-if="selectItem == '13'"></carAuthRules>
        </div>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import applyRules from './applyRules'
import activateRules from './activateRules'
import afterSaleChange from './afterSaleChange'
import afterSaleAmountConfig from './afterSaleAmountConfig'
import afterSaleRemake from './afterSaleRemake'
import cancelRules from './cancelRules.vue'
import switchRules from './switchRules.vue'
import priceUpdate from './priceUpdate.vue'
import dyOrderRules from './dyOrderRules.vue'
import productConverPrice from './productConverPrice.vue'
import carAuthRules from './carAuthRules.vue'
export default {
  components: {
    applyRules,
    activateRules,
    afterSaleChange,
    afterSaleAmountConfig,
    afterSaleRemake,
    cancelRules,
    switchRules,
    priceUpdate,
    dyOrderRules,
    productConverPrice,
    carAuthRules
  },
  created() {},
  mounted() {},
  data() {
    return {
      businessTypeOptions: [
        // { label: '首页', value: '1' },
        { label: '线上发行', value: '2' },
        { label: '线上售后更换', value: '3' },
        { label: '线上售后补办', value: '4' },
        { label: '更换/补办收费类型配置', value: '5' },

        { label: '自助激活', value: '6' },
        { label: '线上注销申请', value: '7' },
        { label: '产品转换', value: '9' },
        { label: '线上业务价格管理配置', value: '10' },
        { label: '抖音发行', value: '11' },
      ],
      selectItem: '2',
      activeName: '线上发行',
    }
  },
  methods: {
    handleClick(val) {
      // if (this.businessTypeOptions[val.index].value == '9') {
      //   return
      // }
      this.selectItem = this.businessTypeOptions[val.index].value
      this.activeName = this.businessTypeOptions[val.index].label
    },
    productClick(val) {
      console.log('val====>>>>', val)
      this.selectItem = val
      this.activeName = '产品转换'
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  height: 100%;
  position: relative;
  padding: 15px 20px;
  flex-flow: column;
  display: flex;
}
.tab-item {
  .el-tabs--border-card {
    box-shadow: none !important;
  }
  .el-tabs__content {
    padding: 15px !important;
  }
  .el-tabs--border-card {
    border-bottom: none !important;
  }
}
</style>