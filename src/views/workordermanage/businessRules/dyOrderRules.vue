<template>
  <div class="business-container">
    <div class="g-flex item-content">
      <div class="label">新办时需人工审核的情况：</div>
      <div class="g-flex g-flex-column">
        <!-- 车辆所有人 -->
        <div class="checkbox-item">
          <el-checkbox
            :indeterminate="belongerListIndeterminate"
            v-model="belongerCheckAll"
            @change="belongerCheckAllChange"
          >
            <span class="checkbox-label">车辆归属人</span></el-checkbox
          >
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="belongerChecked"
              @change="belongerCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in belongerList"
                :label="item.label"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>

        <!-- 客车 -->
        <div class="checkbox-item">
          <!-- <el-checkbox><span class="checkbox-label">支持客货类型</span></el-checkbox> -->
          <el-checkbox
            :indeterminate="vehicleListIndeterminate"
            v-model="vehicleCheckAll"
            @change="vehicleCheckAllChange"
            ><span class="checkbox-label">支持客货类型</span></el-checkbox
          >
          <div style="margin: 15px 0;">
            <el-checkbox-group
              v-model="carChecked"
              @change="carCheckItemChange"
              style="margin-bottom:15px"
            >
              <el-checkbox
                v-for="(item, index) in carType"
                :label="item.label"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
            <el-checkbox-group
              v-model="truckChecked"
              @change="truckCheckItemChange"
              style="margin-bottom:15px"
            >
              <el-checkbox
                v-for="(item, index) in truckType"
                :label="item.label"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
            <el-checkbox-group
              v-model="privateChecked"
              @change="privateCheckItemChange"
            >
              <el-checkbox
                v-for="(item, index) in privateType"
                :label="item.label"
                :key="index"
                >{{ item.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>
    <div class="g-flex g-flex-align-center item-content">
      <!-- <div class="label"></div> -->
      <div class="checkbox-item ">
        <el-checkbox
          :indeterminate="productListIndeterminate"
          v-model="productCheckAll"
          @change="productCheckAllChange"
          ><span class="checkbox-label">支持的产品</span>
        </el-checkbox>
        <div style="margin: 15px 0;">
          <el-checkbox-group
            v-model="productChecked"
            @change="productCheckItemChange"
            class="g-flex g-flex-column"
          >
            <div
              v-for="(item, index) in productType"
              :key="index"
              class="g-flex g-flex-column"
            >
              <el-checkbox :label="item.label">{{ item.label }} </el-checkbox>
              <div class=" business-desc g-flex g-flex-column">
                <!-- 商品IDtag -->
                <div class="g-flex g-flex-column">
                  <span
                    style="font-size:13px;color:#606266;font-weight:500;"
                    class="g-flex g-flex-align-start "
                    >商品ID:</span
                  >
                  <GoodsIdTag :goodsIdList="item.goodsIdList" :itemInfo="item" :configDetail="configDetail"/>
                </div>
                <span
                  style="font-size:13px;color:#606266;"
                  class="g-flex g-flex-align-start "
                  >业务办理须知：</span
                >
                <!-- <el-input type="textarea"
                          v-model="productType[index].data"
                          style="width:500px"></el-input> -->
                <quillEditor
                  :notice="productType[index].data"
                  :type="productType[index].value"
                  @editorChange="editorChange"
                ></quillEditor>
              </div>
            </div>
          </el-checkbox-group>
        </div>
      </div>
    </div>
    <div class="g-flex g-flex-horizontal-vertical">
      <el-button type="primary" @click="updateBusinessRules">修改</el-button>
    </div>
  </div>
</template>

<script>
import {
  vehicleType,
  truckVehicleType,
  passengerVehicleType,
  specialVehicleType
} from '@/common/const/optionsData.js'
import quillEditor from './components/quillEditor'
import GoodsIdTag from './components/goodsId-tag.vue'
import {
  tikTokConfigQuery,
  tikTokConfigure,
} from '@/api/workordermanage'

export default {
  name: '',
  props: {},
  components: { quillEditor, GoodsIdTag },
  data() {
    return {
      //产品类型
      productListIndeterminate: null,
      productCheckAll: '',
      productChecked: [],
      productType: [
        {
          label: '基础款',
          value: '1',
          data: '',
          goodsIdList: []
        },
        {
          label: '进阶款',
          value: '2',
          data: '',
          goodsIdList: []
        }
      ],
      basicNotice: '',
      advanceNotice: '',
      formData: {
        carOwner: '', // 车辆归属人
        isTrunk: '', // 客货类型
        truckModel: '', // 货车车型
        busModel: '', // 客车车型
        specialModel: '', // 专项车车型
        productType: '', // 产品类型
        basicNotice: '', // 基础款业务办理须知
        advanceNotice: '' // 进阶款
      },
      car: [],
      // 车辆归属人
      belongerListIndeterminate: null,
      belongerCheckAll: '',
      belongerChecked: [],
      belongerList: [
        {
          label: '本人车辆',
          value: '0'
        },
        {
          label: '他人车辆',
          value: '1'
        },
        {
          label: '单位车辆',
          value: '2'
        }
      ],
      vehicleList: [
        {
          label: '货车',
          value: '1'
        },
        {
          label: '客车',
          value: '2'
        },
        {
          label: '专项车',
          value: '3'
        }
      ],
      // 客货类型
      vehicleListIndeterminate: null,
      vehicleCheckAll: '',
      //客车
      carListIndeterminate: null,
      carCheckAll: '',
      carChecked: [],
      carType: passengerVehicleType,

      //货车
      truckListIndeterminate: null,
      truckCheckAll: '',
      truckChecked: [],
      truckType: truckVehicleType,

      //专车
      privateListIndeterminate: null,
      privateCheckAll: '',
      privateChecked: [],
      privateType: specialVehicleType,
      configDetail: {}
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getConfig()
  },
  methods: {
    //车辆所有人
    belongerCheckAllChange(val) {
      this.belongerChecked = val ? this.belongerList : []
      this.belongerChecked = this.belongerChecked.map(item => {
        return item.label
      })
      this.belongerListIndeterminate = false
      let tmp = [
        ...new Set(this.formatData(this.belongerList, this.belongerChecked))
      ]
      this.formData.carOwner = this.sortAndSum(tmp)
    },
    belongerCheckItemChange(value) {
      let checkedCount = value.length
      this.belongerCheckAll = checkedCount === this.belongerList.length
      this.belongerListIndeterminate =
        checkedCount > 0 && checkedCount < this.belongerList.length
      let tmp = [
        ...new Set(this.formatData(this.belongerList, this.belongerChecked))
      ]
      this.formData.carOwner = this.sortAndSum(tmp)
    },
    //客货类型
    vehicleCheckAllChange(val) {
      console.log(val, '<<---------val')
      if (val) {
        this.carChecked = this.carType.map(item => {
          return item.label
        })
        this.truckChecked = this.truckType.map(item => {
          return item.label
        })
        this.privateChecked = this.privateType.map(item => {
          return item.label
        })
      } else {
        this.carChecked = []
        this.truckChecked = []
        this.privateChecked = []
        this.vehicleCheckItemChange([])
      }
      this.carCheckItemChange(this.carChecked)
      this.truckCheckItemChange(this.truckChecked)
      this.privateCheckItemChange(this.privateChecked)

      //   this.vehicleChecked = val ? this.vehicleList : []
      //   this.vehicleChecked = this.vehicleChecked.map((item) => {
      //     return item.label
      //   })
      this.vehicleListIndeterminate = false

      //   let tmp = [
      //     ...new Set(this.formatData(this.vehicleList, this.vehicleChecked)),
      //   ]

      //   this.formData.isTrunk = this.sortAndSum(tmp)
    },
    vehicleCheckItemChange(value) {
      let checkedCount = value.length
      this.vehicleCheckAll = checkedCount === this.vehicleList.length
      this.vehicleListIndeterminate =
        checkedCount > 0 && checkedCount < this.vehicleList.length
      let tmp = [...new Set(this.formatData(this.vehicleList, value))]
      this.formData.isTrunk = this.sortAndSum(tmp)
      console.log(this.formData.isTrunk, '<<---------this.formData.isTrunk')
    },

    //客车
    carCheckAllChange(val) {
      this.carChecked = val ? this.carType : []
      this.carChecked = this.carChecked.map(item => {
        return item.label
      })
      this.carListIndeterminate = false

      let tmp = [...new Set(this.formatData(this.carType, this.carChecked))]

      this.formData.busModel = this.sortAndSum(tmp)
    },
    carCheckItemChange(value) {
      let checkedCount = value.length
      this.carCheckAll = checkedCount === this.carType.length
      this.carListIndeterminate =
        checkedCount > 0 && checkedCount < this.carType.length
      let tmp = [...new Set(this.formatData(this.carType, this.carChecked))]

      console.log(value, this.carChecked, '<<---------value,this.carChecked')

      let isInArr = tmp.indexOf('0')
      if (isInArr != -1) {
        this.car.push('客车')
        for (let i = 0; i < tmp.length; i++) {
          if (tmp[i] == '0') {
            tmp.splice(i, 1)
          }
        }
      } else {
        for (let i = 0; i < this.car.length; i++) {
          if (this.car[i] == '客车') {
            this.car.splice(i, 1)
          }
        }
      }
      let tmpArr = [...new Set(this.car)]

      this.vehicleCheckItemChange(tmpArr)

      this.formData.busModel = this.sortAndSum(tmp)
      if (
        this.carChecked.length != this.carType.length ||
        this.truckChecked.length != this.truckType.length ||
        this.privateChecked != this.privateType.length
      ) {
        this.vehicleListIndeterminate = true
      }
      if (
        this.carChecked.length == 0 &&
        this.truckChecked.length == 0 &&
        this.privateChecked.length == 0
      ) {
        this.vehicleListIndeterminate = null
      }
    },

    //货车
    truckCheckAllChange(val) {
      this.truckChecked = val ? this.truckType : []
      this.truckChecked = this.truckChecked.map(item => {
        return item.label
      })
      this.truckListIndeterminate = false

      let tmp = [...new Set(this.formatData(this.truckType, this.truckChecked))]

      console.log(tmp)
      this.formData.truckModel = this.sortAndSum(tmp)
    },
    truckCheckItemChange(value) {
      let checkedCount = value.length
      this.truckCheckAll = checkedCount === this.truckType.length
      this.truckListIndeterminate =
        checkedCount > 0 && checkedCount < this.truckType.length
      let tmp = [...new Set(this.formatData(this.truckType, this.truckChecked))]

      let isInArr = tmp.indexOf('0')
      if (isInArr != -1) {
        this.car.push('货车')
        for (let i = 0; i < tmp.length; i++) {
          if (tmp[i] == '0') {
            tmp.splice(i, 1)
          }
        }
      } else {
        for (let i = 0; i < this.car.length; i++) {
          if (this.car[i] == '货车') {
            this.car.splice(i, 1)
          }
        }
      }

      let tmpArr = [...new Set(this.car)]
      this.vehicleCheckItemChange(tmpArr)

      this.formData.truckModel = this.sortAndSum(tmp)
      if (
        this.carChecked.length != this.carType.length ||
        this.truckChecked.length != this.truckType.length ||
        this.privateChecked != this.privateType.length
      ) {
        this.vehicleListIndeterminate = true
      }
      if (
        this.carChecked.length == 0 &&
        this.truckChecked.length == 0 &&
        this.privateChecked.length == 0
      ) {
        this.vehicleListIndeterminate = null
      }
    },

    //专项作业车
    privateCheckAllChange(val) {
      this.privateChecked = val ? this.privateType : []
      this.privateChecked = this.privateChecked.map(item => {
        return item.label
      })
      this.privateListIndeterminate = false

      let tmp = [
        ...new Set(this.formatData(this.privateType, this.privateChecked))
      ]

      this.formData.specialModel = this.sortAndSum(tmp)
    },
    privateCheckItemChange(value) {
      let checkedCount = value.length
      this.privateCheckAll = checkedCount === this.privateType.length
      this.privateListIndeterminate =
        checkedCount > 0 && checkedCount < this.privateType.length
      let tmp = [
        ...new Set(this.formatData(this.privateType, this.privateChecked))
      ]
      let isInArr = tmp.indexOf('0')
      if (isInArr != -1) {
        this.car.push('专项车')
        for (let i = 0; i < tmp.length; i++) {
          if (tmp[i] == '0') {
            tmp.splice(i, 1)
          }
        }
      } else {
        for (let i = 0; i < this.car.length; i++) {
          if (this.car[i] == '专项车') {
            this.car.splice(i, 1)
          }
        }
      }

      let tmpArr = [...new Set(this.car)]
      console.log(tmpArr, '<<---------tmpArr000')
      this.vehicleCheckItemChange(tmpArr)
      this.formData.specialModel = this.sortAndSum(tmp)
      if (
        this.carChecked.length != this.carType.length ||
        this.truckChecked.length != this.truckType.length ||
        this.privateChecked != this.privateType.length
      ) {
        this.vehicleListIndeterminate = true
      }
      if (
        this.carChecked.length == 0 &&
        this.truckChecked.length == 0 &&
        this.privateChecked.length == 0
      ) {
        this.vehicleListIndeterminate = null
      }
    },

    //产品
    productCheckAllChange(val) {
      this.productChecked = val ? this.productType : []
      this.productChecked = this.productChecked.map(item => {
        return item.label
      })
      this.productListIndeterminate = false
      let tmp = [
        ...new Set(this.formatData(this.productType, this.productChecked))
      ]

      if (tmp.length == 2) {
        this.formData.productType = '12'
      } else {
        this.formData.productType = tmp[0]
      }
    },
    productCheckItemChange(value) {
      let checkedCount = value.length
      this.productCheckAll = checkedCount === this.productType.length
      this.productListIndeterminate =
        checkedCount > 0 && checkedCount < this.productType.length
      let tmp = [
        ...new Set(this.formatData(this.productType, this.productChecked))
      ]

      if (tmp.length == 2) {
        this.formData.productType = '99'
      } else {
        this.formData.productType = tmp[0]
      }
    },

    //通过选中数组中的label匹配出对象中的value值
    formatData(obj, selected) {
      let arr = []
      for (let i = 0; i < selected.length; i++) {
        for (let j = 0; j < obj.length; j++) {
          if (selected[i] == obj[j].label) {
            arr.push(obj[j].value)
          }
        }
      }
      return arr
    },

    //匹配产品描述
    matchProductData(selected) {
      for (let i = 0; i < selected.length; i++) {
        for (let j = 0; j < this.productType.length; j++) {
          if (selected[i] == '1' && selected[i] == this.productType[j].value) {
            this.formData.basicNotice = this.productType[j].data
          }
          if (selected[i] == '2' && selected[i] == this.productType[j].value) {
            this.formData.advanceNotice = this.productType[j].data
          }
        }
      }
    },

    async updateHandle() {
      this.matchProductData(
        this.formatData(this.productType, this.productChecked)
      )
      let params = JSON.parse(JSON.stringify(this.formData))
      let baProductId = this.productType[0].goodsIdList.join(',')
      let adProductId = this.productType[1].goodsIdList.join(',')
      params.baProductId = baProductId
      params.adProductId = adProductId
      console.log(params, 'paramsparamsparams')
      
      this.startLoading()
      let res = await tikTokConfigure(params)

      if (res.code == 200) {
        this.endLoading()
        this.getConfig()
        this.car = []
        this.$message.success('更新成功！')
      } else {
        this.endLoading()
        this.initData()
        this.$message.error(res.msg)
      }
      this.endLoading()
    },
    //更新业务规则
    updateBusinessRules() {
      this.$confirm('请确认是否修改', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.updateHandle()
        })
        .catch(() => {})
    },

    //查询配置
    async getConfig() {
      let params = {}
      this.startLoading()
      let res = await tikTokConfigQuery()
      if (res.code == 200) {
        this.configDetail = res.data || {}
        console.log(this.configDetail, '<<---------this.configDetail')
        this.backfillField()
        this.endLoading()
      } else {
        this.endLoading()
        this.initData()
        this.$message.error(res.msg)
      }

      this.endLoading()
    },
    //根据查询出的配置回填界面
    backfillField() {
      this.belongerChecked = []
      this.vehicleChecked = []
      this.carChecked = []
      this.truckChecked = []
      this.privateChecked = []
      this.productChecked = []
      this.car = []

      if (this.configDetail.productType != null) {
        let tmpArr = []
        if (this.configDetail.productType == '99') {
          tmpArr = ['1', '2']
        } else {
          tmpArr = this.configDetail.productType.split('')
        }

        for (let i = 0; i < this.productType.length; i++) {
          for (let j = 0; j < tmpArr.length; j++) {
            if (tmpArr[j] == this.productType[i].value) {
              this.productChecked.push(this.productType[i].label)
            }
            console.log(
              tmpArr[j] == '1',
              this.productType[i].value == '1',
              '<<---------'
            )
            if (tmpArr[j] == '1' && this.productType[i].value == '1') {
              this.$set(
                this.productType[i],
                'data',
                this.configDetail.basicNotice
              )
            }
            if (tmpArr[j] == '2' && this.productType[i].value == '2') {
              this.$set(
                this.productType[i],
                'data',
                this.configDetail.advanceNotice
              )
            }
          }
        }
        this.productListIndeterminate = tmpArr.length == this.productType.length
        this.productCheckItemChange(this.productChecked)
      }
      if (this.configDetail.carOwner != null) {
        let tmpArr = this.configDetail.carOwner.split('')
        for (let i = 0; i < this.belongerList.length; i++) {
          for (let j = 0; j < tmpArr.length; j++) {
            if (tmpArr[j] == this.belongerList[i].value) {
              this.belongerChecked.push(this.belongerList[i].label)
            }
          }
        }
        this.belongerListIndeterminate =
          tmpArr.length == this.belongerList.length
        this.belongerCheckItemChange(this.belongerChecked)
      }

      if (this.configDetail.isTrunk != null) {
        let tmpArr = this.configDetail.isTrunk.split('')
        let catTypeArr = []
        for (let i = 0; i < tmpArr.length; i++) {
          if (tmpArr[i] == '1') {
            this.truckChecked.push('货车')
            catTypeArr.push('货车')
          }
          if (tmpArr[i] == '2') {
            this.carChecked.push('客车')
            catTypeArr.push('客车')
          }
          if (tmpArr[i] == '3') {
            this.privateChecked.push('专项车')
            catTypeArr.push('专项车')
          }
        }
        this.vehicleCheckItemChange(catTypeArr)
      }
      if (this.configDetail.busModel != null) {
        let tmpArr = this.configDetail.busModel.split('')

        for (let i = 0; i < this.carType.length; i++) {
          for (let j = 0; j < tmpArr.length; j++) {
            if (tmpArr[j] == this.carType[i].value) {
              this.carChecked.push(this.carType[i].label)
            }
          }
        }
        this.carListIndeterminate = tmpArr.length == this.carType.length
        this.carCheckItemChange(this.carChecked)
      }
      if (this.configDetail.truckModel != null) {
        let tmpArr = this.configDetail.truckModel.split('')

        for (let i = 0; i < this.truckType.length; i++) {
          for (let j = 0; j < tmpArr.length; j++) {
            if (tmpArr[j] == this.truckType[i].value) {
              this.truckChecked.push(this.truckType[i].label)
            }
          }
        }
        this.truckListIndeterminate = tmpArr.length == this.truckType.length
        this.truckCheckItemChange(this.truckChecked)
      }

      if (this.configDetail.specialModel != null) {
        let tmpArr = this.configDetail.specialModel.split('')

        for (let i = 0; i < this.privateType.length; i++) {
          for (let j = 0; j < tmpArr.length; j++) {
            if (tmpArr[j] == this.privateType[i].value) {
              this.privateChecked.push(this.privateType[i].label)
            }
          }
        }
        this.privateListIndeterminate = tmpArr.length == this.privateType.length
        this.privateCheckItemChange(this.privateChecked)
      }

      if (
        this.carChecked.length == this.carType.length &&
        this.truckChecked.length == this.truckType.length &&
        this.privateChecked.length == this.privateType.length
      ) {
        this.vehicleListIndeterminate = false
      }

      // 回填商品ID
      if(this.configDetail.baProductId){
        this.productType[0].goodsIdList = this.configDetail.baProductId.split(',')
      }
      if(this.configDetail.adProductId){
        this.productType[1].goodsIdList = this.configDetail.adProductId.split(',')
      }
    },
    editorChange(val) {
      console.log(val, '<<---------val')
      for (let i = 0; i < this.productType.length; i++) {
        if (this.productType[i].value == '1' && val.type == '1') {
          //   this.formData.basicNotice = val.content
          this.$set(this.productType[i], 'data', val.content)
        }
        if (this.productType[i].value == '2' && val.type == '2') {
          //   this.formData.advanceNotice = val.content
          this.$set(this.productType[i], 'data', val.content)
        }
      }
    },
    //匹配出的value数组从大到小排序并拼接成字符串
    sortAndSum(arr) {
      let arr1 = arr.sort(function(a, b) {
        return a - b
      })
      let sum = ''
      arr1.forEach(item => (sum = sum + item))
      return sum
    }
  }
}
</script>

<style lang='scss' scoped>
.business-container {
  .checkbox-item {
    margin: 0px 20px 20px 20px;
  }
  .checkbox-label {
    font-weight: bold;
  }
  .label {
    width: 220px;
  }
  .business-desc {
    margin: 10px;
  }
  .deposit-money {
    width: 150px;
    margin-left: 10px;
    &:before {
      content: '*';
      color: red;
    }
  }
}
.item-content {
  border-top: 0.5px solid #f2f2f2;
  border-bottom: 0.5px solid #f2f2f2;
  border-left: 1px solid #f2f2f2;
  border-right: 1px solid #f2f2f2;
  padding: 30px;
}
</style>
