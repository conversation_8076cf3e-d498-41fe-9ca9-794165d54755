import {
  getVehicleColor,
  getVehicleType,
  getCarType,
} from '@/common/method/formatOptions'

import {
  licenseColorOptionAll,
  vehicleTypeAll,
  customerTypeAll,
  applyChannel,
} from '@/common/const/optionsData.js'
import { convertFormat } from '@/utils'

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'productName',
      width: 220,
      label: '产品名称'
    },
    {
      prop: 'isTrunk',
      label: '支持客货类型',
      // formatter: (row) => {
      //   return getVehicleType(row)
      // }
    },
    {
      prop: 'carType',
      label: '车辆类型',
      // formatter: (row) => {
      //   return getCarType(row)
      // }
    },
    {
      prop: 'deviceType',
      label: '设备类型',
      // formatter: (row) => {
      //   return row == 1 ? '基础款' : row == 2 ?'进阶款' :''
      // }
    },
    {
      prop: 'benefitServiceFee',
      label: '权益服务费（元）',
    },
    {
      prop: 'activationDeposit',
      label: '激活保证金（元）',
    },
    // {
    //   prop: 'productType',
    //   label: '首页广告金额',
    //   formatter: (row) => {
    //     return row * 100
    //   },
    // },
    {
      prop: 'action',
      // fixed: 'right',
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  console.log(_this, '_this')
  return [
    {
      type: 'select',
      field: 'productCode',
      label: '产品名称：',
      default: '',
      options: [
        {
          label: '捷通日日通记账卡',
          value: '5'
        },
        {
          label: '捷通次次顺记账卡',
          value: '10'
        },
      ]
    },
    // {
    //   type: 'input',
    //   field: 'vehicleNo',
    //   label: '车牌号：',
    //   default: '',
    // },
    // {
    //   type: 'select',
    //   field: 'vehicleColor',
    //   label: '车牌颜色：',
    //   placeholder: '车牌颜色',
    //   options: licenseColorOptionAll
    // },
    // {
    //   type: 'input',
    //   field: 'cardNo',
    //   label: 'ETC号：',
    //   isCollapse: true,
    //   default: '',
    // },
    // {
    //   type: 'input',
    //   field: 'obuNo',
    //   label: 'OBU号：',
    //   isCollapse: true,
    //   default: '',
    // },
    // {
    //   type: 'select',
    //   field: 'isTrunk',
    //   label: '客货类型：',
    //   isCollapse: true,
    //   options: vehicleTypeAll
    // },
    // {
    //   type: 'input',
    //   field: 'custName',
    //   label: '用户名称：',
    //   isCollapse: true,
    //   default: '',
    // },
    // {
    //   type: 'input',
    //   field: 'mobile',
    //   label: '手机号：',
    //   isCollapse: true,
    //   default: '',
    // },
  ]
}
