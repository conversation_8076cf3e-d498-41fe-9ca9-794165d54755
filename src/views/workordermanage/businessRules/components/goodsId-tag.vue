<template>
  <div class="goodsId-tag">
    <el-tag
      :key="tag"
      v-for="tag in goodsIdList"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      {{ tag }}
    </el-tag>
    <el-input
      class="input-new-tag"
      v-if="inputVisible"
      v-model="inputValue"
      ref="saveTagInput"
      size="small"
      @keyup.enter.native="handleInputConfirm"
      @blur="handleInputConfirm"
    >
    </el-input>
    <el-button v-else class="button-new-tag" size="small" @click="showInput"
      >+ 新增商品ID</el-button
    >
  </div>
</template>

<script>
import { tikTokConfigProduct } from '@/api/workordermanage'

export default {
  props: {
    goodsIdList: {
      type: Array,
      default: () => []
    },
    itemInfo: {
      type: Object,
      default: () => {}
    },
    configDetail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // goodsIdList: ['标签一', '标签二', '标签三'],
      inputVisible: false,
      inputValue: ''
    }
  },
  methods: {
    handleClose(tag) {
      let params = {
        id: this.configDetail.id,
        operateType: this.itemInfo.value == 1 ? 1 : 3,
        productId: tag
      }
      this.$confirm('请确认是否翻除该商品ID？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await tikTokConfigProduct(params)
        if (res.code == 200) {
          this.goodsIdList.splice(this.goodsIdList.indexOf(tag), 1)
        }
      })
    },

    showInput() {
      this.inputVisible = true
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus()
      })
    },

    handleInputConfirm() {
      let inputValue = this.inputValue

      if (inputValue) {
        let params = {
          id: this.configDetail.id,
          operateType: this.itemInfo.value == 1 ? 0 : 2,
          productId: inputValue
        }
        this.$confirm(`请确认是否新增该${inputValue}商品ID？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          console.log(params)
          let res = await tikTokConfigProduct(params)
          if (res.code == 200) {
            this.goodsIdList.push(inputValue)
          }
        })
      }
      this.inputVisible = false
      this.inputValue = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.goodsId-tag {
  margin: 10px 10px 20px 0;
  .el-tag + .el-tag {
    // margin:10px 10px 20px 0 ;
  }
  .el-tag {
    margin-right: 10px;
  }
  .button-new-tag {
    margin-left: 10px;
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
}
</style>