<template>
  <div>
    <el-dialog
      title="导入结果"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="70%"
      center
    >
      <div class="account-list">
        <div class="import-text">
          本次导入共[{{ failLen + successLen }}]条数据，<span
            :class="failLen > 0 ? 'success-text' : ''"
            >{{ '[' + successLen + ']' }}</span
          >
          条导入成功，<span :class="failLen > 0 ? 'danger-text' : ''">{{
            '[' + failLen + ']'
          }}</span
          >条导入失败，详细信息如下:
        </div>
        <div class="table-box">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            height="300"
            style="width: 100%"
          >
            <!-- <el-table-column prop="custName" align="center" label="用户名称" /> -->
            <el-table-column
              prop="vehicleColor"
              label="车牌颜色"
              align="center"
              width="120"
            >
              <template slot-scope="scope">
                {{ getVehicleColor(scope.row.vehicleColor) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="vehicleNo"
              label="车牌号"
              align="center"
              width="130"
            />
            <el-table-column
              prop="aftProductName"
              label="可选自营产品配置"
              width="280"
              align="center"
            >
              <template slot-scope="scope">
                <!-- <el-checkbox-group v-model="scope.row.aftProductType">
                  <el-checkbox disabled label="5">日日通记账卡</el-checkbox>
                  <el-checkbox disabled label="10">次次顺记账卡</el-checkbox>
                </el-checkbox-group> -->
                <span
                  v-if="
                    scope.row.aftProductType.includes('5') ||
                    scope.row.aftProductType.includes('9')
                  "
                  >日日通记账卡</span
                >
                <span v-if="scope.row.aftProductType.length > 2">、</span>
                <span v-if="scope.row.aftProductType.includes('10')"
                  >次次顺记账卡</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="vehicleColor"
              label="有偿转换价格配置"
              align="center"
              width="160"
            >
              <template slot-scope="scope">
                {{ scope.row.isFree == '1' ? '免费' : '付费' }}
              </template>
            </el-table-column>
            <el-table-column prop="userName" label="配置结果" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == '1'" type="success"
                  >配置成功</el-tag
                >
                <el-tag v-else type="danger">失败</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="checkResult"
              align="center"
              label="失败原因"
              width="220"
            >
              <template slot-scope="scope">
                {{ scope.row.checkResult || '' }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="failLen > 0" @click="exportFail" type="primary"
          >导 出</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
var moment = require('moment')
import {
  getbusinessType,
  getVehicleColor,
  getnowstate,
  getProductTypeOptions,
  getApplyChannelOptions,
  getCheckTypeOptions,
  getVehicleType,
  getCarType,
  getallGxCardType,
} from '@/common/method/formatOptions'
import { converExport } from '@/api/refund'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    result: {
      type: Object,
    },
  },

  data() {
    return {
      dialogVisible: false,
      loading: false,
      center: 'center',
      tableData: [],
      failLen: 0,
      successLen: 0,
    }
  },

  watch: {
    visible: function (val) {
      this.init()
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: {},

  computed: {},
  created() {
    this.init()
  },
  methods: {
    getbusinessType,
    getVehicleColor,
    getnowstate,
    getProductTypeOptions,
    getApplyChannelOptions,
    getCheckTypeOptions,
    getVehicleType,
    getCarType,
    getallGxCardType,
    init() {
      console.log('result', this.result)
      if (!this.result) {
        return
      }
      let failLen = this.result.failRecords ? this.result.failRecords.length : 0
      let successLen = this.result.successRecords
        ? this.result.successRecords.records.length
        : 0
      this.failLen = failLen
      this.successLen = successLen
      let failRecords = []
      let successRecords = []
      if (this.result.failRecords) {
        failRecords = JSON.parse(JSON.stringify(this.result.failRecords))
        console.log('failRecords', failRecords)
        for (let i = 0; i < failRecords.length; i++) {
          let str = failRecords[i].aftProductType
          if (Array.isArray(str)) {
            return
          }
          let strArr = str.split('|')
          console.log('strArr', strArr)
          failRecords[i].aftProductType = strArr
          failRecords[i].vehicleColor = failRecords[i].carColor
          failRecords[i].vehicleNo = failRecords[i].carNo
          failRecords[i].isFree = failRecords[i].free
          failRecords[i].status = '0'
        }
      }
      if (this.result.successRecords) {
        successRecords = this.result.successRecords.records
        for (let index = 0; index < successRecords.length; index++) {
          let sucStr = successRecords[index].aftProductType
          if (Array.isArray(sucStr)) {
            return
          }
          let sucStrArr = sucStr.split('|')
          // console.log('strArr', strArr)
          successRecords[index].aftProductType = sucStrArr
        }
      }
      console.log('failRecords', failRecords)
      console.log('successRecords', successRecords)
      if (failLen > 0 && successLen > 0) {
        let newArr = failRecords.concat(successRecords)
        this.tableData = newArr
      } else if (failLen > 0 && successLen == 0) {
        this.tableData = failRecords
      } else if (failLen == 0 && successLen > 0) {
        this.tableData = successRecords
      }

      console.log('this.result.failRecords', this.result.failRecords)
      // if()

      // for (let index = 0; index < 100; index++) {
      //   this.tableData[index] = {
      //     aftProductType: '5|9|10',
      //     carColor: '0',
      //     carNo: '桂BA077A',
      //   }
      // }
      // this.tableData = []

      this.$nextTick(() => {
        this.dialogVisible = this.visible
      })
    },
    exportFail() {
      let params = {
        failRecords: this.result.failRecords,
      }
      this.downloadClick(params)
    },
    async downloadClick(params) {
      this.startLoading()
      const response = await converExport(params)
      // window.open(uploadPath)
      const link = document.createElement('a')
      let blob = new Blob([response]) //构造一个blob对象来处理数据
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      // let titleObj = this.refundType.filter(
      //   (item) => item.value == this.search.refund_type
      // )[0]
      link.download = `车辆权限导入失败.xlsx` //下载的文件名
      document.body.appendChild(link)
      link.click() // 执行下载
      document.body.removeChild(link) // 释放标签
      this.endLoading()

    },
  },
}
</script>
<style lang="scss"  scoped>
.import-text {
  background: #f8f8f8;
  color: #666666;
  padding: 12px 20px;
  font-size: 14px;
}

.success-text {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}
.danger-text {
  background-color: #fef0f0;
  border-color: #fde2e2;
  color: #f56c6c;
}
.repayment {
  position: relative;
  .view-item {
    margin-bottom: 6px;
  }
  .captcha {
    width: 120px;
    height: 32px;
    margin-left: 10px;
  }
  .sendSMS {
    width: 120px;
    color: #409eff;
    margin-left: 10px;
  }
}
.photograph {
  position: absolute;
  top: 0;
  left: 50%;
}
</style>