<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:富文本编辑器
  * @author:zhang<PERSON>
  * @date:2023/04/11 12:43:32
-->
<template>
  <div class="editor-container">

    <quill-editor class="ql-editor"
                  v-model="content"
                  ref="myQuillEditor"
                  :options="editorOption"
                  @blur="onEditorBlur($event)"
                  @focus="onEditorFocus($event)"
                  @change="onEditorChange($event)">
    </quill-editor>
  </div>
</template>
 
<script>
export default {
  props: {
    notice: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      content: '', //双向数据绑定数据
      // 富文本编辑器配置
      editorOption: {
        modules: {
          toolbar: [
            ['bold', 'italic', 'underline', 'strike'], // 加粗 斜体 下划线 删除线
            ['blockquote', 'code-block'], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: 'ordered' }, { list: 'bullet' }], // 有序、无序列表
            [{ script: 'sub' }, { script: 'super' }], // 上标/下标
            [{ indent: '-1' }, { indent: '+1' }], // 缩进
            [{ direction: 'rtl' }], // 文本方向
            [
              {
                size: ['small', false, 'large'],
              },
            ], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            // [{ font: ['songti'] }], // 字体种类
            [{ align: [] }], // 对齐方式
            ['clean'], // 清除文本格式
            // ['image', 'video'] // 链接、图片、视频
          ],
        },
        placeholder: '请输入正文',
      },
    }
  },
  methods: {
    // 失去焦点事件
    onEditorBlur(quill) {},
    // 获得焦点事件
    onEditorFocus(quill) {},
    // 准备富文本编辑器
    onEditorReady(quill) {},
    // 内容改变事件
    onEditorChange({ quill, html, text }) {
      this.content = html
      let params = {
        content: this.content,
        type: this.type,
      }
      this.$emit('editorChange', params)
    },
  },
  watch: {
    notice(val) {
      if (val) {
        this.content = val
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.editor-container {
  //   height: 200px;
}
::v-deep .ql-container {
  height: 120px !important;
}

/*加上height和滚动属性就可以，滚动条样式是系统默认样式，可能不同*/
::v-deep .ql-picker-options {
  height: 100px;
  overflow-y: scroll;
}

::v-deep .ql-picker-item[data-value='1']::before,
::v-deep .ql-picker-label[data-value='1']::before {
  content: '一级标题' !important;
}
::v-deep .ql-picker-item[data-value='2']::before,
::v-deep .ql-picker-label[data-value='2']::before {
  content: '二级标题' !important;
}
::v-deep .ql-picker-item[data-value='3']::before,
::v-deep .ql-picker-label[data-value='3']::before {
  content: '三级标题' !important ;
}
::v-deep .ql-picker-item[data-value='4']::before,
::v-deep .ql-picker-label[data-value='4']::before {
  content: '四级标题' !important;
}
::v-deep .ql-picker-item[data-value='5']::before,
::v-deep .ql-picker-label[data-value='5']::before {
  content: '五级标题' !important;
}
::v-deep .ql-picker-item[data-value='6']::before,
::v-deep .ql-picker-label[data-value='6']::before {
  content: '六级标题' !important;
}
</style>