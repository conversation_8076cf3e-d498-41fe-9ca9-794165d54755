<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上订单售后更换业务金额配置
  * @author:zhangys
  * @date:2023/04/26 14:02:51
-->
<template>
  <div>

    <div>
      <el-table :data="tableData"
                :span-method="objectSpanMethod"
                border
                style="width: 100%; margin-top: 20px"
                size="medium">
        <el-table-column prop="type"
                         label="线上业务类型"
                         width="180"
                         align="center">
          <template slot-scope="scope">
            {{scope.row.type=='1'?'更换业务':'补办业务'}}
          </template>
        </el-table-column>
        <el-table-column prop="equipmentAppearanceType"
                         label="设备外观"
                         align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.equipmentAppearanceType">
              {{getChangeEquipmentAppearance(scope.row.equipmentAppearanceType)}}
            </span>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceType"
                         label="设备类型"
                         align="center">
          <template slot-scope="scope">
            {{getChangeEquipment(scope.row.deviceType)}}
          </template>
        </el-table-column>
        <el-table-column prop="exchangeFeeType"
                         label="更换类型"
                         align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.exchangeFeeType||scope.row.exchangeFeeType=='0'">
              {{getChangeType(scope.row.exchangeFeeType)}}
            </span>
            <span v-else>/</span>
          </template>
        </el-table-column>
        <el-table-column prop="fee"
                         label="应付金额(元)"
                         align="center">
          <template slot-scope="scope">
            <span v-if="isEdit">
              <el-input size="small"
                        v-model="scope.row.fee" />

            </span>
            <span v-else>{{scope.row.fee}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin:16px 0">
        <el-button size="medium"
                   type="primary"
                   plain
                   style="width: 100%; border-style: dashed"
                   @click="edit"
                   v-if="!isEdit">修改</el-button>
        <el-button size="medium"
                   type="primary"
                   plain
                   style="width: 100%; border-style: dashed"
                   @click="saveConfig"
                   v-else>保存修改</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getChangeEquipment,
  getChangeEquipmentAppearance,
  getChangeType,
} from '@/common/method/formatOptions.js'
import float from '@/common/method/float.js'

export default {
  name: '',
  props: {},
  components: {},
  data() {
    return {
      tableData: [],
      rowSpanArr: [],
      rowSpanArr2: [],
      isEdit: false,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getConfigDetail()
  },
  methods: {
    getChangeEquipment,
    getChangeEquipmentAppearance,
    getChangeType,
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    //格式化参数，合并单元格使用
    handleTableData(tableData) {
      let rowSpanArr = [],
        position = 0,
        rowSpanArr2 = [],
        position2 = 0

      for (let [index, item] of tableData.entries()) {
        if (index == 0) {
          rowSpanArr.push(1)
          position = 0
          rowSpanArr2.push(1)
          position2 = 0
        } else {
          if (item.type == tableData[index - 1].type) {
            rowSpanArr[position] += 1 //项目名称相同，合并到同一个数组中
            rowSpanArr.push(0)
          } else {
            rowSpanArr.push(1)
            position = index
          }

          if (
            item.equipmentAppearanceType ==
            tableData[index - 1].equipmentAppearanceType
          ) {
            rowSpanArr2[position2] += 1 //项目名称相同，合并到同一个数组中
            rowSpanArr2.push(0)
          } else {
            rowSpanArr2.push(1)
            position2 = index
          }
        }
      }
      this.rowSpanArr = rowSpanArr
      this.rowSpanArr2 = rowSpanArr2
      console.log(this.rowSpanArr,this.rowSpanArr2)
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      console.log(this.rowSpanArr,this.rowSpanArr2)
      if (columnIndex === 0) {
        let rowSpan = this.rowSpanArr[rowIndex]
        return {
          rowspan: rowSpan, //行
          colspan: 1, //列
        }
      }
      if (columnIndex === 1) {
        let rowSpan = this.rowSpanArr2[rowIndex]
        return {
          rowspan: rowSpan, //行
          colspan: 1, //列
        }
      }
    },
    edit() {
      this.isEdit = true
    },
    //获取配置详情
    getConfigDetail() {
      let params = {}
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleConfig,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            let arr = res.data
            let tmpArr1 = [],
              tmpArr2 = []

            tmpArr1 = arr.slice(0, arr.length - 3)
            tmpArr2 = arr.slice(arr.length - 3)
            tmpArr1.sort(this.sortEquipmentAppearanceType)

            this.tableData = [...tmpArr1, ...tmpArr2]
            for (let i = 0; i < this.tableData.length; i++) {
              this.$set(
                this.tableData[i],
                'fee',
                this.moneyFilter(this.tableData[i].fee)
              )
            }
            this.handleTableData(this.tableData)
          } else {
            this.endLoading()
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    sortEquipmentAppearanceType(a, b) {
      return a.equipmentAppearanceType - b.equipmentAppearanceType
    },
    //baocun 配置信息
    saveConfig() {
      let params = JSON.parse(JSON.stringify(this.tableData))
      for (let i = 0; i < params.length; i++) {
        this.$set(params[i], 'fee', params[i].fee * 100)
      }
      let data = {
        data: params,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.afterSaleConfigSave,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.endLoading()
            this.isEdit = false
            this.$message.success('修改配置成功！')
            this.getConfigDetail()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang='scss' scoped>
</style>