<template>
  <div class="login-container">
    <el-form ref="loginForm"
             :model="loginForm"
             :rules="loginRules"
             class="login-form"
             autocomplete="on"
             label-position="left">
      <div class="title-container">
        <h1 class="title">{{ title }}</h1>
      </div>

      <el-form-item prop="username">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input ref="username"
                  v-model="loginForm.username"
                  placeholder="用户名"
                  name="username"
                  type="text"
                  tabindex="1"
                  autocomplete="on" />
      </el-form-item>

      <el-tooltip v-model="capsTooltip"
                  content="Caps lock is On"
                  placement="right"
                  manual>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
          <el-input :key="passwordType"
                    ref="password"
                    v-model="loginForm.password"
                    :type="passwordType"
                    placeholder="密码"
                    name="password"
                    tabindex="2"
                    autocomplete="on"
                    @keyup.native="checkCapslock"
                    @blur="capsTooltip = false"
                    @keyup.enter.native="handleLogin" />
          <span class="show-pwd"
                @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
          </span>
        </el-form-item>
      </el-tooltip>
      <el-form-item prop="code"
                    style="width: 60%;float: left;">
        <span class="svg-container">
          <svg-icon icon-class="validCode" />
        </span>
        <el-input ref="username"
                  v-model="loginForm.code"
                  placeholder="验证码"
                  name="username"
                  type="text"
                  tabindex="3"
                  autocomplete="off"
                  style=" width: 75%;"
                  @keyup.enter.native="handleLogin" />
      </el-form-item>
      <div class="login-code"
           style=" width: 38%;height: 48px;float: right;">
        <img style="height: 48px;width: 100%;"
             :src="codeUrl"
             @click="getCode">
      </div>

      <el-button :loading="loading"
                 type="primary"
                 style="width:100%;height:40px;margin-bottom:30px;"
                 @click.native.prevent="handleLogin">
        <span v-if="!loading">登 录</span>
        <span v-else>登 录 中...</span>
      </el-button>
    </el-form>

    <div class="footer">
      <span>Copyright © 2015-2020 GXETC. All rights reserved. </span>
      <br>
      <span>桂ICP备15007608号</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login"

export default {
  name: "Login",
  data() {
    return {
      codeUrl: "",
      cookiePassword: "",
      title: "开发者工具",
      loginForm: {
        username: "admin",
        password: process.env.NODE_ENV === "development" ? "Jtjsb@2020" : "", // 原密码admin123 或admin1234
        rememberMe: false,
        code: "",
        uuid: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "用户名不能为空" },
        ],
        password: [
          { required: true, trigger: "blur", message: "密码不能为空" },
        ],
        code: [
          { required: true, trigger: "change", message: "验证码不能为空" },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getCode()
    console.log("process.NODE_ENV", process.env.NODE_ENV)
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.username === "") {
      this.$refs.username.focus()
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        console.log("getImageCode", res)
        this.codeUrl = res.data.image
        this.loginForm.uuid = res.data.id
      })
    },
    checkCapslock({ shiftKey, key } = {}) {
      if (key && key.length === 1) {
        if (
          (shiftKey && key >= "a" && key <= "z") ||
          (!shiftKey && key >= "A" && key <= "Z")
        ) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === "CapsLock" && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = ""
      } else {
        this.passwordType = "password"
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch("user/login", this.loginForm)
            .then((e) => {
              console.log(e, "---++++")
               this.$router.push({
                path: this.redirect || "/",
                query: this.otherQuery,
              })
              this.loading = false
            })
            .catch(() => {
              this.loading = false
              this.getCode()
            })
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
  },
}
</script>
<style lang="scss" scoped>
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #f2f4f5;
$dark_gray: #889aa4;
$light_gray: #eee;
$cursor: #fff;
$title_color: #1890ff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .el-input {
    display: inline-block;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $dark_gray;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }
  .login-code {
    background-color: $bg;
  }
  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: $light_gray;
    border-radius: 5px;
    color: #454545;
  }

  .login-form {
    position: relative;
    width: 420px;
    max-width: 100%;
    padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 44px;
      margin: 0;
      line-height: 48px;
      margin: 0px auto 40px auto;
      text-align: center;

      color: $title_color;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }

  .footer {
    position: absolute;
    bottom: 30px;
    width: 100%;
    text-align: center;
    color: $dark_gray;
    font-size: 14px;
  }
}
</style>
