<template>
  <div class="login-container">
    <img src="@/image/bg.png" class="bgImg" />
    <div class="content">
      <div class="bg-left">
        <img src="@/image/bg-left.png" class="bg-img" />
      </div>
      <div class="bg-right">
        <p class="logo">
          <img src="@/image/etc_logo.png" class="logo-img" />
        </p>
        <div class="title-container">
          <p class="title">广西ETC发行服务</p>
        </div>
        <div class="form">
          <el-form
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            auto-complete="on"
            label-position="left"
          >
            <el-form-item prop="username">
              <span class="svg-container">
                <svg-icon icon-class="user" />
              </span>
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="账号"
                name="username"
                type="text"
                tabindex="1"
                auto-complete="on"
              />
            </el-form-item>

            <el-form-item prop="password">
              <span class="svg-container">
                <svg-icon icon-class="password" />
              </span>
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="密码"
                name="password"
                tabindex="2"
                auto-complete="off"
                @keyup.enter.native="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <svg-icon
                  :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
                />
              </span>
            </el-form-item>
            <el-form-item prop="code">
              <div class="g-flex" style="line-height: 1">
                <el-input
                  ref="username"
                  v-model="loginForm.code"
                  placeholder="验证码"
                  name="username"
                  type="text"
                  tabindex="3"
                  autocomplete="off"
                  style="width: 160px"
                  @keyup.enter.native="handleLogin"
                />
                <div class="login-code" style="height: 40px">
                  <img
                    style="height: 40px; width: 120px"
                    :src="codeUrl"
                    @click="getCode"
                  />
                </div>
              </div>
            </el-form-item>

            <el-button
              :loading="loading"
              :disabled="true"
              type="primary"
              style="width: 100%; margin-bottom: 30px"
              @click.native.prevent="handleLogin"
              >登录</el-button
            >
          </el-form>
           <span style="font-size: 20px; color: red;"> 本页面暂停服务，请使用统一平台进行登录 </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login'

export default {
  name: 'Login',
  data() {
    return {
      codeUrl: '',
      cookiePassword: '',
      title: '开发者工具',
      loginForm: {
        // username: process.env.NODE_ENV === 'development' ? 'admin' : '',
        // password: process.env.NODE_ENV === 'development' ? 'Jtjsb@2020' : '', 
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: '',
      },
      loginRules: {
        username: [
          { required: true, trigger: 'blur', message: '用户名不能为空' },
        ],
        password: [
          { required: true, trigger: 'blur', message: '密码不能为空' },
        ],
        code: [
          { required: true, trigger: 'change', message: '验证码不能为空' },
        ],
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      isDisabled: false,
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getCode()
    console.log('process.NODE_ENV', process.env.NODE_ENV)
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.username === '') {
      this.$refs.username.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        console.log('getImageCode', res)
        this.codeUrl = res.data.image
        this.loginForm.uuid = res.data.id
      })
    },
    checkCapslock({ shiftKey, key } = {}) {
      if (key && key.length === 1) {
        if (
          (shiftKey && key >= 'a' && key <= 'z') ||
          (!shiftKey && key >= 'A' && key <= 'Z')
        ) {
          this.capsTooltip = true
        } else {
          this.capsTooltip = false
        }
      }
      if (key === 'CapsLock' && this.capsTooltip === true) {
        this.capsTooltip = false
      }
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store
            .dispatch('user/login', this.loginForm)
            .then((e) => {
              console.log(e, '---++++')
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery,
              })
              this.loading = false
            })
            .catch(() => {
              this.loading = false
              this.getCode()
            })
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
  },
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #fff;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  position: absolute;
  width: 100%;
  height: 100%;
  .bgImg {
    position: absolute;
    width: 100%;
    height: 100%;
  }
  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 80%;
    height: 80%;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    min-height: 600px;
  }
  .bg-left {
    height: 90%;
    background-image: linear-gradient(-180deg, #bee5f8 0%, #92d8f8 100%);
    min-width: 600px;
    position: relative;
    border-radius: 10px 0 0 10px;
    .bg-img {
      height: 95%;
      width: calc(100% - 100px);
      position: relative;
      left: 50px;
    }
  }
  .bg-right {
    min-width: 400px;
    height: 100%;
    background-color: #fff;
    box-shadow: 11px 0 6px 1px rgba(49, 49, 169, 0.18);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .logo {
      .logo-img {
        width: 171px;
        height: 41px;
      }
    }
    .title-container {
      .title {
        font-size: 23px;
        color: rgba(0, 0, 0, 0.45);
        text-align: center;
        line-height: 23px;
        font-weight: bold;
      }
    }

    .form {
      height: 250px;
      padding-top: 63px;
      .el-form-item {
        background-color: #fff;
      }
      ::v-deep.el-form-item__content {
        background: #eff2fc;
        border-radius: 20px;
        height: 40px;
        line-height: 35px;
        font-size: 16px;
        color: #666666;
      }
      .el-button {
        background: #1890ff;
        border-radius: 23px;
      }
      .psd {
        font-size: 14px;
        color: #1890ff;
        margin: 0;
        text-align: right;
        position: relative;
        top: -10px;
        left: -10px;
      }
    }
  }
  .el-input {
    display: inline-block;
    height: 40px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      font-size: 16px;
      color: #666666;
      height: 40px;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        // -webkit-text-fill-color: $cursor !important;
        background: #fff !important;
        // border-radius: 20px;
        color: #666 !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;

  .login-form {
    position: relative;
    min-width: 270px;
    width: 100%;
    // padding: 160px 35px 0;
    margin: 0 auto;
    overflow: hidden;

    ::v-deep.el-form-item__content {
      border: 1px solid #ddd;
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding-left: 15px;
    color: #889aa4;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    position: relative;
    top: -2px;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      line-height: 23px;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 4px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }
}
</style>