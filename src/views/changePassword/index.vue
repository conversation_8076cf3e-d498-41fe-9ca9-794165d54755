<template>
  <div class="changePasd">
    <div class="form">
      <el-form
        ref="pasdForm"
        label-position="right"
        label-width="80px"
        :model="pasdForm"
        :rules="pasdRules"
        class="pasdForm"
      >
        <el-form-item label="原密码" prop="oldPsd">
          <el-input
            v-model="pasdForm.oldPsd"
            placeholder="请输入原密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPsd">
          <el-input
            v-model="pasdForm.newPsd"
            placeholder="请输入至少8位数密码，由字母、数字组成，区分大小写"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPsd">
          <el-input
            v-model="pasdForm.confirmPsd"
            placeholder="请确认新密码"
            show-password
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" class="btn" @click="submitForm('pasdForm')"
            >确定修改</el-button
          >
          <el-button class="btn" @click="resetForm('pasdForm')">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { checkPasd } from "@/utils/validate"

export default {
  name: "ChangePasd",
  data() {
    const validatePassword = (rule, value, callback) => {
      console.log(checkPasd(value))
      if (!checkPasd(value)) {
        callback(new Error("请输入至少8位数密码，由字母、数字组成，区分大小写"))
      } else {
        callback()
      }
    }
    const samePasd = (rule, value, callback) => {
      if (this.pasdForm.newPsd !== value) {
        callback(new Error("密码不一致"))
      } else {
        callback()
      }
    }
    return {
      pasdForm: {
        oldPsd: "",
        newPsd: "",
        confirmPsd: ""
      },
      pasdRules: {
        oldPsd: [
          { required: true, trigger: "blur", message: "原密码不能为空" }
        ],
        newPsd: [
          { required: true, trigger: "blur", validator: validatePassword }
        ],
        confirmPsd: [
          {
            required: true,
            trigger: "blur",
            message: "密码不能为空"
          },
          {
            required: true,
            trigger: "blur",
            validator: samePasd
          }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.changePasd(formName)
        } else {
          console.log("error submit!!")
          return false
        }
      })
    },
    resetForm(formName) {
      this.$emit("comfirm")
      this.$refs[formName].resetFields()
    },
    changePasd(formName) {
      const data = {
        newPassword: this.pasdForm.newPsd,
        oldPassword: this.pasdForm.oldPsd
      }
      this.$store.dispatch("user/changePasd", data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: "修改成功",
            type: "success"
          })
          this.$emit("comfirm")
          this.$refs[formName].resetFields()
          this.logOut()
        }
      })
    },
    async logOut() {
      await this.$store.dispatch("user/logOut")
      await this.$store.dispatch("tagsView/delAllViews", [])
      this.$router.push(`/login?redirect=dashboard`)
    }
  }
}
</script>

<style scoped lang="scss">
.changePasd {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // position: absolute;
  width: 100%;
  height: 100%;
  .form {
    width: 400px;
    .btn {
      width: 120px;
      position: relative;
    }
  }
}
</style>
