<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:互联网充值流水
  * @author:zhangys
  * @date:2022/08/04 17:12:32
!-->
<template>
  <div>
    <dart-search ref="searchForm1"
                 class="search"
                 :formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="订单申请时间:"
                          prop="">
          <el-date-picker v-model="time"
                          type="datetimerange"
                          clearable
                          value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="支付方式:"
                          prop="company">
          <el-select clearable
                     v-model="search.payType"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="(item,index) in rechargeTypeOptions"
                       :key="index"
                       :label="item.label"
                       :value="item.value" />
          </el-select>

        </dart-search-item>
        <dart-search-item label="第三方订单号:"
                          prop="company">
          <el-input v-model="search.bankOrderNo"
                    placeholder="请输入三方订单号"
                    clearable></el-input>
        </dart-search-item>

        <dart-search-item :is-button="true"
                          :colElementNum='1'>
          <div class="g-flex g-flex-end">
            <el-button @click="reset">重置</el-button>
            <el-button type="primary"
                       @click="getBalanceList">查询</el-button>
            <el-button type="primary"
                          @click="exportHandle">导出</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <el-table :data="tableData"
              align="center"
              height="300px"
              header-align="center"
              style="width: 100%;"
              :row-style="{ height: '54px' }"
              :cell-style="{ padding: '0px' }"
              v-loading="loading"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }">
      <el-table-column prop="createTime"
                       align="center"
                       min-width="180"
                       label="充值时间" />
      <el-table-column prop="netUserNo"
                       align="center"
                       label="用户编号"
                       min-width="200" />
      <el-table-column prop="loginName"
                       align="center"
                       label="登录名"
                       min-width="140" />
      <el-table-column prop="netMobile"
                       align="center"
                       label="手机号"
                       min-width="120">
      </el-table-column>
      <el-table-column prop="amount"
                       align="center"
                       min-width="100"
                       label="充值金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.amount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="payType_str"
                       align="center"
                       min-width="160"
                       label="支付方式" />
      <el-table-column prop="payStatus"
                       align="center"
                       min-width="100"
                       label="支付状态">
        <template slot-scope="scope">
          {{getpayStatus(scope.row.payStatus)}}
        </template>
      </el-table-column>
      <el-table-column prop="branchName"
                       align="center"
                       min-width="120"
                       label="网点" />

      <el-table-column prop="bankOrderNo"
                       align="center"
                       min-width="180"
                       label="三方订单号" />

      <el-table-column prop="refundOrder"
                       align="center"
                       min-width="180"
                       label="退费订单号" />
    </el-table>
    <div class="pagination g-flex g-flex-start ">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
import { rechargeTypeOptions } from '@/common/const/optionsData.js'
import { getVehicleColor, getpayStatus } from '@/common/method/formatOptions'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { exportFile } from './util'
import {
  exportList
} from '@/api/workordermanage'
export default {
  name: '',
  props: {
    userNo: {
      type: String,
      default: '',
    },
    flowSlideVisiable: {
      type: Boolean,
      default: false,
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      tableData: [],
      search: {
        userNo: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        endTime: '',
        payType: '',
        bankOrderNo: '',
      },
      rechargeTypeOptions,
      total: 0,
      time: '',
      loading:false
    }
  },
  computed: {},
  watch: {
    userNo(val) {
      console.log(val, '<<---------val')
      if (val) {
        this.search.userNo = this.userNo
        this.reset()
        this.getBalanceList()
      }
    },
  },
  created() {},
  methods: {
    getVehicleColor,
    getpayStatus,
    reset() {
      for (let key in this.search) {
        this.search[key] = ''
      }
      this.search.userNo = this.userNo

      this.time = ''
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    getBalanceList() {
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }
      request({
        url: api.netRechargeList,
        method: 'post',
        data: this.search,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getBalanceList()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return 0
      value = float.div(float.mul(val, 100), 10000)
      return value
    },
    exportHandle() {
      this.loading = true
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }
      let query = JSON.parse(JSON.stringify(this.search))
      let params = {
        ...query,
        name:'netRechargeRecordsReport',
        applyStartTime:query.startTime,
        applyFinishTime:query.endTime,
        payMethod:query.payType
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      delete params.pageNum
      delete params.pageSize
      delete params.startTime
      delete params.endTime
      delete params.payType
      exportFile(params, exportList,()=> {
        this.loading = false
      })
    }
  },
}
</script>

<style lang='scss' scoped>
</style>