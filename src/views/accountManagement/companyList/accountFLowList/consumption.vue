<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:互联网消费流水
  * @author:zhangys
  * @date:2022/08/04 17:12:00
!-->
<template>
  <div>
    <dart-search ref="searchForm1"
                 class="search"
                 :formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="订单申请时间:"
                          prop="">
          <el-date-picker v-model="time"
                          type="datetimerange"
                          clearable
                          value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item label="网点:"
                          prop="branchNo">
          <el-cascader v-model="search.branchNo"
                       class="form-select"
                       ref="deptNodes"
                       clearable
                       :options="deptOptions"
                       :expand-trigger="'click'"
                       :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }" />
        </dart-search-item>

        <dart-search-item :is-button="true"
                          :colElementNum='1'>
          <div class="g-flex g-flex-end">
            <el-button @click="reset">重置</el-button>
            <el-button type="primary"
                       @click="getBalanceList">查询</el-button>
            <el-button type="primary"
                          @click="exportHandle">导出</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <el-table :data="tableData"
              align="center"
              height="300px"
              header-align="center"
              style="width: 100%;"
              :row-style="{ height: '54px' }"
              v-loading="loading"
              :cell-style="{ padding: '0px' }"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }">
      <el-table-column prop="id"
                       align="center"
                       label="订单编号"
                       min-width="200" />
      <el-table-column prop="userNo"
                       align="center"
                       label="用户编号"
                       min-width="200" />
      <el-table-column prop="userName"
                       align="center"
                       label="用户名"
                       min-width="200" />
      <el-table-column prop="carNo"
                       align="center"
                       label="出账来源"
                       min-width="160">
        <template slot-scope="scope">
          <span> {{scope.row.carNo}}</span>
          <span v-if="getVehicleColor(scope.row.carColor)">[{{getVehicleColor(scope.row.carColor)}}]</span>
        </template>
      </el-table-column>
      <el-table-column prop="beforeAmount"
                       align="center"
                       min-width="100"
                       label="消费前金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.beforeAmount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="amount"
                       align="center"
                       min-width="100"
                       label="划拨金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.amount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="afterAmount"
                       align="center"
                       min-width="100"
                       label="消费后金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.afterAmount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="payStatus"
                       align="center"
                       min-width="100"
                       label="支付状态">
        <template slot-scope="scope">
          {{getpayStatus(scope.row.payStatus)}}
        </template>
      </el-table-column>
      <el-table-column prop="refoundStatus"
                       align="center"
                       min-width="100"
                       label="退款状态">
        <template slot-scope="scope">
          {{scope.row.refoundStatus=='4'?'退款成功':''}}
        </template>
      </el-table-column>
      <el-table-column prop="target_str"
                       align="center"
                       min-width="180"
                       label="流水类型" />
      <el-table-column prop="branchName"
                       align="center"
                       min-width="170"
                       label="操作网点" />

      <el-table-column prop="operatorName"
                       align="center"
                       min-width="100"
                       label="操作人" />
      <el-table-column prop="createTime"
                       align="center"
                       min-width="180"
                       label="消费时间" />
    </el-table>
    <div class="pagination g-flex g-flex-start ">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
import { getVehicleColor, getpayStatus } from '@/common/method/formatOptions'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import upmsRequest from '@/utils/upmsRequest'
import { exportFile } from './util'
import {
  exportList
} from '@/api/workordermanage'
export default {
  name: '',
  props: {
    userNo: {
      type: String,
      default: '',
    },
    flowSlideVisiable: {
      type: Boolean,
      default: false,
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      tableData: [],
      search: {
        userNo: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        endTime: '',
        branchNo: '',
        branchName: '',
      },
      total: 0,
      time: '',
      deptOptions: [],
      currentDept: null,
      loading:false
    }
  },
  computed: {},
  watch: {
    userNo(val) {
      console.log(val, '<<---------val22')
      if (val) {
        this.search.userNo = this.userNo
        this.reset()
        this.getBalanceList()
      }
    },
  },
  created() {
    this.getGroup()
  },
  methods: {
    getVehicleColor,
    getpayStatus,
    getGroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get',
      })
        .then((res) => {
          console.log(res.data)
          this.deptOptions = res.data
        })
        .catch((error) => {
          console.log(error)
        })
    },
    reset() {
      for (let key in this.search) {
        this.search[key] = ''
      }
      this.search.userNo = this.userNo
      this.search.branchNo = ''
      this.time = ''
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    getBalanceList() {
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }

      request({
        url: api.netWalletPays,
        method: 'post',
        data: this.search,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },

    changePage(page) {
      this.search.pageNum = page
      this.getBalanceList()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return 0
      value = float.div(float.mul(val, 100), 10000)
      return value
    },
    exportHandle() {
      this.loading = true
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }
      let query = JSON.parse(JSON.stringify(this.search))
      let params = {
        ...query,
        name:'netExpenseRecordsReport',
        applyStartTime:query.startTime,
        applyFinishTime:query.endTime,
        branchCode:query.branchNo
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      delete params.pageNum
      delete params.pageSize
      delete params.startTime
      delete params.endTime
      delete params.branchNo
      exportFile(params, exportList,()=> {
        this.loading = false
      })
    }
  },
}
</script>

<style lang='scss' scoped>
</style>