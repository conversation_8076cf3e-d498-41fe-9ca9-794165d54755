
import { decode } from 'js-base64'

export const  exportFile = async (data,apiMethod,callBack,fileObj) => {
  if(!apiMethod){
    console.error('缺少导出地址apiMethod！')
    return
  }
  let res = await apiMethod(data)
  if(fileObj){ // 如果是流blob的形式
    const link = document.createElement('a')
    let blob = new Blob([res]) //构造一个blob对象来处理数据
    link.style.display = 'none'
    link.href = URL.createObjectURL(blob)
    link.download = `${fileObj.fileName}` //下载的文件名
    document.body.appendChild(link)
    link.click() // 执行下载
    document.body.removeChild(link) // 释放标签
    callBack && callBack()
    return
  }
  // 如果是链接的形式
  if (res.code == 200) {
    callBack && callBack()
    let url = res.data
    let decodeUrl = decode(url)
    // console.log(decodeUrl,'地址')
    let clientWidth = document.documentElement.clientWidth
    let clientHeight = document.documentElement.clientHeight
    window.open(
      decodeUrl,
      '_blank',
      'width=' +
        clientWidth +
        ',height=' +
        clientHeight +
        ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
    )
  }
}