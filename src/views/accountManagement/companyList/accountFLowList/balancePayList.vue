<!--
  * @projectName:gxetc-issue-manage-web
  * @desc: 互联网划拨卡账流水  
  * @author:zhang<PERSON>
  * @date:2022/08/04 17:07:57
!-->
<template>
  <div>
    <dart-search ref="searchForm1"
                 class="search"
                 :formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="订单申请时间:"
                          prop="">
          <el-date-picker v-model="time"
                          type="datetimerange"
                          clearable
                          value-format="yyyy-MM-dd HH:mm:ss"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="ETC卡号:"
                          prop="company">
          <el-input v-model="search.cardNo"
                    placeholder="请输入ETC卡号" clearable></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号:"
                          prop="company">
          <el-input v-model="search.carNo"
                    placeholder="请输入车牌号" clearable></el-input>
        </dart-search-item>

        <dart-search-item :is-button="true"
                          :colElementNum='1'>
          <div class="g-flex g-flex-end">
            <el-button @click="reset">重置</el-button>
            <el-button type="primary"
                       @click="getBalanceList">查询</el-button>
            <el-button type="primary"
                          @click="exportHandle">导出</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <el-table :data="tableData"
              align="center"
              height="300px"
              header-align="center"
              style="width: 100%;"
              :row-style="{ height: '54px' }"
              :cell-style="{ padding: '0px' }"
              v-loading="loading"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }">
      <el-table-column prop="createTime"
                       align="center"
                       min-width="180"
                       label="划拨时间" />
      <el-table-column prop="userNo"
                       align="center"
                       label="用户编号"
                       min-width="200" />
      <el-table-column prop="vehicleNo"
                       align="center"
                       label="车牌号"
                       min-width="120">
        <template slot-scope="scope">
          {{scope.row.vehicleNo}}[{{getVehicleColor(scope.row.vehicleColor)}}]
        </template>
      </el-table-column>

      <el-table-column prop="cardNo"
                       align="center"
                       label="卡号"
                       min-width="180" />
      <el-table-column prop="cardBeforeAmount"
                       align="center"
                       min-width="100"
                       label="划拨前金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.cardBeforeAmount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="amount"
                       align="center"
                       min-width="100"
                       label="划拨金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.amount)}}元
        </template>
      </el-table-column>
      <el-table-column prop="cardAfterAmount"
                       align="center"
                       min-width="100"
                       label="划拨后金额">
        <template slot-scope="scope">
          {{moneyFilter(scope.row.cardAfterAmount)}}元
        </template>
      </el-table-column>

      <el-table-column prop="payStatus"
                       align="center"
                       min-width="140"
                       label="互联网支付状态">
        <template slot-scope="scope">
          {{getpayStatus(scope.row.payStatus)}}
        </template>
      </el-table-column>
      <el-table-column prop="payStatus"
                       align="center"
                       min-width="140"
                       label="是否到账状态">
        <template slot-scope="scope">
          {{transferStatusFilter(scope.row.transferStatus)}}
        </template>
      </el-table-column>
      <el-table-column prop="returnStatus"
                       align="center"
                       min-width="140"
                       label="冲正状态">
        <template slot-scope="scope">
          <span v-if="scope.row.transferStatus=='2'||scope.row.transferStatus=='5'"> {{getReturnStatus(scope.row.returnStatus)}}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column prop="operatorName"
                       align="center"
                       min-width="100"
                       label="操作人" />

    </el-table>
    <div class="pagination g-flex g-flex-start ">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
import {
  getVehicleColor,
  getpayStatus,
  getReturnStatus,
} from '@/common/method/formatOptions'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { exportFile } from './util'
import {
  exportList
} from '@/api/workordermanage'
export default {
  name: '',
  props: {
    userNo: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      tableData: [],
      search: {
        userNo: '',
        pageNum: 1,
        pageSize: 10,
        startTime: '',
        endTime: '',
        carNo: '',
        cardNo: '',
      },
      total: 0,
      time:'',
      loading:false
    }
  },
  computed: {},
  watch: {
    userNo(val) {
      console.log(val, '<<---------val23332')

      if (val) {
          this.search.userNo = this.userNo
        this.reset()
        this.getBalanceList()
      }
    },
  },
  created() {},
  methods: {
    getVehicleColor,
    getpayStatus,
    getReturnStatus,
    reset() {
      for (let key in this.search) {
        this.search[key] = ''
      }
      this.search.userNo = this.userNo
      this.time = ''
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    getBalanceList() {
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }
      request({
        url: api.netBalanPayList,
        method: 'post',
        data: this.search,
      }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getBalanceList()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return 0
      value = float.div(float.mul(val, 100), 10000)
      return value
    },
    transferStatusFilter(val) {
      if (!val) return ''
      let status = {
        1: '初始化',
        2: '划拨成功',
        3: '卡异常',
        4: '卡账户异常',
        5: '划拨成功',
        6: '划拨失败',
      }
      return status[val] ? status[val] : val
    },
    exportHandle() {
      this.loading = true
      if (this.time) {
        this.search.startTime = this.time[0]
        this.search.endTime = this.time[1]
      } else {
        this.search.startTime = ''
        this.search.endTime = ''
      }
      let query = JSON.parse(JSON.stringify(this.search))
      let params = {
        ...query,
        name:'netTransferRecordsReport',
        applyStartTime:query.startTime,
        applyFinishTime:query.endTime,
        plateNo:query.carNo,
        cpuCardId:query.cardNo
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      delete params.pageNum
      delete params.pageSize
      delete params.startTime
      delete params.endTime
      delete params.carNo
      delete params.cardNo
      exportFile(params, exportList,()=> {
        this.loading = false
      })
    }
  },
}
</script>

<style lang='scss' scoped>
</style>