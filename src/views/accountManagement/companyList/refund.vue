<template>
  <div class="detail">
    <div class="info">
      <userdetail :userinfo="userinfo" @toBind="toBind"></userdetail>
    </div>
    <div class="info">
      <refundinfo
        ref="refundinfo"
        :userAmount="userinfo.availableAmount"
        :refundType="refundType"
        :fileUUID="fileUUID"
        :customerId="customerId"
        @change="changecanrefund"
      ></refundinfo>
    </div>
    <div class="info">
      <update
        ref="update"
        :usertype="userinfo.accountType"
        :fileUUID="fileUUID"
        @change="changerefundType"
      ></update>
    </div>
    <div class="info">
      <div class="butdown">
        <el-button size="small" type="primary" @click="refund"
          >账户退费</el-button
        >
      </div>
    </div>
    <binduser :visible.sync="bindUserVisible" @change="changeUser"></binduser>
  </div>
</template>

<script>
import userdetail from './components/userdetail'
import refundinfo from './components/refundinfo'
import update from './components/update'
import binduser from './components/binduser'
import request from '@/utils/request'
import api from '@/api/index'
import { guid } from '@/utils/utils'
export default {
  components: {
    userdetail,
    refundinfo,
    update,
    binduser,
  },
  created() {
    this.fileUUID = this.$route.query.userNo + guid(16)
    this.getAccountView()
  },
  data() {
    return {
      userinfo: {},
      refundType: '2',
      bindUserVisible: false,
      canrefund: false,
      fileUUID: '',
      customerId: '',
    }
  },
  methods: {
    getAccountView() {
      request({
        url: api.accountinfo,
        method: 'post',
        data: {
          userNo: this.$route.query.userNo,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.userinfo = res.data
          }
        })
        .catch((error) => {
          this.$msgbox({
            message: error.message,
            title: '提示',
            dangerouslyUseHTMLString: true,
            customClass: 'my_msgBox singelBtn',
            confirmButtonText: '确定',
            type: 'error',
          })
        })
    },
    refund() {
      if (!this.customerId) {
        this.$message({
          message: '请先关联ETC用户',
          type: 'warning',
        })
        return
      }
      if (!this.canrefund) {
        this.$message({
          message: '请打印业务申请单',
          type: 'warning',
        })
        return
      }
      let obj = this.$refs.update.checklist()
      if (obj && obj.lable) {
        this.$message({
          message: '请上传' + obj.lable + '档案',
          type: 'warning',
        })
        return
      }
      this.$refs.refundinfo.torefund()
    },
    toBind() {
      //关联账户
      this.bindUserVisible = true
    },
    changeUser(customerId) {
      this.customerId = customerId
      this.bindUserVisible = false
      this.$message({
        message: '关联成功',
        type: 'success',
      })
    },
    changerefundType(val) {
      this.refundType = val
    },
    changecanrefund() {
      this.canrefund = true
    },
  },
}
</script>

<style lang="scss" scoped>
.detail {
  padding: 20px;
  .info {
    padding: 10px 24px;
    .down {
      background-color: #fff;
      .thetable {
        .title {
          margin: 0 0 20px;
          padding: 16px 24px;
          font-weight: 600;
          border-bottom: 1px solid #f0f0f0;
        }
      }

      .downnav {
        padding: 0 44px;
      }
      .itembox {
        line-height: 40px;
        .item {
          margin: auto;
          font-size: 14px;
          span {
            display: inline-block;
            padding-right: 10px;
            color: #606266;
            font-weight: 600;
            min-width: 100px;
            text-align: right;
          }
        }
      }
    }
    .butdown {
      padding: 11px 20px;
      text-align: center;
      background-color: #fff;
    }
  }
  .textarea-wrapper {
    padding: 0 20px 30px 20px;
  }
}
</style>