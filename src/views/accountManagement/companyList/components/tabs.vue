<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:tabs选项卡
  * @author:zhangys
  * @date:2022/08/05 09:34:43
!-->
<template>
  <div>
    <el-tabs type="border-card"
             v-model="activeName"
             class="tabs">
      <el-tab-pane label="互联网划拨卡账流水"
                   name="transfer">
        <balancePayList :userNo='userNo'
                        v-if="activeName=='transfer'"></balancePayList>
      </el-tab-pane>
      <el-tab-pane label="互联网消费流水"
                   name="sale">
        <consumption :userNo='userNo'
                     v-if="activeName=='sale'"></consumption>
      </el-tab-pane>
      <el-tab-pane label="互联网充值流水"
                   name="recharge">
        <rechargeList :userNo='userNo'
                      v-if="activeName=='recharge'"></rechargeList>
      </el-tab-pane>

    </el-tabs>
  </div>
</template>

<script>
import rechargeList from '../accountFLowList/rechargeList'
import consumption from '../accountFLowList/consumption'
import balancePayList from '../accountFLowList/balancePayList'

export default {
  name: '',
  props: {
    userNo: {
      type: String,
      default: '',
    },
    flowSlideVisiable: {
      type: Boolean,
      default: false,
    },
  },
  components: { balancePayList, consumption, rechargeList },
  data() {
    return {
      activeName: 'transfer',
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {},
}
</script>

<style lang='scss' scoped>
.tabs {
  padding: 0px 20px;
}
</style>