<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:绑定车辆信息
  * @author:zhangys
  * @date:2023/03/29 14:50:10
-->
<template>
  <div>

    <div style="padding:10px">
      <el-table ref="multipleTable"
                :data="tableData"
                align="center"
                height="350px"
                header-align="center"
                style="width: 100%;">
        <el-table-column prop="carOwnerName"
                         align="center"
                         label="ETC用户名"
                         min-width="150" />
        <el-table-column prop="plateNumber"
                         align="center"
                         label="车牌号"
                         width="170">
          <template slot-scope="scope">
            [{{getVehicleColor(scope.row.plateColor)}}]{{scope.row.plateNumber}}
          </template>
        </el-table-column>
        <el-table-column prop="plateType"
                         align="center"
                         label="车辆类型"
                         width="120">
          <template slot-scope="scope">
            {{getCarType(scope.row.plateType)}} <span v-if="scope.row.plateTrunk">[{{getVehicleType(scope.row.plateTrunk)}}]</span>
          </template>
        </el-table-column>
        <el-table-column prop="cardNo"
                         align="center"
                         label="ETC卡号"
                         min-width="150" />

        <el-table-column prop="cardStatus"
                         align="center"
                         label="ETC卡状态"
                         min-width="120">
          <template slot-scope="scope">
            {{getCpuStatus(scope.row.cardStatus)}}
          </template>
        </el-table-column>

        <el-table-column prop="amount"
                         align="center"
                         label="可用余额">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.amount)}} <span v-if="scope.row.amount!=null">元</span>
          </template>
        </el-table-column>
        <el-table-column prop="gxCardType"
                         align="center"
                         label="广西卡类型"
                         min-width="150">
          <template slot-scope="scope">
            {{getGxCardType(scope.row.gxCardType)}}
          </template>
        </el-table-column>

        <el-table-column prop=""
                         align="center"
                         label="操作">
          <template slot-scope="scope">
            <el-button type="text"
                       @click="searchTollList(scope.row)">查看消费记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <tollRecord :visible.sync="tollListVisible"
                :vehicleInfo="vehicleInfo"></tollRecord>

  </div>
</template>

<script>
import {
  getVehicleColor,
  getallGxCardType,
  getCarType,
  getGxCardType,
  getCpuStatus,
  getVehicleType,
} from '@/common/method/formatOptions'
import request from '@/utils/request'
import api from '@/api/index'
import tollRecord from './tollRecord'
import float from '@/common/method/float.js'
export default {
  props: {
    accountInfo: {
      type: Object,
      default: {},
    },
  },
  components: {
    tollRecord,
  },
  data() {
    return {
      tableData: [],
      formData: {
        pageNum: 1,
        pageSize: 10,
        customerId: '',
      },
      total: 0,
      tollListVisible: false,
      vehicleInfo: {},
    }
  },

  watch: {
    accountInfo(val) {
      if (val.bindVehicleList) {
        this.tableData = val.bindVehicleList
      } else {
        this.tableData = []
      }
    },
  },
  created() {},

  methods: {
    getVehicleColor,
    getallGxCardType,
    getCarType,
    getGxCardType,
    getCpuStatus,
    getVehicleType,
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
    searchTollList(val) {
      this.tollListVisible = true
      this.vehicleInfo = val
    },
  },
}
</script>

<style lang="scss" scoped>
.foot {
  margin-top: 20px;
  text-align: center;
}
.bindTips {
  margin-top: 10px;
  .bindTips-item {
    margin: 4px;
  }
}
</style>