<template>
  <div class="down">
    <div class="thetable">
      <div class="title">附件上传</div>
    </div>
    <div class="downnav">
      <el-form class="nat-form nat-form-list"
               label-width="180px">
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="办理类型：">
              <div>
                <el-radio-group v-if="usertype=='COMMON_USER'"
                                v-model="handleType"
                                size="mini">
                  <el-radio-button label="1">本人</el-radio-button>
                  <el-radio-button label="2">他人</el-radio-button>
                </el-radio-group>
                <el-radio-group v-else
                                v-model="handleType"
                                size="mini">
                  <el-radio-button label="2">他人</el-radio-button>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="电子档案：">
              <photograph :pictureList='pictureList'
                          :handleType='handleType'
                          :fileUUID='fileUUID'
                          @on-change='onPhotographChange'></photograph>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import photograph from './photograph'
export default {
  components: {
    photograph
  },
  props: {
    usertype: {
      type: String,
      default: 'COMMON_USER'
    },
    fileUUID: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      handleType: '1',
      nowtype: [],
      Individual: ['21', '22', '23', '33'],//个人本人
      Individualothers: ['21', '22', '23', '25', '26', '27', '33'],//个人他人
      Unitothers: ['21', '24', '25', '26', '27', '33'],//单位他人
      pictureList: [
        {
          lable: '退款申请单',
          photo_code: '21',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '身份证(人像面)',
          photo_code: '22',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '身份证(国徽面)',
          photo_code: '23',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '单位证件',
          photo_code: '24',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '委托代办书',
          photo_code: '25',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '代办人身份证(人像面)',
          photo_code: '26',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '代办人身份证(国徽面)',
          photo_code: '27',
          file_url: '',
          file_serial: '',
          isShow: false
        },
        {
          lable: '银行卡照片',
          photo_code: '33',
          file_url: '',
          file_serial: '',
          isShow: false
        }
      ],
    }
  },
  methods: {
    changeList(arr) {
      let _self = this;
      this.pictureList.forEach((item) => {
        _self.$set(item, 'isShow', !!arr.includes(item.photo_code))
      })
    },
    onPhotographChange(data) {
      for (let i = 0; i < this.pictureList.length; i++) {
        if (this.pictureList[i].photo_code == data.photo_code) {
          this.$set(this.pictureList[i], 'file_url', data.file_url || '')
          this.$set(this.pictureList[i], 'file_serial', data.file_serial || '')
        }
      }
    },
    checklist() {
      let flag = true;
      let obj = null;
      for (let i = 0; i < this.pictureList.length; i++) {
        if (this.pictureList[i].isShow && !this.pictureList[i].file_url && flag) {
          flag = false;
          obj = this.pictureList[i]
        }
      }
      return obj;
    }
  },
  watch: {
    handleType: {
      handler(val) {
        if (val == '1' && this.usertype == 'COMMON_USER') {
          this.nowtype = this.Individual
        } else if (val == '2' && this.usertype == 'COMMON_USER') {
          this.nowtype = this.Individualothers
        } else {
          this.nowtype = this.Unitothers
        }
        this.changeList(this.nowtype)
        this.$emit('change', val)
      },
      immediate: true
    },
    usertype(val) {
      if (val == 'GROUP_USER') {
        this.handleType = '2'
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.title {
  line-height: 28px;
  margin: 0 0 20px;
  padding: 11px 24px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
}
.textarea {
  // overflow: hidden;
  border-color: #e8e8e8;
  padding: 10px;
  width: 70%;
}
</style>