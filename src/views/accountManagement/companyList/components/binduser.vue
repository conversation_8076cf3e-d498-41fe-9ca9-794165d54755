<template>
  <div class="account-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="50%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 关联ETC用户 </span>
        </div>
      </template>
      <div class="search-list">
        <dart-search
          :formSpan="24"
          :gutter="20"
          ref="searchForm1"
          label-position="right"
          :model="search"
          :fontWidth="2"
        >
          <template slot="search-form" style="padding-left: 10px">
            <dart-search-item :span="12" label="车牌号：" prop="vehicle_code">
              <el-input
                @focus="handleOnClear('car')"
                v-model="search.vehicle_code"
                clearable
                placeholder="车牌号"
              ></el-input>
            </dart-search-item>
            <dart-search-item label="车牌颜色：" prop="vehicle_color">
              <el-select
                @focus="handleOnClear('car')"
                v-model="search.vehicle_color"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in licenseColorOption"
                  :key="item.index"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </dart-search-item>
            <dart-search-item :span="12" label="手机号：" prop="phone">
              <el-input
                @focus="handleOnClear('phone')"
                v-model="search.phone"
                placeholder="请输入手机号"
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item :is-button="true">
              <el-button
                type="primary"
                size="mini"
                native-type="submit"
                @click="onSearchHandle"
                >查询</el-button
              >
            </dart-search-item>
          </template>
        </dart-search>
        <div class="content">
          <userItem ref="bindItem" :list="list" @change="change"></userItem>
          <div style="margin: 50px 0;" class="g-flex g-flex-align-center g-flex-center" v-if="dataFlag">查询暂无数据</div>
        </div>
        <div class="content" style="margin-top: 30px">
          <span style="color: red">
            注意:请核实现场申请退款的用户是否已开ETC账户，未开ETC账户的先给用户开设ETC账户，已开ETC账户但是预留手机号与互联网联系手机号不相符的，
            则通过修改ETC账户预留手机号或者修改互联网账户预留手机号的方法确保能搜索到ETC用户名称及证件号。
          </span>
        </div>
      </div>
      <template slot="footer">
        <el-button :disabled="!customer_id" @click="bind()" type="primary">
          关联
        </el-button>
        <el-button @click="close()">关闭</el-button></template
      >
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { licenseColorOption } from '@/common/const/optionsData'
import userItem from './useritem'
import { getVehicleColor } from '@/common/method/formatOptions'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    userItem,
  },
  data() {
    return {
      licenseColorOption,
      loading: false,
      isFullscreen: false,
      showLoading: false,
      center: 'center',
      timeArr: [],
      search: {
        phone: '',
        vehicle_code: '',
        vehicle_color: '0',
      },
      list: [],
      customer_id: '',
      dataFlag: false,
    }
  },
  methods: {
    getVehicleColor,
    handleOnClear(type) {
      if (type == 'car') {
        this.search.phone = ''
      } else if (type == 'phone') {
        this.search.vehicle_code = ''
        this.search.vehicle_color = '0'
      }
    },
    searchByUser(customer_id) {
      this.showLoading = true
      let params = {}
      if (!customer_id) {
        params = {
          phone: this.search.phone,
        }
      } else {
        params = {
          customer_id: customer_id,
        }
      }
      this.$request({
        url: this.$interfaces.customerBizList,
        method: 'post',
        data: params,
      }).then((res) => {
        this.showLoading = false
        console.log(res)
        if (res.code == 200) {
          console.log('this', res.data)
          this.list = res.data
          if (this.list.length == 0) {
            this.dataFlag = true
          } else {
            this.dataFlag = false
          }
        }
      })
    },
    searchByVehicle() {
      this.showLoading = true
      let params = {
        vehicle_code: this.search.vehicle_code,
        vehicle_color: this.search.vehicle_color,
      }
      this.$request({
        url: this.$interfaces.getCarinfo,
        method: 'post',
        data: params,
      }).then((res) => {
        this.showLoading = false
        console.log(res)
        if (res.code == 200) {
          console.log('this', res.data)
          let customer_id = res.data[0].customer_id
          this.searchByUser(customer_id)
        }
      })
    },
    onSearchHandle() {
      //重置查询操作
      this.customer_id = ''
      this.list = []
      this.$refs.bindItem.reset()

      if (!this.search.phone && !this.search.vehicle_code) {
        this.$message({
          type: 'warning',
          message: '请输入手机号或车辆信息查询',
        })
        return
      }

      if (this.search.phone) {
        this.searchByUser()
        return
      }

      if (this.search.vehicle_code && this.search.vehicle_color) {
        this.searchByVehicle()
        return
      }
    },
    change(customer_id) {
      this.customer_id = customer_id
    },
    bind() {
      if (!this.customer_id) {
        this.$message({ type: 'warning', message: '请先选择要关联的ETC用户' })
        return
      }
      this.$emit('change', this.customer_id)
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
  width: 80px !important;
  white-space: nowrap;
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}

.account-dialog {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
