<template>
  <div class="down">
    <div class="thetable">
      <div class="title">
        <p>账户详情</p>
        <el-button size="mini" type="primary" @click="toBind()"
          >关联ETC用户</el-button
        >
      </div>
    </div>
    <div class="downnav">
      <el-form class="nat-form nat-form-list" label-width="180px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="账户编号：">
              <div>{{ userinfo.userNo }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户类型：">
              <div>
                {{ userinfo.accountType == 'GROUP_USER' ? '单位' : '个人' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户余额(元)：">
              <div>{{ userinfo.availableAmount | moneyFilter }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="登录名：">
              <div>{{ userinfo.loginName }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人手机号：">
              <div>{{ userinfo.mobile }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间：">
              <div>{{ userinfo.createTime }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item
              v-if="userinfo.accountType == 'GROUP_USER'"
              label="公司名称："
            >
              <div>{{ userinfo.userName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  created() {},
  props: {
    userinfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {}
  },
  methods: {
    toBind() {
      this.$emit('toBind')
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.title {
  line-height: 28px;
  margin: 0 0 20px;
  padding: 11px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  p {
    font-weight: 600;
    margin: 0;
  }
}
</style>