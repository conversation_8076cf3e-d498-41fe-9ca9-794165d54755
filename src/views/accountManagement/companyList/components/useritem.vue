<template>
  <div class="user-item">
    <div class="user-content" v-if="currentIndex == null">
      <div
        class="item"
        v-for="(item, index) in list"
        :key="index"
        @click.stop="change(index)"
      >
        <!-- <i v-show="currentIndex == index" class="icon el-icon-success"></i> -->
        <div class="type">
          {{ item.customer_type == '0' ? '个人' : '单位' }}
        </div>
        <div class="info" style="margin-right: 30px">
          <span class="username">{{ item.customer_name }}</span>
          <span class="id">编号：{{ item.customer_id }}</span>
        </div>
        <div class="info">
          <span class="username">联系电话：{{ item.link_mobile }}</span>
          <span class="id">开户日期：{{ item.open_date }}</span>
        </div>
      </div>
    </div>
    <div class="detail-content" v-if="Object.keys(detail).length > 0">
      <div class="icon" @click="back()">
        <i class="icon-back el-icon-back"></i>
      </div>
      <div class="content-wrapper">
        <div class="content">
          <div class="detail-item">
            <div>
              用户编号：<span>{{ detail.customer_id }}</span>
            </div>
            <div>
              开户日期：<span>{{ detail.open_date }}</span>
            </div>
          </div>
          <div class="detail-item">
            <div>
              联系人：<span>{{ detail.link_man }}</span>
            </div>
            <div>
              手机号码：<span>{{ detail.link_mobile }}</span>
            </div>
          </div>
          <div class="detail-item">
            <div>
              证件类型：
              <span v-show="detail.customer_type == '0'">{{
                getType(personalOCRType, detail.certificates_type)
              }}</span>
              <span v-show="detail.customer_type == '1'">{{
                getType(enterpriseOCRType, detail.certificates_type)
              }}</span>
            </div>
            <div>
              证件号码：<span>{{ detail.certificates_code }}</span>
            </div>
          </div>
          <div class="detail-item">
            <div>
              开户网点：<span>{{ detail.branch_name }}</span>
            </div>
            <div>
              操作员：<span>{{ detail.operator_name }}</span>
            </div>
          </div>
          <div class="detail-item">
            <div>
              联系地址：<span>{{ detail.link_address }}</span>
            </div>
            <div>
              绑定车辆数：
              <span v-if="detail.vehicles">
                {{ detail.vehicles.length }}
              </span>
            </div>
          </div>
          <div class="detail-item">
            <div>已绑定车辆：</div>
          </div>
        </div>
      </div>
    </div>
    <div class="car-item">
      <div class="vehicleListscroll">
        <div class="vehicleListO vlist" v-if="detail['vehicles']">
          <ul>
            <li
              :key="index"
              :style="vList[v.vehicle_color]"
              :class="v.vehicle_color === '5' ? 'special' : ''"
              v-for="(v, index) in detail.vehicles"
              class="infoItem"
            >
              <div style="font-size: 15px" class="infoItem_item">
                <div v-text="'[' + getVType(v.vehicle_type) + ']'"></div>
                <div v-text="v.vehicle_code" class="vehicleCode"></div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getVehicleType } from '@/utils/dictionaries'
import {
  personalOCRType,
  enterpriseOCRType,
} from '@/common/const/optionsData.js'
export default {
  components: {},
  props: {
    list: {
      type: Array,
      default: [],
    },
  },
  created() {},
  data() {
    return {
      personalOCRType,
      enterpriseOCRType,
      search: {},
      detail: {},
      currentIndex: null,
      customer_id: '',
      vList: {
        0: {
          background: '#4494F9',
          color: '#fff',
        },
        1: {
          background: '#ffbc52',
        },
        2: {
          background: '#4a4a4a',
          color: '#fff',
        },
        3: {
          background: '#f3f3f3',
        },
        4: {
          backgroundImage: `url(${require('@/assets/gradualGreen.png')})`,
          backgroundRepeat: 'repeat-x',
          backgroundSize: '100% 100%',
        },
        5: {},
        6: {
          backgroundImage: `url(${require('@/assets/bw.png')})`,
          backgroundRepeat: 'repeat-x',
          backgroundSize: '100% 100%',
        },
      },
      vListSpan: {
        5: {
          background: '#ffbc52',
        },
      },
    }
  },
  methods: {
    getVType(val) {
      return getVehicleType(val)
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    reset() {
      //重置
      this.customer_id = ''
      this.currentIndex = null
      this.detail = {}
    },
    change(index) {
      this.currentIndex = index
      this.detail = this.list[index]
      this.customer_id = this.list[index].customer_id
      this.$emit('change', this.customer_id)
    },
    back() {
      this.currentIndex = null
      this.detail = {}
      this.$emit('change', '')
    },
  },
}
</script>

<style lang="scss" scoped>
// .margin-left {
//   margin-left: 50px;
// }
// .bg-color {
//   background: rgb(245, 247, 250);
// }
.icon-back {
  font-size: 30px;
  padding: 10px;
  //   color: #409eff;
}
.icon:hover {
  color: #409eff;
  background: rgb(245, 247, 250);
}
.user-content {
  .item {
    display: flex;
    align-items: center;
    border: 1px solid rgb(245, 247, 250);
    border-radius: 6px;
    padding: 10px;
    margin-top: 10px;
    &:hover {
      background: rgb(245, 247, 250);
    }
    .type {
      margin-right: 30px;
      text-align: center;
      line-height: 36px;
      font-size: 18px;
    }
    .info {
      display: flex;
      flex-direction: column;
      font-size: 14px;
      .username {
        margin-bottom: 10px;
      }
    }
  }
}

.detail-content {
  // border: 1px solid #e8e8e8;
  .content-wrapper {
    display: flex;
    // justify-content: center;
    margin-top: 10px;
    padding: 0 50px;
    .content {
      //   width: 300px;
      .detail-item {
        display: flex;
        font-size: 16px;
        margin-bottom: 15px;
        & > div:first-child {
          width: 260px;
          margin-right: 40px;
        }
      }
    }
  }
}

.car-item {
  font-size: 16px;
  .bind_card_list {
    display: flex;
    font-size: 0.4rem;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 5px;

    .fl {
      color: #666666;

      b {
        color: #01c1b2;
      }
    }

    .rg {
      width: 116px;

      .el-input__inner {
        /*  */
        height: 30px;
        line-height: 30px;
        border-radius: 15px;
      }

      .el-input__icon {
        line-height: 30px;
      }
    }
  }

  .vlist {
    &.vehicleListT {
      ul {
        padding-top: 0;
      }
    }

    ul {
      // padding-top: 20px;
      font-size: 16px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      flex-wrap: wrap;

      li {
        &:hover {
          box-shadow: 2px 2px 3px #999;
        }

        i {
          font-style: normal;
          white-space: nowrap;
        }

        cursor: pointer;
        width: 119px;
        height: 30px;
        word-break: keep-all;
        line-height: 30px;
        color: #000000;
        margin: 0 2px;
        text-align: center;
        margin-bottom: 15px;
        border-radius: 5px;
        box-shadow: 1px 1px 3px #999;
        font-size: 0.3rem;

        span {
          display: inline-block;
          width: 25px;
          border-top-left-radius: 5px;
          border-bottom-left-radius: 5px;
          float: left;
        }

        &.more {
          background: #acacac;
          color: #fff;
          font-size: 15px;
        }
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: all 0.2s ease;
  }

  .fade-enter,
  .fade-leave-to {
    transform: translateY(-10px);
  }
}

.infoItem {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 120px;

  .infoItem_item {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}

.special {
  height: 140px;
  width: 200px;
  background-image: linear-gradient(90deg, #f8aa00 35px, rgb(57, 255, 17) 35px);
}
</style>