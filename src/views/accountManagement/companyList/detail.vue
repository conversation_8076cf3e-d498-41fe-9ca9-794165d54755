<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:互联网账户详情
  * @author:zhang<PERSON>
  * @date:2023/03/29 10:13:14
-->
<template>
  <div class="detail-wrap">

    <div class="orderItem">
      <div class="title">互联网账户详情</div>
      <el-form label-width="120px"
               size="small">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="账户编号:">
              {{accountInfo.netUserNo}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户类型:">
              {{accountInfo.userType=='1'?'个人':"单位"}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户余额:">
              {{moneyFilter(accountInfo.amout) }}元
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="登录名:">
              {{accountInfo.netLoginName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人手机号:">
              {{accountInfo.netMobile}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间:">
              {{accountInfo.createdTime}}

            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="公司名称:">
              {{accountInfo.companyName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="账户状态:">
              {{accountInfo.isActive=='1'?'已激活':'未激活'}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="绑定车辆数:">
              {{accountInfo.bindVehicleCount}}

            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="orderItem">
      <div class="title">账户交易详情</div>
      <div style="padding:10px;">
        <el-tabs type="border-card"
                 v-model="activeName"
                 class="tabs">
          <el-tab-pane label="充值记录"
                       name="recharge">
            <rechargeList :userNo='accountInfo.netUserNo'></rechargeList>
          </el-tab-pane>
          <el-tab-pane label="划拨记录"
                       name="transfer">
            <balancePayList :userNo='accountInfo.netUserNo'></balancePayList>
          </el-tab-pane>
          <el-tab-pane label="消费记录"
                       name="sale">
            <consumption :userNo='accountInfo.netUserNo'></consumption>
          </el-tab-pane>

        </el-tabs>
      </div>
    </div>
    <div class="orderItem">
      <div class="title">绑定车辆信息</div>
      <bindVehicleInfo :accountInfo="accountInfo"></bindVehicleInfo>
    </div>

  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import { typeAdapter } from '@/common/method/formatOptions.js'
import balancePayList from './accountFLowList/balancePayList'
import consumption from './accountFLowList/consumption'
import rechargeList from './accountFLowList/rechargeList'
import tabs from './components/tabs'
import bindVehicleInfo from './components/bindVehicleInfo'
import float from '@/common/method/float.js'

export default {
  components: {
    balancePayList,
    consumption,
    rechargeList,
    tabs,
    bindVehicleInfo,
  },
  props: {
    accountInfo: {
      type: Object,
      default() {
        return {}
      },
    },

    detailVisible: {
      type: Boolean,
      default: false,
    },
  },
  watch: {},
  data() {
    return {
      activeName: 'recharge',
    }
  },
  created() {},
  methods: {
    typeAdapter,
    moneyFilter(val) {
      if (!val || val == '0') {
        return val
      }
      return float.div(val, 100)
    },
  },
}
</script>
<style lang="scss" scoped>
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-bottom: 20px;
}

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
</style>


<style lang='scss' scoped>
.el-form-item--small.el-form-item {
  margin-bottom: 0px;
}
.title {
  font-weight: 550;
  padding: 10px 20px;
  border-bottom: 1px solid #f5f7fa;
}
</style>