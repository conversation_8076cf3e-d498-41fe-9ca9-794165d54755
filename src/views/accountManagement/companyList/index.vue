<template>
  <div class="account-list">
    <dart-search
      ref="searchForm1"
      class="search"
      label-position="right"
      :model="search"
      :formSpan="24"
      :gutter="20"
      :fontWidth="2"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="公司名：" prop="company">
          <el-input v-model="search.company" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="手机号码：" prop="mobile">
          <el-input v-model="search.mobile" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="登录名：" prop="loginName">
          <el-input v-model="search.loginName" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="用户类型：" prop="userType">
          <el-select v-model="search.userType" clearable placeholder="请选择">
            <el-option
              v-for="item in useroption"
              :key="item.index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="用户编号：" prop="userNo">
          <el-input v-model="search.userNo" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true" :span="8">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >查询</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%;"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="netUserNo"
          align="center"
          label="用户编号"
          min-width="200"
        />
        <el-table-column
          prop="netLoginName"
          align="center"
          label="登录名"
          min-width="180"
        />
        <el-table-column prop="idCardNo" align="center" label="证件号" />
        <el-table-column
          prop="amout"
          align="center"
          min-width="120"
          label="账户余额"
        >
          <template slot-scope="scope">
            ￥{{ scope.row.amout | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="companyName"
          align="center"
          label="公司名字"
          min-width="180"
        />
        <el-table-column
          prop="userType"
          align="center"
          min-width="120"
          label="用户类型"
        >
          <template slot-scope="scope">
            {{ scope.row.userType == 2 ? '单位' : '个人' }}
          </template>
        </el-table-column>
        <el-table-column
          prop="netMobile"
          align="center"
          min-width="120"
          label="联系电话"
        />
        <el-table-column
          prop="createdTime"
          align="center"
          min-width="180"
          label="创建时间"
        />
        <el-table-column
          align="left"
          header-align="center"
          width="260"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <!-- <el-button @click="tochange(scope.row)"
                       type="mini">修改</el-button> -->
            <el-dropdown>
              <el-button style="margin:0 10px;" type="mini">修改</el-button>
              <el-dropdown-menu
                trigger="click"
                slot="dropdown"
                style="padding:10px 20px"
              >
                <div class="g-flex">
                  <el-button type="text" @click="tochange(scope.row,'phone')"
                    >互联网账户手机号修改</el-button
                  >
                  <el-button type="text" :disabled="scope.row.userType == 1" @click="tochange(scope.row,'account')">互联网账户基本信息修改</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              @click="torefund(scope.row)"
              v-if="scope.row.amout > 0"
              size="mini"
              type="primary"
              >账户退款</el-button
            >
            <!-- <el-button @click="checkFlow(scope.row)"
                       type="mini">流水查询</el-button> -->
            <el-button @click="detail(scope.row)" type="mini">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-start ">
      <el-pagination
        background
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>
    <editInfo
      v-if="showvisible"
      :visible.sync="showvisible"
      :userdata="usernav"
      @getlist="getAccountList()"
    ></editInfo>
    <editAccount
      v-if="accountShowvisible"
      :visible.sync="accountShowvisible"
      :userdata="usernav"
      @getlist="getAccountList()"
    ></editAccount>
    <!-- 售后订单审批处理 -->
    <dartSlide
      :visible.sync="flowSlideVisiable"
      title="流水记录查询"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <tabs :userNo="userNo" :flowSlideVisiable="flowSlideVisiable"></tabs>
    </dartSlide>

    <dartSlide
      :visible.sync="detailVisible"
      title="互联网账户详情"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
    >
      <detail
        :accountInfo="accountInfo"
        :detailVisible="detailVisible"
      ></detail>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import editInfo from './editInfo.vue'
import editAccount from './editAccount.vue'
import detail from './detail.vue'

import dartSlide from '@/components/dart/Slide/index.vue'
import tabs from './components/tabs.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: String
    }
  },
  components: {
    dartSearch,
    dartSearchItem,
    editInfo,
    dartSlide,
    tabs,
    detail,
    editAccount
  },
  created() {
    this.getAccountList()
  },
  data() {
    return {
      loading: false,
      showvisible: false,
      center: 'center',
      search: {
        company: '',
        mobile: '',
        loginName: '',
        userType: '',
        pageNum: 1,
        pageSize: 10,
        isQueryBindVehicle: true
      },
      total: 0,
      userNoList: [],
      tableData: [],
      useroption: [
        { value: '1', label: '个人' },
        { value: '2', label: '单位' }
      ],
      usernav: {},
      flowSlideVisiable: false,
      userNo: '',
      detailVisible: false,
      accountInfo: {},
      accountShowvisible:false
    }
  },
  methods: {
    detail(val) {
      this.userNo = val.netUserNo
      this.accountInfo = val
      this.detailVisible = true
    },
    getAccountList() {
      console.log('刷新了吗', this.orderId)
      this.loading = true
      this.$request({
        url: this.$interfaces.netnetAccount,
        method: 'post',
        data: this.search
      })
        .then(res => {
          console.log(res)
          this.loading = false
          this.tableData = res.data.records
          this.total = res.data.total
          this.search.pageNum = res.data.current
          this.search.pageSize = res.data.size
        })
        .catch(error => {
          this.loading = false
          console.log('err', err)
        })
    },

    onSearchHandle() {
      this.search.pageNum = 1
      this.getAccountList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.isQueryBindVehicle = true
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    handleSelectionChange(selection) {
      this.userNoList = []
      selection.forEach(item => {
        if (!this.userNoList.includes(item.netUserNo)) {
          this.userNoList.push(item.netUserNo)
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getAccountList()
    },
    close() {
      this.$emit('update:visible', false)
    },
    torefund(item) {
      //跳转退费申请页面
      this.$router.push({
        path: './refund',
        query: {
          userNo: item.netUserNo,
          mobile: item.netMobile
        }
      })
    },
    tochange(nav,type) {
      this.usernav = nav
      if(type == 'phone'){
      this.showvisible = true
      }else{
        this.accountShowvisible = true
      }
    },
    checkFlow(value) {
      this.userNo = value.netUserNo
      this.flowSlideVisiable = true
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  margin: 10px 0;
}
</style>
