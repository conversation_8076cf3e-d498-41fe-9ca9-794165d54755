<template>
  <el-dialog title="信息修改"
             :center="true"
             :visible.sync="dialogVisible"
             width="40%">
    <el-form class="nat-form nat-form-list"
             ref="userForm"
             :model="formData"
             :rules="rules"
             label-width="120px">
      <el-form-item class="my_form_label"
                    label="用户编号"
                    prop="netUserId">
        <el-input disabled
                  v-model="formData.netUserId"> </el-input>
      </el-form-item>
      <el-form-item class="my_form_label"
                    label="公司名称"
                    prop="companyName">
        <el-input disabled
                  v-model="formData.companyName"> </el-input>
      </el-form-item>
      <el-form-item class="my_form_label"
                    label="账户余额"
                    prop="amout">
        <el-input disabled
                  :placeholder="moneyFilter(this.formData.amout)">
        </el-input>
      </el-form-item>
      <el-form-item class="my_form_label"
                    label="登录名"
                    prop="netLoginName">
        <el-input disabled
                  v-model="formData.netLoginName"> </el-input>
      </el-form-item>
      <el-form-item class="my_form_label"
                    label="联系电话"
                    prop="netMobile">
        <el-input v-model="formData.netMobile"> </el-input>
      </el-form-item>
      <!-- <el-form-item class="my_form_label" label="联系人邮箱" prop="createName">
        <el-input  v-model="formData.createName"> </el-input>
      </el-form-item> -->
      <el-form-item label="图形验证码："
                    prop="captchaCode">
        <div class="g-flex g-flex-align-center">
          <el-input v-model="formData.captchaCode"
                    placeholder="请输入图形验证码"></el-input>
          <img :src="captchaUrl"
               class="captcha"
               @click="getCaptcha" />
        </div>
      </el-form-item>
      <el-form-item label="短信验证码："
                    prop="mobile_code">
        <div class="g-flex">
          <el-input v-model="formData.mobile_code"
                    placeholder="请输入短信验证码"></el-input>
          <div @click="sendSmsHandle"
               class="sendSMS">{{ smsName }}</div>
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer"
         class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary"
                 @click="save()">修 改</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import { validPhone } from '@/utils/validate'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    userdata: {
      type: Object,
      default: null,
    },
  },
  data() {
    const validatephone = (rule, value, callback) => {
      if (!validPhone(value)) {
        callback(new Error('请输入正确的手机号码'))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      formData: {},
      captchaUrl: '',
      smsName: '发送验证码',
      rules: {
        netMobile: [
          { required: true, trigger: 'change', validator: validatephone },
        ],
        captchaCode: [
          { required: true, message: '请输入图形验证码', trigger: 'change' },
        ],
        mobile_code: [
          { required: true, message: '请输入短信验证码', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.getCaptcha()
    this.dialogVisible = this.visible
    let form = JSON.parse(JSON.stringify(this.userdata))
    this.formData = {
      ...form,
      captchaId: '',
      captchaCode: '',
      mobile_code: '',
    }
  },
  methods: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
    save() {
      this.$refs.userForm.validate((valid) => {
        if (valid) {
          this.tosave()
        } else {
          return false
        }
      })
    },
    tosave() {
      this.$confirm('是否确认修改用户信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.startLoading()
          let params = {
            mobile: this.formData.netMobile,
            mobileCode: this.formData.mobile_code,
            userNo: this.formData.netUserNo,
          }
          request({
            url: api.changeCompanyAccount,
            method: 'post',
            data: {
              ...params,
            },
          })
            .then((res) => {
              this.endLoading()
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '修改成功!',
                })
                this.$emit('getlist')
                this.dialogVisible = false
              }
            })
            .catch((error) => {
              this.endLoading()
              this.getCaptcha()
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消修改',
          })
        })
    },
    getCaptcha() {
      request({
        url: api.getCaptcha,
        method: 'post',
      }).then((res) => {
        if (res.code == 200) {
          this.formData.captchaId = res.data.captchaId
          this.captchaUrl = res.data.image
        }
      })
    },
    sendSmsHandle() {
      if (!this.formData.captchaCode) {
        this.$message({
          message: '请输入图形验证码',
          type: 'warning',
        })
        return
      }
      if (this.time) return
      this.startLoading()
      let params = {
        captchaId: this.formData.captchaId,
        mobileCode: this.formData.captchaCode,
        // userNo: this.formData.netUserNo,
        mobile: this.formData.netMobile,
      }
      let countdown = 60
      request({
        url: api.sendSms,
        method: 'post',
        data: {
          ...params,
        },
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.time = setInterval(() => {
              countdown = countdown - 1
              this.smsName = countdown + '秒后重新发送'
              if (countdown === 0) {
                clearInterval(this.time)
                this.time = null
                this.smsName = '重新发送'
              }
            }, 1000)
          }
        })
        .catch((error) => {
          this.endLoading()
          this.getCaptcha()
        })
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style>
.captcha {
  width: 120px;
  height: 32px;
  margin-left: 10px;
}
.el-input {
  flex: 1;
}
.sendSMS {
  width: 120px;
  color: #409eff;
  margin-left: 10px;
}
</style>