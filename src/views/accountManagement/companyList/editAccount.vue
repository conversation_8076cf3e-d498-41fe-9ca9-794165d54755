<template>
  <el-dialog
    title="信息修改"
    :center="true"
    :visible.sync="dialogVisible"
    width="40%"
  >
    <el-form
      class="nat-form nat-form-list"
      ref="userForm"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item class="my_form_label" label="用户编号" prop="netUserId">
        <el-input disabled v-model="formData.netUserId"> </el-input>
      </el-form-item>
      <el-form-item class="my_form_label" label="公司名称" prop="companyName">
        <el-input v-model="formData.companyName"> </el-input>
      </el-form-item>

      <el-form-item class="my_form_label" label="地址" prop="address">
        <el-input v-model="formData.address"> </el-input>
      </el-form-item>
      <el-form-item class="my_form_label"  label="证件号码" prop="idCardNo">
        <el-input v-model="formData.idCardNo" disabled> </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="save">修 改</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { changeCompanyName } from '@/api/paramsManagement'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userdata: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: {},
      captchaUrl: '',
      smsName: '发送验证码',
      rules: {}
    }
  },
  created() {
    this.dialogVisible = this.visible
    let form = JSON.parse(JSON.stringify(this.userdata))
    this.formData = form
  },
  methods: {
    save() {
      this.$refs.userForm.validate(async valid => {
        if (valid) {
          this.startLoading()
          let params = {
            licenseNo: this.formData.idCardNo,
            companyName: this.formData.companyName,
            userNo: this.formData.netUserNo,
            address: this.formData.address
          }
          let res = await changeCompanyName(params)
          this.endLoading()
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '修改成功!'
            })
            this.$emit('getlist')
            this.dialogVisible = false
          }
        } else {
          return false
        }
      })
    }
  },
  watch: {
    visible: function(val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  }
}
</script>

<style>
.captcha {
  width: 120px;
  height: 32px;
  margin-left: 10px;
}
.el-input {
  flex: 1;
}
.sendSMS {
  width: 120px;
  color: #409eff;
  margin-left: 10px;
}
</style>