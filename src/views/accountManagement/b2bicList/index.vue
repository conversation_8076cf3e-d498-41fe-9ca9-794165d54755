<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:平安账户列表
  * @author:zhang<PERSON>
  * @date:2022/06/23 16:54:23
!-->
<template>
  <div class="account-list">
    <shadowAccountInfo :mainAccount='mainAccount'
                       @buttonHandle='buttonHandle'></shadowAccountInfo>

    <dart-search ref="searchForm1"
                 class="search"
                 label-position="right"
                 :model="search"
                 :formSpan="24"
                 :gutter="24"
                 :fontWidth="2">
      <template slot="search-form"
                style="padding-left: 20px">
        <dart-search-item label="用户名称:"
                          prop="subAccountName">
          <el-input v-model="search.subAccountName"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item label="互联网账户编号:"
                          prop="userNo">
          <el-input v-model="search.userNo"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item label="平安银行子账号:"
                          prop="subAccountNo">
          <el-input v-model="search.subAccountNo"
                    clearable></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">查询</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table v-loading="loading"
                :data="tableData"
                :align="center"
                height="100%"
                :header-align="center"
                style="width: 100%;"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }">
        <el-table-column prop="subAccountName"
                         align="center"
                         label="平安银行子账号别名"
                         min-width="180" />
        <el-table-column prop="subAccountNo"
                         align="center"
                         label="平安银行子账号"
                         min-width="120" />
        <el-table-column prop="userNo"
                         align="center"
                         label="互联网账户编号"
                         min-width="190" />

        <el-table-column prop="subAccBalance"
                         align="center"
                         min-width="120"
                         label="平安银行子账号余额(元)">
          <template slot-scope="scope">
            {{ scope.row.amount }}
          </template>
        </el-table-column>

        <el-table-column prop="subStt"
                         align="center"
                         label="平安银行子账号状态"
                         min-width="120">
          <template slot-scope="scope">
            {{ scope.row.isDelete =='0'?'正常':'删除' }}
          </template>
        </el-table-column>

        <el-table-column prop="createTime"
                         align="center"
                         label="创建时间"
                         min-width="130" />

        <el-table-column prop="subStt"
                         align="center"
                         label="操作"
                         min-width="170">
          <template slot-scope="scope">
            <el-button type="text"
                       @click="buttonHandle(scope.row,'clearing')">清分记录</el-button>
            <el-button type="text"
                       @click="buttonHandle(scope.row,'flow')">账户流水</el-button>
            <el-button type="text"
                       @click="buttonHandle(scope.row,'detail')">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-start "
         v-if="total>10">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>

    <!-- 侧边展示组件 -->
    <dart-slide :visible.sync="showDrawer"
                :title="DrawerTitle"
                width='80%'
                transfer-dom
                @close='showDrawerClose'>
      <!-- 账户流水 -->
      <b2bFlow v-if="drawerType=='flow'"
               :subAccountNo='subAccountNo'
               :shadowFlag="shadowFlag"></b2bFlow>
      <!-- 账户详情 -->
      <b2bDetail v-if="drawerType=='detail'"
                 :subAccountNo='subAccountNo'></b2bDetail>
      <!-- 清分记录 -->
      <b2bClearing v-if="drawerType=='clearing'"
                   :subAccountNo='subAccountNo'></b2bClearing>
    </dart-slide>

  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import dartSlide from '@/components/Slide/index'
import b2bFlow from './b2bFlow.vue'
import b2bDetail from './b2bDetail.vue'
import b2bClearing from './b2bClearing.vue'
import shadowAccountInfo from './shadowAccountInfo.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    b2bFlow,
    b2bDetail,
    b2bClearing,
    shadowAccountInfo,
  },
  created() {
    this.getB2bList()
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      authorizeDialogVisible: false,
      center: 'center',
      search: {
        subAccountNo: '',
        subAccountName: '',
        pageNum: 1,
        pageSize: 10,
        userNo: '',
      },
      total: 0,
      userNoList: [],
      tableData: [],
      subAccountNo: '',
      showDrawer: false,
      drawerType: '',
      DrawerTitle: '',
      mainAccount: {},
      shadowFlag: false,
    }
  },
  methods: {
    getB2bList() {
      let params = JSON.parse(JSON.stringify(this.search))
      this.loading = true
      this.$request({
        url: this.$interfaces.b2bicList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res)
          this.loading = false
          this.tableData = res.data.childAccountPage.records
          this.total = res.data.childAccountPage.total
          this.mainAccount = res.data.mainAccount
        })
        .catch((error) => {
          this.loading = false
          console.log('error', error)
        })
    },

    onSearchHandle() {
      this.search.pageNum = 1
      this.getB2bList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10
      this.getB2bList()
    },

    changePage(page) {
      this.search.pageNum = page
      this.getB2bList()
    },
    close() {
      this.$emit('update:visible', false)
    },
    showDrawerClose() {
      this.showDrawer = false
      this.drawerType = ''
    },
    buttonHandle(val, type, shadowFlag) {
      if (shadowFlag) {
        this.shadowFlag = true
      } else {
        this.shadowFlag = false
      }
      this.drawerType = type
      this.showDrawer = true
      this.subAccountNo = val.subAccountNo
      if (type == 'flow') {
        this.DrawerTitle = '账户流水'
        return
      }
      if (type == 'detail') {
        this.DrawerTitle = '账户详情'
        return
      }
      if (type == 'clearing') {
        this.DrawerTitle = '清分记录'
        return
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  //   margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  margin: 10px 0;
}
</style>