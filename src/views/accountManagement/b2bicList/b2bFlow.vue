<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:平安子账户流水记录
  * @author:zhang<PERSON>
  * @date:2022/06/27 11:18:24
!-->
<template>
  <div class="detail-box">
    <dart-search ref="searchForm1"
                 class="search"
                 label-position="right"
                 :model="searchInfo"
                 :formSpan="24"
                 :gutter="20"
                 :fontWidth="2">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="开始时间"
                          prop="company">
          <el-date-picker v-model="searchInfo.startDate"
                          type="date"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="结束时间"
                          prop="company">
          <el-date-picker v-model="searchInfo.endDate"
                          type="date"
                          :clearable="false"
                          placeholder="选择日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">查询</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <el-table :data="tableData"
              align="center"
              header-align="center"
              border
              style="width: 100%; margin-bottom: 20px">
      <el-table-column prop="bizFlowNo"
                       align="center"
                       label="流水号"
                       min-width="200" />
      <el-table-column prop="subAccoutName"
                       align="center"
                       label="平安银行子账号别名"
                       min-width="150">
      </el-table-column>
      <el-table-column prop="subAccount"
                       align="center"
                       label="平安银行子账号"
                       min-width="140">
      </el-table-column>
      <el-table-column prop="tranAmount"
                       align="center"
                       label="交易金额">
      </el-table-column>
      <el-table-column prop="balance"
                       align="center"
                       label="交易后余额(元)"
                       min-width="120">
      </el-table-column>
      <el-table-column prop="journalNo"
                       align="center"
                       label="日志号"
                       min-width="200">
      </el-table-column>
      <el-table-column prop="seqNo"
                       align="center"
                       label="顺序号">
      </el-table-column>

      <el-table-column prop="checkTime"
                       align="center"
                       label="交易日期"
                       min-width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.accountDate,scope.row.tranTime) }}
        </template>
      </el-table-column>

      <el-table-column prop="dcFlag"
                       align="center"
                       label="交易类型"
                       min-width="100">
        <template slot-scope="scope">
          {{ scope.row.dcFlag =='D'?'支出':'收入' }}
        </template>
      </el-table-column>
      <el-table-column v-if="!shadowFlag"
                       prop="dcFlag"
                       align="center"
                       label="清分状态"
                       min-width="100">
        <template slot-scope="scope">
          <span> {{scope.row.dcFlag=="D"?'无需清分':(scope.row.isFinish=="0"?'未清分':'已清分')}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark"
                       align="center"
                       label="备注"
                       min-width="180">
      </el-table-column>

    </el-table>
    <div class="pagination g-flex g-flex-start"
         v-if="total>10">
      <el-pagination background
                     :current-page="searchInfo.pageNum"
                     :page-size="searchInfo.pageSize"
                     layout="total,prev,pager,next"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')

export default {
  name: '',
  props: {
    subAccountNo: {
      type: String,
      default: '',
    },
    shadowFlag: {
      type: Boolean,
      default: false,
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      tableData: [],
      searchInfo: {
        pageNum: 1,
        pageSize: 10,
        startDate: '',
        endDate: '',
        subAccountNo: this.subAccountNo,
        // type: '2',
      },
      total: null,
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.searchInfo.startDate = moment().startOf('day').format('YYYY-MM-DD')
    this.searchInfo.endDate = moment().startOf('day').format('YYYY-MM-DD')
    this.accountPayInfo(JSON.parse(JSON.stringify(this.searchInfo)))
  },
  methods: {
    formatDate(time, date) {
      if (time && date) {
        let arrDate = date.split('')
        arrDate[2] = ':' + arrDate[2]
        arrDate[4] = ':' + arrDate[4]
        let allTime =
          moment(time).format('YYYY-MM-DD') +
          ' ' +
          arrDate.join().replace(/,/g, '')
        return allTime
      }
    },
    changePage(val) {
      this.searchInfo.pageNum = val
      this.accountPayInfo(JSON.parse(JSON.stringify(this.searchInfo)))
    },
    accountPayInfo(params) {
      params.startDate = moment(this.searchInfo.startDate).format('YYYYMMDD')
      params.endDate = moment(this.searchInfo.endDate).format('YYYYMMDD')
      this.startLoading()
      this.$request({
        url: this.$interfaces.b2bFlow,
        method: 'post',
        data: params,
      }).then((res) => {
        this.endLoading()
        if (res.code == 200) {
          this.tableData = res.data.records
          this.total = res.data.total
        }
      })
    },
    onSearchHandle() {
      if (moment(this.searchInfo.startDate).isAfter(this.searchInfo.endDate)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
      let params = JSON.parse(JSON.stringify(this.searchInfo))
      this.accountPayInfo(params)
    },
    onResultHandle() {
      this.searchInfo.startDate = moment().startOf('day').format('YYYY-MM-DD')
      this.searchInfo.endDate = moment().startOf('day').format('YYYY-MM-DD')
      let params = JSON.parse(JSON.stringify(this.searchInfo))
      this.accountPayInfo(params)
    },
  },
}
</script>

<style lang='scss' scoped>
.detail-box {
  padding: 0 15px;
}
</style>