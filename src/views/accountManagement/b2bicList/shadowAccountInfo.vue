<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:影子账户详情
  * @author:zhang<PERSON>
  * @date:2022/06/27 16:53:44
!-->
<template>
  <div class="shadowInfo">
    <div class="title">影子账户</div>
    <div class="content">
      <el-form class="nat-form nat-form-list"
               label-width="100px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="影子平安银行子账号别名:">
              <div>{{mainAccount.subAccountName}}</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="状态:">
              <div>{{mainAccount.isDelete=='0'?'正常':'删除'}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间:">
              <div>{{mainAccount.createTime}}</div>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="影子平安银行子账号:">
              <div>{{mainAccount.subAccountNo}}</div>
            </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label="账户余额:">
              <div>{{mainAccount.amount}}</div>
            </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label="操作:">
              <div>
                <!-- <el-button type="text"
                           @click="buttonHandle('clearing')">清分记录</el-button> -->
                <el-button type="text"
                           @click="buttonHandle('flow')">账户流水</el-button>
                <el-button type="text"
                           @click="buttonHandle('detail')">详情</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    mainAccount: {
      type: Object,
      default: {},
    },
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    //流水
    buttonHandle(type) {
      this.$emit(
        'buttonHandle',
        { subAccountNo: this.mainAccount.subAccountNo },
        type,
        'shadowFlag'
      )
    },
  },
}
</script>

<style lang='scss' scoped>
.shadowInfo {
  background-color: #fff;
  margin: 10px 0;
  .title {
    margin: 0 0 10px;
    padding: 16px 24px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
  }
  .nat-form.nat-form-list .el-form-item {
    margin-bottom: 0px;
  }
  ::v-deep .el-form-item__label {
    min-width: 180px !important;
  }
}
</style>
