<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:账户清分记录
  * @author:zhangys
  * @date:2022/07/04 14:57:49
!-->
<template>
  <div class="detail-box">
    <el-table :data="tableData"
              align="center"
              header-align="center"
              border
              style="width: 100%; margin-bottom: 20px">
      <el-table-column prop="outSubAccountName"
                       align="center"
                       label="付款平安银行子账号别名"
                       min-width="200" />
      <el-table-column prop="outSubAccount"
                       align="center"
                       label="付款平安银行子账号"
                       min-width="150">
      </el-table-column>
      <el-table-column prop="inSubAccname"
                       align="center"
                       label="收款平安银行子账号别名"
                       min-width="200">
      </el-table-column>
      <el-table-column prop="inSubAccno"
                       align="center"
                       label="收款平安银行子账号"
                       min-width="150">
      </el-table-column>
      <el-table-column prop="tranAmount"
                       align="center"
                       label="转出金额(元)"
                       min-width="120">
      </el-table-column>
      <el-table-column prop="useEx"
                       align="center"
                       label="转帐附言"
                       min-width="200">
      </el-table-column>
      、
      <el-table-column prop="createTime"
                       align="center"
                       label="创建时间"
                       min-width="160">
      </el-table-column>
      <el-table-column prop="checkTime"
                       align="center"
                       label="检查时间"
                       min-width="120">
      </el-table-column>
      <el-table-column prop="isFinish"
                       align="center"
                       label="清分状态">
        <template slot-scope="scope">
          {{getClearUpStatus( scope.row.isFinish) }}
        </template>
      </el-table-column>
      <el-table-column prop="outId"
                       align="center"
                       label="关联流水ID"
                       min-width="180">
      </el-table-column>

      <el-table-column prop="isTrans"
                       align="center"
                       label="到账状态">
        <template slot-scope="scope">
          {{getReceiptStatus(scope.row.isTrans)  }}
        </template>
      </el-table-column>
      <el-table-column prop="outSubAccbalance"
                       align="center"
                       label="付款平安银行子账号余额(元)"
                       min-width="190">
      </el-table-column>
      <el-table-column prop="inSubAccbalance"
                       align="center"
                       label="收款平安银行子账号余额(元)"
                       min-width="190">
      </el-table-column>
      <el-table-column prop="netBeforeBalance"
                       align="center"
                       label="互联网账户充值前金额(元)"
                       min-width="190">
      </el-table-column>
      <el-table-column prop="netAfterBanlance"
                       align="center"
                       label="互联网账户充值后金额(元)"
                       min-width="190">
      </el-table-column>
    </el-table>
    <div class="pagination"
         v-if="total>10">
      <el-pagination background
                     :current-page="searchInfo.pageNo"
                     :page-size="searchInfo.pageSize"
                     layout="total,prev,pager,next"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>

</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
var moment = require('moment')
import{getReceiptStatus,getClearUpStatus} from '@/common/method/formatOptions.js'
export default {
  name: '',
  props: {
    subAccountNo: {
      type: String,
      default: '',
    },
    flowId: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem },
  data() {
    return {
      tableData: [],
      searchInfo: {
        pageNum: 1,
        pageSize: 10,
        subAccountNo: this.subAccountNo,
        flowId: '',
      },
      total: null,
    }
  },
  computed: {},
  watch: {},
  created() {
    this.clearingInfo()
  },
    methods: {
    getClearUpStatus,getReceiptStatus,
    changePage(val) {
      this.searchInfo.pageNo = val
      this.clearingInfo()
    },
    clearingInfo() {
      let params = JSON.parse(JSON.stringify(this.searchInfo))
      this.startLoading()
      this.$request({
        url: this.$interfaces.b2bViewClearing,
        method: 'post',
        data: params,
      }).then((res) => {
        this.endLoading()
        if (res.code == 200) {
          this.tableData = res.data.records
        }
      })
    },
  },
}
</script>

<style lang='scss' scoped>
.detail-box {
  padding: 0 15px;
}
::v-deep .el-pager {
  pointer-events: none;
}
</style>