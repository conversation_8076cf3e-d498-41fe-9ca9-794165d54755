<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:银行子账户详情
  * @author:zhang<PERSON>
  * @date:2022/06/27 16:21:32
!-->
<template>
  <div class="shadowInfo">
    <div class="title">账户详情</div>
    <div class="content">
      <el-form class="nat-form nat-form-list"
               label-width="100px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="清分台账编码别名:">
              <div>{{detailData.subAccountName}}</div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="状态:">
              <div>{{detailData.stt=='A'?'正常':'删除'}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="修改时间:">
              <div>{{detailData.lastModifyDate}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="清分台账编码:">
              <div>{{detailData.subAccountNo}}</div>
            </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label="币种:">
              <div>{{detailData.ccyCode}}</div>
            </el-form-item>

          </el-col>
          <el-col :span="8">
            <el-form-item label="清分台账编码余额:">
              <div>{{detailData.subAccBalance}}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
var moment = require('moment')

export default {
  name: '',
  props: {
    subAccountNo: {
      type: String,
      default: '',
    },
  },
  components: {},
  data() {
    return {
      detailData: {},
    }
  },
  computed: {},
  watch: {},
  created() {
    this.b2bView()
  },
  methods: {
    b2bView() {
      let params = {
        subAccountNo: this.subAccountNo,
      }
      this.$request({
        url: this.$interfaces.b2bView,
        method: 'post',
        data: params,
      }).then((res) => {
        console.log(res)
        if (res.code == 200) {
          this.detailData = res.data
          this.detailData.lastModifyDate = moment(
            this.detailData.lastModifyDate
          ).format('YYYY-MM-DD')
        }
      })
    },
  },
}
</script>

<style lang='scss' scoped>
.shadowInfo {
  background-color: #fff;
  .title {
    margin: 0 0 10px;
    padding: 10px 24px;
    font-weight: 600;
    border-bottom: 1px solid #f0f0f0;
  }
  .nat-form.nat-form-list .el-form-item {
    margin-bottom: 0px;
  }
  ::v-deep .el-form-item__label {
    min-width: 160px !important;
  }
}
</style>