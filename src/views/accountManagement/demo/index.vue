<template>
  <div class="account-list">
    <dart-search ref="searchForm1"
                 class="search"
                 formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="开始日期："
                          prop="company">
          <el-input v-model="search.company"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="结束日期："
                          prop="mobile">
          <el-input v-model="search.mobile"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="统计结束日期："
                          prop="mobile">
          <el-input v-model="search.mobile"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="统计结束日期："
                          prop="mobile">
          <el-input v-model="search.mobile"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex ">
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">查询</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table v-loading="loading"
                :data="tableData"
                :align="center"
                height="100%"
                :header-align="center"
                style="width: 100%;"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }">

        <el-table-column prop="netUserId"
                         align="center"
                         label="用户编号"
                         min-width="180" />
        <el-table-column prop="netLoginName"
                         align="center"
                         label="登录名"
                         min-width="180" />
        <el-table-column prop="incomeAmount"
                         align="center"
                         min-width="120"
                         label="账户余额">
          <template slot-scope="scope">
            ￥{{ scope.row.amout | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column prop="companyName"
                         align="center"
                         label="公司名字"
                         min-width="180" />
        <el-table-column prop="netMobile"
                         align="center"
                         min-width="120"
                         label="联系电话" />
        <el-table-column prop="createdTime"
                         align="center"
                         min-width="180"
                         label="创建时间" />
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination @size-change="handleSizeChange"
                     @current-change="handleCurrentChange"
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { getSecuCode } from '@/utils/dialogUtils'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
  },
  created() {

  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      authorizeDialogVisible: false,
      center: 'center',
      search: {
        company: '',
        mobile: '',
        loginName: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      userNoList: [],
      tableData: [],
    }
  },
  methods: {
    handleSizeChange() {

    },
    handleCurrentChange() {

    },
    getAccountList() {
      console.log('刷新了吗', this.orderId)
      this.loading = true;
      this.$request({
        url: this.$interfaces.getNetAccountList,
        method: 'post',
        data: this.search
      }).then(res => {
        console.log(res);
        this.loading = false
        this.tableData = res.data.records
        this.total = res.data.total
        this.search.pageNum = res.data.current
        this.search.pageSize = res.data.size
      }).catch(error => {
        this.loading = false
        console.log('err', err)
      });

    },

    onSearchHandle() {
      this.search.pageNum = 1
      this.getAccountList()
    },
    onResultHandle() {

      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    handleSelectionChange(selection) {
      this.userNoList = []
      selection.forEach((item) => {
        if (!this.userNoList.includes(item.netUserNo)) {
          this.userNoList.push(item.netUserNo)
        }
      })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getAccountList()
    },
    close() {
      this.$emit('update:visible', false)
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
</style>
