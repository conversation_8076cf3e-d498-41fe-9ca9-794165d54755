<template>
  <div class="user">
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <div class="title">互联网账户余额明细表</div>

        <dart-search-item label="用户名称：" prop="userName">
          <el-input
            v-model="search.userName"
            disabled
            placeholder=""
          ></el-input>
          <el-button
            style="margin-left: 10px"
            type="primary"
            @click="selectUserHandle"
            >选择</el-button
          >
        </dart-search-item>
        <dart-search-item label="车牌：" prop="carNo">
          <el-input v-model="search.carNo" placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色：" prop="carColor">
          <el-select v-model="search.carColor" placeholder="请选择" clearable>
            <el-option
              v-for="item in licenseColorOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="卡号：" prop="cardNo">
          <el-input v-model="search.cardNo" placeholder=""></el-input>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="section">
      <div class="g-flex g-flex-align-center">
        <div style="margin-right: 20px; font-weight: bold">
          互联网账户余额明细表 ( 实时余额 )
        </div>
        <div class="g-flex">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </div>
      <div style="margin-top: 10px">
        <span style="color: red">注:</span> 该表数据统计实时出具
      </div>
    </div>
    <div class="section">
      <div style="margin-bottom: 20px; font-weight: bold">
        互联网账户余额明细表
      </div>
      <div class="g-flex g-flex-align-center">
        <div style="margin-right: 30px">
          <span style="color: red">*</span>统计截止时间：
        </div>
        <div style="margin-right: 30px">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期时间"
            v-model="date"
          >
          </el-date-picker>
        </div>
        <div class="g-flex">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle1"
            >搜索</el-button
          >
          <!-- <el-button size="mini" @click="onResultHandle1">重置</el-button> -->
        </div>
      </div>
      <div></div>
      <div style="margin-top: 10px">
        <span style="color: red">注:</span>
        该表数据不为实时出具，系统统计完成后，可在下方“任务执行情况”处下载，同时将通过“消息通知”管理进行通知，请注意查收
      </div>
    </div>
    <div class="section">
      <div class="g-flex g-flex-align-center">
        <span style="font-weight: bold">任务执行情况</span>
        <div class="g-flex" style="margin-left: 15px">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="getJobList"
            >任务刷新</el-button
          >
          <span style="color: red; margin: 5px 0 0 10px"
            >如果无法点击下载按钮，请点击任务刷新</span
          >
        </div>
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          :align="center"
          :header-align="center"
          border
          style="width: 100%"
          :row-style="{ height: '54px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '54px' }"
          :header-cell-style="{ padding: '0px' }"
          row-key="id"
        >
          <el-table-column prop="custName" align="center" label="用户名称" />
          <el-table-column prop="custType" align="center" label="用户类型">
            <template slot-scope="scope">
              <span v-if="scope.row.custType == 0">个人</span>
              <span v-if="scope.row.custType == 1">单位</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="custMobile"
            align="center"
            min-width="120"
            label="手机号码"
          />
          <el-table-column prop="custMastId" align="center" label="用户编号" />
          <el-table-column
            prop="idNo"
            align="center"
            min-width="230"
            label="证件编号"
          />
          <el-table-column
            prop="parmDate"
            align="center"
            min-width="160"
            label="统计截止时间"
          />
          <el-table-column
            prop="startTime"
            align="center"
            min-width="160"
            label="任务开始时间"
          />
          <el-table-column
            prop="endTime"
            align="center"
            min-width="160"
            label="任务结束时间"
          />
          <el-table-column
            prop="createBy"
            align="center"
            min-width="160"
            label="操作员"
          />
          <el-table-column
            prop="branchNo"
            align="center"
            min-width="150"
            label="所属网点"
          />
          <el-table-column
            prop="jobStatus"
            align="center"
            min-width="160"
            label="任务状态"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.jobStatus == 0">创建</span>
              <span v-if="scope.row.jobStatus == 1">运行中</span>
              <span v-if="scope.row.jobStatus == 2">运行成功</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed="right"
            label="操作"
            header-align="center"
            min-width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="primary"
                size="mini"
                :disabled="scope.row.jobStatus != 2"
                @click="downFile(scope.row.fileUrl)"
                >下载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div> -->
    <!-- 选择用户 -->
    <selectUser
      :visible.sync="selectUserVisible"
      @on-submit="selectUserSubmit"
    ></selectUser>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import selectUser from './selectUser'
import { licenseColorOption } from '@/common/const/optionsData'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    selectUser,
  },
  data() {
    return {
      licenseColorOption,
      list: [],
      selectUserVisible: false,
      loading: false,
      center: true,
      businessType: '3', //互联网账户余额明细表
      search: {
        custMastId: '',
        userName: '',
        carNo: '',
        carColor: '',
        cardNo: '',
      },
      date: '',
      tableHeight: 0,
      tableData: [],
      rules: {
        // startTime: [
        //   { required: true, message: '请选择开始日期', trigger: 'change' },
        // ],
        // // endTime: [
        // //   { required: true, message: '请选择结束日期', trigger: 'change' },
        // // ],
        // searchitem: [
        //   { required: true, message: '请选择统计部门', trigger: 'change' },
        // ],
      },
    }
  },
  created() {
    this.getJobList('init')
  },
  mounted() {},
  methods: {
    selectUserHandle() {
      this.selectUserVisible = true
    },
    onSearchHandle() {
      if (
        !this.search.carNo &&
        !this.search.cardNo &&
        !this.search.custMastId
      ) {
        this.$message.warning('用户，车牌，卡号，不能同时为空！')
        return
      }
      if (!this.search.custMastId && !this.search.cardNo) {
        if (this.search.carNo && !this.search.carColor) {
          this.$message.warning('请再选择车牌颜色！')
          return
        }
        if (!this.search.carNo && this.search.carColor) {
          this.$message.warning('请再输入车牌！')
          return
        }
      }
      let data = {
        businessType: this.businessType,
        operatorType: '1',
        custMastId: this.search.custMastId,
        carNo: this.search.carNo,
        carColor: this.search.carColor,
        cardNo: this.search.cardNo,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.searchUserBalance,
        method: 'post',
        data: data,
        responseType: 'blob',
      })
        .then((res) => {
          console.log('实时报表res===>>', res)
          this.getBlob(
            res,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '八桂行卡余额明细报表(实时余额)'
          )
          // if (res.code == 200) {
          //   this.tableData = res.data && res.data.length ? res.data : []
          // }
        })
        .catch((error) => {})
    },
    onSearchHandle1() {
      if (
        !(this.search.carNo || this.search.carColor) &&
        !this.search.cardNo &&
        !this.search.custMastId
      ) {
        this.$message.warning('用户，车牌，卡号，不能同时为空！')
        return
      }
      if (!this.search.custMastId && !this.search.cardNo) {
        if (this.search.carNo && !this.search.carColor) {
          this.$message.warning('请再选择车牌颜色！')
          return
        }
        if (!this.search.carNo && this.search.carColor) {
          this.$message.warning('请再输入车牌！')
          return
        }
      }
      if (!this.date) {
        this.$message.warning('请先选择统计截止时间！')
        return
      }
      console.log('date', this.date)
      let data = {
        businessType: this.businessType,
        operatorType: '2',
        date: this.date,
        custMastId: this.search.custMastId,
        carNo: this.search.carNo,
        carColor: this.search.carColor,
        cardNo: this.search.cardNo,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.searchUserBalance,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.endLoading()
          console.log('历史报表res===>>', res)
          if (res.code == 200) {
            //继续调用任务列表
            this.getJobList()
          }
        })
        .catch((error) => {
          this.endLoading()
        })
    },
    getJobList(type) {
      let data = {
        businessType: this.businessType,
        operatorType: '2',
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.userBalanceJob,
        method: 'post',
        data: data,
      })
        .then((res) => {
          console.log('调动任务成功res===>>', res)
          this.endLoading()
          if (res.code == 200) {
            if (type != 'init') {
              this.$message.success('任务已执行刷新')
            }
            this.tableData = res.data && res.data.length ? res.data : []
          }
        })
        .catch((error) => {})
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
      this.endLoading()
    },
    downFile(url) {
      this.openUrl(url)
    },
    openUrl(url) {
      window.open(url)
    },
    selectUserSubmit(userInfo) {
      console.log('userInfo', userInfo)
      this.search.userName = userInfo.customer_name
      this.search.custMastId = userInfo.customer_id
    },
    onResultHandle() {
      for (let key in this.search) {
        this.search[key] = ''
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  .table {
    margin-top: 20px;
    padding: 0;
  }
}
.section {
  margin-top: 15px;
  background: #fff;
  padding: 30px;
}

::v-deep.dart-search-wrapper
  .dart-search-container
  .el-form-item__content
  .el-input {
  width: 70%;
}
</style>
