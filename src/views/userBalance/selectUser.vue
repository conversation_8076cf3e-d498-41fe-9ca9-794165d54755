<template>
  <div>
    <el-dialog
      title="选择用户"
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="70%"
      center
    >
      <div class="account-list">
        <dart-search
          ref="selectAccount"
          class="search"
          label-position="right"
          :model="search"
          :searchOperation="false"
          :formSpan="24"
          :gutter="20"
          :fontWidth="2"
        >
          <template slot="search-form" style="padding-left: 10px">
            <dart-search-item label="客户编号：" prop="customerId">
              <el-input
                v-model="search.customerId"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item label="客户名称：" prop="customerName">
              <el-input
                v-model="search.customerName"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item label="手机号码：" prop="phone">
              <el-input
                v-model="search.phone"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item label="证件号码：" prop="certificatesCode">
              <el-input
                v-model="search.certificatesCode"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item label="卡号：" prop="cardNo">
              <el-input
                v-model="search.cardNo"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item label="车牌：" prop="carNo">
              <el-input
                v-model="search.carNo"
                placeholder=""
                clearable
              ></el-input>
            </dart-search-item>
            <dart-search-item :is-button="true" :span="24">
              <div class="g-flex g-flex-end">
                <el-button
                  type="primary"
                  size="mini"
                  @click="onSearchHandle"
                  native-type="submit"
                  >查询</el-button
                >
                <el-button size="mini" @click="onResetHandle">重置</el-button>
              </div>
            </dart-search-item>
          </template>
        </dart-search>
        <div class="table-box">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            height="300"
            @row-click="singleElection"
            style="width: 100%"
          >
            <el-table-column label="" width="50">
              <template slot-scope="scope">
                <el-checkbox
                  :value="templateSelection == scope.row.customer_id"
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column prop="customer_id" align="center" label="客户编码" />
            <el-table-column prop="customer_name" align="center" label="客户名称" />
            <el-table-column prop="customer_type" align="center" label="客户类型">
              <template slot-scope="scope">
                {{ getcustomerType(scope.row.customer_type) }}
              </template>
            </el-table-column>
            <el-table-column prop="link_man" align="center" label="联系人" />
            <el-table-column prop="link_mobile" align="center" label="手机" />
            <el-table-column prop="certificates_code" align="center" label="证件号码" />
          </el-table>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="onSubmitHandle" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { getcustomerType } from '@/common/method/formatOptions'
var moment = require('moment')

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      dialogVisible: false,
      search: {
        customerId: '',
        customerName: '',
        certificatesCode: '',
        phone: '',
        cardNo: '',
        carNo: '',
      },
      loading: false,
      center: 'center',
      tableData: [],
      templateSelection: '',
      currentAccountInfo: null,
    }
  },

  watch: {
    visible: function (val) {
      // this.init()
      this.dialogVisible = val
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
  },
  computed: {},
  created() {
    // this.init()
  },
  methods: {
    getcustomerType,
    // init() {
    //   this.templateSelection = ''
    //   this.currentAccountInfo = ''
    //   this.tableData = []
    //   for (let key in this.search) {
    //     this.search[key] = ''
    //   }
    //   this.$nextTick(() => {
    //     this.dialogVisible = this.visible
    //   })
    // },
    onResetHandle() {
      this.$refs.selectAccount.resetForm()
    },
    onSearchHandle() {
      console.log('111',this.search)
      if (
        !this.search.customerId &&
        !this.search.customerName &&
        !this.search.certificatesCode &&
        !this.search.phone &&
        !this.search.cardNo &&
        !this.search.carNo
      ) {
        this.$message.warning('查询用户参数不能同时为空！')
        return
      }
      let data = {
        ...this.search,
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.customerBizList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.endLoading()
          console.log('查询用户res===>>', res)
          if (res.code == 200) {
            this.tableData = res.data && res.data.length ? res.data : []
          }
        })
        .catch((error) => {})
    },
    singleElection(row, column, event) {
      if (event.target.tagName === 'INPUT') {
        return
      }
      console.lo
      if (this.templateSelection == row.customer_id) {
        this.templateSelection = ''
        this.currentAccountInfo = ''
        return
      }
      this.templateSelection = row.customer_id
      this.currentAccountInfo = row
    },
    onSubmitHandle() {
      if (
        this.currentAccountInfo &&
        Object.keys(this.currentAccountInfo).length
      ) {
        console.log(this.currentAccountInfo, 'this.currentAccountInfo')
        this.$emit('on-submit', this.currentAccountInfo)
        this.dialogVisible = false
      } else {
        this.$message({
          message: '请选择用户',
          type: 'warning',
        })
      }
    },
  },
}
</script>
<style lang="scss"  scoped>
.repayment {
  position: relative;
  .view-item {
    margin-bottom: 6px;
  }
  .captcha {
    width: 120px;
    height: 32px;
    margin-left: 10px;
  }
  .sendSMS {
    width: 120px;
    color: #409eff;
    margin-left: 10px;
  }
}
.photograph {
  position: absolute;
  top: 0;
  left: 50%;
}
</style>