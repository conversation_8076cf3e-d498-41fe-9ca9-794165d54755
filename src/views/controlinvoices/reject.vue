<template>
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogFormVisible" width="30%" :before-close="tochange">
      <div class="tiele">请填写拒绝理由:</div>
			<el-input
				type="textarea"
				:rows="3"
				resize="none"
				placeholder="请输入拒绝理由"
				v-model="remarknav">
			</el-input>

      <div class="foot">
        <el-button size="small" @click="toreject()" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      remarknav: '',
      dialogFormVisible:false
    }
  },
  created(){
		this.$nextTick(() => {
      this.dialogFormVisible = this.visible;
    })
	},
  methods: {
    tochange() {
      this.dialogFormVisible = false
    },
    toreject() {
      this.$confirm('是否确认拒绝', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let data = {
            order_id: this.$route.query.id,
            refuse_reason: this.remarknav,
          }
          request({
            url: api.specialInvoicerefuse,
            method: 'post',
            data: data,
          })
            .then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '拒绝成功!',
                })
                this.$store.dispatch('tagsView/delView', this.$route)
                this.$router.push({
                  path: '/controlinvoices/specialticket',
                })
              }
            })
            .catch(() => {})
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消',
          })
        })
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}  
::v-deep .el-dialog {
  margin-top: 0 !important;
  max-width: 500px;
  min-width: 400px;
}
.tiele {
  font-weight: 700;
  margin-bottom: 15px;
}
.textarea {
  text-align: left;
  margin: 0px 20px;
  height: 100px;
  width: 100%;
  margin: auto;
  padding: 4px;
  border-radius: 3px;
  border: 1px solid #b7bac0;
  // resize: vertical;
  overflow: auto;
  outline: none;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.foot {
  margin-top: 20px;
  text-align: center;
}
</style>