<template>
  <div>
    <el-dialog :close-on-click-modal="false" :visible.sync="dialogFormVisible" width="30%" :before-close="tochange">
     <el-form :model="ruleForm" :rules="type == 1 ? rules : rules2" ref="ruleForm" label-width="125px" class="demo-ruleForm">
        <el-form-item label="开票日期：" prop="value1">
        <el-date-picker
        v-model="ruleForm.value1"
        type="datetime"
        placeholder="选择日期时间">
        </el-date-picker>
        </el-form-item>
        <el-form-item label="发票代码：" prop="invoice_code">
            <el-input v-model="ruleForm.invoice_code"></el-input>
        </el-form-item>
        <el-form-item label="发票号码：" prop="invoice_num">
            <el-input v-model="ruleForm.invoice_num"></el-input>
        </el-form-item>
     </el-form>   

      <div class="foot">
        <el-button size="small" @click="toreject()" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
var moment = require('moment');
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type:{
      type:Number,
      default:null
    }
  },
  data() {
    return {
      dialogFormVisible:false,
      ruleForm:{
        value1:null,
        invoice_code:'',
        invoice_num:''
      },
      rules:{
        value1: [
          { required: true, message: '请选择开票日期', trigger: 'change' },
        ],
        invoice_code: [
          { required: true, message: '请输入发票代码', trigger: 'change' },
        ],
        invoice_num: [
          { required: true, message: '请选择发票号码', trigger: 'change' },
        ],
      },
      rules2:[]
    }
  },
  created(){
		this.$nextTick(() => {
      this.dialogFormVisible = this.visible;
    })
	},
  methods: {
    tochange() {
      this.dialogFormVisible = false
    },
    toreject() {
        this.$refs['ruleForm'].validate((valid) => {
          if (valid) {
            this.goconfirm()
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    goconfirm(){
        console.log(this.ruleForm.value1)
        this.$confirm('是否确认通过开票申请', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$emit('changeload',true)
          let data = {
            order_id: this.$route.query.id,
            billing_date: this.gettime(this.ruleForm.value1),
            invoice_code: this.ruleForm.invoice_code,
            invoice_num: this.ruleForm.invoice_num,
          }
          request({
            url: api.specialInvoiceconfirm,
            method: 'post',
            data: data,
          })
            .then((res) => {
              this.$emit('changeload',false)
              if (res.code == 200) {
                this.$message({
                  type: 'success',
                  message: '通过成功!',
                })
                this.$store.dispatch('tagsView/delView', this.$route)
                this.$router.push({
                  path: '/controlinvoices/specialticket',
                })
              }
            })
            .catch(() => {
              this.$emit('changeload',false)
            })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消通过',
          })  
        })
    },
    gettime(data) {
      if(data){
        const value =
          moment(data).format("YYYYMMDDHHmmss")
        return value
      }
      return ''
  },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}  
::v-deep .el-dialog {
  margin-top: 0 !important;
  max-width: 500px;
  min-width: 400px;
}
::v-deep .el-date-editor{
    width: 100% !important;
}
.tiele {
  font-weight: 700;
  margin-bottom: 15px;
}
.textarea {
  text-align: left;
  margin: 0px 20px;
  height: 100px;
  width: 100%;
  margin: auto;
  padding: 4px;
  border-radius: 3px;
  border: 1px solid #b7bac0;
  // resize: vertical;
  overflow: auto;
  outline: none;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.foot {
  margin-top: 20px;
  text-align: center;
}
</style>