<template>
  <div>
    <el-dialog
      title="撤销申请"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      width="40%"
      custom-class="shipment-dialog"
    >
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="125px" class="demo-ruleForm">
        <el-form-item label="申请用户：" prop="apply_user">
            <el-input v-model="ruleForm.apply_user"></el-input>
        </el-form-item>
        <el-form-item label="请填写撤销理由：" prop="apply_reason">
            <el-input
                type="textarea"
                :rows="3"
                resize="none"
                placeholder="请输入驳回理由"
                v-model="ruleForm.apply_reason"
            >
            </el-input>
        </el-form-item>
    </el-form>
        
      <div class="foot">
        <el-button size="small" @click="submitForm('ruleForm')" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
		orderid:{
			type: String,
      default: null,
		}
  },
  data() {
    return {
      dialogFormVisible: false,
      ruleForm:{
        apply_user:'',
        apply_reason:''
      },
      rules:{
        apply_user:[{ required: true, message: '请输入申请用户', trigger: 'blur' }]
      }
    }
  },
	created(){
		this.$nextTick(() => {
      this.dialogFormVisible = this.visible;
    })
	},
	methods:{
    submitForm(formName){
      this.$refs[formName].validate((valid) => {
				if (valid) {
					this.tocancel()
				} else {
					return false;
				}
			});
    },
    tocancel(){
			let data = {
        order_id:this.orderid,
				apply_user:this.ruleForm.apply_user,
				apply_reason:this.ruleForm.apply_reason
      }
      request({
        url: api.specialInvoicecancel,
        method: 'post',
        data: data,
      })
        .then((res) => {
					if(res.code == 200){
            this.$message({
						message: '撤销成功',
						type: 'success'
					});
					this.$emit('golist')
					this.dialogFormVisible = false
					}
        })
        .catch(() => {

        })
		}
	},
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
}  
::v-deep .el-dialog {
  margin-top: 0 !important;
  max-width: 500px;
  min-width: 400px;
}
/* .el-form-item__label{
  padding: 0;  
} */
.tiele {
  font-weight: 700;
  padding-left: 15px;
  margin-bottom: 15px;
}
.textarea {
  text-align: left;
  margin: 0px 20px;
  height: 100px;
  width: 100%;
  margin: auto;
  padding: 4px;
  border-radius: 3px;
  border: 1px solid #b7bac0;
  overflow: auto;
  outline: none;
}
::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.foot {
  margin-top: 20px;
  text-align: center;
}
</style>