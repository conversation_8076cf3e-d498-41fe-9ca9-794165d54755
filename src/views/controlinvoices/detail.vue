<template>
  <div v-loading.fullscreen.lock="showLoading">
    <div class="down">
      <div class="thetable">
        <div class="title">开票信息</div>
        <div class="downnav">
          <el-form class="nat-form nat-form-list" label-width="140px">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="订单号：">
                  <div>{{ $route.query.id}}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="状态：">
                   <!-- <div>{{ detaildata.statusStr }}</div> -->
                  <div>{{ detaildata.statusStr }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="特殊票种：">
                  <!-- <div>{{ detaildata.specialInvoiceKindStr }}</div> -->
                  <div>{{ getspecial(detaildata.specialInvoiceKind) }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
               <el-col :span="8">
                <el-form-item label="开票日期：">
                  <div>{{ detaildata.billingDate }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开票结果：">
                  <div>{{ detaildata.resultMsg }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开票类型：">
                  <!-- <div>{{ detaildata.invoiceTypeCodeStr}}</div> -->
                  <div v-if="detaildata.invoiceTypeCode">
                    {{ getinvoiceTypeCode(detaildata.invoiceTypeCode) }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="发票代码：">
                  <div>{{ detaildata.invoiceCode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发票号码：">
                  <div>{{ detaildata.invoiceNum }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发票状态：">
                  <!-- <div>{{ detaildata.invoiceStatusStr }}</div> -->
                  <div v-if="detaildata.invoiceStatus">
                    {{ detaildata.invoiceStatus == 1 ? '正常' : '已红冲' }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="发票类型：">
                  <!-- <div>{{ detaildata.invoiceTypeStr}}</div> -->
                  <div v-if="detaildata.invoiceType">
                    {{ getinvoiceType(detaildata.invoiceType)}}
                  </div>
                </el-form-item>
              </el-col>
               <el-col :span="8">
                <el-form-item label="征税方式：">
                   <!-- <div>{{ detaildata.zsfsStr }}</div> -->
                  <div>
                    {{ detaildata.zsfs == 0 ? '普通征税' : '差额征税' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="合计金额(元)：">
                  <div>{{ detaildata.totalAmount }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="收款人：">
                  <div>{{ detaildata.payee }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="复核人：">
                  <div>{{ detaildata.checker }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开票人：">
                  <div>{{ detaildata.drawer }}</div>
                </el-form-item>
              </el-col>

            </el-row>
                        <el-row :gutter="24">

              <el-col :span="8">
                <el-form-item label="手机号：">
                  <div>{{ detaildata.mobilePhone }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="邮箱：">
                  <div>{{ detaildata.email }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="备注：">
                  <div>{{ detaildata.remark }}</div>
                </el-form-item>
              </el-col>
            </el-row>
           
          </el-form>
        </div>
      </div>
    </div>
    <div class="down">
      <div class="thetable">
        <div class="title">购买方信息</div>
          <el-form class="nat-form nat-form-list" label-width="140px">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="名称：">
                  <div>{{ detaildata.buyerName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="纳税人识别号：">
                  <div>{{ detaildata.buyerTaxpayerNum }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="地址：">
                  <div>{{ detaildata.buyerAddress }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="电话：">
                  <div>{{ detaildata.buyerPhone }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开户行名称：">
                  <div>{{ detaildata.buyerBankName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开户行账号：">
                  <div>{{ detaildata.buyerBankAccount }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
      </div>
    </div>  
    <div class="down">
      <div class="thetable">
        <div class="title">销售方信息</div>
        <el-form class="nat-form nat-form-list" label-width="140px">
          <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="名称：">
                  <div>{{ detaildata.sellerName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="纳税人识别号：">
                  <div>{{ detaildata.sellerTaxpayerNum }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="地址：">
                  <div>{{ detaildata.address }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="电话：">
                  <div>{{ detaildata.phone }}</div>
                </el-form-item>
              </el-col>
               <el-col :span="8">
                <el-form-item label="开户行名称：">
                  <div>{{ detaildata.sellerBankName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="开户行账号：">
                  <div>{{ detaildata.sellerBankAccount }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
            </el-row>
        </el-form>
      </div>
    </div>
    <div
          class="downnav"
          v-for="(item, index) in detaildata.invoiceItemList"
          :key="index"
        >
    <div class="down">
      <div class="thetable">
        <div class="title">商品信息</div>
        
          <el-form class="nat-form nat-form-list" label-width="140px">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="商品编码：">
                  <div>{{ item.goodsCode }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品名称：">
                  <div>{{ item.goodsName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品类型：">
                  <div>{{ item.models }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="单位：">
                  <div>{{ item.unit }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="数量：">
                  <div>{{ item.quantity }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="税率：">
                  <div>{{ item.taxRate }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="扣除金额(元)：">
                  <div>{{ item.discount }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="含税金额(元)：">
                  <div>{{ item.amountWithTax }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="发票行性质：">
                  <div>{{ getinvoiceItem(item.invoiceItem) }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="零税率标识：">
                  <div v-if="item.zeroTaxFlag">
                    {{ getzeroTaxFlag(item.zeroTaxFlag) }}
                  </div>
                  <div v-else>无</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="优惠政策标识：">
                  <div>
                    {{ item.preferentialPolicyFlag == 0 ? '不使用' : '使用' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="增值税特殊管理：">
                  <div>{{ item.vatSpecialManagement }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="备注：">
                  <div>{{ item.remark }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </div>
    <div class="foot">
      <!-- status 0:新建,1:已开票,2:开票失败,3:开票中,4:撤销申请中,5:已撤销,6:红冲中,7:已红冲-->
      <el-button v-if="detaildata.status == 0 || detaildata.status == 4" size="small" @click="adopt(2)" type="primary">审核通过</el-button>
      <el-button v-if="detaildata.status == 3" size="small" @click="adopt(1)" type="primary">开票完成</el-button>
      <el-button v-if="detaildata.status == 6" size="small" @click="adopt(2)" type="primary">红冲开票</el-button>
      <el-button v-if="detaildata.status == 0 || detaildata.status == 4" size="small" @click="toreject()" type="danger">拒绝</el-button>
      <el-button v-if="detaildata.status == 6" size="small" @click="toreject()" type="danger">红冲拒绝</el-button>
    </div>
		<reject v-if="rejectDialog" :visible.sync='rejectDialog'></reject>
    <confirm v-if="adoptDialog" :visible.sync='adoptDialog' :type='confirmtype' @changeload="changeshowload"></confirm>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import reject from './reject.vue'
import confirm from './confirm.vue'
export default {
	components:{
    reject,
    confirm
	},
  data() {
    return {
      detaildata: {},
			rejectDialog:false,
      adoptDialog:false,
      showLoading:false,
      confirmtype:null,
      invoiceItemdata: [
        { value: 0, label: '正常行' },
        { value: 1, label: '折扣行' },
        { value: 2, label: '被折扣行' },
      ],
      TaxFlagdata: [
        { value: 0, label: '出口退税' },
        { value: 1, label: '出口免税和其他免税优惠政策' },
        { value: 2, label: '不征增值税' },
        { value: 3, label: '普通零税率' },
      ],
      TypeCodedata: [
        { value: '004', label: '专票开具' },
        { value: '005', label: '机动车票开具' },
        { value: '006', label: '二手车票开具' },
        { value: '007', label: '普票开具' },
        { value: '025', label: '卷票开具' },
        { value: '026', label: '电票开具' },
      ],
      Typedata:[
        { value: 0, label: '蓝票' },
        { value: 1, label: '红票' },
        { value: 2, label: '专票' },
        { value: 3, label: '专票撤销' },
      ],
      specialdata: [
        { value: '00', label: '非特殊票种' },
        { value: '01', label: '农产品销售' },
        { value: '02', label: '产品收购' },
        { value: '06', label: '抵扣通行费' },
        { value: '08', label: '成品油' },
      ],
      statusdata: [
        { value: 0, label: '新建' },
        { value: 1, label: '已开票' },
        { value: 2, label: '开票失败' },
      ],
    }
  },
  created() {
    this.getdetail()
  },
  methods: {
    getdetail() {
      let data = {
        order_id: this.$route.query.id,
      }
      request({
        url: api.invoicedetail,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.detaildata = res.data
        })
        .catch(() => {})
    },
    adopt(val) {
      if(this.detaildata.status == 3 || this.detaildata.status == 4 || this.detaildata.status == 6){
        this.confirmtype = val
        this.adoptDialog = true
      }else{
        this.gopass()
      }
    },
    toreject() {
       this.rejectDialog = true
    },
    gopass(){
          this.$confirm('是否确认通过专票审核', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            this.showLoading = true
            let data = {
              order_id: this.$route.query.id,
            }
            request({
              url: api.specialInvoicepass,
              method: 'post',
              data: data,
            })
              .then((res) => {
                if (res.code == 200) {
                  this.$message({
                    type: 'success',
                    message: '通过成功!',
                  })
                  this.showLoading = false
                  this.$store.dispatch('tagsView/delView', this.$route)
                  this.$router.push({
                    path: '/controlinvoices/specialticket',
                  })
                }
              })
              .catch(() => {
                this.showLoading = false
              })
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消通过',
            })
          })
    },
    changeshowload(type){
      this.showLoading = type
    },
    getinvoiceItem(val) {
      for (let i = 0; i < this.invoiceItemdata.length; i++) {
        if ((this.invoiceItemdata[i].value == val)) {
          return this.invoiceItemdata[i].label
        }
      }
      return ''
    },
    getzeroTaxFlag(val) {
      for (let i = 0; i < this.TaxFlagdata.length; i++) {
        if ((this.invoiceItemdata[i].value == val)) {
          return this.invoiceItemdata[i].label
        }
      }
      return ''
    },
    getinvoiceTypeCode(val) {
      for (let i = 0; i < this.TypeCodedata.length; i++) {
        if ((this.TypeCodedata[i].value == val)) {
          return this.TypeCodedata[i].label
        }
      }
      return ''
    },
    getinvoiceType(val){
      for (let i = 0; i < this.Typedata.length; i++) {
        if ((this.Typedata[i].value == val)) {
          return this.Typedata[i].label
        }
      }
      return ''
    },
    getspecial(val) {
      for (let i = 0; i < this.specialdata.length; i++) {
        if ((this.specialdata[i].value == val)) {
          return this.specialdata[i].label
        }
      }
      return ''
    },
    getstatus(val) {
      for (let i = 0; i < this.statusdata.length; i++) {
        if ((this.statusdata[i].value == val)) {
          return this.statusdata[i].label
        }
      }
      return ''
    },
  },
  watch: {},
}
</script>

<style lang="scss" scoped>
.down {
  padding: 10px 24px;
  .thetable {
    background-color: #fff;
    padding-bottom: 20px;
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .downnav {
    padding: 0 44px;
    .itembox {
      line-height: 40px;
      .item {
        margin: auto;
        font-size: 14px;
        span {
          display: inline-block;
          padding-right: 10px;
          color: #606266;
          font-weight: 600;
          min-width: 100px;
          text-align: right;
        }
      }
    }
  }
}
.foot {
  margin-top: 20px;
  text-align: center;
}
.el-form-item {
  margin-bottom: 0px;
}
</style>>
