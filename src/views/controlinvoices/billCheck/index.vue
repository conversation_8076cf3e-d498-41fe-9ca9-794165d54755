<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="3"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        showBorder
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
      >
        <!-- 销售订单编号 -->
        <template slot="srcOrderId" slot-scope="{ scope }">
          <div>
            {{ scope.srcOrderId }}
            <el-button
              size="mini"
              type="text"
              @click="collapseOrder(scope, 'collapseOrder')"
              >展开</el-button
            >
          </div>
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            size="mini"
            type="primary"
            :class="{isGray:scope.handleType != 0}"
            @click="handelRow(scope, 'check')"
            >审核</el-button
          >
        </template>
        <!-- 操作记录 -->
        <template slot="actionRecord" slot-scope="{ scope }">
          <el-button
            size="mini"
            type="primary"
            @click="handelRow(scope, 'view')"
            >查看</el-button
          >
        </template>
      </my-table>
    </div>
    <!-- 订单列表 -->
    <orderList
      v-if="orderListVisible"
      ref="orderList"
      @closeDialg="closeDialg"
      :orderList="rowObj.dtoList"
    ></orderList>
    <!-- 历史记录 -->
    <logList
      ref="logList"
      v-if="logListVisible"
      @closeDialg="closeDialg"
      :detailId="rowObj.invoiceZpApplyId"
    ></logList>
    <!-- 详情 -->
    <dartSlide
      :visible.sync="slideVisible"
      title="开具发票详情"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
    >
      <detail
        :rowInfo="rowObj"
        :slideVisible="slideVisible"
        @refreshList="getTableData"
      ></detail>
    </dartSlide>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { zpList, zpExamine, zpDetails } from '@/api/bill'
import dartSlide from '@/components/dart/Slide/index.vue'
import orderList from './components/orderList.vue'
import logList from './components/logList.vue'
import detail from './components/detatil.vue'

export default {
  components: {
    MyTable,
    SearchForm,
    orderList,
    logList,
    dartSlide,
    detail
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: zpList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageNo',
      rowObj: {},
      orderListVisible: false,
      logListVisible: false,
      slideVisible: false
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 操作
    handelRow(row, type) {
      this.rowObj = row
      if (type == 'view') {
        this.logListVisible = true
        this.$nextTick(() => {
          this.$refs.logList.dialogVisible = true
        })
      } else {
        this.slideVisible = true
      }
    },
    collapseOrder(row) {
      this.rowObj = row
      this.orderListVisible = true
      this.$nextTick(() => {
        this.$refs.orderList.dialogVisible = true
      })
    },
    closeDialg() {
      this.logListVisible = false
      this.orderListVisible = false
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  .img-box {
    display: flex;
    justify-content: center;
    align-items: center;
    .el-image {
      width: 60px;
      height: 60px;
      display: flex;
      justify-content: center;
      align-items: center;
      .el-icon-picture-outline {
        font-size: 24px;
      }
    }
  }
  .isGray{
    color: #FFFFFF;
    background-color: #a0cfff;
    border-color: #a0cfff;
  }
}

.show-box {
  ::v-deep .el-dialog__body {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>