
import {
  getVehicleColor
} from '@/common/method/formatOptions'

import {
  licenseColorOptionAll,
} from '@/common/const/optionsData.js'

let handleTypeObj = {
  0:'待审核',
  1:'审核通过',
  2:'审核驳回'
}

let finishFlagObj = {
  0:'进行中',
  1:'完结'
}

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'invoiceZpApplyId',
      width: 140,
      label: '红冲申请单id',
    },
    {
      prop: 'invoiceNum',
      width: 160,
      label: '发票号',
    },
    {
      prop: 'totalFee',
      label: '开票金额',
      formatter:(row) => {
        if (row) {
          return (row / 100).toFixed(2)
        } else {
          return '0.00'
        }
      }
    },
    {
      prop: 'srcOrderId',
      width: 160,
      label: '销售订单编号',
    },
    {
      prop: 'carNo',
      width: 140,
      label: '车牌',
    },
    {
      prop: 'carColor',
      label: '车牌颜色',
      formatter:(row) => {
        return getVehicleColor(row)
      }
    },
    {
      prop: 'customerName',
      label: '用户名称',
    },
    {
      prop: 'buyerName',
      label: '购方名称'
    },
    {
      prop: 'buyerTaxpayerNum',
      width: 200,
      label: '购方纳税人识别号'
    },
    {
      prop: 'buyerAddress',
      width: 200,
      label: '购方地址'
    },
    {
      prop: 'buyerPhone',
      width: 140,
      label: '购方电话'
    },
    {
      prop: 'buyerBankName',
      width: 140,
      label: '购方开户行名称'
    },
    {
      prop: 'buyerBankAccount',
      width: 200,
      label: '购方开户行账号'
    },
    {
      prop: 'mail',
      width: 180,
      label: '邮箱'
    },
    {
      prop: 'phone',
      width: 140,
      label: '手机号'
    },
    {
      prop: 'remark',
      width: 200,
      label: '备注'
    },
    {
      prop: 'reason',
      width: 200,
      label: '申请红冲原因'
    },
    {
      prop: 'finishFlag',
      width: 140,
      label: '申请单完结状态',
      formatter:(row) => {
        return finishFlagObj[row]
      }
    },
    {
      prop: 'stateNode',
      width: 160,
      label: '操作节点',
    },
    {
      prop: 'handleType',
      label: '审核结果',
      width: 120,
      formatter:(row) => {
        return handleTypeObj[row]
      }
    },
    {
      prop: 'action',
      fixed: 'right',
      label: '操作'
    },
    {
      prop: 'actionRecord',
      fixed: 'right',
      label: '操作记录'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'srcOrderId',
      label: '销售订单编号',
      default: '',
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'carColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll
    },
    {
      type: 'input',
      field: 'customerName',
      label: '用户名称',
      // isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'invoiceNum',
      label: '发票号',
      // isCollapse: true,
      default: '',
    },
  ]
}
