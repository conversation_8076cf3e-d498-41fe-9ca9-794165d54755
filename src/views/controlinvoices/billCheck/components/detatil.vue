
<template>
  <div class="detail-wrap" v-loading="loading">
    <!-- 基础信息 -->
    <div class="orderItem">
      <BasicInfo :detailInfo="detailInfo" :rowInfo="rowInfo"></BasicInfo>
    </div>
    <!-- 操作 -->
    <div class="orderItem" style="margin-bottom:30px" v-if="detailInfo.handleType == 0">
      <el-form
        :model="form"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="ruleForm"
      >
        <!-- 备注填写 -->
        <el-form-item label="审核备注" prop="remark">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="form.remark"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="btns g-flex g-flex-center">
        <el-button style="margin:0 20px" @click="examine(2)">驳回</el-button>
        <el-button type="primary" style="margin:0 20px" @click="examine(1)">审核通过</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import BasicInfo from './basicInfo'
import { zpDetails, zpExamine } from '@/api/bill'

export default {
  name: '',
  props: {
    rowInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: { BasicInfo },
  data() {
    return {
      loading: false,
      invoiceZpApplyId: '',
      detailInfo: {},
      form: {},
      rules: {
        remark: [{ required: true, message: '请输入备注内容', trigger: 'blur' }]
      }
    }
  },
  computed: {},
  watch: {
    rowInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        this.invoiceZpApplyId = val.invoiceZpApplyId
        if (this.invoiceZpApplyId) {
          this.form = {}
          this.init()
        }
      }
    }
  },
  created() {},
  methods: {
    async init() {
      let params = {
        invoiceZpApplyId: this.invoiceZpApplyId
      }
      this.loading = true
      let res = await zpDetails(params)
      console.log(res)
      this.loading = false
      if (res.code == 200) {
        this.detailInfo = res.data
      }
    },
    examine(handleType) {
      this.$refs.ruleForm.validate(async valid => {
        if (valid) {
          this.loading = true
          let params = {
            handleType,
            invoiceZpApplyId: this.invoiceZpApplyId,
            remark: this.form.remark
          }
          let res = await zpExamine(params)
          this.loading = false
          if (res.code == 200) {
            this.init()
            this.$emit('refreshList')
            this.$message.success('成功')
          }
        }
      })
    }
  }
}
</script>

<style lang='scss' scoped>
@import './style/newApplyCommon.css';
.btns {
  padding: 8px 0;
}
.ruleForm {
  padding-top: 20px;
}
</style>