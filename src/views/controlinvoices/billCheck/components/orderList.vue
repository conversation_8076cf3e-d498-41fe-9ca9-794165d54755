<template>
  <el-dialog :visible.sync="dialogVisible" @close="close" title="销售订单列表" width="60%">
    <div class="table-box">
      <el-table 
                :data="tableData"
                style="width: 100%;"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }">

        <el-table-column prop="srcOrderId"
                         align="center"
                         label="销售订单编号"
                         min-width="200" />
        <el-table-column prop="customerName"
                         align="center"
                         label="用户名称"
                         min-width="180" />
        <el-table-column prop="carNo"
                         align="center"
                         label="车牌号" />
        <el-table-column prop="carColor"
                         align="center"
                         min-width="120"
                         label="车牌颜色">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>

      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import {
  getVehicleColor
} from '@/common/method/formatOptions'
export default {
  props:{
    orderList:{
      type:Array,
      default:() => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      getVehicleColor,
      tableData:[]
    }
  },
  methods: {
    close(){
      this.$emit('closeDialg')
    }
  },
  watch:{
    orderList:{
      deep:true,
      immediate:true,
      handler(val){
        this.tableData = val
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
