<template>
  <el-dialog :visible.sync="dialogVisible" @close="close" title="操作记录" width="60%">
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%;"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="orderHandle"
          align="center"
          label="订单操作"
          min-width="140"
        />
        <el-table-column
          prop="createdTime"
          align="center"
          label="操作时间"
          min-width="180"
        />
        <el-table-column
          prop="handleType"
          align="center"
          min-width="120"
          label="审核结果"
        >
        </el-table-column>
        <el-table-column prop="createdBy" align="center" label="操作人" />
        <el-table-column prop="remark" align="center" min-width="200" label="审核备注" />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import { zpDetails } from '@/api/bill'

export default {
  props: {
    detailId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      getVehicleColor,
      tableData: [],
      loading: false,
      invoiceZpApplyId: ''
    }
  },
  methods: {
    async init() {
      let params = {
        invoiceZpApplyId: this.invoiceZpApplyId
      }
      this.loading = true
      let res = await zpDetails(params)
      console.log(res)
      this.loading = false
      if (res.code == 200) {
        this.tableData = res.data.logList
      }
    },
    close(){
      this.$emit('closeDialg')
    }
  },
  watch: {
    detailId: {
      deep: true,
      immediate: true,
      handler(val) {
        this.invoiceZpApplyId = val
        this.init()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
