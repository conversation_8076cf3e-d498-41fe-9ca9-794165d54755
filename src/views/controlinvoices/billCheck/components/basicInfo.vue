
<template>
  <div>
    <el-descriptions
      :column="2"
      border
      size="medium"
      title="发票信息"
      class="descriptions-content"
    >
      <el-descriptions-item label="销售订单编号">
        {{ srcOrderListId }}
        <el-button
          size="mini"
          type="text"
          @click="collapseOrder"
          v-if="info.srcOrderListId && info.srcOrderListId.length >= 4"
          >展开</el-button
        >
      </el-descriptions-item>
      <el-descriptions-item label="开票金额">
        {{ info.totalFee | moneyFilter }}
      </el-descriptions-item>
      <el-descriptions-item label="购方名称">
        {{ info.buyerName }}
      </el-descriptions-item>
      <el-descriptions-item label="购方纳税人识别号">
        {{ info.buyerTaxpayerNum }}
      </el-descriptions-item>
      <el-descriptions-item label="购方地址">
        {{ info.buyerAddress }}
      </el-descriptions-item>
      <el-descriptions-item label="购方电话">
        {{ info.buyerPhone }}
      </el-descriptions-item>
      <el-descriptions-item label="购方开户行名称">
        {{ info.buyerBankName }}
      </el-descriptions-item>
      <el-descriptions-item label="购方开户行账号">
        {{ info.buyerBankAccount }}
      </el-descriptions-item>
      <el-descriptions-item label="邮箱">
        {{ info.mail }}
      </el-descriptions-item>
      <el-descriptions-item label="手机号">
        {{ info.phone }}
      </el-descriptions-item>
      <el-descriptions-item label="备注" span="2">
        {{ info.remark }}
      </el-descriptions-item>
      <el-descriptions-item label="申请红冲原因" span="2">
        {{ info.reason }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    detailInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    rowInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      info: {}
    }
  },
  computed: {},
  watch: {
    detailInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        this.info = val
      }
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  },
  computed:{
    srcOrderListId(){
      return this.info.dtoList?.map(item => item.srcOrderId).slice(0,3).join(' ')
    }
  },
  methods: {
    collapseOrder() {
      console.log(this.$parent.$parent.$parent)
      this.$parent.$parent.$parent.collapseOrder(this.rowInfo)
      // this.orderListVisible = true
      // this.$nextTick(() => {
      //   this.$refs.orderList.dialogVisible = true
      // })
    }
  },
  created() {}
}
</script>

<style lang='scss' scoped>
@import './style/newApplyCommon.css';
</style>