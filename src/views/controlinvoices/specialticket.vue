<template>
  <div class="page-box">
      <dart-search ref="searchForm1"
                 label-position="right"
                 class="search"
                 :formSpan='24'
                 :rules="rules"
                 :model="search">
      <template slot="search-form">
        <dart-search-item label="统计日期："
                          prop="value1">
          <el-date-picker
						v-model="search.value1"
						type="daterange"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
					>
					</el-date-picker>               
        </dart-search-item>
				<dart-search-item label="客户ID："
                          prop="customerId">
          <el-input v-model="search.customerId"
                    placeholder=""></el-input>
        </dart-search-item>
				<dart-search-item label="发票号码："
                          prop="invoiceNum">
          <el-input v-model="search.invoiceNum	"
                    placeholder=""></el-input>
        </dart-search-item>
				<dart-search-item label="订单号"
                          prop="orderId">
          <el-input v-model="search.orderId	"
                    placeholder=""></el-input>
        </dart-search-item>
				<!-- <dart-search-item label="发票类型："
                          prop="invoiceType">
          <el-select v-model="search.invoiceType"
                     placeholder="请选择"
                     :clearable='true'
                     collapse-tags>
            <el-option v-for="item in optiondata1"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item> -->
				<!-- <dart-search-item label="开票类型："
                          prop="status">
          <el-select v-model="search.status"
                     placeholder="请选择"
                     :clearable='true'
                     collapse-tags>
            <el-option v-for="item in optiondata2"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item> -->
				<dart-search-item label="开票状态："
                          prop="status">
          <el-select v-model="search.status"
                     placeholder="请选择"
                     :clearable='true'  
                     collapse-tags>
            <el-option v-for="item in optiondata3"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
				 <dart-search-item>
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
					<el-button 
                     size="mini"
                     native-type="submit"
                     @click="resetForm('searchForm1')">重置</el-button>					 
				 </dart-search-item>
      </template>
      </dart-search>
      <div class="table table-box">
      <el-table :data="tableData"
                v-loading="tableloading"
                border
                style="width: 100%"
                height="100%"
                ref="multipleTable"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }">
                <el-table-column prop="createdDate"
                         align="center"
                         min-width="130"
                         label="创建日期" />
                <el-table-column prop="customerId"
                         align="center"
                         label="客户ID" />         
                <el-table-column prop="buyerBankName"
                         align="center"
                         min-width="120"
                         label="购方开户行名称" />
                <el-table-column prop="buyerTaxpayerNum"
                         align="center"
                         min-width="120"
                         label="购方纳税人识别号" />
                <el-table-column prop="billingDate"
                         align="center"
                         min-width="120"
                         label="开票日期" >   
                         <template slot-scope="scope">
                            <span v-if="scope.row.billingDate">{{getllisttime(scope.row.billingDate)}}</span>
                         </template>
                </el-table-column>
                <el-table-column prop="invoiceStatus"
                         align="center"
                         min-width="100"
                         label="开票状态">
                         <template slot-scope="scope">
                           {{getstatus(scope.row.status)}}
                         </template>
                </el-table-column>         
                <el-table-column prop="orderId"
                         align="center"
                         label="订单号" />   
                <el-table-column prop="invoiceNum"
                         align="center"
                         label="发票号码" /> 
                <el-table-column prop="invoiceCode"
                         align="center"
                         label="发票代码" />
                <el-table-column prop="invoiceStatus"
                         align="center"
                         label="发票状态">
                         <template slot-scope="scope">
                           {{scope.row.invoiceStatus == 1 ? '正常' : '已红冲'}}
                         </template>
                </el-table-column>
                 <el-table-column prop=""
                         fixed="right"
                         align="center"
                         label="操作">
                <template slot-scope="scope">
                  <el-button 
                            size="mini"
                            @click="todetail(scope.row)">详情</el-button>
                  <!-- <el-button 
                            v-if="scope.row.status == 1"
                            size="mini"
                            type="danger"
                            @click="tocancel(scope.row)">撤销</el-button>           -->
                </template>
                 </el-table-column>         

      </el-table> 
      </div>
      <div class="pagination g-flex g-flex-end">
        <el-pagination background
                      @current-change="changePage"
                      :current-page="search.pageNumber"
                      :page-size="search.pageSize"
                      layout="total, prev, pager, next, jumper"
                      :total="total">
        </el-pagination> 
      </div>
      <cancel v-if="cancelDialog" :visible.sync='cancelDialog' @golist="onSearchHandle" :orderid='this.orderid'></cancel>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import cancel from './cancel.vue'
var moment = require('moment');
export default {
	components:{
    dartSearch,
		dartSearchItem,
    cancel
	},
created(){
  this.getlist()
},  
data(){
	return{
    tableData:[],
		rules:{

		},
    tableloading:false,
    cancelDialog:false,
    total:0,
    orderid:'',
		search:{
			value1:null,
      customerId:'',
			invoiceNum:'',
			// invoiceType:'',
			invoiceTypeCode:'',
			orderId:'',
			// status:'',
			pageNumber:1,
			pageSize:10
		},
		optiondata1: [
			{ value: 1, label: '正数发票开具' },
			{ value: 2, label: '负数发票开具' }
		],
		optiondata2: [
			{ value: '004', label: '专票' },
			{ value: '026', label: '电子发票'},
		],
		optiondata3: [
			{ value: '0', label: '新建' },
			{ value: '1', label: '已开票'},
			{ value: '2', label: '开票失败'},
      { value: '3', label: '开票中'},
      { value: '4', label: '撤销申请中'},
      { value: '5', label: '已撤销'},
      { value: '6', label: '红冲中'},
      { value: '7', label: '已红冲'}
		],
    statusdata: [
			{ value: '0', label: '新建' },
			{ value: '1', label: '已开票'},
			{ value: '2', label: '开票失败'},
      { value: '3', label: '开票中'},
      { value: '4', label: '撤销申请中'},
      { value: '5', label: '已撤销'},
      { value: '6', label: '红冲中'},
      { value: '7', label: '已红冲'}
		],
	}
},
methods:{
	getlist(){
    this.tableloading=true
    let data = {
        customerId: this.search.customerId ? this.search.customerId : null,
        invoiceNum: this.search.invoiceNum ? this.search.invoiceNum : null,
				// invoiceType: this.search.invoiceType ? this.search.invoiceType : null,
				// invoiceTypeCode: this.search.invoiceTypeCode ? this.search.invoiceTypeCode : null,
				orderId: this.search.orderId ? this.search.orderId : null,
				status: this.search.status ? this.search.status : null,
        createdDateStart: this.search.value1 ? this.gettime(this.search.value1[0]) : null,
				createdDateEnd: this.search.value1 ? this.gettime(this.search.value1[1]) : null,
        // trans_month:'2021-10-24',
        pageNumber: this.search.pageNumber,
        pageSize: this.search.pageSize,
      }
      request({
        url: api.invoicesearch,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.tableloading = false
          this.tableData = res.data.list
          this.total = res.data.totalCount
        })
        .catch(() => {
          this.tableloading = false
        })
	},
	onSearchHandle(){
		this.search.pageNumber = 1
		this.getlist()
	},
  resetForm(formName) {
    this.$refs[formName].$children[0].resetFields();
  },
  changePage(e){
    this.search.pageNumber = e
		this.getlist()
  },
	gettime(data) {
    const value =
      moment(data).format("YYYY-MM-DDTHH:mm:ss")
    return value
  },
  getllisttime(data)  {
    let dateString=data;
    let formatedDate = dateString.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '$1/$2/$3 $4:$5:$6');
    return formatedDate
  },
  getstatus(val){
    for (let i = 0; i < this.statusdata.length; i++) {
        if ((this.statusdata[i].value == val)) {
          return this.statusdata[i].label
        }
      }
      return ''
  },
  tocancel(item){
    this.orderid = item.orderId
    this.cancelDialog = true
  },
  todetail(item){
    this.$router.push({
        path: '/controlinvoices/detail',
        query: { id: item.orderId }
      })
  }
}
}
</script>
<style lang="scss" scoped>
.page-box {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
	.search {
		margin-top: 20px;
	}
}
.page-box .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.page-box .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}

</style>