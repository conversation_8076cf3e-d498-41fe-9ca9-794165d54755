<template>
  <div class="user">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="8"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import moment from 'moment'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    reportForms,
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      reportList: [
        {
          id: 1,
          name: 'vehicleSpecialActive',
          title: 'ETC车道特情促活数据汇总表',
        },
      ],
      tableHeight: 0,
      rules: {
        startTime: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
        endTime: [{ required: true, message: '请选择日期', trigger: 'change' }],
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'audit_start_date',
          label: '统计开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: '',
        },
        {
          type: 'datePicker',
          field: 'audit_end_date',
          label: '统计结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          pickerOptions: this.pickerOptions,
          default: '',
        },

      ]
    },
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.startTime).isAfter(fromData.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm'].resetForm()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>