<template>
  <div class="active-reminder">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      :btnSpan="8"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        @changeTableData="changeTableData"
      >
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { getActiveReminderList } from '@/api/paramsManagement' 
import { licenseColorOption } from '@/common/const/optionsData.js'
import { getVehicleColor } from '@/common/method/formatOptions'

export default {
  name: 'ActiveReminder',
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      loading: false,
      api: getActiveReminderList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      timeField: ['dateRange']
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'select',
          field: 'carColor',
          label: '车牌颜色：',
          clearable: true,
          default: '',
          options: licenseColorOption
        },
        {
          type: 'input',
          field: 'carNo',
          label: '车牌号：',
          placeholder: '请输入车牌号'
        },
        {
          type: 'select',
          field: 'errCode',
          label: '滞留原因：',
          clearable: true,
          options: [
            { label: 'ETC卡在状态名单内', value: '24' },
            { label: 'OBU拆卸', value: '2' },
            { label: 'ETC卡片储值卡余额不足', value: '27' }
          ]
        },
        {
          type: 'select',
          field: 'activiteStatus',
          label: '促活状态：',
          clearable: true,
          options: [
            { label: '未促活', value: '0' },
            { label: '已促活', value: '1' },
            { label: '无需促活', value: '2' },
            { label: '促活无效', value: '3' },
            { label: '促活检查中', value: '9' }
          ]
        },
        {
          type: 'dateRange',
          field: 'dateRange',
          label: '特情时间：',
          keys: ['startDate', 'endDate'],
          // format: 'YYYY-MM-DD',
          default: []
        }
      ]
    },
    listColumns() {
      return [
        {
          prop: 'carNo',
          label: '车牌号',
          width: 160
        },
        {
          prop: 'carColor',
          label: '车牌颜色',
          width: 160,
          formatter: (row) => {
            return getVehicleColor(row)
          }
        },
        {
          prop: 'outOfTime',
          label: '特情时间',
          width: 200
        },
        {
          prop: 'errCode',
          label: '特情原因',
          formatter: (row) => {
            const errCodeMap = {
              '24': 'ETC卡在状态名单内',
              '2': 'OBU拆卸',
              '27': 'ETC卡片储值卡余额不足'
            }
            return errCodeMap[row] || row
          }
        },
        {
          prop: 'activiteStatus',
          label: '促活状态',
          width: 200,
          formatter: (row) => {
            const statusMap = {
              0: '未促活',
              1: '已促活',
              2: '无需促活',
              3: '促活无效',
              9: '促活检查中'
            }
            return statusMap[row] || row
          }
        },
        {
          prop: 'activiteType',
          label: '促活凭证',
          width: 160,
          formatter: (row) => {
            const typeMap = {
              0: '解黑',
              1: '充值',
              2: '激活'
            }
            return typeMap[row] || '-'
          }
        },
        {
          prop: 'activiteDate',
          label: '促活时间',
          width: 200
        }
      ]
    }
  },
  methods: {
    onReSetHandle(formData) {
      this.currentFormData = formData
      this.tableData = []
      this.getTableData()
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.active-reminder {
  height: 100%;
  position: relative;
  flex-flow: column;
  display: flex;
  
  .table {
    flex: 1;
    overflow: auto;
  }
  
  .pagination {
    margin: 10px 0;
  }
}
</style>
