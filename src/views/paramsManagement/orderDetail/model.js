import {
  getVehicleColor
} from '@/common/method/formatOptions'

import {
  licenseColorOptionAll,
} from '@/common/const/optionsData.js'

//客户信息上传表格
export const listColoumns = [
  {
    prop: 'orderId',
    label: '订单编号',
  },
  {
    prop: 'customerName',
    label: '用户名',
  },
  {
    prop: 'marketingName',
    label: '营销方案名称',
  },
  {
    prop: 'branchName',
    label: '网点编号',
  },
  {
    prop: 'operatorName',
    label: '操作人',
  },
  {
    prop: 'createTime',
    width: 160,
    label: '下单时间',
  },
  {
    prop: 'payTime',
    width: 160,
    label: '支付时间',
  },
  {
    prop: 'payMoney',
    width: 120,
    label: '支付金额(元)',
    formatter: (val) => {
      return moneyFilter(val)
    }
  },
  {
    prop: 'payType_str',
    label: '支付方式',
  },
  {
    prop: 'rule',
    label: '协议规则',
  },
  {
    prop: 'action',
    label: '操作',
  },
]
function moneyFilter(val) {
  if (val) {
    return (val / 100).toFixed(2)
  } else {
    return '0.00'
  }
}
//客户信息上传表单
export const listForm = (state) => {
  return [
    {
      type: 'input',
      field: 'orderId',
      label: '订单编号',
      placeholder: '订单编号',
      default: '',
      span: 5
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll,
      span: 5
    },
    {
      type: 'input',
      field: 'vehicleNo',
      label: '车牌号',
      placeholder: '车牌号',
      default: '',
      span: 5
    },
  ]
}