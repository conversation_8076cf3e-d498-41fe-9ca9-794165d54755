<template>
  <div class="toll-record">
    <SearchForm
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :btnSpan="8"
      :rules="rules"
      :btnAlign="'left'"
    >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :hasPagination="false"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button
              slot="btn"
              size="mini"
              type="primary"
              @click="handelRow(scope)"
              >修改退货时间</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
    <el-dialog
      :title="'修改退货时间'"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
    >
      <el-form
        ref="ruleForm"
        :model="ruleFormData"
        :rules="ruleForm"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="12">
            <el-form-item label="订单编号：" prop="cardNo">
              <el-input disabled v-model="ruleFormData.orderId" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户名：" prop="customerName">
              <el-input disabled v-model="ruleFormData.customerName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="12">
            <el-form-item label="营销方案名称：" prop="marketingName">
              <el-input disabled v-model="ruleFormData.marketingName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="修改天数：" prop="day">
              <el-input v-model="ruleFormData.day" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button
          type="primary"
          :loading="loading"
          size="medium"
          @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="handleCloseIcon()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import { listColoumns, listForm } from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { orderDetailView, delivery } from '@/api/paramsManagement'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    MyTable,
    SearchForm,
  },
  mixins: [tableListMixin],
  data() {
    return {
      loading: false,
      tableData: [],
      listColoumns,
      api: orderDetailView,
      pageSize: '',
      pageNum: '',
      rules: {
        // cardNo: [{ required: true, message: '请填写卡号', trigger: 'change' }],
      },
      ruleForm: {
        day: [
          { required: true, message: '请填写要修改的天数', trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              if (value === '' || /^[0-9]+$/.test(value)) {
                callback() // 校验通过
              } else {
                callback(new Error('只能输入整数'))
              }
            },
            trigger: ['blur', 'input'],
          },
        ],
      },
      ruleFormData: {},
      dialogFormVisible: false,
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    },
  },
  methods: {
    // 搜索框表单操作
    onSearchHandle(formData) {
      if (!formData.orderId && !formData.vehicleNo) {
        this.$message.warning('订单编号与车牌号不能同时为空！')
        return
      }
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach((item) => {
        delete params[item]
      })
      // console.log(params,'searchFormData')
      this.currentFormData = params
      this.getTableData({}, (res) => {
        console.log('res', res)
        this.tableData = res.data.records || []
        //计算协议规则
        this.tableData.forEach((item) => {
          console.log('item', item)
          if (item.orderSimpleViewList && item.orderSimpleViewList.length) {
            let arr = item.orderSimpleViewList
            console.log('arr', arr[0])
            let ruleStr = ''
            if (arr[0].rule) {
              ruleStr = arr[0].rule
            }
            if (arr[1].rule) {
              ruleStr = arr[1].rule
            }
            let ruleText = JSON.parse(ruleStr)
            console.log('ruleText', ruleText)
            if (ruleText.length > 0)
              item.rule =
                ruleText[0].ruleRemark + ruleText[0].validityFate + '天'
          }
        })
      })
    },
    handelRow(row) {
      this.ruleFormData.orderId = row.orderId
      this.ruleFormData.customerName = row.customerName
      this.ruleFormData.marketingName = row.marketingName
      console.log(row, 'row')
      this.dialogFormVisible = true
    },
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.update()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    async update() {
      this.loading = true
      let params = {
        orderId: this.ruleFormData.orderId + '',
        day: this.ruleFormData.day,
      }
      let res = await delivery(params).catch((error) => {
        console.error('请求失败:', error)
        return null // 返回兜底值防止后续逻辑报错
      })
      this.loading = false
      if (res.code == 200) {
        this.$message.success('修改成功')
        this.onSearchHandle(this.currentFormData)
        this.dialogFormVisible = false
        this.ruleFormData.day = ''
      }
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
      this.ruleFormData.day = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>