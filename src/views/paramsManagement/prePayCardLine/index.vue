  <template>
  <div class="user">
    <div class="top_btn">
      <div class="search">
        <div class="search-list">
          <div class="conditions">
            <el-button
              type="primary"
              class="btn"
              size="medium"
              @click="addCardLine()"
              >添加
            </el-button>
            <el-button
              type="primary"
              class="btn"
              size="medium"
              @click="addCardLine('edit')"
              >编辑
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        :max-height="650"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
        border
      >
        <el-table-column label="选择" width="50" align="center">
          <template slot-scope="scope">
            <el-radio
              v-model="radio"
              :label="scope.$index"
              @change="getCurrentRow(scope.$index, scope.row.id)"
              ><span></span
            ></el-radio>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="id" align="center" label="配置编号" /> -->
        <el-table-column
          prop="is_trunk"
          align="center"
          label="客货"
          min-width="100"
        >
          <template slot-scope="scope">
            {{ getVehicleType(scope.row.is_trunk) }}
          </template>
        </el-table-column>
        <el-table-column prop="car_type" align="center" label="车型">
          <template slot-scope="scope">
            {{ getCarType(scope.row.car_type) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="warn_default"
          align="center"
          min-width="120"
          label="默认提醒线(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.warn_default | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="warn_min"
          align="center"
          min-width="120"
          label="提醒线下限(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.warn_min | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="warn_max"
          align="center"
          min-width="120"
          label="提醒线上限(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.warn_max | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="black_default"
          align="center"
          min-width="140"
          label="默认黑名单线(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.black_default | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="black_min"
          align="center"
          min-width="140"
          label="黑名单线下限(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.black_min | moneyFilter }}
          </template>
        </el-table-column>
        <el-table-column
          prop="black_max"
          align="center"
          min-width="140"
          label="黑名单线上限(元)"
        >
          <template slot-scope="scope">
            {{ scope.row.black_max | moneyFilter }}
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="创建人" />
        <el-table-column
          prop="id"
          align="center"
          min-width="160"
          label="创建时间"
        /> -->
        <el-table-column prop="up_by_name" align="center" label="更新人" />
        <el-table-column
          prop="up_time"
          align="center"
          min-width="160"
          label="更新时间"
        />
      </el-table>
      <div v-if="total > formData.page_size" class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="changePage"
          :current-page="formData.page_index"
          :page-sizes="[10, 20, 50]"
          :page-size="formData.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <add-and-edit
      :visible.sync="dialogAddVisible"
      :type.sync="dialogType"
      :originData="originData"
      @on-submit="onUpdateList"
    ></add-and-edit>
  </div>
</template>

<script>
import { getVehicleType, getCarType } from '@/common/method/formatOptions'
import addAndEdit from './addAndEdit.vue'
export default {
  name: 'prePayCardLine',
  components: {
    addAndEdit,
  },
  data() {
    return {
      center: 'center',
      loading: false,
      radio: [],
      id: '',
      total: '',
      dialogType: 'add',
      dialogAddVisible: false,
      originData: {},
      formData: {
        is_trunk: '', //客货标识
        car_type: '', //车型
        page_index: 1,
        page_size: 10,
      },
      tableData: [],
    }
  },
  created() {
    this.getPrePayCardList()
  },
  methods: {
    getVehicleType,
    getCarType,
    getPrePayCardList() {
      this.loading = true
      console.log('列表入参', this.formData)
      this.$store
        .dispatch('paramsManagement/getPrePayCardList', this.formData)
        .then((res) => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.data
          this.total = res.total
        })
        .catch((err) => {
          this.loading = false
          console.log('err', err)
        })
    },
    //操作按钮打开对话框
    addCardLine(dialogType = 'add') {
      if (dialogType === 'edit' && !this.id) {
        this.$confirm('请选中一条记录！', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning',
        })
        return
      }
      this.dialogType = dialogType
      this.dialogAddVisible = true
    },
    //获取选中
    getCurrentRow(index, id) {
      this.originData = this.tableData[index]
      console.log(
        '数据回填~~~~~~~~~~~~',
        index,
        this.tableData[index],
        this.originData
      )
      this.id = id
    },
    changePage(page) {
      this.radio = []
      this.formData.page_index = page
      this.getPrePayCardList()
    },
    handleSizeChange(pageSize) {
      this.radio = []
      this.formData.page_size = pageSize
      this.getPrePayCardList()
    },
    //更新
    onUpdateList() {
      this.radio = []
      this.getPrePayCardList()
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;

  .table {
    margin: 20px 0 10px 0;
    & > el-table-column {
      font-size: 12px;
    }
  }
  .nowrap {
    white-space: nowrap;
  }
}
</style>
