<template>
  <div class="form">
    <el-dialog
      title="单位预付费记账卡线值添加"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      custom-class="special_dialog form_dialog"
      width="80%"
      :before-close="handleCloseIcon"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
        class="demo-ruleForm"
      >
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="客货：" prop="is_trunk">
              <el-select
                v-model="ruleForm.is_trunk"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in vehicleType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item class="car-type" label="车型：" prop="car_type">
              <el-select
                v-model="ruleForm.car_type"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in vehicleCatgoryType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="默认提醒线(元)：" prop="warn_default">
              <el-input type="number" v-model="ruleForm.warn_default" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提醒线下限(元)：" prop="warn_min">
              <el-input type="number" v-model="ruleForm.warn_min" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="提醒线上限(元)：" prop="warn_max">
              <el-input type="number" v-model="ruleForm.warn_max" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24" :sm="24" :gutter="10">
          <el-col :span="8">
            <el-form-item label="默认黑名单线(元)：" prop="black_default">
              <el-input type="number" v-model="ruleForm.black_default" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="黑名单线下限(元)：" prop="black_min">
              <el-input type="number" v-model="ruleForm.black_min" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="黑名单线上限(元)：" prop="black_max">
              <el-input type="number" v-model="ruleForm.black_max" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary" size="medium" @click="submitForm('ruleForm')"
          >提交</el-button
        >
        <el-button size="medium" @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// import DartDateRange from '@/components/DateRange/date-range'
var moment = require('moment')
import float from '@/common/method/float.js'
export default {
  components: {
    // DartDateRange,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
    originData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      //   licenseColorOption: licenseColorOption,
      //   provinces: provinces,
      // vehicleCatgoryType: vehicleCatgoryType,
      dialogFormVisible: false,
      dialogType: 'add',
      dialogOriginData: {},
      ruleForm: {
        car_type: '',
        is_trunk: '',
        warn_default: '',
        warn_max: '',
        warn_min: '',
        black_default: '',
        black_max: '',
        black_min: '',
      },
      vehicleType: [
        {
          value: 1,
          label: '货车',
        },
        {
          value: 2,
          label: '客车',
        },
        {
          value: 3,
          label: '专项作业车',
        },
      ],
      rules: {
        is_trunk: [
          {
            required: true,
            message: '[客货]必须选择一个!',
            trigger: 'change',
          },
        ],
        car_type: [
          {
            required: true,
            message: '[车型]必须选择一个!',
            trigger: 'change',
          },
        ],
        warn_default: [
          {
            required: true,
            message: '[默认提醒线]不能为空!',
            trigger: 'blur',
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
        warn_max: [
          { required: true, message: '[提醒线上限]不能为空!', trigger: 'blur' },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
        warn_min: [
          { required: true, message: '[提醒线下限]不能为空!', trigger: 'blur' },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
        black_default: [
          {
            required: true,
            message: '[默认黑名单线]不能为空!',
            trigger: 'blur',
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
        black_max: [
          {
            required: true,
            message: '[黑名单线上限]不能为空!',
            trigger: 'blur',
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
        black_min: [
          {
            required: true,
            message: '[黑名单线下限]不能为空!',
            trigger: 'blur',
          },
          {
            pattern: /^(\-|\+)?\d+(\.\d+)?$/,
            message: '请输入正确的金额格式',
          },
        ],
      },
      vehicleCatgoryType: [
        { value: 1, label: '一类车' },
        { value: 2, label: '二类车' },
        { value: 3, label: '三类车' },
        { value: 4, label: '四类车' },
        { value: 5, label: '五类车' },
        { value: 6, label: '六类车' },
        { value: 7, label: '七类车' },
      ],
    }
  },
  computed: {
    formData() {
      return {
        warn_default: float.mul(this.ruleForm.warn_default, 100),
        warn_max: float.mul(this.ruleForm.warn_max, 100),
        warn_min: float.mul(this.ruleForm.warn_min, 100),
        black_default: float.mul(this.ruleForm.black_default, 100),
        black_max: float.mul(this.ruleForm.black_max, 100),
        black_min: float.mul(this.ruleForm.black_min, 100),
        car_type: this.ruleForm.car_type,
        is_trunk: this.ruleForm.is_trunk,
      }
    },
    changeOriginData() {
      return {
        warn_default: float.div(this.dialogOriginData.warn_default, 100),
        warn_max: float.div(this.dialogOriginData.warn_max, 100),
        warn_min: float.div(this.dialogOriginData.warn_min, 100),
        black_default: float.div(this.dialogOriginData.black_default, 100),
        black_max: float.div(this.dialogOriginData.black_max, 100),
        black_min: float.div(this.dialogOriginData.black_min, 100),
        car_type: this.dialogOriginData.car_type,
        is_trunk: this.dialogOriginData.is_trunk,
      }
    },
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    originData(val) {
      this.dialogOriginData = val
    },
    type(val) {
      this.dialogType = val
      if (this.dialogType === 'edit') {
        console.log('changeOriginData', this.changeOriginData)
        //数据回填
        this.ruleForm = { ...this.ruleForm, ...this.changeOriginData }
        console.log('数据合并', this.ruleForm)
      } else {
        this.ruleForm = {}
      }
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  methods: {
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          if (this.dialogType === 'add') {
            this.addPrePayCardLine()
          } else {
            this.editPrePayCardLine()
          }
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // changeOriginData() {},
    addPrePayCardLine() {
      this.dialogType = ''
      this.$store
        .dispatch('paramsManagement/addPrePayCardLine', this.formData)
        .then((res) => {
          this.$message({
            message: '添加成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.$emit('on-submit')
        })
        .catch((err) => {})
    },
    editPrePayCardLine() {
      this.dialogType = ''
      this.$set(this.formData, 'id', this.originData.id)
      console.log('编辑提交的数据', this.formData)
      this.$store
        .dispatch('paramsManagement/editPrePayCardLine', this.formData)
        .then((res) => {
          // this.dialogOriginData = this.formData
          this.$message({
            message: '修改成功',
            type: 'success',
          })
          this.dialogFormVisible = false
          this.$emit('on-submit')
        })
        .catch((err) => {
          //错误后继续回填数据
          this.ruleForm = { ...this.ruleForm, ...this.changeOriginData }
        })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
.el-form-item__label {
  text-align: center;
  white-space: nowrap;
}
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
</style>
