<template>
  <div class="form">
    <el-dialog :title="title"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               custom-class="special_dialog form_dialog"
               width="50%"
               :before-close="handleCloseIcon">
      <el-form ref="ruleForm"
               :model="ruleForm"
               :rules="rules"
               label-width="120px"
               class="demo-ruleForm">
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="车牌颜色"
                          prop="licenseColor">
              <el-select v-model="ruleForm.licenseColor"
                         placeholder="请选择"
                         clearable
                         :disabled="dialogType=='edit'"
                         style="width: 100%">
                <el-option v-for="item in licenseColorOption"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="车牌号码："
                          prop="licenseCode">
              <el-input v-model="ruleForm.licenseCode"
                        :disabled="dialogType=='edit'"
                        placeholder="请输入车牌号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="高频车属性"
                          prop="vipAttribute">
              <el-select v-model="ruleForm.vipAttribute"
                         placeholder="请选择高频车属性"
                         clearable
                         :disabled="dialogType=='edit'"
                         style="width: 100%">
                <el-option v-for="item in vipAttributeOption"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="备注："
                          prop="remarks">
              <el-input type="textarea"
                        :rows="3"
                        placeholder="请输入备注"
                        v-model="ruleForm.remarks">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template slot="footer">
        <el-button type="primary"
                   size="medium"
                   @click="submitForm('ruleForm')">提交</el-button>
        <el-button size="medium"
                   @click="cancel()">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// import DartDateRange from '@/components/DateRange/date-range'
// import { getVehicleColor } from '@/common/method/formatOptions'
import { licenseColorOption } from '@/common/const/optionsData'
import request from '@/utils/request'
import api from '@/api/index'
import { _ignoreNull, _ignoreEmpty } from '@/utils/utils'
import { mapGetters, mapActions } from 'vuex'
var moment = require('moment')
export default {
  components: {
    // DartDateRange,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'add',
    },
    originData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      licenseColorOption,
      //   provinces: provinces,
      // vehicleCatgoryType: vehicleCatgoryType,
      dialogFormVisible: false,
      dialogType: 'add',
      dialogOriginData: {},
      ruleForm: {
        id: '',
        licenseColor: '', //车颜色
        licenseCode: '', //车牌，
        vipAttribute: '', //高频车属性
        remarks: '', //备注
      },
      rules: {
        licenseColor: [
          {
            required: true,
            message: '[车牌颜色]必须选择一个!',
            trigger: 'change',
          },
        ],
        licenseCode: [
          { required: true, message: '[车牌号码]不能为空!', trigger: 'blur' },
        ],
        vipAttribute: [
          { required: true, message: '请选择高频车属性!', trigger: 'change' },
        ],
        remarks: [],
      },
      vipAttributeOption: [],
      title: '高频车添加',
    }
  },
  computed: {
    ...mapGetters(['vipCarAttribute']),
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
      if (val && Object.keys(this.vipCarAttribute).length == 0) {
        this.vipCarAttributeOptions()
      }
    },
    originData(val) {
      this.dialogOriginData = val
    },
    type(val) {
      this.dialogType = val
      if (this.dialogType === 'edit') {
        // console.log('changeOriginData', this.changeOriginData)
        // //数据回填
        this.title = '高频车修改'
        this.ruleForm.id = this.originData.id
        this.ruleForm.licenseColor = this.originData.licenseColor
        this.ruleForm.licenseCode = this.originData.licenseCode
        this.ruleForm.remarks = this.originData.remarks
        this.ruleForm.vipAttribute = this.originData.vipAttribute
      } else {
        this.title = '高频车添加'
        console.log('type======>>', val)
        // this.ruleForm = {}
        this.$refs['ruleForm'].resetFields()
        for (const key in this.ruleForm) {
          this.ruleForm[key] = ''
        }
      }
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
    dialogType(val) {
      this.$emit('update:type', val)
    },
    // dialogOriginData(val) {
    //   this.$emit('update:originData', val)
    // },
  },
  created() {
    if (Object.keys(this.vipCarAttribute).length != 0) {
      this.vipAttributeOption = this.vipCarAttribute
    }
  },
  methods: {
    ...mapActions(['setVipCarAttribute']),
    //获取高频车属性枚举
    vipCarAttributeOptions() {
      let params = {
        dictKey: 'VIP_VEHICLE_ATTRIBUTE',
      }
      request({
        url: api.vipCarAttribute,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, '获取枚举')
          if (res.code == 200) {
            this.vipAttributeOption = res.data.map((item) => {
              return {
                label: item.value,
                value: item.code,
              }
            })
            this.setVipCarAttribute(this.vipAttributeOption)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //   if (this.verifyHandle()) {
          if (this.dialogType === 'add') {
            this.addVipCar()
          } else {
            this.editVipCar()
          }
          //   }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addVipCar() {
      let params = _ignoreEmpty(JSON.parse(JSON.stringify(this.ruleForm)))
      //新增不需要id
      params.id || delete params.id
      console.log('prams===>>>', params)
      request({
        url: api.addVipCar,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res) {
            this.$message({
              message: '添加成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.dialogType = ''
            this.$emit('on-submit')
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    editVipCar() {
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      console.log('prams===>>>', params)
      /**
       * 2022.5.30修改
       * 1.只允许修改备注
       * 2.重置高频车和修改高频车用同一接口，入参不同（修改：id,remarks；重置：id,isReset）
       *  */
      params.licenseColor || delete params.licenseColor
      params.licenseCode || delete params.licenseCode
      params.vipAttribute || delete params.vipAttribute
      request({
        url: api.updateVipCar,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res) {
            this.$message({
              message: '修改成功',
              type: 'success',
            })
            this.dialogFormVisible = false
            this.dialogType = ''
            this.$emit('on-submit')
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    cancel() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
    handleCloseIcon() {
      this.dialogType = ''
      this.dialogFormVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog--center .el-dialog__body {
  padding: 30px;
}
// .el-form-item__label {
//   text-align: center;
//   white-space: nowrap;
// }
.special_dialog .el-dialog__header {
  border-bottom: 1px solid #e8e8e8;
  // padding: 20px 0;
}
</style>
