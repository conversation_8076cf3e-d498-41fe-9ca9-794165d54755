<template>
  <div class="authorizeDialog">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      title="导入高频车"
      :before-close="handleCloseIcon"
    >
      <!-- <div class="selector g-flex g-flex-start g-flex-align-center">
        <div class="label">银行：</div>
        <el-select v-model="formData.bankType"
                   placeholder="请选择">
          <el-option v-for="item in bankTypeList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
      </div> -->
      <fieldset class="fieldset">
        <legend>附件上传</legend>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
          <div slot="tip" class="el-upload__tip">1、只能上传.xls/.xlsx文件</div>
          <div slot="tip" class="el-upload__tip">
            2、导入字段包含：[ 车牌号 | 车牌颜色 | 高频车属性 | 备注 ]
          </div>
        </el-upload>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      formData: {
        file: '',
        // bankType: '',
      },
      fileList: [],
      // bankTypeList: [
      //   { value: '1', label: '农信' },
      //   { value: '2', label: '建行' },
      //   { value: '3', label: '柳行' },
      //   { value: '4', label: '邮政' },
      //   { value: '5', label: '招商' },
      // ],
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    submitUpload() {
      // if (!this.formData.bankType) {
      //   this.$message({
      //     type: 'error',
      //     message: '请先选择银行',
      //   })
      //   return
      // } else
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }

      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      this.upload()
    },
    upload() {
      console.log('入参', config.importPayment)
      var formData = new FormData()
      formData.append('file', this.formData.file)
      // formData.append('bankType', this.formData.bankType)
      // let url =
      //   process.env.VUE_APP_BASE_API +
      //   '/issue-web/freeVehicleList/importFreeVehicleList'
      // console.log('url', url)
      this.$request
        .post(this.$interfaces.exportVipCar, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken(),
          },
        })
        .then((res) => {
          console.log('res', res)
          if (!res.data) {
            this.$message({
              type: 'error',
              message: res.msg,
            })
            return
          }
          this.$refs.upload.clearFiles()
          // this.formData.bankType = ''
          this.formData.file = ''
          this.$emit('uploadSuccess')
        })
        .catch((err) => {
          this.$message({
            type: 'error',
            message: res.msg,
          })
        })
      // this.$store
      //   .dispatch('custManagement/importPayment', formData)
      //   .then((res) => {
      //     this.formData.file = ''
      //     this.$emit('uploadSuccess')
      //   })
      //   .catch((err) => {
      //     this.formData.file = ''
      //     this.$message({
      //       type: 'error',
      //       message: '导入错误，请检测导入文件',
      //     })
      //   })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>