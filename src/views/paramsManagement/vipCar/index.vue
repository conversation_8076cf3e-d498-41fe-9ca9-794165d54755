<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search :formSpan="24"
                   :gutter="20"
                   ref="searchForm1"
                   label-position="right"
                   :model="search"
                   :fontWidth="2">
        <template slot="search-form"
                  style="padding-left: 10px">
          <dart-search-item label="车牌颜色："
                            prop="licenseColor">
            <el-select v-model="search.licenseColor"
                       clearable
                       placeholder="请选择">
              <el-option v-for="item in licenseColorOption"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌："
                            prop="licenseCode"
                            placeholder="请输入车牌号">
            <el-input v-model="search.licenseCode"
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="创建时间："
                            prop="timeArr">
            <el-date-picker v-model="timeArr"
                            type="daterange"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="备注"
                            prop="remarksCode">
            <el-select v-model="search.remarksCode"
                       clearable
                       placeholder="请选择">
              <el-option v-for="item in remarkList"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            style="margin-top: 10px"
                            :span="24">
            <div class="btn-wrapper">
              <el-button type="primary"
                         size="small"
                         native-type="submit"
                         @click="onSearchHandle"><i class="el-icon-search"></i> 搜索</el-button>
              <el-button size="small"
                         @click="onResultHandle">清空</el-button>
              <el-button type="primary"
                         size="small"
                         @click="addVipCar()"><i class="el-icon-plus"></i>添加</el-button>
              <el-button type="danger"
                         size="small"
                         @click="delOneVipCar('more')"><i class="el-icon-delete"></i>批量删除</el-button>
              <el-button size="mini"
                         type="primary"
                         @click="uploadDialogVisible = true"><i class="el-icon-upload"></i> 导入</el-button>
              <!-- <el-button size="mini" type="primary" @click="onExportHandle"
                ><i class="el-icon-download"></i> 导出</el-button
              > -->
              <!-- <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              > -->
            </div>
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table">
        <el-table v-loading="loading"
                  :data="tableData"
                  :align="center"
                  :header-align="center"
                  border
                  :max-height="550"
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '40px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '40px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id"
                  @selection-change="handleSelectionChange"
                  @sort-change="sortChange">
          <el-table-column type="selection"
                           width="40"></el-table-column>
          <el-table-column prop="id"
                           align="center"
                           min-width="80"
                           label="序号"
                           sortable="custom" />
          <el-table-column prop="licenseCode"
                           align="center"
                           min-width="100"
                           label="车牌号" />
          <el-table-column prop="licenseColor"
                           align="center"
                           min-width="120"
                           label="车牌颜色">
            <template slot-scope="scope">
              {{ getVehicleColor(scope.row.licenseColor) }}
            </template>
          </el-table-column>
          <el-table-column prop="remarks"
                           align="center"
                           min-width="200"
                           label="备注">
            <template slot-scope="scope">
              <el-tooltip class="tooltip-item"
                          effect="dark"
                          placement="top">
                <div slot="content">
                  {{ scope.row.remarks }}
                </div>
                <span>{{ scope.row.remarks }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="vipAttribute"
                           align="center"
                           min-width="120"
                           label="高频车属性" />
          <el-table-column prop="creatorName"
                           align="center"
                           min-width="120"
                           label="创建人" />
          <el-table-column prop="createTime"
                           align="center"
                           min-width="160"
                           label="创建时间"
                           sortable="custom" />
          <el-table-column prop="updatePerson"
                           align="center"
                           min-width="120"
                           label="更新人" />
          <el-table-column prop="updateTime"
                           align="center"
                           min-width="160"
                           label="更新时间"
                           sortable="custom" />

          <el-table-column fixed="right"
                           label="操作"
                           header-align="center"
                           min-width="240"
                           align="center">
            <template slot-scope="scope">
              <el-button size="mini"
                         style="width:70px"
                         :disabled="scope.row.isReset!='0'"
                         @click="resetVipCarHandle( scope.row)">{{scope.row.isReset=='0'?'重置':'已重置'}}</el-button>
              <el-button size="mini"
                         type="primary"
                         @click="addVipCar('edit', scope.row)">修改</el-button>
              <el-button size="mini"
                         type="danger"
                         @click="delOneVipCar('one', scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination background
                         @size-change="handleSizeChange"
                         @current-change="changePage"
                         :current-page="search.page"
                         :page-sizes="[20, 100, 200, 500]"
                         :page-size="search.pageSize"
                         layout="total, sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <add-and-edit :visible.sync="dialogAddVisible"
                  :type.sync="dialogType"
                  :originData="originData"
                  @on-submit="onUpdateList"></add-and-edit>
    <upload-dialog :visible.sync="uploadDialogVisible"
                   @uploadSuccess="uploadSuccess">
    </upload-dialog>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import {
  licenseColorOption,
  gxCardTypeOptionsIssue,
} from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import addAndEdit from './addAndEdit'
import uploadDialog from './uploadDialog'
import request from '@/utils/request'
import api from '@/api/index'
var moment = require('moment')
// import { decode } from 'js-base64'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    addAndEdit,
    uploadDialog,
  },
  data() {
    return {
      gxCardTypeOptionsIssue,
      licenseColorOption,
      loading: false,
      dialogType: 'add',
      dialogAddVisible: false,
      uploadDialogVisible: false,
      multipleSelection: [],
      id: '',
      idArr: [],
      originData: {},
      center: 'center',
      timeArr: [],
      search: {
        vehicleCode: '', //车牌号
        vehicleColor: '', //车牌颜色// 0-蓝色 // 1-黄色，2-黑色，3-白色，4-渐变绿色，5-黄绿双拼色， 6-蓝白渐变色，
        createTimeStart: '',
        createTimeEnd: '',
        remarksCode: '0',
        page: 1,
        pageSize: 20,
      },
      remarkList: [
        {
          value: '0',
          label: '全部',
        },
        {
          value: '1',
          label: '有备注',
        },
        {
          value: '2',
          label: '没备注',
        },
      ],
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getVipCarList()
  },
  methods: {
    sortChange(e) {
      let params = JSON.parse(JSON.stringify(this.search))

      if (e.prop == 'createTime') {
        params.orderByField = 'CREATE_TIME'
      } else if (e.prop == 'updateTime') {
        params.orderByField = 'UPDATE_TIME'
      } else if (e.prop == 'id') {
        params.orderByField = 'ID'
      }

      if (e.order == 'descending') {
        params.sort = 'desc'
      } else if (e.order == 'ascending') {
        params.sort = 'asc'
      } else {
        delete params.orderByField
        delete params.sort
      }
      this.getVipCarList(params)
    },
    getVehicleColor,
    getVipCarList(sortParams) {
      this.loading = true
      let params = {}
      if (sortParams) {
        params = sortParams
      } else {
        params = { ...this.search }
      }
      params.createTimeStart =
        this.timeArr && this.timeArr[0]
          ? moment(this.timeArr[0]).format('YYYY-MM-DD HH:mm:ss')
          : ''
      params.createTimeEnd =
        this.timeArr && this.timeArr[1]
          ? moment(this.timeArr[1]).format('YYYY-MM-DD HH:mm:ss')
          : ''

      console.log('prams入参', params)
      request({
        url: api.getVipCarList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          console.log('返回的高频车列表', res)
          this.tableData = res.data.data
          this.total = res.data.total
          this.multipleSelection = []
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    },
    changePage(page) {
      this.search.page = page
      this.getVipCarList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getVipCarList()
    },
    onSearchHandle() {
      this.search.page = 1
      this.getVipCarList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.timeArr = []
      this.search.page = 1
      this.search.pageSize = 20

      this.getVipCarList()
    },

    update(data) {},
    //重置高频车
    resetVipCarHandle(data) {
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '是否确认重置高频车属性？'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.resetVipCar(data)
          }
        },
      })
    },
    resetVipCar(data) {
      let params = {
        id: data.id,
        isReset: '1',
      }
      request({
        url: api.updateVipCar,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, '重置高频车')
          if (res) {
            this.message('重置成功', 'success')
            this.getVipCarList()
          }
        })
        .catch((err) => {
          this.loading = false
          console.log(err)
        })
    },
    addVipCar(dialogType = 'add', originData) {
      if (dialogType === 'edit') {
        // this.$confirm('请选中一条记录！', '提示', {
        //   confirmButtonText: '确定',
        //   showCancelButton: false,
        //   type: 'warning',
        // })
        // return
        this.originData = originData
      }
      this.dialogType = dialogType
      this.dialogAddVisible = true
    },
    delOneVipCar(type, id) {
      let ids = []
      if (type == 'more') {
        //批量删除
        if (this.multipleSelection.length == 0) {
          this.message('请先选中一条记录！', 'warning')
          return
        }
        ids = this.multipleSelection
      } else {
        //单条删除
        ids.push(id)
      }

      this.confirmDilog('确定要删除该条数据吗？', '删除操作', 'danger')
        .then(() => {
          this.loading = true
          request({
            url: api.delVipCar,
            method: 'post',
            data: { ids },
          })
            .then((res) => {
              this.loading = false
              if (res) {
                this.message('删除成功', 'success')
                this.getVipCarList()
              }
            })
            .catch((err) => {
              this.loading = false
              console.log(err)
            })
        })
        .catch((err) => {
          this.message(err.msg, err.type)
        })
    },
    onUpdateList() {
      // this.radio = []
      this.getVipCarList()
    },
    handleSelectionChange(selection) {
      console.log('selection', selection)
      this.multipleSelection = []
      selection.forEach((item) => {
        if (!this.multipleSelection.includes(item.id)) {
          this.multipleSelection.push(item.id)
        }
      })
    },
    //导入文件成功回调
    uploadSuccess() {
      this.uploadDialogVisible = false
      this.message('导入文件成功', 'success')
      this.getVipCarList()
    },
    confirmDilog(msg, title, type) {
      return new Promise((resolve, reject) => {
        // if (useType === 'moreConfirm') {
        // if (this.radio.length === 0) {
        //   reject({ msg: '请先选中一条记录！', type: 'warning' })
        //   return
        // }
        // }
        this.$confirm(msg, title, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: type,
        })
          .then(() => {
            console.log('确定按钮')
            resolve()
          })
          .catch(() => {
            reject({ msg: '取消确认', type: 'info' })
          })
      })
    },
    message(msg, type) {
      this.$message({
        type: type,
        message: msg,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.refund {
  padding: 20px;

  .table {
    margin: 0px 0 10px 0;
    // height: 500px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }

  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .btn-wrapper {
    margin-left: 40px;
    // margin-top: 10px;
  }

  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
}
</style>
