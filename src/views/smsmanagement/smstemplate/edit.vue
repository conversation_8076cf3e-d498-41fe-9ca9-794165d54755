<template>
  <div>
      <el-dialog
			:title="title"
      width="40%"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      :before-close="handleCloseIcon"
    >
				<el-form
						ref="ruleForm"
						:model="detaildata"
						label-width="120px"
						class="demo-ruleForm"
					>
				  <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item class="my_form_label"
                      label="模板类型："
                      prop="templateType">
										<el-select v-model="detaildata.templateType"
										placeholder="请选择"
										collapse-tags>
										<el-option v-for="item in optiondata"
															:key="item.value"
															:label="item.label"
															:value="item.value" />
									</el-select>
						</el-form-item>
          </el-col>
					</el-row>
					<el-row :gutter="24">
          <el-col :span="24">
            <el-form-item class="my_form_label"
                      label="模板名称："
                      prop="corporateName">
										<el-input placeholder="请输入模板名称"
													v-model="detaildata.templateName">
										</el-input>
						</el-form-item>
          </el-col>
					</el-row>
					<el-row :gutter="24">
          <el-col :span="24">
            <el-form-item class="my_form_label"
                      label="模板CODE："
                      prop="corporateName">
										<el-input placeholder="请输入模板名称"
													v-model="detaildata.templateCode">
										</el-input>
						</el-form-item>
          </el-col>
					</el-row>
					<el-row :gutter="24">
          <el-col :span="24">
            <el-form-item class="my_form_label"
                      label="模板内容："
                      prop="templateContent">
										<el-input type="textarea" :rows="5" placeholder="请输入模板内容"
													v-model="detaildata.templateContent">
										</el-input>
						</el-form-item>
          </el-col>
					</el-row>
					<el-row :gutter="24">
          <el-col :span="24">
            <el-form-item class="my_form_label"
                      label="模板变量："
                      prop="templateVariable">
										<el-input type="textarea" :rows="5" placeholder="请输入模板内容"
													v-model="detaildata.templateVariable">
										</el-input>
						</el-form-item>
          </el-col>
					</el-row>
				</el-form>
				<span slot="footer"
            class="dialog-footer">
					<el-button @click="dialogFormVisible = false">取 消</el-button>
					<el-button type="primary"
										@click="tosave()">确 定</el-button>
				</span>
      </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
	props:{
    visible:{
			type:Boolean,
			default:false
		},
		title:{
			type:String,
			default:''
		},
		detaildata:{
			type:Object,
			default:{}
		}
	},
  data () {
    return{
			dialogFormVisible:false,
			optiondata:[
				{ value: '01', label: '验证码'},
				{ value: '02', label: '短信通知'},
				{ value: '03', label: '推广短信'},
			]
		}
  },
	methods:{
		handleCloseIcon(){
			this.dialogFormVisible = false
		},
		tosave(){
      if(this.detaildata.id){
        let data = {
					id:this.detaildata.id,
					templateType:this.detaildata.templateType,
					templateName:this.detaildata.templateName,
					templateCode:this.detaildata.templateCode,
					templateContent:this.detaildata.templateContent,
					templateVariable:this.detaildata.templateVariable,
				}
				request({
					url: api.smsupdate,
					method: 'post',
					data: data,
				})
        .then((res) => {
					if(res.code == 200){
            this.$message({
							message: '新建模板成功',
							type: 'success'
						});
						this.$emit('getdata')
						this.dialogFormVisible = false
					}
        })
        .catch(() => {})
			}else{
        let data = {
					templateType:this.detaildata.templateType,
					templateName:this.detaildata.templateName,
					templateCode:this.detaildata.templateCode,
					templateContent:this.detaildata.templateContent,
					templateVariable:this.detaildata.templateVariable,
				}
				request({
					url: api.smscreate,
					method: 'post',
					data: data,
				})
        .then((res) => {
					if(res.code == 200){
						this.$message({
							message: '修改模板成功',
							type: 'success'
						});
						this.$emit('getdata')
						this.dialogFormVisible = false
					}
        })
        .catch(() => {})
			}
		},
	},
	watch:{
		visible(val){
			this.dialogFormVisible = val
		},
		dialogFormVisible(val){
			this.$emit('update:visible',val)
		}
	}
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body{
	padding-bottom: 0 !important;
}
</style>>

