<template>
  <div class="page-box">
    <dart-search
      ref="searchForm1"
      label-position="right"
      class="search"
      :formSpan="22"
      :rules="rules"
      :model="search"
    >
      <template slot="search-form">
				<dart-search-item label="创建时间起始：" prop="createdTimeSt">
          <el-date-picker
            v-model="search.createdTimeSt"
            type="datetime"
            :clearable="false"
            placeholder="选择日期"
            :default-time="defaultTime1"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="创建时间截止：" prop="createdTimeEn">
          <el-date-picker
            v-model="search.createdTimeEn"
            type="datetime"
            :clearable="false"
            placeholder="选择日期"
            :default-time="defaultTime2"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="模板类型：" prop="templateType">
          <el-select
            v-model="search.templateType"
            placeholder="请选择"
            :clearable="true"
            collapse-tags
          >
            <el-option
              v-for="item in optiondata"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="模板CODE：" prop="templateCode">
          <el-input v-model="search.templateCode" placeholder=""></el-input>
        </dart-search-item>
        
        <dart-search-item>
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button
            size="mini"
            native-type="submit"
            @click="onResultHandle()"
            >重置</el-button
          >
					<el-button
					  type="primary"
            size="mini"
            native-type="submit"
            @click="add()"
            >新增</el-button
          >
        </dart-search-item>
      </template>
    </dart-search>
		<div class="table table-box">
      <el-table :data="tableData"
                v-loading="tableloading"
                border
                style="width: 100%"
                height="100%"
                ref="multipleTable"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }">
								<el-table-column prop="createdName" align="center" label="创建人" />
								<el-table-column prop="createdTime" min-width="100" align="center" label="创建时间" />
								<el-table-column prop="updatedName" align="center" label="更新人" />
								<el-table-column prop="updatedTime" align="center" label="更新时间" />
								<el-table-column prop="templateType" align="center" label="模板类型">
                  <template slot-scope="scope">
                    {{gettype(scope.row.templateType)}}
                  </template>
                </el-table-column>
								<el-table-column prop="templateName" align="center" label="模板名称" />
								<el-table-column prop="templateCode" align="center" label="模板CODE" />
								<el-table-column prop="templateContent" min-width="240" align="center" label="模板内容" />
								<el-table-column prop="templateVariable" align="center" label="模板变量" />
								<el-table-column prop=""
                         fixed="right"
                         align="center"
                         width="150"
                         label="操作">
                <template slot-scope="scope">
                  <el-button 
                            size="mini"
                            @click="toedit(scope.row)">修改</el-button>
                  <el-button 
                            size="mini"
                            type="danger"
                            @click="todelete(scope.row)">删除</el-button>          
                </template>	
								</el-table-column>			 
			</el-table>
			
		</div>
		<div class="pagination">
        <el-pagination background
											@current-change="changePage"
											:current-page="search.pageIndex"
											:page-size="search.pageSize"
											layout="prev, pager, next, jumper"
											:total="total">
        </el-pagination> 
      </div>
     <edit :visible.sync="dialogFormVisible" :title="nowtitle" :detaildata="detaildata" @getdata='onSearchHandle'></edit>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import edit from './edit'
var moment = require('moment')
export default {
	components:{
		dartSearch,
		dartSearchItem,
    edit
	},
  created(){
    this.search.createdTimeSt = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.search.createdTimeEn = moment().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.getlist()
  },
  data() {
    return {
      dialogFormVisible:false,
      defaultTime1:'00:00:00',
      defaultTime2:'23:59:59',
      nowtitle:'',
			rules:{},
      detaildata:{
        templateType:'',
        templateName:'',
        templateCode:'',
        templateContent:'',
        templateVariable:''
      },
      search: {
				createdTimeSt:'',
				createdTimeEn:'',
				templateCode:'',
				templateType:'',
				pageSize:10,
				pageIndex:1
			},
			total:0,
			tableloading:false,
			optiondata:[
				{ value: '01', label: '验证码'},
				{ value: '02', label: '短信通知'},
				{ value: '03', label: '推广短信'},
			],
			tableData:[],
			pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
	methods:{
		getlist(){
      let data = {
        createdTimeSt:  moment(this.search.createdTimeSt).format('YYYY-MM-DD HH:mm:ss'),
				createdTimeEn: moment(this.search.createdTimeEn).format('YYYY-MM-DD HH:mm:ss'),
				templateCode: this.search.templateCode,
				templateType: this.search.templateType,
				pageSize:this.search.pageSize,
				pageIndex:this.search.pageIndex,
      }
      request({
        url: api.smsquery,
        method: 'post',
        data: data,
      })
        .then((res) => {
         this.tableData = res.data.records
        })
        .catch(() => {})
		},
		onSearchHandle(){
      if (moment(this.search.createdTimeSt).isAfter(this.search.createdTimeEn)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }
			this.search.pageIndex = 1
			this.getlist()
		},
		changePage(e){
			this.search.pageIndex = e
			this.getlist()
		},
    add(){
      this.nowtitle = '新增模板'
      this.dialogFormVisible = true
      this.detaildata = {
        templateType:'',
        templateName:'',
        templateCode:'',
        templateContent:'',
        templateVariable:''
      }
    },
    toedit(item){
      this.nowtitle = '编辑模板'
      this.dialogFormVisible = true
      this.detaildata = {
        id:item.id,
        templateType:item.templateType,
        templateName:item.templateName,
        templateCode:item.templateCode,
        templateContent:item.templateContent,
        templateVariable:item.templateVariable,
      }
    },
    todelete(item){
      this.$confirm('此操作将永久删除该模板, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
         this.godelete(item.id)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });          
        });
    },
    godelete(id){
      let data = {
        id:id
      }
      request({
        url: api.smsdelete,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if(res.code == 200){
            this.$message({
							message: '删除模板成功',
							type: 'success'
						});
            this.onSearchHandle()
          }
        })
      .catch(() => {})
    },
    onResultHandle(){
      this.$refs['searchForm1'].$children[0].resetFields()
		},
    gettype(val){
      for(let i = 0;i<this.optiondata.length;i++ ){
         if (this.optiondata[i].value == val ){
           return this.optiondata[i].label
         }
      }
      return ''
    }
	}
}
</script>

<style lang="scss" scoped>
.page-box{
  padding: 20px 20px 0;
	display: flex;
	flex-direction: column;
	height: 100%;
	.table{
		flex: 1;
	}
	.pagination {
    margin: 10px 0;
  }
}
</style>>
