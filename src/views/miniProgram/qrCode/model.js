

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'businessType',
      label: '业务类型',
      width: 140,
      formatter:(row) => {
        return row == '0' ? '线上发行推广码' :''
      }
    },
    {
      prop: 'name',
      label: '小程序名称',
    },
    {
      prop: 'createdByName',
      label: '工号',
    },
    {
      prop: 'promotionCodeNumber',
      label: '推广码编号',
      width: 140,
    },
    {
      prop: 'page',
      width: 180,
      label: '跳转路径',
    },
    {
      prop: 'scene',
      width: 140,
      label: '场景值',
    },
    {
      prop: 'sceneValue',
      width: 200,
      label: '场景值对应Value',
    },
    {
      prop: 'envVersion',
      label: '小程序版本',
      formatter:(row) => {
        return row == 'release' ? '正式' :row == 'trial' ? '体验':'开发'
      }
    },
    {
      prop: 'remarks',
      label: '备注',
    },
    {
      prop: 'qrCode',
      // fixed: 'right',
      width: 250,
      label: '小程序码'
    },
    {
      prop: 'action',
      // fixed: 'right',
      width: 120,
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'name',
      label: '小程序名称',
      default: '',
    },
    {
      type: 'select',
      field: 'businessType',
      label: '业务类型',
      default: '',
      options:[
        {
          label:'线上发行推广码',
          value: '0'
        }
      ]
    },
    {
      type: 'input',
      field: 'promotionCodeNumber',
      label: '推广码编号',
      default: '',
    },
  ]
}
