<template>
  <div class="url-link">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <div class="top-btn" style="margin-bottom: 10px;">
        <el-button type="primary" size="mini" @click="add">新增</el-button>
        <el-button type="primary" size="mini" @click="batchDelete"
          >批量删除</el-button
        >
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 二维码 -->
        <template slot="qrCode" slot-scope="{ scope }">
          <div class="img-box">
            <el-image
              :src="scope.imageUrl"
              fit="fill"
              @click.native="showDialog(scope)"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline"></i>
              </div>
            </el-image>
          </div>
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="danger" @click="handelRow(scope, 'del')"
            >删除</el-button
          >
        </template>
      </my-table>
    </div>
    <!-- 表单弹框 -->
    <formLayer ref="formLayer" @submit="handleSubmit"></formLayer>
    <!-- 图片预览 -->
    <el-dialog class="show-box" width="36%" :visible.sync="dialogVisible">
      <el-image :src="selectedImage" />
    </el-dialog>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { linkList, addLink, delLink } from '@/api/wechat'
import formLayer from './components/form-layer.vue'

export default {
  components: {
    MyTable,
    SearchForm,
    formLayer
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: linkList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'page',
      selectedImage: '',
      dialogVisible: false
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 打开新增/编辑弹框
    add() {
      this.$refs.formLayer.reset()
      this.$refs.formLayer.dialogVisible = true
    },
    // 提交处理
    async handleSubmit(params) {
      const res = await addLink(params)
      if (res.code == 200) {
        this.$message.success('添加成功')
        this.$refs.formLayer.dialogVisible = false
        this.getTableData()
      }
    },
    // 二维码图放大
    showDialog(row) {
      this.selectedImage = row.imageUrl
      this.dialogVisible = true
    },
    // 操作
    handelRow(row, type) {
      let params = {
        ids: [row.id]
      }
      this.deleteUrlLink(params)
    },
    // 批量删除
    batchDelete() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let params = {
        ids: this.selectArr.map(item => item.id)
      }
      this.deleteUrlLink(params)
    },
    // 删除接口
    deleteUrlLink(params) {
      this.$confirm('请确认是否要删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await delLink(params)
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    }
  },
  created() {
    this.getTableData()
  }
}
</script> 

<style lang="scss" scoped>
.img-box {
  display: flex;
  justify-content: center;
  align-items: center;
  .el-image {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    .el-icon-picture-outline {
      font-size: 24px;
    }
  }
}

.show-box {
  ::v-deep .el-dialog__body {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>