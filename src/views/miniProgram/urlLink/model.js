

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'businessType',
      label: '业务类型',
      width: 140,
      formatter:(row) => {
        return row == '2' ? '微信小程序跳转链接' :''
      }
    },
    {
      prop: 'name',
      label: '小程序名称',
      width: 140,
    },
    {
      prop: 'createdByName',
      label: '工号',
      width: 140,
    },
    {
      prop: 'promotionCodeNumber',
      label: '推广码编号',
      width: 140,
    },
    {
      prop: 'urlLink',
      label: '微信小程序跳转链接',
      width: 240,
      // wordWrap:true
    },
    {
      prop: 'linkExpireTime',
      label: '小程序链接到期时间',
      width: 180,
    },
    // {
    //   prop: 'qrCode',
    //   // fixed: 'right',
    //   width: 200,
    //   label: '小程序码'
    // },
    {
      prop: 'page',
      width: 180,
      label: '跳转路径',
    },
    {
      prop: 'envVersion',
      label: '小程序版本',
      width: 140,
      formatter:(row) => {
        return row == 'release' ? '正式' :row == 'trial' ? '体验':'开发'
      }
    },
    {
      prop: 'scene',
      width: 160,
      label: '场景值',
    },
    {
      prop: 'sceneValue',
      width: 200,
      label: '场景值对应Value',
    },
    {
      prop: 'remarks',
      label: '备注',
      width: 200,
    },
    {
      prop: 'action',
      fixed: 'right',
      width: 120,
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'name',
      label: '小程序名称',
      default: '',
    },
    {
      type: 'select',
      field: 'businessType',
      label: '业务类型',
      default: '',
      options:[
        {
          label:'微信小程序跳转链接',
          value: '2'
        }
      ]
    },
    {
      type: 'input',
      field: 'promotionCodeNumber',
      label: '推广码编号',
      default: '',
    },
  ]
}
