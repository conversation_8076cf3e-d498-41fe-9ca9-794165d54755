<template>
  <el-dialog :visible.sync="dialogVisible" title="新增小程序码" width="40%">
    <el-form ref="form" :model="formData" label-width="150px" v-if="dialogVisible" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item label="跳转路径" prop="page">
        <el-input v-model="formData.page"></el-input>
      </el-form-item>
      <el-form-item label="场景值" prop="scene">
        <el-input v-model="formData.scene"></el-input>
      </el-form-item>
      <el-form-item label="场景值对应Value" prop="scene">
        <el-input v-model="formData.sceneValue"></el-input>
      </el-form-item>
      <el-form-item label="小程序版本" prop="envVersion">
        <el-radio-group v-model="formData.envVersion">
          <el-radio :label="'release'">生产</el-radio>
          <el-radio :label="'trial'">体验</el-radio>
          <el-radio :label="'develop'">开发</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input type="textarea" v-model="formData.remarks"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      formData: {},
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        scene: [{ required: true, message: '场景值不能为空', trigger: 'blur' }],
        sceneValue: [
          {
            required: true,
            message: '场景值对应Value不能为空',
            trigger: 'blur'
          }
        ],
        envVersion: [
          { required: true, message: '请选择小程序版本', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          this.$emit('submit', this.formData)
          // this.dialogVisible = false
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    reset() {
      this.formData = {}
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
