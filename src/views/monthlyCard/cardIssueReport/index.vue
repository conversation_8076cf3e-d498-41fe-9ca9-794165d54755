<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:月月行记账卡发行报表 reportName:"monthIssueReport"
  * @author:zhangys
  * @date:2022/06/07 09:44:14
!-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="统计开始日期"
                          prop="startDate">
          <el-date-picker v-model="search.startDate"
                          type="date"
                          :clearable="false"
                          placeholder="选择开始日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="统计结束日期"
                          prop="endDate">
          <el-date-picker v-model="search.endDate"
                          type="date"
                          :clearable="false"
                          placeholder="选择结束日期"
                          :picker-options="pickerOptions">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      loading: false,
      search: {
        startDate: '',
        endDate: '',
      },
      tableHeight: 0,
      rules: {
        startDate: [
          { required: true, message: '请选择统计日期', trigger: 'change' },
        ],
        endDate: [
          { required: true, message: '请选择统计日期', trigger: 'change' },
        ],
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  created() {
    // this.search.thedate = moment().subtract(1, 'day').format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate((valid) => {
        if (valid) {
          if (moment(this.search.startDate).isAfter(this.search.endDate)) {
            this.$msgbox({
              title: '提示',
              showClose: true,
              type: 'error',
              customClass: 'my_msgBox singelBtn',
              dangerouslyUseHTMLString: true,
              message: '统计日期时间段异常',
            })
            return
          }
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },

    sendReportRequest() {
      this.loading = true
      let data = {
        transStart: moment(this.search.startDate).format('YYYY-MM-DD'),

        transEnd: moment(this.search.endDate)
          .add(1, 'days')
          .format('YYYY-MM-DD'),
        name: 'monthIssueReport',
      }

      request({
        url: api.report,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
