<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:vip卡账单可调差滞纳金明细
  * @author:zhangys
  * @date:2022/06/28 10:10:47
!-->
<template>
  <div class="container-box ">
    <dart-search ref="search"
                 label-position="right"
                 class="search"
                 :formSpan="24"
                 :gutter="20"
                 :rules="searchRules"
                 :model="search">
      <template slot="search-form">
        <dart-search-item label="用户名称："
                          prop="customer_name">
          <el-select v-model="search.customer_name"
                     filterable
                     clearable
                     @change="customerNameChange"
                     placeholder="请输入或选择用户名">
            <el-option v-for="item in customerBizList"
                       :key="item.customerId"
                       :label="item.customerNameId"
                       :value="item.customerNameId">
            </el-option>
          </el-select>
        </dart-search-item>

        <dart-search-item label="调差状态："
                          prop="pay_status">
          <el-select v-model="search.isDel"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in delStatus"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="8">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>

        </dart-search-item>
        <dart-search-item label="      "
                          prop="pay_status"
                          :span="24">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="rmForfeitsHandle">滞纳金调差</el-button>
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="rmForfeitsMonthHandle">转账凭证调差</el-button>
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="rmForfeitsExport"><i class="el-icon-download"></i>滞纳金调差明细导出</el-button>

        </dart-search-item>
      </template>
    </dart-search>

    <div class="table table-box">
      <div class="total_mount">滞纳金合计金额: <span style="color:red">{{moneyFilter(totalForfeits)}}</span> 元</div>
      <el-table :data="tableData"
                style="width: 100%"
                height="100%"
                ref="multipleTable"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                :row-class-name="tableRowClassName"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection"
                         width="55"
                         align="center"
                         :selectable="selectable">
        </el-table-column>
        <el-table-column prop="transMonth"
                         align="center"
                         min-width="110"
                         label="滞纳账单月份">
        </el-table-column>

        <el-table-column prop="forfeitAmount"
                         align="center"
                         min-width="120"
                         label="滞纳金金额(元)">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.forfeitAmount)}}
          </template>
        </el-table-column>
        <el-table-column prop="forfeitDate"
                         align="center"
                         min-width="110"
                         label="滞纳日期" />
        <el-table-column prop="isDel"
                         align="center"
                         min-width="130"
                         label="滞纳金调差状态">
          <template slot-scope="scope">
            {{scope.row.isDel=='0'?'未调差':'已调差'}}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="settleMonth"
                         align="center"
                         min-width="110"
                         label="账单月份">
        </el-table-column> -->

        <el-table-column prop="createdTime"
                         align="center"
                         min-width="180"
                         label="创建时间" />
        <el-table-column prop="reason"
                         align="center"
                         min-width="180"
                         label="原因">
        </el-table-column>
        <el-table-column prop="updateBy"
                         align="center"
                         min-width="110"
                         label="操作人">
        </el-table-column>

        <el-table-column prop="updateTime"
                         align="center"
                         min-width="170"
                         label="调差时间">
        </el-table-column>

      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.pageIndex"
                     :page-size="search.pageSize"
                     :page-sizes="[10, 20, 30, 50]"
                     layout="total,sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <el-dialog title="备注"
               width="30%"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :show-close="true">
      <el-form ref="forfeitsData"
               :rules="rules"
               :model="forfeitsData"
               label-width="50px"
               class="demo-ruleForm">
        <!-- <el-row :gutter="24">
          <el-col :span="24"> -->
        <el-form-item class="my_form_label"
                      label="备注"
                      prop="reason">
          <el-input placeholder="请输入备注"
                    maxlength="50"
                    v-model="forfeitsData.reason"
                    type="textarea">
          </el-input>
        </el-form-item>
        <div class="bottom-btn g-flex g-flex-center">
          <el-button @click="dialogVisible=false">关闭</el-button>

          <el-button type="primary"
                     @click="submitForm">
            确认
          </el-button>
        </div>
        <!-- </el-col>
        </el-row> -->
      </el-form>
    </el-dialog>
    <forfeitsDetail v-if="forfeitsDetailDialog"
                    :visible.sync='forfeitsDetailDialog'
                    :customerId='search.customerId'
                    :customerName='search.customer_name'></forfeitsDetail>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import forfeitsDetail from './forfeitsDetail'
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'

var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    forfeitsDetail,
  },
  data() {
    return {
      search: {
        customerId: '',
        customer_name: '',
        pageIndex: 1,
        pageSize: 30,
        isDel: '',
      },
      tableData: [],
      searchRules: {
        customer_name: [
          { required: true, message: '请输入或选择用户名', trigger: 'blur' },
        ],
      },
      nowtime: null,
      customerarr: [],
      total: 0,
      delStatus: [
        { value: '0', label: '未调差' },
        { value: '1', label: '已调差' },
      ],

      customerBizList: [],
      forfeitsData: {
        customerId: '',
        fIds: [],
        reason: '',
      },
      dialogVisible: false,
      rules: {
        reason: [{ required: true, message: '请输入备注', trigger: 'blur' }],
      },
      forfeitsDetailDialog: false,
      totalForfeits: '',
    }
  },
  created() {
    this.getCustomerBizList()
  },
  mounted() {},
  methods: {
    getList() {
      let params = JSON.parse(JSON.stringify(this.search))
      this.customerBizList.forEach((item) => {
        if (params.customerId == item.customerId) {
          params.customer_name = item.customerName
        }
      })
      if (!params.isDel) {
        delete params.isDel
      }
      this.loading = true
      request({
        url: api.vipCardForfeits,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total
          this.totalForfeits = res.data.totalForfeits
          console.log(res.data.records, res.data.orders, 'vipCardForfeits')
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    onSearchHandle() {
      this.$refs['search'].$children[0].validate((valid) => {
        if (valid) {
          this.getList()
          console.log(this.search, 'this.search')
        }
      })
    },
    onResultHandle() {
      this.search.customer_name = ''
      this.search.customerId = ''
      this.tableData = []
      this.total = 0
      this.search.isDel = ''
    },
    handleSizeChange(e) {
      this.search.pageSize = e
      this.getList()
    },
    changePage(val) {
      this.search.pageIndex = val
      this.getList()
    },
    //2022.6.7改动 要求选择或模糊查询用户名
    getCustomerBizList() {
      let params = {}
      this.loading = true
      request({
        url: api.getVipCustomerList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            let params = JSON.parse(JSON.stringify(res.data)).map((item) => {
              return {
                customerNameId: '(' + item.customerId + ')' + item.customerName,
                customerName: item.customerName,
                customerId: item.customerId,
              }
            })
            this.customerBizList = params
          }
        })
        .catch(() => {
          this.loading = false
        })
    },

    customerNameChange(val) {
      console.log(val)
      if (!val) {
        this.search.customerId = ''
        return
      }
      this.customerBizList.forEach((item) => {
        if (item.customerNameId == val) {
          console.log(item.customerId)
          this.search.customerId = item.customerId
        }
      })
    },
    clearInput(val) {
      this.search.customer_name = ''
    },

    //根据条件判断是否禁选
    selectable(row, index) {
      if (row.isDel == '0') {
        return true
      } else {
        return false
      }
    },

    handleSelectionChange(val) {
      this.forfeitsData.fIds = val.map((item) => {
        return item.fid
      })
    },
    submitForm() {
      this.$refs['forfeitsData'].validate((valid) => {
        if (valid) {
          this.rmForfeits()
        }
      })
    },
    rmForfeitsHandle() {
      if (this.forfeitsData.fIds.length == 0) {
        this.$message({
          message: '请选至少选中一条数据',
          type: 'warning',
        })
        return
      }
      this.dialogVisible = true
    },
    rmForfeits() {
      let params = JSON.parse(JSON.stringify(this.forfeitsData))
      params.customerId = this.search.customerId
      this.loading = true
      request({
        url: api.vipCardRmForfeits,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log(res, 'vipCardForfeits')
          this.loading = false
          this.dialogVisible = false
          this.forfeitsData.reason = ''
          this.getList()
          this.$message({
            message: '滞纳金调差成功!',
            type: 'success',
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.isDel === '1') {
        return 'success-row'
      }
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = float.div(float.mul(val, 100), 10000)
      return value
    },

    //滞纳金月份调差
    rmForfeitsMonthHandle() {
      this.$refs['search'].$children[0].validate((valid) => {
        if (valid) {
          this.forfeitsDetailDialog = true
        }
      })
    },
    //滞纳金调差明细导出
    rmForfeitsExport() {
      if (!this.search.customerId) {
        this.$message({
          message: '请先选择用户',
          type: 'warning',
        })
        return
      }
      let params = JSON.parse(JSON.stringify(this.search))
      if (!params.isDel) {
        delete params.isDel
      }
      this.loading = true
      request({
        url: api.vipCardForfeitsExport,
        method: 'post',
        data: params,
        responseType: 'arraybuffer',
        headers: {
          ResType: 'download',
        },
      })
        .then((response) => {
          var blob = new Blob([response], { type: 'application/vnd.ms-excel' })
          var objectUrl = URL.createObjectURL(blob)
          var a = document.createElement('a')
          document.body.appendChild(a)
          a.style = 'display: none'
          a.href = objectUrl
          a.download = '滞纳金调差明细.xlsx'
          a.click()
          document.body.removeChild(a)
        })
        .catch((err) => {})
    },
  },
}
</script>

<style lang="scss">
.el-table {
  .success-row {
    background-color: #f0f2f5 !important;
  }
}
.container-box {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
  .search {
    margin-top: 20px;
  }
  .table-box {
    padding: 20px 20px 10px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    padding: 25px 20px 10px 20px;
    background-color: #fff;
  }
}

.downbill {
  color: #409eff;
  cursor: pointer;
}
.tureamount {
  color: #409eff;
  cursor: pointer;
}
.total_mount {
  font-weight: bold;
  margin-bottom: 15px;
}
</style>