<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:2022.7.1重构滞纳金调差功能
  * @author:zhang<PERSON>
  * @date:2022/07/01 16:14:37
!-->
<template>
  <div>
    <el-dialog title="转账凭证调差"
               :visible.sync="dialogVisible"
               :close-on-click-modal="false"
               :center="true"
               width="80%"
               custom-class="shipment-dialog">
      <div class="container-box">
        <dart-search ref="search"
                     label-position="right"
                     class="search"
                     :formSpan="24"
                     :gutter="20"
                     :rules="searchRules"
                     :model="search">
          <template slot="search-form">
            <dart-search-item label="账单月份："
                              prop="transMonth">

              <el-date-picker v-model="search.transMonth"
                              type="month"
                              :clearable="false"
                              placeholder="选择日期"
                              :picker-options="pickerOptions">
              </el-date-picker>

            </dart-search-item>
            <dart-search-item label="调差状态："
                              prop="pay_status">
              <el-select v-model="search.isDel"
                         placeholder="请选择"
                         clearable
                         collapse-tags>
                <el-option v-for="item in delStatus"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item>
            <dart-search-item :is-button="true"
                              :span="8">
              <el-button type="primary"
                         size="mini"
                         native-type="submit"
                         @click="onSearchHandle">搜索</el-button>
              <el-button size="mini"
                         @click="onResultHandle">重置</el-button>
            </dart-search-item>
            <dart-search-item label="      "
                              prop="pay_status">

              <el-button type="primary"
                         size="mini"
                         native-type="submit"
                         @click="rmForfeitsHandle">转账日期调差</el-button>

            </dart-search-item>
          </template>
        </dart-search>
        <div class="table table-box">
          <el-table :data="tableData"
                    style="width: 100%"
                    height="100%"
                    ref="multipleTable"
                    :row-style="{ height: '54px' }"
                    :cell-style="{ padding: '0px' }"
                    :header-row-style="{ height: '54px' }"
                    :header-cell-style="{ padding: '0px' }"
                    :row-class-name="tableRowClassName">
            <!-- <el-table-column type="selection"
                             width="55"
                             align="center"
                             :selectable="selectable">
            </el-table-column> -->
            <el-table-column prop="transMonth"
                             align="center"
                             min-width="110"
                             label="滞纳账单月份">
            </el-table-column>

            <el-table-column prop="forfeitAmount"
                             align="center"
                             min-width="120"
                             label="滞纳金金额(元)">
              <template slot-scope="scope">
                {{moneyFilter(scope.row.forfeitAmount)}}
              </template>
            </el-table-column>
            <el-table-column prop="forfeitDate"
                             align="center"
                             min-width="110"
                             label="滞纳日期" />
            <el-table-column prop="isDel"
                             align="center"
                             min-width="130"
                             label="滞纳金调差状态">
              <template slot-scope="scope">
                {{scope.row.isDel=='0'?'未调差':'已调差'}}
              </template>
            </el-table-column>
            <el-table-column prop="settleMonth"
                             align="center"
                             min-width="110"
                             label="账单月份">
            </el-table-column>

            <el-table-column prop="createdTime"
                             align="center"
                             min-width="180"
                             label="创建时间" />
            <el-table-column prop="reason"
                             align="center"
                             min-width="180"
                             label="原因">
            </el-table-column>
            <el-table-column prop="updateBy"
                             align="center"
                             min-width="110"
                             label="操作人">
            </el-table-column>

            <el-table-column prop="updateTime"
                             align="center"
                             min-width="170"
                             label="调差时间">
            </el-table-column>

          </el-table>
        </div>
        <div class="pagination g-flex g-flex-end">
          <el-pagination background
                         @size-change="handleSizeChange"
                         @current-change="changePage"
                         :current-page="search.pageIndex"
                         :page-size="search.pageSize"
                         :page-sizes="[10, 20, 30, 50]"
                         layout="total,sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <refundDtffer :visible.sync='refundDtfferDialog'
                  :billDetail='billDetail'
                  @getList="getList"></refundDtffer>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
import refundDtffer from '../monthlyapprove/refundDtffer.vue'
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    refundDtffer,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerId: {
      type: String,
      default: '',
    },
    customerName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      search: {
        customerId: '',
        pageIndex: 1,
        pageSize: 30,
        isDel: '',
        transMonth: '',
      },
      tableData: [],
      searchRules: {
        transMonth: [
          { required: true, message: '请选择日期', trigger: 'blur' },
        ],
      },
      nowtime: null,
      customerarr: [],
      total: 0,
      delStatus: [
        { value: '0', label: '未调差' },
        { value: '1', label: '已调差' },
      ],

      customerBizList: [],
      dialogVisible: false,
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
      refundDtfferDialog: false,
      billDetail: {},
    }
  },

  created() {
    this.search.transMonth = moment().startOf('month').format('YYYY-MM')
    this.$nextTick(() => {
      this.dialogVisible = this.visible
      this.search.customerId = this.customerId
      this.getList()
    })
  },
  mounted() {},
  methods: {
    getList() {
      let params = JSON.parse(JSON.stringify(this.search))
      params.transMonth = moment(this.search.transMonth).format('YYYY-MM')
      if (!params.isDel) {
        delete params.isDel
      }
      this.loading = true
      request({
        url: api.vipCardForfeits,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total

          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    onSearchHandle() {
      this.$refs['search'].$children[0].validate((valid) => {
        if (valid) {
          this.getList()
        }
      })
    },
    onResultHandle() {
      this.search.transMonth = moment().startOf('month').format('YYYY-MM')
      //   this.search.customerId = ''
      this.tableData = []
      this.total = 0
      this.search.isDel = ''
    },
    handleSizeChange(e) {
      this.search.pageSize = e
      this.getList()
    },
    changePage(val) {
      this.search.pageIndex = val
      this.getList()
    },

    clearInput(val) {
      this.search.customer_name = ''
    },

    //根据条件判断是否禁选
    selectable(row, index) {
      if (row.isDel == '0') {
        return true
      } else {
        return false
      }
    },

    tableRowClassName({ row, rowIndex }) {
      if (row.isDel === '1') {
        return 'success-row'
      }
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = float.div(float.mul(val, 100), 10000)
      return value
    },

    //滞纳金调差
    rmForfeitsHandle() {
      if (this.tableData.length == 0) {
        this.$message({
          message: '暂无数据，无需调差',
          type: 'warning',
        })
        return
      }
      this.billDetail.customerName = this.customerName
      this.billDetail.customerId = this.search.customerId
      this.billDetail.transMonth = moment(this.search.transMonth).format(
        'YYYY-MM'
      )
      this.billDetail.sid = this.tableData[0].settleId
      this.refundDtfferDialog = true
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.el-table {
  .success-row {
    background-color: #f0f2f5 !important;
  }
}
.container-box {
  height: 500px;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
  .search {
    margin-top: 20px;
  }
  .table {
    padding: 0 0 !important;
  }
  .table-box {
    padding: 20px 20px 10px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    padding: 0px 20px 10px 20px;
    background-color: #fff;
  }
}

.downbill {
  color: #409eff;
  cursor: pointer;
}
.tureamount {
  color: #409eff;
  cursor: pointer;
}
</style>