<template>
  <div class="report-page">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="24"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import moment from 'moment'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      reportList: [
        {
          id: 1,
          name: 'monthReceivedReport',
          title: '账单回款情况统计报表'
        }
      ],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择账单月份', trigger: 'change' }
        ],
        transStart: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        transEnd: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'billMonth',
          label: '账单月份',
          placeholder: '请选择账单月份',
          valueFormat: 'yyyy-MM',
          customType: "month",
          // pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'transStart',
          label: '回款开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          // pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'transEnd',
          label: '回款结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          // pickerOptions: this.pickerOptions,
          default: ''
        }
      ]
    }
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.transStart).isAfter(fromData.transEnd)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.report-page {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>