<template>
  <div class="user">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="formConfig"
      :formTitle="item.title"
      :name="item.name"
      isDownload
      :rules="rules"
      btnAlign="left"
      @onSearchHandle="onSearchHandle"
      :btnSpan="6"
      :stampConfig="stampConfig"
    ></reportForms>
    <!-- isDownload -->
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
var moment = require('moment')
export default {
  components: {
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      tableHeight: 0,
      search: {
        name: 'monthForfeitProfitDetail', //报表名称
        billMonth: '' // 消费月份
      },
      reportList: [
        {
          id: 1,
          name: 'monthForfeitProfitDetail',
          title: '滞纳金分润明细报表'
        }
      ],
      stampConfig: {
        name: 'monthForfeitProfitDetail', //报表名称
        fileName: 'monthForfeitProfitDetail',
        keyword: '公司'
      },
      rules: {
        billMonth: [{ required: true, message: '统计月份', trigger: 'change' }]
        // transStart: [
        //   { required: true, message: '请选择日期', trigger: 'change' }
        // ],
        // transEnd: [{ required: true, message: '请选择日期', trigger: 'change' }]
      },
      pickerOptions: {
        disabledDate(time) {
          // console.log('time.getTime()', time.getTime())
          const b = time.getTime() < moment('2023-09') // 限制不能小于9月
          return b
        }
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'billMonth',
          label: '统计月份',
          placeholder: '请选择统计月份',
          valueFormat: 'yyyy-MM',
          customType: 'month',
          pickerOptions: this.pickerOptions,
          default: ''
        }
        // {
        //   type: 'datePicker',
        //   field: 'transStart',
        //   label: '回款开始日期',
        //   placeholder: '请选择日期',
        //   valueFormat: 'yyyy-MM-dd',
        //   // pickerOptions: this.pickerOptions,
        //   default: ''
        // },
        // {
        //   type: 'datePicker',
        //   field: 'transEnd',
        //   label: '回款结束日期',
        //   placeholder: '请选择日期',
        //   valueFormat: 'yyyy-MM-dd',
        //   // pickerOptions: this.pickerOptions,
        //   default: ''
        // }
      ]
    }
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.transStart).isAfter(fromData.transEnd)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
