<template>
  <div>
    <el-dialog title="账单详情"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               width="60%"
               custom-class="shipment-dialog">
      <el-form class="nat-form nat-form-list"
               label-width="160px">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="客户名称：">
              <div>{{detail.cust_name}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="审核状态：">
              <div>{{getcheckstatus(detail.check_status)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="支付状态：">
              <div>{{getpaystatus(detail.pay_status)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="流水月份：">
              <div>{{detail.trans_month}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="通行交易笔数：">
              <div>{{detail.trans_total}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通行交易总金额(元)：">
              <div>{{moneyFilter(detail.trans_amount)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退费交易笔数：">
              <div>{{detail.refund_total}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="退费交易金额(元)：">
              <div>{{moneyFilter(detail.refund_amount)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="补交总数：">
              <div>{{detail.resettle_total}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="补交金额(元)：">
              <div>{{moneyFilter(detail.resettle_amount)}}</div>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="服务费(元)：">
              <div>{{moneyFilter(detail.service_amount)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="滞纳金(元)：">
              <div>{{moneyFilter(detail.forfeit_amount)}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实付金额合计(元)：">
              <div>{{moneyFilter(detail.total_amount)}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col v-if="detail.pay_status != 0"
                  :span="8">
            <el-form-item label="支付时间：">
              <div>{{detail.pay_time}}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="detail.check_status > 0"
                  :span="8">
            <el-form-item label="核对人：">
              <div>{{detail.verify_name}}</div>
            </el-form-item>
          </el-col>
          <el-col v-if="detail.check_status > 0"
                  :span="8">
            <el-form-item label="核对时间：">
              <div>{{detail.verify_time}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">

          <el-col v-if="detail.check_status > 1"
                  :span="8">
            <el-form-item label="复核人：">
              <div>{{detail.check_Name}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="detail.check_status > 1"
                          label="复核时间：">
              <div>{{detail.check_time}}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="detail.check_status > 2"
                          label="审核人：">
              <div>{{detail.confirm_Name}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">

          <el-col :span="8">
            <el-form-item v-if="detail.check_status > 2"
                          label="审核时间：">
              <div>{{detail.confirm_time}}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <div class="foot" style="padding-top:0">
					<el-button v-if="detail.forfeit_amount>0" @click="tobilllist()" type="primary">月账单滞纳金</el-button>
        </div> -->

      </el-form>
      <div v-if="!readtype"
           class="foot">
        <el-button style="margin:0 20px"
                   @click="dialogFormVisible=false">取消</el-button>
        <el-button v-if="detail.check_status == 0"
                   type="primary"
                   style="margin:0 20px"
                   native-type="submit"
                   @click="tobill(detail,1)">核对</el-button>
        <el-button v-if="detail.check_status == 1"
                   type="primary"
                   style="margin:0 20px"
                   native-type="submit"
                   @click="tobill(detail,2)">复核</el-button>
        <el-button v-if="detail.check_status == 2"
                   type="primary"
                   style="margin:0 20px"
                   native-type="submit"
                   @click="tobill(detail,3)">审核</el-button>
        <el-button v-if="detail.check_status == 3 && detail.pay_status != 2"
                   type="primary"
                   style="margin:0 20px"
                   native-type="submit"
                   @click="onRepaymentHandle(detail)">还款</el-button>
      </div>
    </el-dialog>
    <!-- <billForfeit v-if="billDialog" :visible.sync='billDialog' :custid="detail.customer_id"  :transmonth="detail.trans_month"></billForfeit> -->
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
// import billForfeit from './billForfeit.vue'
export default {
  // components:{
  //   billForfeit
  // },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detail: {
      type: Object,
      default: {},
    },
    readtype: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible
    })
    console.log(this.readtype)
  },
  data() {
    return {
      billDialog: false,
      dialogFormVisible: false,
      optiondata: [
        { value: '0', label: '未审核' },
        { value: '1', label: '已核对' },
        { value: '2', label: '已复核' },
        { value: '3', label: '已审核' },
      ],
      optiondata2: [
        { value: '0', label: '未支付' },
        { value: '1', label: '支付中' },
        { value: '2', label: '支付成功' },
        { value: '3', label: '支付失败' },
      ],
    }
  },
  methods: {
    getcheckstatus(val) {
      let arr = this.optiondata
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    getpaystatus(val) {
      let arr = this.optiondata2
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    tobill(item, type) {
      let billtime = this.detail.trans_month + '-01'
      let data = {
        trans_month: billtime,
        check_status: type,
        customs: [
          {
            customer_id: item.customer_id,
          },
        ],
      }
      request({
        url: api.verifyBill,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.$message({
            message: '处理成功',
            type: 'success',
          })
          this.$emit('getlist')
          this.dialogFormVisible = false
        })
        .catch(() => {})
    },
    tobilllist() {
      this.billDialog = true
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = value / 100
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 1000px !important;
}
::v-deep .el-col {
  height: 40px !important;
}
.foot {
  padding-top: 20px;
  text-align: center;
}
</style>