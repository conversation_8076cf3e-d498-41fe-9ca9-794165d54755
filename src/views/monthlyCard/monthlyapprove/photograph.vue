<template>
  <div>
    <div class="dft-box">
      <input
        type="file"
        id="myInput"
        ref="myInput"
        class="imagebase"
        v-show="false"
        :accept="imgList"
        @change="getImgBase"
      />
    </div>
    <div class="archives-viewer" v-viewer="viewerOptions">
      <template v-for="(item, index) in options">
        <!-- 预览模式需要file_url存在 上传模式需要isShow -->
        <div
          class="archives-item"
          :key="index"
          v-if="(item.isShow && !previewMode) || (previewMode && item.file_url)"
        >
          <template v-if="!item.file_url && !item.flag">
            <div
              :style="imgAreaStyle"
              class="archives-item__img-area"
              @click="openFDialog(item)"
            >
              <img src="@/image/local.png" alt="" file_url="" class="img" />
            </div>
          </template>
          <template v-else>
            <div
              v-if="!item.flag"
              class="archives-item__img-area"
              :style="imgAreaStyle"
            >
              <img
                :src="item['file_url']"
                :file_url="item['file_url']"
                alt=""
                class="img"
              />
            </div>
          </template>

          <div class="archives-item__text-area">
            <div class="archives-item__title">
              {{ item.lable }}
            </div>
          </div>
          <div
            class="archives-item__close-area"
            v-if="!previewMode && item.file_url"
          >
            <i class="el-icon-close" @click="deleteimg(item)"></i>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import axios from 'axios'
import { getToken } from '@/utils/auth'
export default {
  props: {
    customerId: {
      type: String,
      default: '',
    },
    vehicle_code: {
      type: String,
      default: '',
    },
    vehicle_color: {
      type: String,
      default: '',
    },
    other_code: {
      // 图片上传唯一
      type: String,
      default: '',
    },
    scene: {}, // 图片上传场景
    pictureSource: {}, // 图片列表
    viewerOptions: {
      //  图片预览配置项
      type: Object,
      default() {
        return {
          toolbar: true,
          navbar: false,
          title: false,
          url: 'file_url',
        }
      },
    },
    imgAreaStyle: {
      // 加载图片区域样式
      type: Object,
      default() {
        return {}
      },
    },
    uploadType: {
      //上传档案类型 CACHEIMGUPLAOD 获取上传图片地址 UPLAOD存储文件上传
      type: String,
      default: 'CACHEIMGUPLAOD',
    },
    previewMode: {
      // 开启预览模式
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      imgList: [
        'image/jpg',
        'image/jpeg',
        'image/gif',
        'image/png',
        'image/bmp',
      ],
      imgBase: '',
      photo_code: '',
      options: [],
    }
  },
  watch: {
    pictureSource(val) {
      if (val && val.length) {
        this.options = val
      }
    },
  },
  computed: {},
  created() {
    if (this.pictureSource && this.pictureSource.length) {
      this.options = this.pictureSource
    }
  },
  methods: {
    openFDialog(item) {
      this.photo_code = item.photo_code || ''
      let _self = this
      this.$nextTick(() => {
        _self.$refs.myInput.value = ''
        _self.$refs.myInput.click()
      })
    },
    deleteimg(item) {
      this.$confirm('是否确定删除该档案图片？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          if (this.uploadType == 'CACHEIMGUPLAOD') {
            this.$emit('on-delete', item)
          } else {
            this.delPic(item)
          }
        })
        .catch((e) => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          })
        })
    },
    delPic(item) {
      let data = {
        customer_id: this.customerId,
        file_serial: item.file_serial,
        photo_code: item.photo_code,
        other_code: this.other_code || '',
      }
      request({
        url: api.delPic,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              message: '删除成功',
              type: 'success',
            })
            this.$emit('on-delete', data)
          }
        })
        .catch(() => {})
    },
    upload(obj) {
      let param = {}
      let contentType = 'application/json'
      let url = process.env.VUE_APP_BASE_API + '/issue-web' + api.upImg
      if (this.uploadType == 'CACHEIMGUPLAOD') {
        url = process.env.VUE_APP_BASE_API + '/issue-web' + api.cacheImgUplaod
        param = {
          image: obj.imgBase64,
          photoCode: obj.photo_code,
        }
      } else {
        param = new FormData() //创建form对象
        param.append('customer_id', this.customerId || '')
        param.append('vehicle_code', this.vehicle_code || '')
        param.append('vehicle_color', this.vehicle_color || '')
        param.append('photo_code', obj.photo_code || '')
        param.append('other_code', this.other_code || '')
        param.append('scene', obj.scene || '')
        let blob = this.dataUrlToBlob(obj.imgBase64 || '')
        param.append('file', blob, 'img.jpg')
        contentType = 'multipart/form-data'
      }
      let config = {
        headers: {
          'Content-Type': contentType,
          Authorization: getToken(),
        },
      } //添加请求头
      axios
        .post(url, param, config)
        .then((response) => {
          if (response.data.code == 200) {
            this.$emit('on-upload', response.data)
          } else {
            this.$msgbox({
              title: '提示',
              message: response.data.msg,
              customClass: 'my_msgBox singelBtn',
              confirmButtonText: '确定',
              type: 'error',
            })
          }
        })
        .catch((err) => {
          _this.$alert(err.message, '提示', {
            dangerouslyUseHTMLString: true,
            showClose: false,
            confirmButtonText: '确定',
          })
        })
    },
    getImgBase() {
      let _this = this
      let event = event || window.event
      let file = event.target.files[0]
      if (parseInt(file.size / 1024 / 1024) >= 20) {
        // 超过10M，提示无法上传
        this.$msgbox({
          title: '温馨提示',
          message: '上传图片大小不得超过20M',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      if (this.imgList.indexOf(file.type) === -1) {
        this.$msgbox({
          title: '温馨提示',
          message: '请上传图片格式的文件',
          customClass: 'my_msgBox singelBtn',
          // showCancelButton: true,
          confirmButtonText: '确定',
        })
        return
      }
      var maxSize = 400 * 1024
      this.changeImage(file, maxSize, this.getFileBase64)
      return
    },
    changeImage(file, size, callback, type) {
      let _self = this
      if (file.size > size) {
        // 文件大于size 则进行压缩处理
        var reader = new FileReader()
        reader.readAsDataURL(file)
        var img = new Image()
        reader.onload = function (e) {
          console.log('reader', e)
          img.src = e.target.result
          img.onload = function () {
            var data = _self.compress(img)
            console.log('压缩后', data)
            var text = window.atob(data.split(',')[1])
            var buffer = new Uint8Array(text.length)
            for (var i = 0; i < text.length; i++) {
              buffer[i] = text.charCodeAt(i)
            }
            console.log('文件类型', file.type)
            var rev = _self.getBlob([buffer], file.type)
            console.log('压缩转码完成', rev)
            var rfile = new File([rev], file.name)
            callback(rfile, type)
          }
        }
      } else {
        callback(file, type)
      }
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader()
        let imgResult = ''
        reader.readAsDataURL(file)
        reader.onload = function () {
          imgResult = reader.result
        }
        reader.onerror = function (error) {
          reject(error)
        }
        reader.onloadend = function () {
          resolve(imgResult)
        }
      })
    },
    getFileBase64(file) {
      let _this = this

      this.getBase64(file).then((res) => {
        // _this.imgBase64 = res;
        const param = {
          imgBase64: res,
          scene: this.scene,
          photo_code: this.photo_code,
        }
        _this.upload(param)
      })
    },
    compress(img) {
      //    用于压缩图片的canvas
      var canvas = document.createElement('canvas')
      var ctx = canvas.getContext('2d')
      //    瓦片canvas
      var tCanvas = document.createElement('canvas')
      var tctx = tCanvas.getContext('2d')
      var initSize = img.src.length
      var width = img.width
      var height = img.height
      // 如果图片大于四百万像素，计算压缩比并将大小压至400万以下
      var ratio
      if ((ratio = (width * height) / 4000000) > 1) {
        ratio = Math.sqrt(ratio)
        width /= ratio
        height /= ratio
      } else {
        ratio = 1
      }
      canvas.width = width
      canvas.height = height
      //        铺底色
      ctx.fillStyle = '#fff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      // 如果图片像素大于100万则使用瓦片绘制
      var count
      if ((count = (width * height) / 1000000) > 1) {
        count = ~~(Math.sqrt(count) + 1) // 计算要分成多少块瓦片
        //            计算每块瓦片的宽和高
        var nw = ~~(width / count)
        var nh = ~~(height / count)
        tCanvas.width = nw
        tCanvas.height = nh
        for (var i = 0; i < count; i++) {
          for (var j = 0; j < count; j++) {
            tctx.drawImage(
              img,
              i * nw * ratio,
              j * nh * ratio,
              nw * ratio,
              nh * ratio,
              0,
              0,
              nw,
              nh
            )
            ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh)
          }
        }
      } else {
        ctx.drawImage(img, 0, 0, width, height)
      }
      // 进行最小压缩
      var ndata = canvas.toDataURL('image/jpeg', 0.5)
      tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0
      return ndata
    },
    getBlob(buffer, format) {
      try {
        return new Blob(buffer, { type: format })
      } catch (e) {
        var bb = new (window.BlobBuilder ||
          window.WebKitBlobBuilder ||
          window.MSBlobBuilder)()
        buffer.forEach(function (buf) {
          bb.append(buf)
        })
        return bb.getBlob(format)
      }
    },
    dataUrlToBlob(dataURI) {
      let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0] // mime类型
      let byteString = atob(dataURI.split(',')[1]) //base64 解码
      let arrayBuffer = new ArrayBuffer(byteString.length) //创建缓冲数组
      let intArray = new Uint8Array(arrayBuffer) //创建视图
      for (let i = 0; i < byteString.length; i++) {
        intArray[i] = byteString.charCodeAt(i)
      }
      return new Blob([intArray], { type: mimeString })
    },
  },
}
</script>

<style scoped>
.archives-viewer {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.archives-viewer .archives-item {
  margin-right: 20px;
  margin-bottom: 20px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}
.archives-viewer .archives-item .archives-item__img-area {
  height: 184px;
  width: 256px;
}
.archives-viewer .archives-item .archives-item__img-area .img {
  display: block;
  width: 100%;
  height: 100%;
}
.archives-viewer .archives-item .archives-item__text-area {
  position: absolute;
  width: 100%;
  text-align: center;
  margin: 0;
  bottom: 0;
  height: 30px;
  line-height: 30px;
  color: white;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.5);
}
.archives-viewer
  .archives-item
  .archives-item__text-area
  .archives-item__title {
  width: 100%;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}
.archives-viewer .archives-item .archives-item__close-area {
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 4px;
  right: 4px;
  width: 22px;
  height: 22px;
  color: white;
  border-radius: 50%;
  display: none;
  line-height: 22px;
}
.archives-viewer .archives-item .archives-item__close-area .el-icon-close {
  font-size: 22px;
}
.archives-viewer .archives-item:hover .archives-item__close-area {
  display: block;
}
</style>
