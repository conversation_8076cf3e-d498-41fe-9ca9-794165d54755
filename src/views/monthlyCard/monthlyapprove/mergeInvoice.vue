<template>
  <el-dialog
    title="开票信息"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    :center="true"
    width="70%"
    custom-class="shipment-dialog"
  >
    <div class="search-form">
      <el-form label-width="120px">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="开始月份" prop="monthStart">
              <el-date-picker
                v-model="monthStart"
                type="month"
                placeholder="开始月份"
                value-format="yyyyMM"
                :picker-options="pickerOptionsYearMonth"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="结束月份" prop="monthEnd">
              <el-date-picker
                v-model="monthEnd"
                type="month"
                value-format="yyyyMM"
                :picker-options="pickerOptionsYearMonth"
                placeholder="结束月份"
                clearable
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="" prop="monthEnd">
              <el-button type="primary" @click="onSearchHandle" size="small"
                >筛选</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="dialog-class" style="padding: 0 15px">
      <el-table
        :data="tableData"
        height="450"
        style="width: 100%; margin-top: 30px"
        :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
        center
      >
        <el-table-column align="center" width="80" label="全选">
          <template slot="header">
            <div>
              全选
              <el-checkbox
                v-model="checkAll"
                @change="handleCheckAll"
              ></el-checkbox>
            </div>
          </template>
          <template slot-scope="scope">
            <el-checkbox
              v-model="scope.row.checked"
              :checked="scope.row.checked"
              @change="handleCheckOne($event, scope.$index)"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="账单月份" align="center" min-width="100">
          <template slot-scope="scope">{{ scope.row.transMonth }}</template>
        </el-table-column>
        <el-table-column label="ETC消费金额(元)" align="center" min-width="160">
          <template slot-scope="scope">{{
            moneyFilter(scope.row.transAmount)
          }}</template>
        </el-table-column>
        <el-table-column label="服务费(元)" align="center" min-width="100">
          <template slot-scope="scope">{{
            moneyFilter(scope.row.serviceAmount)
          }}</template>
        </el-table-column>
        <el-table-column label="滞纳金(元)" align="center" min-width="100">
          <template slot-scope="scope">{{
            moneyFilter(scope.row.forfeitAmount)
          }}</template>
        </el-table-column>
        <el-table-column label="滞纳天数(天)" align="center" min-width="100">
          <template slot-scope="scope">{{ scope.row.forfeitDays }}</template>
        </el-table-column>
        <el-table-column label="账单合计(元)" align="center" min-width="100">
          <template slot-scope="scope">{{
            moneyFilter(scope.row.totalAmount)
          }}</template>
        </el-table-column>
      </el-table>
      <div class="accountPagination g-flex g-flex-justify">
        <div class="count" style="line-height: 32px">
          <span class="text">共{{ totalCount }}个选项，</span>
          <span class="text">合计{{ moneyFilter(totalPrice) }}元</span>
        </div>
      </div>
    </div>
    <div name="footer" slot="footer" class="dialog-footer">
      <el-button type="primary" @click="mergeConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
// import { validPhone } from "@/utils/validate"
import { mapGetters } from 'vuex'
var moment = require('moment')
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerid: {
      type: String,
    },
  },
  computed: {
    ...mapGetters(['mergeDetail']),
  },
  data() {
    return {
      dialogFormVisible: false,
      monthStart: null,
      monthEnd: null,
      pickerOptionsYearMonth: {
        disabledDate(time) {
          let t = new Date().getDate()
          return time.getTime() > Date.now() - 8.64e7 * t
        },
      },
      tableData: [],
      // mergeDetail: {},
      checkAll: false,
      currentPage: 1,
      totalCount: 0,
      totalPrice: 0,
    }
  },
  created() {
    this.$nextTick(() => {
      console.log('创建了吗')
      // console.log('userinfo', this.userinfo)
      this.dialogFormVisible = this.visible
      // this.ruleForm.email = this.userinfo.email
      // this.ruleForm.mobilePhone = this.userinfo.mobilePhone
      // this.ruleForm.custMastId = this.customerid

      this.monthStart = this.mergeDetail.monthStart || null
      this.monthEnd = this.mergeDetail.monthEnd || null
      this.tableData = this.mergeDetail.tableData || []
      console.log('tbale里面的数据是什么', this.tableData)
      this.checkAll = this.mergeDetail.checkAll
      this.totalCount = this.mergeDetail.mergeDetail || 0
      this.totalPrice = this.mergeDetail.totalPrice || 0
    })
  },
  methods: {
    handleCheckOne(val, index) {
      console.info('check one change is ', index, val)
      console.log('单选', this.tableArrList, this.tableData)
      this.$set(this.tableData[index], 'checked', val)

      //更新全选框状态
      this.checkALLFlag()
      // //更新总金额
      this.countTableArrList()
    },
    handleCheckAll(val) {
      // console.info('check all change is ', val)
      for (let i = 0; i < this.tableData.length; i++) {
        // if (this.tableData[i].isOrder == "1") {
        this.$set(this.tableData[i], 'checked', val)
        // }
      }
      //更新全选框状态
      this.checkALLFlag()
      // //更新总金额
      this.countTableArrList()
    },
    checkALLFlag() {
      console.log('表格数据=========>>>>>', this.tableData)
      let filterArr = this.tableData.filter((item) => {
        // return item.isOrder == "1" && !item.checked;
        return !item.checked
      })
      //全选按钮判定
      if (filterArr.length > 0) {
        this.checkAll = false
      } else {
        this.checkAll = true
      }
      console.log('this.checkALLFlag', filterArr.length, this.checkAll)
    },
    countTableArrList() {
      // console.log("初始化数据=========>>>>>", val);
      let totalCount = 0
      let totalPrice = 0
      this.tableData.forEach((item) => {
        if (item.checked) {
          totalCount++
          totalPrice += parseInt(item.totalAmount)
        }
      })
      this.totalCount = totalCount
      this.totalPrice = totalPrice

      console.log('this.countTableArrList', this.totalPrice, this.totalCount)
    },
    mergeConfirm() {
      let orderIdArr = []
      let orderName = ''
      this.tableData.forEach((item, index) => {
        if (item.checked) {
          orderIdArr.push(item.orderId)
          if (!orderName) {
            orderName = item.transMonth
          }
        }
      })
      if (orderIdArr.length == 0) {
        this.$message({
          message: '请先勾选需要开票的信息',
          type: 'warning',
        })
        return
      }

      // if (Object.keys(this.mergeDetail).length == 0) {
      //  let mergeDetail = {
      //   oldOrder: orderIdArr,
      //   orderName: orderName,
      //   totalCount: this.totalCount,
      //   totalPrice: this.totalPrice,
      // }
      let mergeDetail = {
        oldOrder: orderIdArr,
        orderName: orderName,
        totalCount: this.totalCount,
        totalPrice: this.totalPrice,
        tableData: JSON.parse(JSON.stringify(this.tableData)),
        monthStart: this.monthStart,
        monthEnd: this.monthEnd,
        checkAll: this.checkAll,
      }
      // }
      console.log('total', this.totalCount, this.totalPrice, mergeDetail)
      // this.$bus.$emit('getMergeDetail', this.mergeDetail)
      this.$store.dispatch('setMergeDetail', mergeDetail)
      // this.$emit('on-submit', this.mergeDetail)
      this.$emit('update:visible', false)
    },
    // //获取月结账单信息
    getMergeList() {
      let params = {
        invoiceApplyType: '3',
        custMastId: this.customerid,
        bizSource: '9004',
        startDate: '',
        endDate: '',
      }
      //日期格式计算
      params.startDate = this.monthStart + '01'
      let days = '1'
      let monthDays = moment(this.monthEnd, 'yyyyMM').daysInMonth()
      if (monthDays < 10) {
        days = '0' + monthDays
      } else {
        days = monthDays.toString()
      }
      params.endDate = this.monthEnd + days

      console.log(params, '<<---------params')
      this.startLoading()
      request({
        url: api.monthInviceMergeList,
        method: 'post',
        data: params,
      }).then((res) => {
        console.log(res, '<<---------res')
        if (res.code == 200) {
          // this.fileUrl = res.data.url;
          this.tableData = res.data.orderList
          for (let i = 0; i < this.tableData.length; i++) {
            // if (this.tableData[i].isOrder == "1") {
            this.$set(this.tableData[i], 'checked', false)
            // }
          }
          //更新全选框状态
          this.checkALLFlag()
          // //更新总金额
          this.countTableArrList()
          // this.checkAll = false;
          this.endLoading()
        }
      })
    },
    onSearchHandle() {
      //筛选后重置合并信息
      this.$store.dispatch('setMergeDetail', {})
      console.log('this.monthStart', this.monthStart)
      console.log('this.monthEnd', this.monthEnd)
      if (!this.monthStart || !this.monthEnd) {
        this.$message({
          message: '请先筛选月份范围',
          type: 'warning',
        })
        return
      } else {
        let start = moment(this.monthStart)
        let end = moment(this.monthEnd)

        if (!start.isSame(end, 'year')) {
          this.$message({
            message: '只能选择相同的年份进行筛选',
            type: 'warning',
          })
          return
        }
        if (start.isAfter(end)) {
          this.$message({
            message: '结束月份不能小于开始月份',
            type: 'warning',
          })
          return
        }
      }

      this.getMergeList()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = value / 100
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log('进来改变状态了吗', val)
        this.dialogFormVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.form-info {
  padding: 10px 35px;
}
.editBtn {
  width: 100%;
  text-align: center;
  margin-top: 50px;
}
.sip {
  font-weight: 600;
  color: #f56c6c;
}
</style>