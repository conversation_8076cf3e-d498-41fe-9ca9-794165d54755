<template>
  <div class="page-box">
    <dart-search ref="searchForm1"
                 :searchOperation="false"
                 label-position="right"
                 class="search"
                 :formSpan="24"
                 :gutter="20"
                 :rules="rules"
                 :model="search">
      <template slot="search-form">
        <dart-search-item label="账单月份："
                          prop="value1">
          <el-date-picker v-model="search.value1"
                          :clearable="true"
                          type="month"
                          placeholder="选择日期"
                          :picker-options="pickerOptionsYearMonth">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="用户编号："
                          prop="customer_id">
          <el-input v-model="search.customer_id"
                    onkeyup="this.value = this.value.replace(/[^\d.]/g,'');"
                    clearable
                    @input="clearInput"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="用户名称："
                          prop="customer_name">
          <el-select v-model="search.customer_name"
                     filterable
                     clearable
                     @change="customerNameChange"
                     placeholder="请输入或选择用户名">
            <el-option v-for="item in customerBizList"
                       :key="item.customerId"
                       :label="item.customerNameId"
                       :value="item.customerNameId">
            </el-option>
          </el-select>
        </dart-search-item>
        <dart-search-item label="手机号码："
                          onkeyup="this.value = this.value.replace(/[^\d.]/g,'');"
                          prop="phone">
          <el-input v-model="search.phone"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="证件号码："
                          prop="certificates_code">
          <el-input v-model="search.certificates_code"
                    @keydown.native="keydown($event)"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item label="审核状态："
                          prop="check_status">
          <el-select v-model="search.check_status"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in optiondata"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="支付状态："
                          prop="pay_status">
          <el-select v-model="search.pay_status"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in optiondata2"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <dart-search-item label="发票号：" prop="tax_num">
          <el-input
            v-model="search.tax_num"
            clearable
            placeholder=""
          ></el-input>
        </dart-search-item>
        <!-- <dart-search-item label="发送状态："
                          prop="sendStatus">
          <el-select v-model="search.sendStatus"
                     placeholder="请选择"
                     clearable
                     collapse-tags>
            <el-option v-for="item in emailstatus"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item> -->
        <dart-search-item :span="24" style="text-align: right">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            :disabled="nowtime == null"
            v-permisaction="['vipCard:billOp:verifyBill']"
            @click="tobatchaudit(1)"
            >批量核对</el-button
          >
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            :disabled="nowtime == null"
            v-permisaction="['vipCard:billOp:checkBill']"
            @click="tobatchaudit(2)"
            >批量复核</el-button
          >
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            :disabled="nowtime == null"
            v-permisaction="['vipCard:billOp:confirmBill']"
            @click="tobatchaudit(3)"
            >批量审核</el-button
          >
          <el-button type="primary" size="mini" @click="billListExport"
            >账单导出</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table table-box">
      <el-table :data="tableData"
                v-loading="tableloading"
                border
                style="width: 100%"
                height="100%"
                ref="multipleTable"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                @selection-change="handleSelectionChange"
                :row-key="getRowKeys">
        <el-table-column type="selection"
                         width="55"
                         :selectable="selectable"
                         :reserve-selection="true">
        </el-table-column>
        <el-table-column prop="cust_name"
                         align="center"
                         min-width="180"
                         label="客户名称" />
        <el-table-column prop="trans_total"
                         align="center"
                         min-width="110"
                         label="通行交易笔数" />
        <el-table-column prop="trans_amount"
                         align="center"
                         min-width="130"
                         label="通行交易金额(元)">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.trans_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="refund_total"
                         align="center"
                         min-width="110"
                         label="退费交易笔数" />
        <el-table-column prop="refund_amount"
                         align="center"
                         min-width="130"
                         label="退费交易金额(元)">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.refund_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="resettle_total"
                         align="center"
                         label="补交总数" />
        <el-table-column prop="resettle_amount"
                         align="center"
                         min-width="120"
                         label="补交金额(元)">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.resettle_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="service_amount"
                         align="center"
                         min-width="100"
                         label="服务费(元)">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.service_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="forfeit_amount"
                         align="center"
                         min-width="100"
                         label="滞纳金(元)">
          <template slot-scope="scope">
            <div :class="{ tureamount: scope.row.forfeit_amount > 0 }">
              <span v-if="scope.row.forfeit_amount > 0"
                    @click="tobilll(scope.row)">{{ moneyFilter(scope.row.forfeit_amount) }}</span>
              <span v-else>{{ moneyFilter(scope.row.forfeit_amount) }}</span>
              <el-tooltip v-if="scope.row.forfeit_amount > 0"
                          class="item"
                          effect="dark"
                          content="点击金额查看滞纳金月账单"
                          placement="top">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="total_amount"
                         align="center"
                         min-width="130"
                         label="实付金额合计(元)">
          <template slot-scope="scope">
            {{ moneyFilter(scope.row.total_amount) }}
          </template>
        </el-table-column>
        <el-table-column prop="check_status"
                         align="center"
                         label="审核状态">
          <template slot-scope="scope">
            {{ getcheckstatus(scope.row.check_status) }}
          </template>
        </el-table-column>
        <el-table-column prop="trans_month"
                         align="center"
                         label="流水月份" />
        <el-table-column prop="pay_status_str"
                         align="center"
                         label="支付状态">
          <template slot-scope="scope">
            {{ scope.row.pay_status_str }}
          </template>
        </el-table-column>
        <el-table-column prop="pay_time"
                         align="center"
                         min-width="160"
                         label="支付时间" />
        <el-table-column prop="transferDate"
                         align="center"
                         min-width="160"
                         label="转账日期" />
        <el-table-column prop="email_status"
                         align="center"
                         min-width="120"
                         label="发送邮箱状态">
          <template slot-scope="scope">
            {{ getemail(scope.row.email_status) }}
          </template>
        </el-table-column>
        <el-table-column prop="bill_url"
                         align="center"
                         min-width="120"
                         label="账单文件地址">
          <template slot-scope="scope">
            <span @click="downbill(scope.row.bill_url)"
                  class="downbill"
                  v-if="scope.row.bill_url">下载</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="tax_status"
                         align="center"
                         label="开票状态">
          <template slot-scope="scope">
            {{ gettax(scope.row.tax_status) }}
          </template>
        </el-table-column>
        <el-table-column prop="tax_type"
                         align="center"
                         label="发票类型">
          <template slot-scope="scope">
            <span v-if="scope.row.tax_status != 0">{{
              scope.row.tax_type == 0 ? '蓝票' : '红票'
            }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop=""
                         fixed="right"
                         header-align="center"
                         align="left"
                         min-width="370"
                         label="操作">
          <template slot-scope="scope">
            <el-button style="width:80px"
                       size="mini"
                       v-if="scope.row.pay_status== 2&&scope.row.isHisTax== 1"
                       v-permisaction="['invoice:applyInvoice']"
                       @click="onInvoiceHandle(scope.row)">发票</el-button>
            <el-button style="width:80px"
                       size="mini"
                       @click="tobill(scope.row, true)">详情</el-button>

            <span style="margin-left: 10px"
                  v-if="scope.row.check_status == 0">
              <el-button type="primary"
                         size="mini"
                         style="width: 80px"
                         native-type="submit"
                         v-permisaction="['vipCard:billOp:verifyBill']"
                         @click="tobill(scope.row, false)">核对</el-button>
            </span>
            <span style="margin-left: 10px"
                  v-if="scope.row.check_status == 1">
              <el-button type="primary"
                         size="mini"
                         style="width: 80px"
                         native-type="submit"
                         v-permisaction="['vipCard:billOp:checkBill']"
                         @click="tobill(scope.row, false)">复核</el-button>
            </span>
            <span style="margin-left: 10px"
                  v-if="scope.row.check_status == 2">
              <el-button type="primary"
                         size="mini"
                         style="width: 80px"
                         native-type="submit"
                         v-permisaction="['vipCard:billOp:confirmBill']"
                         @click="tobill(scope.row, false)">审核</el-button>
            </span>
            <span style="margin-left: 10px"
                  v-if="scope.row.check_status == 3 && scope.row.pay_status != 2 && scope.row.total_amount >= 0">
              <el-button type="primary"
                         size="mini"
                         style="width: 80px"
                         native-type="submit"
                         @click="onRepaymentHandle(scope.row)">还款</el-button>
            </span>
            <!-- <el-button v-if="scope.row.check_status == 3 && scope.row.pay_status != 2"
                       type="primary"
                       size="mini"
                       style="width:90px"
                       native-type="submit"
                       @click="onRepaymentDelHandle(scope.row)">滞纳金调差</el-button> -->
            <!-- 这么做的原因是因为 v-permisaction和v-if一起可能会冲突 -->
            <span
              style="margin-left: 10px"
              v-if="
                scope.row.isHisTax != '1' &&
                scope.row.check_status == '3' &&
                ((scope.row.tax_type == '3' && scope.row.tax_status == '2') ||
                  scope.row.tax_status == '0' ||
                  (scope.row.tax_type != '3' && scope.row.tax_status == '3') ||
                  (scope.row.tax_status == '2' && scope.row.tax_type == '1'))
              "
            >
              <el-button
                type="primary"
                size="mini"
                style="width: 80px"
                native-type="submit"
                @click="OrderInvoiceHandle(scope.row)"
                v-permisaction="['invoice:applyInvoice']"
                >开发票</el-button
              >
            </span>
            <span style="margin-left: 10px">
              <el-button
                v-if="
                  scope.row.isHisTax != '1' &&
                  scope.row.tax_status != '0' &&
                  scope.row.tax_type != '2' &&
                  scope.row.tax_type != '3'
                "
                size="mini"
                type="primary"
                @click="queryProgressInvoice(scope.row)"
              >
                查看发票</el-button
              >
            </span>
            <span style="margin-left: 10px">
              <el-button
                v-if="
                  scope.row.isHisTax != '1' &&
                  scope.row.tax_status == 2 &&
                  scope.row.tax_type == 0
                "
                :disabled="scope.row.channel == '1'"
                size="mini"
                type="danger"
                @click="OrderinvoiceRedHandle(scope.row)"
              >
                发票红冲</el-button
              >
              <el-button
                v-if="
                  scope.row.isHisTax != '1' &&
                  scope.row.tax_status == 2 &&
                  scope.row.tax_type == 4 &&
                  scope.row.invoiceApplyType == '5'
                "
                size="mini"
                type="danger"
                @click="OrderinvoiceRedApplyHandle(scope.row)"
              >
                红冲申请</el-button
              >
            </span>
            <el-dropdown>
              <el-button type="text"
                         style="margin-left: 10px">更多操作</el-button>
              <el-dropdown-menu trigger="click"
                                slot="dropdown"
                                style="padding: 5px 20px">
                <div class="g-flex">
                  <el-button v-if='scope.row.check_status=="3"&&scope.row.bill_url'
                             type="text"
                             v-permisaction="['vipCard:resend']"
                             @click="resendEmail(scope.row)">
                    重发邮件</el-button>
                  <el-button type="text"
                             @click="onViewArchives(scope.row)">
                    查看凭证</el-button>
                  <el-button type="text"
                             @click="toreport(scope.row)">查看报表</el-button>
                </div>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-end">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.page_index"
                     :page-size="search.page_size"
                     :page-sizes="[10, 20, 30, 50]"
                     layout="total,sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <repayment
      v-if="repaymentDialog"
      :visible.sync="repaymentDialog"
      :monthlyInfo="currentRow"
      @getlist="getlist"
    ></repayment>
    <detail
      v-if="detailDialog"
      :visible.sync="detailDialog"
      :detail="billdetail"
      @getlist="getlist"
      :readtype="toread"
    ></detail>
    <invoice
      v-if="openinvoice"
      :visible.sync="openinvoice"
      :customerid="invoiceid"
      :orderId="inorderid"
      :userinfo="usernav"
      @getlist="getlist"
      @showMerge="showMerge"
    ></invoice>
    <redFlush
      v-if="redFlushDialog"
      :orderId="flushid"
      :visible.sync="redFlushDialog"
      :customerid="invoiceid"
      @getlist="getlist"
    ></redFlush>
    <redFlushApply
      v-if="redFlushApplyDialog"
      :orderId="flushid"
      :orderList="orderList"
      :visible.sync="redFlushApplyDialog"
      :customerid="invoiceid"
      @getlist="getlist"
    ></redFlushApply>
    <billForfeit
      v-if="billDialog"
      :visible.sync="billDialog"
      :custid="billdetail.customer_id"
      :transmonth="billdetail.trans_month"
    ></billForfeit>
    <refundDtffer
      :visible.sync="refundDtfferDialog"
      :billDetail="billData"
      @getList="getlist"
    ></refundDtffer>
    <!-- 合并开票 -->
    <mergeInvoice
      v-if="showMergeDialog"
      :visible.sync="showMergeDialog"
      :customerid="invoiceid"
    ></mergeInvoice>
    <el-dialog
      title="查看发票"
      :append-to-body="true"
      style="width: 100%; margin: 0px auto"
      class="my_main_dialog"
      custom-class="specia-dialog"
      :visible.sync="isShowQrCode"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="qrBox g-flex g-flex-center" ref="qrBox">
        <div class="box-wrapper">
          <div>
            <p>温馨提示：</p>
            <p>1.请您扫描下方二维码，保存您的增值税电子普通发票。</p>
            <p>2.二维码是获取发票的凭证之一，请妥善保管。</p>
          </div>
          <div id="qrCode" class="g-flex g-flex-center" ref="qrCodeBox"></div>
        </div>
        <div class="link-wrapper" style="width: 50%; padding: 0 30px">
          <p>发票链接:</p>
          <a
            style="
              text-decoration-line: underline;
              color: #92abd6;
              cursor: pointer;
            "
            target="_blank"
            :href="url"
          >
            {{ url }}
          </a>
        </div>
      </div>
      <div style="width: 100%; margin: 30px 0px 10px"
           class="g-flex g-flex-center">
        <el-button style="width: 120px"
                   @click.stop="printQr">
          打印二维码
        </el-button>
      </div>
    </el-dialog>
    <!-- 查看凭证 -->
    <certificate v-if="isEvidenceArchives"
                 :visible.sync="isEvidenceArchives"
                 :source="pictureSource"></certificate>
    <!-- <el-dialog
      title="查看凭证"
      :append-to-body="true"
      style="margin: 0px auto;"
      width="340px"
      class="my_main_dialog"
      custom-class="specia-dialog"
      :visible.sync="isEvidenceArchives"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <img
        :src="evidenceArchives"
        alt=""
        style="width:300px;height:300px;display: block;"
      />
      <div
        style="width:100%;margin: 30px 0px 10px"
        class="g-flex g-flex-center "
      >
        <el-button style="width: 120px;" @click.stop="onEvidenceArchivesHandle">
          下载凭证
        </el-button>
      </div>

    </el-dialog>
    <-- 发票页面 -->
    <dartSlide :visible.sync='invoiceVisible'
               title='发票详情'  v-transfer-dom
               :maskClosable='true'>
      <invoiceList v-if="invoiceVisible"
                   :currentRow='currentRow'></invoiceList>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import repayment from './repayment.vue'
import detail from './detail.vue'
import invoice from './invoice.vue'
import redFlush from './redFlush.vue'
import redFlushApply from './redFlushApply.vue'
import { decode } from 'js-base64'
import QRCode from 'qrcodejs2'
import billForfeit from './billForfeit.vue'
import refundDtffer from './refundDtffer.vue'
import certificate from './certificate'
import mergeInvoice from './mergeInvoice'
import {
  yyxAuditStsatus,
  yyxPayStsatus,
  yyxBillStsatus,
  yyxSendStsatus,
} from '@/common/const/optionsData.js'
var moment = require('moment')
import dartSlide from '@/components/dart/Slide/index'
import invoiceList from './invoice/list.vue'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    billForfeit,
    repayment,
    detail,
    invoice,
    redFlush,
    refundDtffer,
    certificate,
    mergeInvoice,
    redFlushApply,
    dartSlide,
    invoiceList,
  },
  provide() {
    return {
      mergeDetail: {}, //合并开票信息
    }
  },
  data() {
    return {
      showMergeDialog: false, //合并开票弹窗
      invoiceVisible: false, // 发票详情
      currentRow: null, // 月结账单详情
      repaymentDialog: false, // 还款弹窗
      detailDialog: false, //详情处理弹窗
      isShowQrCode: false, //发票弹窗
      toread: false, //查看详情
      billDialog: false, //滞纳金弹窗
      billdetail: {},
      billtype: null,
      tableloading: false,
      openinvoice: false,
      redFlushDialog: false,
      redFlushApplyDialog: false,
      refundDtfferDialog: false,
      invoiceid: '',
      inorderid: '',
      usernav: {},
      flushid: '',
      billdetail: {
        customer_id: '',
        trans_month: '',
      },
      search: {
        value1: null,
        customer_id: '',
        customer_name: '',
        phone: '',
        certificates_code: '',
        check_status: '', //审核状态
        pay_status: '', //支付状态
        trans_month: '',
        tax_num: '', //发票号
        page_index: 1,
        page_size: 10,
        sendStatus: '', //发送状态
      },
      tableData: [],
      rules: {
        value1: [],
      },
      nowtime: null,
      customerarr: [],
      total: 0,
      optiondata: yyxAuditStsatus,
      optiondata2: yyxPayStsatus,
      taxoption: yyxBillStsatus,
      emailstatus: yyxSendStsatus,
      pickerOptionsYearMonth: {
        disabledDate(time) {
          let t = new Date().getDate()
          return time.getTime() > Date.now() - 8.64e7 * t
        },
      },
      billData: {},
      customerBizList: [],
      evidenceArchives: '',
      isEvidenceArchives: false,
      pictureSource: [],
      url: '',
      orderList: {}, //专票红冲审核数据
    }
  },
  created() {
    this.search.value1 = this.getstarttime()
    this.getlist()
    this.getCustomerBizList()
  },
  mounted() {},
  methods: {
    showMerge(flag) {
      this.showMergeDialog = flag
    },
    // 打开发票详情页面
    onInvoiceHandle(row) {
      this.currentRow = row
      this.invoiceVisible = true
    },
    onEvidenceArchivesHandle() {
      window.location.href = this.evidenceArchives
      this.isEvidenceArchives = false
    },
    // 查看转账凭证
    onViewArchives(row) {
      console.log(row)
      this.evidenceArchives = ''
      let params = {
        customer_id: row.customer_id,
        scene: 16,
        photo_code: 43,
        other_code: row.order_id,
        status: 0,
      }
      request({
        url: api.getDetailImgs,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data && res.data.length) {
            this.pictureSource = res.data
            let file_url = res.data[0].file_url
            this.evidenceArchives = file_url + '?download=1'
            this.isEvidenceArchives = true
          } else {
            this.$alert('未查询到转账凭证', '提示', {
              confirmButtonText: '确定',
            })
          }
        } else {
          this.$alert(res.msg, '提示', {
            confirmButtonText: '确定',
          })
        }
      })
    },
    //还款
    onRepaymentHandle(row) {
      this.currentRow = row
      //   this.currentRow.trans_month = this.nowtime
      this.repaymentDialog = true
    },
    onSearchHandle() {
      if (
        this.search.value1 != null ||
        this.search.customer_id != '' ||
        this.search.customer_name != '' ||
        this.search.phone != '' ||
        this.search.certificates_code != ''
      ) {
        this.search.page_index = 1
        this.getlist()
      } else {
        this.$alert('搜索条件错误！', '提示', {
          confirmButtonText: '确定',
        })
        return
      }
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.page_index = 1
      this.getlist()
    },
    tobilll(item) {
      this.billdetail.customer_id = item.customer_id
      this.billdetail.trans_month = item.trans_month
      this.billDialog = true
    },
    getlist() {
      let data = {
        customer_id: this.search.customer_id ? this.search.customer_id : null,
        customer_name: this.search.customer_name
          ? this.search.customer_name
          : null,
        phone: this.search.phone ? this.search.phone : null,
        certificates_code: this.search.certificates_code
          ? this.search.certificates_code
          : null,
        check_status: this.search.check_status
          ? this.search.check_status
          : null,
        pay_status: this.search.pay_status ? this.search.pay_status : null,
        trans_month: this.search.value1
          ? this.gettime(this.search.value1)
          : null,
        // trans_month:'2021-10-24',
        tax_num: this.search.tax_num,
        page_index: this.search.page_index,
        page_size: this.search.page_size,
        sendStatus: this.search.sendStatus,
      }
      console.log(data, '000000')
      this.customerBizList.forEach((item) => {
        if (data.customer_id == item.customerId) {
          data.customer_name = item.customerName
        }
      })
      request({
        url: api.billList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.$refs.multipleTable.clearSelection()
          this.tableData = res.data.records
          this.total = res.data.total
          if (this.search.value1) {
            this.nowtime = this.gettime(this.search.value1)
          } else {
            this.nowtime = null
          }
        })
        .catch(() => {})
    },
    //开发票
    OrderInvoiceHandle(item) {
      console.log('item', item)
      this.getuserinfo(item)

      this.invoiceid = item.customer_id
      this.inorderid = item.order_id
    },
    //获取开票用户信息
    getuserinfo(item) {
      let data = {
        customer_id: item.customer_id,
      }
      request({
        url: api.customerBizList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          console.log('resuser', res)
          this.usernav = {
            email: res.data[0].email ? res.data[0].email : '',
            mobilePhone: res.data[0].link_mobile ? res.data[0].link_mobile : '',
            ...item,
          }
          this.openinvoice = true
        })
        .catch(() => {})
    },
    //查看发票
    queryProgressInvoice(row) {
      let params = {
        invoiceApplyType: '3',
        custMastId: row.customer_id,
        bizSource: '9004',
        bizOrderId: row.order_id,
      }
      // let data = {
      //   order_id: row.order_id,
      //   invoice_type: 'MS',
      // }
      this.startLoading()
      request({
        url: api.monthInviceQuery,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          console.log('res', res)
          if (res.code == 200) {
            this.url = res.data.url
            this.showQrcode(res.data.url)
          }
        })
        .catch(() => {})
    },
    //发票红冲
    OrderinvoiceRedHandle(row) {
      this.redFlushDialog = true
      this.invoiceid = row.customer_id
      this.flushid = row.order_id
    },
    //发票专票红冲申请
    OrderinvoiceRedApplyHandle(row) {
      console.log('row', row)
      this.redFlushApplyDialog = true
      this.orderList = row
      this.invoiceid = row.customer_id
      this.flushid = row.order_id
    },
    getcheckstatus(val) {
      let arr = this.optiondata
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    getpaystatus(val) {
      let arr = this.optiondata2
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    gettax(val) {
      let arr = this.taxoption
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    getemail(val) {
      let arr = this.emailstatus
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    downbill(url) {
      window.open(url)
    },
    handleSizeChange(e) {
      this.search.page_size = e
      this.search.page_index = 1
      this.getlist()
    },
    changePage(e) {
      this.search.page_index = e
      this.getlist()
    },
    getRowKeys(row) {
      if (this.nowtime) {
        return row.customer_id //指定row-key的一个标识
      } else {
        return row.order_id
      }
    },
    selectable() {
      if (this.nowtime) {
        return true
      } else {
        return false
      }
    },
    tobill(item, type) {
      this.toread = type
      console.log(this.toread)
      this.billdetail = item
      this.detailDialog = true
    },
    // 月月行账单导出
    billListExport() {
      if (!(this.tableData && this.tableData.length)) {
        this.$alert('列表暂无数据可以导出', '提示', {
          confirmButtonText: '确定',
        })
        return
      }
      let data = {
        customer_id: this.search.customer_id ? this.search.customer_id : null,
        customer_name: this.search.customer_name
          ? this.search.customer_name
          : null,
        phone: this.search.phone ? this.search.phone : null,
        certificates_code: this.search.certificates_code
          ? this.search.certificates_code
          : null,
        check_status: this.search.check_status
          ? this.search.check_status
          : null,
        pay_status: this.search.pay_status ? this.search.pay_status : null,
        trans_month: this.search.value1
          ? this.gettime(this.search.value1)
          : null,
        page_index: this.search.page_index,
        page_size: this.search.page_size,
      }
      request({
        url: api.billListExport,
        method: 'post',
        data: data,
        responseType: 'arraybuffer',
        headers: {
          ResType: 'download',
        },
      })
        .then((response) => {
          var blob = new Blob([response], { type: 'application/vnd.ms-excel' })
          var objectUrl = URL.createObjectURL(blob)
          var a = document.createElement('a')
          document.body.appendChild(a)
          a.style = 'display: none'
          a.href = objectUrl
          a.download = '月月行账单.xlsx'
          a.click()
          document.body.removeChild(a)
        })
        .catch((err) => {})
    },
    toreport(item) {
      let data = {
        name: 'newMonthBill',
        custMastId: item.customer_id,
        billMonth: item.trans_month,
        transStart: moment(item.trans_month)
          .startOf('month')
          .format('YYYY-MM-DD'),
        transEnd: moment(item.trans_month).endOf('month').format('YYYY-MM-DD'),
      }
      request({
        url: api.refundReport,
        method: 'post',
        data: data,
      })
        .then((res) => {
          let url = res.data
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
        .catch(() => {})
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = value / 100
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
    tobatchaudit(type) {
      let arr = this.customerarr
      if (arr.length == 0) {
        this.$message.error('请选择账单')
        return
      }
      let brr = arr.filter((item) => item.check_status == type - 1)
      brr = brr.map((item) => {
        return { customer_id: item.customer_id }
      })
      if (brr.length == 0) {
        this.$message.error('请选择审核状态正确的账单')
        return
      }
      let data = {
        trans_month: this.nowtime,
        check_status: type,
        customs: brr,
      }
      request({
        url: api.verifyBill,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.$message({
            message: '核对成功',
            type: 'success',
          })
          this.$refs.multipleTable.clearSelection()
          this.getlist()
        })
        .catch(() => {})
    },
    showQrcode(url) {
      this.isShowQrCode = true
      this.$nextTick(() => {
        document.getElementById('qrCode').innerHTML = ''
        // const h = this.$createElement;
        let qrBox = this.$refs.qrCodeBox
        new QRCode(qrBox, {
          text: url,
          width: 150,
          height: 150,
          colorDark: '#333333', //二维码颜色
          colorLight: '#ffffff', //二维码背景色
          correctLevel: QRCode.CorrectLevel.L, //容错率，L/M/H
        })
      })
    },
    handleSelectionChange(val) {
      this.customerarr = val
    },
    gettime(data) {
      const value = moment(data).startOf('month').format('YYYY-MM-DD')
      return value
    },
    getstarttime() {
      const value = moment(new Date())
        .subtract(1, 'months')
        .startOf('month')
        .valueOf()
      return value
    },
    // checkTime(i) {
    //   if (i < 10) {
    //     i = '0' + i
    //   }
    //   return i
    // },
    printQr() {
      this.$print(this.$refs.qrBox)
    },
    keydown(e) {
      if (e.keyCode == 32) {
        e.returnValue = false
      }
    },
    //还款调差
    onRepaymentDelHandle(val) {
      this.billData = val
      this.refundDtfferDialog = true
    },
    //2022.6.7改动 要求选择或模糊查询用户名
    getCustomerBizList() {
      let params = {}
      this.loading = true
      request({
        url: api.getVipCustomerList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            let params = JSON.parse(JSON.stringify(res.data)).map((item) => {
              return {
                customerNameId: '(' + item.customerId + ')' + item.customerName,
                customerName: item.customerName,
                customerId: item.customerId,
              }
            })
            this.customerBizList = params
          }
        })
        .catch(() => {
          this.loading = false
        })
    },

    customerNameChange(val) {
      console.log(val, '0123123')
      if (!val) {
        this.search.customer_id = ''
        return
      }
      this.customerBizList.forEach((item) => {
        if (item.customerNameId == val) {
          console.log(item.customerId)
          this.search.customer_id = item.customerId
        }
      })
    },
    clearInput(val) {
      this.search.customer_name = ''
    },
    //2022.7.19 重发邮件
    resendEmail(val) {
      let data = []
      data.push(val.order_id)
      let params = {
        sids: data,
      }
      this.startLoading()
      request({
        url: api.vipCarResend,
        method: 'post',
        data: params,
      }).then((res) => {
        this.endLoading()
        if (res.code == 200) {
          this.$message({
            message: '邮件重发成功，请注意查收！',
            type: 'success',
          })
        } else {
          this.endLoading()
          this.$message.error(res.msg)
        }
        console.log(res)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.page-box {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.page-box .search {
  margin-top: 20px;
}
.page-box .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.page-box .pagination {
  padding: 0px 20px 10px 20px;
  background-color: #fff;
}
.downbill {
  color: #409eff;
  cursor: pointer;
}
.tureamount {
  color: #409eff;
  cursor: pointer;
}
</style>
