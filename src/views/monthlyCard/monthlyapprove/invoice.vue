<template>
  <el-dialog
    title="开票信息"
    :visible.sync="dialogFormVisible"
    :close-on-click-modal="false"
    :center="true"
    width="70%"
    custom-class="shipment-dialog"
  >
    <div class="form-info">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        size="medium"
        class="dt-form dt-form--max"
      >
        <el-row :span="20" style="margin-bottom: 20px">
          <el-col :span="20" :offset="1" class="item">
            <div class="item-container">
              <span class="item-label" style="font-weight: bold"
                >发票种类选择:</span
              >
              <div class="item-content" style="margin-top: 10px">
                <el-radio-group v-model="radio">
                  <el-radio :label="'0'" style="line-height: 22px"
                    >增值税普通发票</el-radio
                  >
                  <!-- <el-radio :label="'4'">增值税专用发票</el-radio> -->
                </el-radio-group>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :span="20" style="margin-bottom: 20px">
          <el-col :span="20" :offset="1" class="item">
            <div class="item-container">
              <span class="item-label" style="font-weight: bold"
                >选择开具项目:</span
              >
              <div class="item-content" style="margin-top: 10px">
                <el-radio-group v-model="radio1">
                  <el-radio :label="'0'" style="line-height: 22px"
                    >合并开具(服务费及滞纳金)</el-radio
                  >
                  <!-- <el-radio :label="'2'">增值税专用发票</el-radio> -->
                  <!-- <el-radio :label="'4'">增值税专用发票(电子)</el-radio> -->
                </el-radio-group>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="20">
            <el-form-item label="已选择月份账单:">
              <span
                v-if="
                  Object.keys(mergeDetail).length > 0 &&
                  mergeDetail.totalCount > 0
                "
              >
                {{ mergeDetail.orderName }}等，共{{
                  mergeDetail.totalCount
                }}个月结账单，合计{{ moneyFilter(mergeDetail.totalPrice) }}元
                <span @click="showMerge(true)" style="color: blue">展开</span>
              </span>
              <span v-else class="rg">{{ userinfo.trans_month }}</span>
              <el-button
                style="margin-left: 20px"
                type="primary"
                @click="showMerge(true)"
                >选择其他月份账单合并开票</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="购方名称:" prop="buyerName">
              <el-select
                filterable
                remote
                reserve-keyword
                :remote-method="initCompanyList"
                placeholder="输入关键词搜索"
                :value-key="'nsrsbh'"
                :value="companyItem.qymc"
                @change="companyChange"
                @visible-change="visibleChange"
              >
                <el-option
                  class="select-wrapper"
                  v-for="item in companyList"
                  :key="item.nsrsbh"
                  :label="item.qymc"
                  :value="item"
                >
                  <div class="select-item">
                    <div>{{ item.qymc }}</div>
                    <div>税号:{{ item.nsrsbh }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="购方纳税人识别号:" prop="buyerTaxpayerNum">
              <el-input v-model="ruleForm.buyerTaxpayerNum"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="购方地址:" prop="buyerAddress">
              <el-input v-model="ruleForm.buyerAddress"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="购方电话:" prop="buyerPhone">
              <el-input v-model="ruleForm.buyerPhone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="	购方开户行名称:" prop="buyerBankName">
              <el-input v-model="ruleForm.buyerBankName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="	购方开户行账号:" prop="buyerBankAccount">
              <el-input v-model="ruleForm.buyerBankAccount"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="	邮箱:" prop="email">
              <el-input v-model="ruleForm.email"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="	手机号:" prop="mobilePhone">
              <el-input v-model="ruleForm.mobilePhone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="20">
            <el-form-item label="	备注:" prop="remark">
              <el-input type="textarea" v-model="ruleForm.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="20">
            <el-form-item label="">
              <span class="sip">开票购方名称和邮箱必填</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="editBtn">
        <el-button type="primary" style="width: 150px" @click="open"
          >开发票</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
// import { validPhone } from "@/utils/validate"
import { mapGetters } from 'vuex'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    customerid: {
      type: String,
    },
    orderId: {
      type: Number,
    },
    userinfo: {
      type: Object,
      default: {
        email: '',
        mobile_phone: '',
      },
    },
  },
  computed: {
    ...mapGetters(['mergeDetail']),
  },
  data() {
    // const validatephone = (rule, value, callback) => {
    //   if (!validPhone(value)) {
    //     callback(new Error("请输入正确的手机号码"))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      dialogFormVisible: false,
      radio: '0',
      radio1: '0',
      ruleForm: {
        custMastId: '',
        buyerName: '', //购方名称
        buyerTaxpayerNum: '', //纳税号
        buyerAddress: '', //购方地址
        buyerPhone: '', //购方手机号
        buyerBankName: '', //购方开户行名称
        buyerBankAccount: '', //购方开户行账号
        email: '',
        mobilePhone: '',
        invoiceApplyType: '3', // 月结账单
        invoiceTaxType: '0',
        remark: '',
      },
      companyList: [], //购方list
      companyItem: {}, //购方选择信息
      query: '',
      // mergeDetail: {}, //合并的最新信息
      rules: {
        buyerName: [
          { required: true, message: '请输入购方名称', trigger: 'blur' },
        ],
        email: [
          {
            required: true,
            message: '请输入正确邮箱',
            pattern:
              '^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$',
            trigger: 'blur',
          },
        ],
        mobilePhone: [
          {
            message: '请输入正确手机号',
            trigger: 'blur',
            pattern: '^1[0-9]{10}$',
          },
        ],
      },
    }
  },
  created() {
    this.$nextTick(() => {
      console.log('userinfo', this.userinfo)
      this.dialogFormVisible = this.visible
      this.ruleForm.email = this.userinfo.email
      this.ruleForm.mobilePhone = this.userinfo.mobilePhone
      this.ruleForm.custMastId = this.customerid
    })
  },
  mounted() {
    // this.$bus.$on('getMergeDetail', (val) => {
    //   console.log('监听数据===>>>', val)
    //   this.showMerge(false)
    //   this.mergeDetail = val
    // })
  },
  methods: {
    showMerge(flag) {
      this.$emit('showMerge', flag)
    },
    initCompanyList(query) {
      this.query = query
      this.startLoading()
      let params = {
        key: query,
      }
      request({
        url: api.getInvoiceCompanyInfo,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            console.log(res, '模糊查询结果')
            // this.$emit("on-submit");
            this.companyList = res.data
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    visibleChange(flag) {
      console.log(
        'flag',
        flag,
        Object.keys(this.companyItem).length,
        this.query
      )
      if (!flag) {
        this.$set(this.companyItem, 'qymc', this.query)
        this.ruleForm.buyerName = this.query
      }
      console.log('this.companyItem', this.companyItem, this.ruleForm.buyerName)
    },
    companyChange(item) {
      this.companyItem = JSON.parse(JSON.stringify(item))
      this.query = item.qymc

      this.ruleForm.buyerName = item.qymc
      this.ruleForm.buyerTaxpayerNum = item.nsrsbh
      this.ruleForm.buyerAddress = item.dz
      this.ruleForm.buyerPhone = item.dh
      this.ruleForm.buyerBankName = item.bankAccount
      this.ruleForm.buyerBankAccount = item.bankNo
    },
    open() {
      // if (this.ruleForm.email == '' && this.ruleForm.mobile_phone == "") {
      //   this.$message({
      //     message: '请填写邮箱或手机号',
      //     type: 'warning'
      //   });
      //   return
      // }
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.invoice()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    replace() {
      this.$refs['ruleForm'].resetFields()
    },
    invoice() {
      this.startLoading()
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      let orderIds = []
      if (Object.keys(this.mergeDetail).length > 0) {
        //把原订单id加上
        // this.mergeDetail.oldOrder.push(this.monthSettleData.order_id);
        //改造数据
        this.mergeDetail.oldOrder.forEach((item) => {
          let obj = {}
          obj.orderId = item
          obj.bizSource = '9004'
          orderIds.push(obj)
        })
        // let uniqueArr = Array.from(new Set(orderIds));
        // orderIds = uniqueArr;
      } else {
        //单个开票
        orderIds = [{ bizSource: '9004', orderId: this.orderId }]
      }

      params.custMastId = this.customerid
      params.orderIds = orderIds
      params.invoiceTaxType = this.radio //新增开专票

      request({
        url: api.monthOpenInvoice,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message({
              message: '开票成功',
              type: 'success',
            })
            this.$emit('getlist')
            this.dialogFormVisible = false
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = value / 100
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogFormVisible(val) {
      if (!val) {
        this.$store.dispatch('setMergeDetail', {})
      }
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.form-info {
  padding: 10px 35px;
}
.editBtn {
  width: 100%;
  text-align: center;
  margin-top: 50px;
}
.sip {
  font-weight: 600;
  color: #f56c6c;
}
</style>