<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:2022.7.8紧急需求，还款界面加上转账凭证调差功能
  * @author:zhangys&zcq
  * @date:2022/07/08 08:14:37
!-->
<template>
  <div>
    <el-dialog
      title="确认还款"
      :close-on-click-modal="false"
      :visible.sync="dialogVisible"
      width="50%"
      center
    >
      <div>
        <el-form
          label-width="130px"
          ref="transfer"
          :model="adjustmentData"
          :rules="rules"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="流水月份：" class="view-item">
                <span>{{ formData.trans_month }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="用户ID：" class="view-item">
                <span>{{ formData.customer_id }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item
            label="转账回执单凭据:"
            class="view-item"
            prop="photographData"
          >
            <archivesBox
              uploadType="UPLAOD"
              :pictureSource="pictureSource"
              :imgAreaStyle="archivesStyle"
              :customerId="monthlyInfo.customer_id"
              :other_code="monthlyInfo.order_id + ''"
              @on-upload="onUploadHandle"
              @on-delete="onDeleteHandle"
              scene="16"
            ></archivesBox>
          </el-form-item>
          <el-form-item label="回执单转账日期:" prop="transferDate">
            <el-date-picker
              v-model="adjustmentData.transferDate"
              type="date"
              style="width: 70%"
              clearable
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="repayment">
        <el-form
          ref="payForm"
          :model="formData"
          :rules="balancePayRules"
          label-width="130px"
        >
          <el-form-item label="单位用户余额：" class="view-item">
            <span
              v-if="accountData.availableAmount"
              style="font-size: 28px; color: #3e71f7; font-weight: 500"
              >{{ payMoneyFilter(accountData.availableAmount) }}元</span
            >
            <span
              v-else
              style="font-size: 28px; color: #3e71f7; font-weight: 500"
              >0元</span
            >
          </el-form-item>

          <el-form-item label="还款金额：" class="view-item">
            <span
              v-if="monthlyInfo && monthlyInfo.total_amount"
              style="font-size: 28px; color: #e6a23c; font-weight: 500"
              >{{ payMoneyFilter(monthlyInfo.total_amount) }}元</span
            >
            <span
              v-else
              style="font-size: 28px; color: #3e71f7; font-weight: 500"
              >0元</span
            >
          </el-form-item>
          <el-form-item label="支付方式：">
            <el-select v-model="formData.pay_type" placeholder="请选择支付方式">
              <el-option
                v-for="item in payTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="图形验证码：" prop="captchaCode">
            <div class="g-flex g-flex-align-center">
              <el-input
                v-model="formData.captchaCode"
                placeholder="请输入图形验证码"
                style="width: 60%"
              ></el-input>
              <img :src="captchaUrl" class="captcha" @click="getCaptcha" />
            </div>
          </el-form-item>
          <el-form-item label="短信验证码：" prop="mobile_code">
            <div class="g-flex">
              <el-input
                v-model="formData.mobile_code"
                placeholder="请输入短信验证码"
                style="width: 60%"
              ></el-input>
              <div @click="sendSmsHandle" class="sendSMS">{{ smsName }}</div>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="onSubmitHandle" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import archivesBox from './photograph.vue'
import electronicArchives from '@/components/photoGraph/photograph.vue'

var moment = require('moment')

export default {
  props: {
    monthlyInfo: [Object],
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      archivesStyle: { height: '125px', width: '125px' },
      payTypeOptions: [
        {
          value: '********',
          label: '余额支付',
        },
      ],
      dialogVisible: false,
      time: null,
      smsName: '发送验证码',
      captchaUrl: '', //图形验证码地址
      accountData: {
        availableAmount: 0,
      }, // 互联网账户信息
      formData: {
        captchaCode: '', // 图形验证码code
        captchaId: '', // 图形验证码ID
        pay_type: '********',
        trans_month: '',
        customer_id: '', // ETC用户ID
        mobile: '', // 手机号码
        mobile_code: '', //短信验证码
        money: '', // 还款金额
        user_no: '', // 互联网账户
      },
      balancePayRules: {
        captchaCode: [
          { required: true, message: '请输入图形验证码', trigger: 'blur' },
        ],
        mobile_code: [
          { required: true, message: '请输入短信验证码', trigger: 'blur' },
        ],
      },
      rules: {
        transferDate: [
          { required: true, message: '请选择日期', trigger: 'change' },
        ],
      },

      pictureSource: [
        {
          lable: '转账回执单凭据',
          photo_code: '43',
          file_url: '',
          file_serial: '',
          isShow: true,
        },
      ],
      defaultPicture: {
        lable: '转账回执单凭据',
        photo_code: '43',
        file_url: '',
        file_serial: '',
        isShow: true,
      },
      //转账凭证调差
      adjustmentData: {
        transferDate: '',
        forfeitState: '0',
        sid: '', //账单ID
      },
      //转账凭证
      credentialsFlag: '',
      photographData: '',
    }
  },

  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: { archivesBox, electronicArchives },

  computed: {},
  created() {
    this.getCaptcha()
    this.getAccountView()
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
    this.formData.customer_id = this.monthlyInfo.customer_id
    this.formData.trans_month = this.monthlyInfo.trans_month
    this.formData.money = this.monthlyInfo.total_amount
  },
  methods: {
    onUploadHandle(result) {
      if (result.data) {
        for (let i = 0; i < this.pictureSource.length; i++) {
          if (
            !this.pictureSource[i].file_serial &&
            this.pictureSource[i].photo_code == result.data.photo_code
          ) {
            Object.assign(this.pictureSource[i], result.data)
          }
        }
        this.pictureSource.push(JSON.parse(JSON.stringify(this.defaultPicture)))
      }
    },
    onDeleteHandle(data) {
      let index = 0
      for (let i = 0; i < this.pictureSource.length; i++) {
        if (this.pictureSource[i].file_serial == data.file_serial) {
          index = i
          this.pictureSource[i].file_url = ''
          this.pictureSource[i].file_serial = ''
          break
        }
      }
      this.pictureSource.splice(index, 1)
    },
    verifyTransferHandle() {
      let pictureItem = this.pictureSource.filter((item) => {
        return !!item.file_serial && !!item.file_url
      })
    //   console.log(pictureItem)
    //   if (!pictureItem.length && this.pictureSource.length == 1) {
    //     this.$message({
    //       message: '请先上传转账回执单凭据',
    //       type: 'error',
    //     })
    //     return
    //   }
      if (!this.adjustmentData.transferDate) {
        this.$message({
          message: '请先选择转账日期',
          type: 'error',
        })
        return
      }
      if (
        !this.accountData.availableAmount ||
        (this.accountData.availableAmount &&
          this.accountData.availableAmount < this.monthlyInfo.total_amount)
      ) {
        this.$message({
          message: '余额不足，无法还款！',
          type: 'error',
        })
        return
      }
      return true
    },
    onSubmitHandle() {
      if (!this.verifyTransferHandle()) return
      let noTransferDate = '您未选择转账日期，是否继续还款？'
      let hsTransferDate = `您选择的转账日期为：${
        this.adjustmentData.transferDate
          ? moment(this.adjustmentData.transferDate).format('YYYY-MM-DD')
          : this.adjustmentData.transferDate
      }，此日期之后未生成账单的滞纳金将被抹除，是否继续？`
      let _this = this
      const h = _this.$createElement
      this.$refs.payForm.validate((valid) => {
        if (valid) {
          _this.$msgbox({
            title: '提示',
            message: h('div', null, [
              h(
                'p',
                {
                  style:
                    'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
                },
                _this.adjustmentData.transferDate
                  ? hsTransferDate
                  : noTransferDate
              ),
            ]),
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            showClose: false,
            callback(action) {
              if (action == 'confirm') {
                _this.adjustmentData.transferDate
                  ? _this.adjustHandle().then((res) => {
                      if (res.code == 200) {
                        _this.monthlyBillPay()
                      } else {
                        _this.$message({
                          message: res.msg,
                          type: 'error',
                        })
                      }
                    })
                  : _this.monthlyBillPay()
              }
            },
          })
        } else {
          return false
        }
      })
    },
    //转账凭证调差
    adjustHandle() {
      this.startLoading()
      this.adjustmentData.sid = this.monthlyInfo.order_id
      let params = JSON.parse(JSON.stringify(this.adjustmentData))
      params.transferDate = moment(this.adjustmentData.transferDate).format(
        'YYYY-MM-DD'
      )
      return new Promise((resolve, reject) => {
        request({
          url: api.refundDtffer || '',
          method: 'post',
          data: {
            ...params,
          },
        })
          .then((res) => {
            this.endLoading()
            resolve(res)
          })
          .catch((error) => {
            this.endLoading()
            this.$message({
              message: error.message,
              type: 'error',
            })
            reject(error)
          })
      })
    },
    //还款
    monthlyBillPay() {
      this.startLoading()
      let params = this.formData

      request({
        url: api.monthlyBillPay,
        method: 'post',
        data: {
          ...params,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            this.$emit('getlist')
            this.endLoading()
            this.$message({
              message: '支付成功',
              type: 'success',
            })
            this.dialogVisible = false
          } else {
            this.getCaptcha()
            this.endLoading()
          }
        })
        .catch((error) => {
          console.log('111')
          this.endLoading()
        })
    },
    // 发送短信验证码
    sendSmsHandle() {
      if (!this.formData.captchaCode) {
        this.$message({
          message: '请输入图形验证码',
          type: 'warning',
        })
        return
      }
      if (this.time) return
      this.startLoading()
      let params = {
        captchaId: this.formData.captchaId,
        mobileCode: this.formData.captchaCode,
        mobile: this.formData.mobile,
      }
      if (!params.mobile) {
        params.userNo = this.formData.user_no
      }
      let countdown = 60
      request({
        url: api.sendSms,
        method: 'post',
        data: {
          ...params,
        },
      })
        .then((res) => {
          this.endLoading()
          this.time = setInterval(() => {
            countdown = countdown - 1
            this.smsName = countdown + '秒后重新发送'
            if (countdown === 0) {
              clearInterval(this.time)
              this.time = null
              this.smsName = '重新发送'
            }
          }, 1000)
        })
        .catch((error) => {
          this.endLoading()
          this.getCaptcha()
        })
    },
    // 获取图像验证码
    getCaptcha() {
      request({
        url: api.getCaptcha,
        method: 'post',
      }).then((res) => {
        if (res.code == 200) {
          this.formData.captchaId = res.data.captchaId
          this.captchaUrl = res.data.image
        }
      })
    },
    // 获取互联网账户信息
    getAccountView() {
      request({
        url: api.getAccountView,
        method: 'post',
        data: {
          custMastId: this.monthlyInfo.customer_id,
        },
      })
        .then((res) => {
          if (res.code == 200) {
            if (res.data.userNo) {
              this.accountData = res.data
              this.formData.mobile = res.data.mobile
              this.formData.user_no = res.data.userNo
            } else {
              this.$message({
                message: '未查询到互联网用户信息',
                type: 'warning',
              })
            }
          }
        })
        .catch((error) => {})
    },
    payMoneyFilter(val) {
      let value = val
      if (value == 0) return value
      value = value / 100
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
  },
}
</script>
<style lang="scss" scoped>
.repayment {
  position: relative;
  .view-item {
    margin-bottom: 6px;
  }
  .captcha {
    width: 120px;
    height: 32px;
    margin-left: 10px;
  }
  .sendSMS {
    width: 120px;
    color: #409eff;
    margin-left: 10px;
  }
}
.photograph {
  position: absolute;
  top: 0;
  left: 50%;
}
</style>
