<template>
  <div>
    <el-dialog title="滞纳金月账单"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               width="80%"
               custom-class="shipment-dialog">
      <dart-search ref="searchForm1"
                   label-position="right"
                   :model="search">
        <template slot="search-form">
          <dart-search-item label="账单月份："
                            :span="12"
                            prop="vcchannelid">

            <el-date-picker v-model="search.value1"
                            :clearable='false'
                            type="month"
                            placeholder="选择日期"
                            :picker-options="pickerOptionsYearMonth"
                            @change="changetime">
            </el-date-picker>
          </dart-search-item>
          <dart-search-item :is-button="true"
                            :span="8">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
          </dart-search-item>
        </template>
      </dart-search>
      <div class="table table-box">
        <el-table :data="tableData"
                  v-loading="tableloading"
                  border
                  style="width: 100%"
                  height="400"
                  ref="multipleTable"
                  :row-style="{ height: '54px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '54px' }"
                  :header-cell-style="{ padding: '0px' }">
          <el-table-column prop="trans_month"
                           align="center"
                           label="流水月份" />
          <el-table-column prop="forfeit_day"
                           align="center"
                           min-width="140"
                           label="滞纳日期" />
          <el-table-column prop="forfeit_money"
                           align="center"
                           min-width="140"
                           label="滞纳金金额(元)">
            <template slot-scope="scope">
              {{moneyFilter(scope.row.forfeit_money)}}
            </template>
          </el-table-column>
          <el-table-column prop="isDel"
                           align="center"
                           min-width="140"
                           label="调差状态">
            <template slot-scope="scope">
              {{scope.row.isDel=='0'?'正常':'已调差'}}
            </template>
          </el-table-column>
          <el-table-column prop="pay_status"
                           align="center"
                           min-width="140"
                           label="支付状态">
            <template slot-scope="scope">
              {{getpaystatus(scope.row.pay_status)}}
            </template>
          </el-table-column>
          <el-table-column prop="pay_time"
                           align="center"
                           min-width="180"
                           label="支付时间" />
          <el-table-column prop="remark"
                           align="center"
                           min-width="180"
                           label="备注" />
          <el-table-column prop="tax_type"
                           align="center"
                           label="发票类型">
            <template slot-scope="scope">
              <span v-if="scope.row.tax_status==2">{{scope.row.tax_type == 0 ?  '蓝票' : '红票' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="tax_status"
                           align="center"
                           label="开票状态">
            <template slot-scope="scope">
              {{gettax(scope.row.tax_status)}}
            </template>
          </el-table-column>
          <el-table-column prop="tax_apply_time"
                           align="center"
                           min-width="180"
                           label="开票时间" />
        </el-table>
        <div class="pagination g-flex g-flex-end">
          <el-pagination background
                         @size-change="handleSizeChange"
                         @current-change="changePage"
                         :current-page="search.page_index"
                         :page-size="search.page_size"
                         :page-sizes="[10, 20, 30, 50]"
                         layout="total,sizes, prev, pager, next, jumper"
                         :total="total">
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'

var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    custid: {
      type: String,
      default: null,
    },
    transmonth: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      tableloading: false,
      tableData: [],
      search: {
        value1: this.getbztime(this.transmonth),
        customer_id: this.custid,
        trans_month: this.transmonth,
        page_index: 1,
        page_size: 10,
      },
      total: 0,
      optiondata2: [
        { value: '0', label: '未支付' },
        { value: '1', label: '支付中' },
        { value: '2', label: '支付成功' },
        { value: '3', label: '支付失败' },
      ],
      taxoption: [
        { value: '0', label: '未开票' },
        { value: '1', label: '开票中' },
        { value: '2', label: '开票完成' },
      ],
      pickerOptionsYearMonth: {
        disabledDate(time) {
          let t = new Date().getDate()
          return time.getTime() > Date.now() - 8.64e7 * t
        },
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible
      this.getlist()
    })
  },
  methods: {
    getlist() {
      let data = {
        customer_id: this.search.customer_id ? this.search.customer_id : null,
        trans_month: this.search.trans_month ? this.search.trans_month : null,
        page_index: this.search.page_index,
        page_size: this.search.page_size,
      }
      request({
        url: api.billForfeit,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch(() => {})
    },
    onSearchHandle() {
      this.search.page_index = 1
      this.getlist()
    },
    changePage(val) {
      this.search.page_index = val
      this.getlist()
    },
    handleSizeChange(val) {
      this.search.page_size = val
      this.getlist()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return value
      value = float.div(float.mul(val, 100), 10000)
      return value
    },
    getpaystatus(val) {
      let arr = this.optiondata2
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    gettax(val) {
      let arr = this.taxoption
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].value == val) {
          return arr[i].label
        }
      }
      return ''
    },
    changetime() {
      console.log(this.search.value1)
      this.search.trans_month = moment(this.search.value1)
        .startOf('month')
        .format('YYYY-MM')
    },
    getbztime(val) {
      const sdate = val
      const r = sdate.replace(/^(\d{4})(\d{2})$/, '$1-$2')
      const date = new Date(r).getTime()
      const youData = new Date(date)
      return youData
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="less" scoped>
</style>>
