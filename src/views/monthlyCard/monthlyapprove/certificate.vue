<template>
  <div>
    <el-dialog title="查看凭证"
               :append-to-body="true"
               style="margin: 0px auto"
               width="560px"
               class="my_main_dialog"
               custom-class="specia-dialog"
               :visible.sync="dialogVisible"
               :destroy-on-close="true"
               :close-on-click-modal="false">
      <div class="certificate-box"
           v-if="dialogVisible">
        <div class="dart-image">
          <div class="image-preview-wrap">
            <div class="image-preview">
              <img class="image-preview-image image-preview-transition image-preview-limit"
                   :src="options[previewIndex].file_url"
                   :style="imgStyle" />
              <div class="image-preview-operations">
                <div class="image-preview-operations-inner">
                  <i @click="onReverseRotate"
                     class="el-icon-refresh-left image-preview-operations-item"></i>
                  <i @click="onForwardRotate"
                     class="el-icon-refresh-right image-preview-operations-item"></i>
                  <i class="el-icon-download image-preview-operations-item"
                     @click.stop="onEvidenceArchivesHandle"></i>
                </div>
              </div>
              <div class="image-preview-arrow-left g-flex g-flex-horizontal-vertical">
                <i class="el-icon-arrow-left"
                   @click="onPrevHandle"></i>
              </div>
              <div class="image-preview-arrow-right g-flex g-flex-horizontal-vertical">
                <i class="el-icon-arrow-right"
                   @click="onNextHandle"></i>
              </div>
            </div>
            <!-- <div class="pagination g-flex g-flex-horizontal-vertical">
              <span
                class="pagination-bullet"
                v-for="(item, index) in options"
                :key="index"
                :class="[
                  index == previewIndex ? 'pagination-bullet-active' : '',
                ]"
              ></span>
            </div> -->
          </div>
        </div>
      </div>
      <!-- <div
        style="width: 100%; margin: 30px 0px 10px"
        class="g-flex g-flex-center"
      >
        <el-button style="width: 120px" @click.stop="onEvidenceArchivesHandle">
          下载凭证
        </el-button>
      </div> -->
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    source: {},
  },
  data() {
    return {
      dialogVisible: false,
      options: [],
      previewIndex: 0,
      rotateDeg: 0,
      enableTransition:false
    }
  },

  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
    source(val) {
      if (val && val.length) {
        this.options = val
      }
    },
  },
  computed: {
    transformStyle() {
      return {
        transform:
          'scale(1) rotate(' + this.rotateDeg + 'deg) translate(0px, 0px)',
      }
    },
     imgStyle() {
    
      const style = {
        transform: `scale(1) rotate(${this.rotateDeg}deg)`,
         transition: this.enableTransition ? 'transform .3s' : '',
       
      };
      return style;
    },
  },
  created() {
    if (this.source && this.source.length) {
      this.options = this.source
      this.previewIndex = 0
    }
    this.dialogVisible = this.visible
  },
  mounted() { },
  methods: {
    onReverseRotate() {
        this.enableTransition = true
      this.rotateDeg = this.rotateDeg - 90
    },
    onForwardRotate() {
         this.enableTransition = true
      this.rotateDeg = this.rotateDeg + 90
    },
    onEvidenceArchivesHandle() {
      let evidenceArchives =
        this.options[this.previewIndex]['file_url'] + '?download=1'
      window.location.href = evidenceArchives
    },
    onNextHandle() {
      this.rotateDeg = 0
      if (this.previewIndex == this.options.length) {
        this.$message('没有更多啦')
        return
      }
       this.enableTransition = false
      this.$nextTick(() => {
        this.previewIndex = this.previewIndex + 1
      })

    },
    onPrevHandle() {
      this.rotateDeg = 0
      this.enableTransition = false
      if (this.previewIndex == 0) {
        this.$message('没有更多啦')
        return
      }
      this.$nextTick(() => {
        this.previewIndex = this.previewIndex - 1
      })
    },
  },
}
</script>

<style scoped>
.certificate-box {
  width: 520px;
  margin: 0 auto;
  display: flex;
  -moz-box-pack: center;
  -ms-box-pack: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}
.dart-image {
  display: inline-block;
  position: relative;
}
.image-preview {
  height: 100%;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.image-preview-mask-hidden {
  display: none;
}

.image-preview-wrap {
  position: relative;
}

/* .image-preview-transition {
  -webkit-transition: -webkit-transform 0.3s ease;
  transition: -webkit-transform 0.3s ease;
  transition: transform 0.3s ease;
  transition: transform 0.3s ease, -webkit-transform 0.3s ease;
} */

.image-preview-image {
  cursor: -webkit-grab;
  cursor: grab;
}

.image-preview-limit {
  width: 420px;
  height: 360px;
}
.image-preview-operations {
  height: 40px;
  position: absolute;
  left: 50%;
  bottom: 10px;
  z-index: 1;
  border-radius: 22px;
  overflow: hidden;
  background: rgba(55, 55, 55, 0.6);
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
}
.image-preview-operations-inner {
  width: 100%;
  height: 100%;
  text-align: justify;
  font-size: 23px;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  cursor: pointer;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}
.image-preview-operations-item {
  margin: 0 6px;
  padding: 4px 8px;
  width: 36px;
  height: 40px;

  font-size: 24px;
  color: #fff;
  cursor: pointer;
  -webkit-transition: opacity 0.1s ease-in-out;
  transition: opacity 0.1s ease-in-out;
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
  -moz-box-align: center;
  -webkit-box-align: center;
  box-align: center;
  align-items: center;
  -webkit-align-items: center;
  -moz-align-items: center;
  -moz-box-pack: center;
  -ms-box-pack: center;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-justify-content: center;
  justify-content: center;
}
.image-preview-arrow-left,
.image-preview-arrow-right {
  width: 40px;
  height: 40px;
  line-height: 40px;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  z-index: 1;
  font-size: 26px;
  background: rgba(55, 55, 55, 0.4);
}

.image-preview-arrow-left,
.image-preview-arrow-right {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

.image-preview-arrow-left {
  left: -50px;
}
.image-preview-arrow-right {
  right: -50px;
}
.dart-image .pagination {
  margin-top: 20px;
}
.dart-image .pagination .pagination-bullet {
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 8px;
  background-color: #000;
  margin: 0 4px;
  opacity: 0.3;
}
.dart-image .pagination .pagination-bullet-active {
  background-color: #409eff;
}
</style>
