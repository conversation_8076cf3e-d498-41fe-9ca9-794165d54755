<template>
  <div>
    <el-dialog
      title="发票红冲"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      width="60%"
      custom-class="shipment-dialog"
    >
      <div class="notis">
        <div class="title">通知方式:</div>
        <div class="radio-warpper g-flex">
          <el-radio-group v-model="radio">
            <el-radio :label="'0'">邮箱</el-radio>
            <el-radio :label="'1'">手机</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="form-info">
        <div class="title">红冲申请信息填写:</div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          size="medium"
          class="dt-form dt-form--max"
        >
          <el-row :span="20">
            <el-col :span="20">
              <el-form-item label="发票号:" prop="buyerAddress">
                <span>{{ orderList.taxNum }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="20" v-if="radio == '0'">
            <el-col :span="20">
              <el-form-item label="	邮箱:" prop="email">
                <el-input
                  v-model="ruleForm.email"
                  oninput="value=value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="20" v-if="radio == '1'">
            <el-col :span="20">
              <el-form-item label="	手机号:" prop="mobilePhone">
                <el-input
                  v-model="ruleForm.mobilePhone"
                  oninput="value=value.replace(/\s*/g,'')"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="20">
            <el-col :span="20">
              <el-form-item label="	红冲原因:" prop="reason">
                <el-input type="textarea" v-model="ruleForm.reason"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="editBtn">
          <el-button type="primary" style="width: 150px" @click="toRedflush"
            >确定</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: Number,
    },
    customerid: {
      type: String,
    },
    orderList: {
      type: Object,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      customer: {},
      vehicle: {},
      companyList: [], //购方list
      companyItem: {}, //购方选择信息
      radio: '0', //发票类型选择
      oldOrder: [], //已选择订单数组
      mergeDetail: {}, //合并详情数据
      query: '', //搜索关键字
      invoiceApplyTypeOptions: [
        {
          value: '1',
          label: '设备发票',
        },
        {
          value: '2',
          label: '权益包发票',
        },
      ],
      refundBtn: [
        {
          text: '红冲申请',
          cls: 'green-btn space30',
          cmd: 'refundBtn',
        },
      ],
      ruleForm: {
        email: '',
        mobilePhone: '',
        reason: '',
      },
      rules: {
        mobilePhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
        ],
        email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }],
        reason: [
          { required: true, message: '请输入红冲原因', trigger: 'blur' },
        ],
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible
    })
  },
  methods: {
    toRedflush() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.invoice()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    invoice() {
      this.startLoading()
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      params.notifyType = this.radio
      params.id = this.orderId

      request({
        url: api.redFlushApply,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message({
              message: '提交红冲申请成功',
              type: 'success',
            })
            this.$emit('getlist')
            this.dialogFormVisible = false
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(val)
        this.dialogVisible = val
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss" scoped>
.form-info {
  padding: 10px 35px;
}
.notis {
  padding: 10px 35px;
}
.title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 30px;
}
.editBtn {
  width: 100%;
  text-align: center;
}
</style>