<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:还款调差
  * @author:zhangys
  * @date:2022/06/13 14:32:07
!-->
<template>
  <div>
    <el-dialog title="滞纳金调差"
               :close-on-click-modal="false"
               :visible.sync="dialogVisible"
               width="35%"
               center
               @close='closeDialog'>
      <div class="balance-pay">
        <el-form ref="payForm"
                 :model="formData"
                 :rules="balancePayRules"
                 label-width="130px">

          <el-form-item label="转账回执单凭据:">
            <electronicArchives ref="childs"
                                :pictureList='imgList'
                                :scene="'16'"
                                :photo_code="'43'"
                                :customerId='billDetail.customerId'
                                :other_code="billDetail.sid+''"
                                @on-change='change'>
            </electronicArchives>
          </el-form-item>

          <el-form-item label="客户姓名:"
                        prop="sid">

            <el-input style="width:70%"
                      v-model="billDetail.customerName"
                      disabled></el-input>

          </el-form-item>
          <el-form-item label="账单月份:"
                        prop="sid">

            <el-input style="width:70%"
                      v-model="billDetail.transMonth"
                      disabled></el-input>

          </el-form-item>
          <el-form-item label="回执单转账日期:"
                        prop="transferDate">

            <el-date-picker style="width:70%"
                            v-model="formData.transferDate"
                            type="date"
                            :clearable="false"
                            placeholder="选择日期">
            </el-date-picker>

          </el-form-item>

        </el-form>
      </div>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="onSubmitHandle"
                   type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import electronicArchives from '@/components/photoGraph/photograph.vue'
var moment = require('moment')
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    billDetail: {
      type: Object,
      default: false,
    },
    customerName: {
      type: String,
      default: '',
    },
    customerId: {
      type: String,
      default: '',
    },
    transMonth: {
      type: String,
      default: '',
    },
    sid: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: {
        transferDate: '', //转账日期
        sid: '', //账单ID
        forfeitState: '0',
      },
      dialogVisible: false,
      balancePayRules: {
        transferDate: [
          { required: true, message: '请选择时间', trigger: 'blur' },
        ],
      },
      imgList: [
        {
          lable: '转账回执单凭据',
          photo_code: '43',
          file_url: '',
          file_serial: '',
          isShow: true,
        },
      ],
      credentialsFlag: false,
    }
  },

  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        console.log(this.billDetail, '--------')
        this.dialogVisible = val
        this.formData.sid = this.billDetail.sid
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  components: { electronicArchives },

  computed: {},
  created() {
    this.formData.transferDate = moment().format('YYYY-MM-DD')
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
  },
  methods: {
    change(val) {
      if (val.file_url) {
        this.credentialsFlag = true
      } else {
        this.credentialsFlag = false
      }
    },
    onSubmitHandle() {
      if (!this.credentialsFlag) {
        this.$message({
          message: '请先上传转账回执单凭据',
          type: 'error',
        })
        return
      }
      this.$refs.payForm.validate((valid) => {
        if (valid) {
          this.refundDtffer()
        } else {
          return false
        }
      })
    },
    refundDtffer() {
      this.loading = true
      let data = JSON.parse(JSON.stringify(this.formData))
      data.transferDate = moment(data.transferDate).format('YYYY-MM-DD')
      request({
        url: api.refundDtffer,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              message: '滞纳金调差成功！',
              type: 'success',
            })
            this.dialogVisible = false
            this.formData.sid = ''
            this.$emit('getList')
          }
        })
        .catch(() => {})
    },
    closeDialog() {
      this.$refs.childs.changelist('43', {})
    },
  },
}
</script>
<style >
.balance-pay .view-item {
  margin-bottom: 6px;
}
.verify-info .form {
  padding-bottom: 200px;
}

.balance-pay .el-input {
  flex: 1;
}
</style>