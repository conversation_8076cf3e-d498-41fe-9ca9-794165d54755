<template>
  <div class="invoice-list"
       v-if="invoiceInfo">
    <el-descriptions title="用户信息"
                     border
                     direction="vertical">
      <el-descriptions-item label="客户名称">{{invoiceInfo.custName}}</el-descriptions-item>
      <el-descriptions-item label="订单编号">{{invoiceInfo.orderId}}</el-descriptions-item>
      <el-descriptions-item label="流水月份">{{invoiceInfo.transMonth}}</el-descriptions-item>
    </el-descriptions>
    <div class="divider"></div>
    <el-descriptions title="服务费发票"
                     border
                     direction="vertical">
      <el-descriptions-item label="发票类型">{{invoiceInfo.serviceTaxTypeStr}}</el-descriptions-item>
      <el-descriptions-item label="开票状态">{{invoiceInfo.serviceTaxStatusStr}}</el-descriptions-item>
      <el-descriptions-item label="操作">
        <!-- <el-button size="mini"
                   type="primary"
                   v-if="(invoiceInfo.serviceTaxStatus == 0 || (invoiceInfo.serviceTaxStatus==2 && invoiceInfo.serviceTaxType== 1))"
                   @click="onInvoiceHandle('3')">开发票
        </el-button> -->
        <el-button size="mini"
                   type="primary"
                   v-if="invoiceInfo.serviceTaxStatus!=0"
                   @click="queryProgressInvoice('3')">
          查看发票</el-button>
        <el-button size="mini"
                   type="danger"
                   v-if='(invoiceInfo.serviceTaxStatus==2&&invoiceInfo.serviceTaxType==0)'
                   @click="onRedFlusHandle('3')">
          发票红冲</el-button>
      </el-descriptions-item>
    </el-descriptions>
    <template v-if="invoiceInfo.hasForfeits =='1'">
      <div class="divider"></div>
      <el-descriptions title="滞纳金发票"
                       border
                       direction="vertical">
        <el-descriptions-item label="发票类型">{{invoiceInfo.forfeitTaxTypeStr}}</el-descriptions-item>
        <el-descriptions-item label="开票状态">{{invoiceInfo.forfeitTaxStatusStr}}</el-descriptions-item>
        <el-descriptions-item label="操作">
          <!-- <el-button size="mini"
                     type="primary"
                     v-if="(invoiceInfo.forfeitTaxStatus == 0 || (invoiceInfo.forfeitTaxStatus==2 && invoiceInfo.forfeitTaxType== 1))"
                     @click="onInvoiceHandle('4')">开发票
          </el-button> -->
          <el-button size="mini"
                     type="primary"
                     v-if="invoiceInfo.forfeitTaxStatus!=0"
                     @click="queryProgressInvoice('4')">
            查看发票</el-button>
            
          <el-button size="mini"
                     type="danger"
                     v-if='(invoiceInfo.forfeitTaxStatus==2&&invoiceInfo.forfeitTaxType==0)'
                     @click="onRedFlusHandle('4')">
            发票红冲</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </template>
    <!-- 发票相关业务 -->
    <template>
      <!-- 开发票 -->
      <invoice v-if="invoiceVisible"
               :visible.sync='invoiceVisible'
               :customerid='orderId'
               :orderId='orderId'
               :userinfo="userInfo"
               :invoiceType='invoiceType'
               @on-refresh='init'></invoice>
      <!-- 红冲 -->
      <redFlush v-if="redFlushVisible"
                :orderId='orderId'
                :invoiceType='invoiceType'
                :visible.sync='redFlushVisible'
                @on-refresh='init'></redFlush>
      <!-- 查看发票 -->
      <el-dialog title="查看发票"
                 :append-to-body='true'
                 style="width: 70%;margin: 0px auto;"
                 class="my_main_dialog"
                 custom-class="specia-dialog"
                 :visible.sync="isShowQrCode"
                 :destroy-on-close='true'
                 :close-on-click-modal="false">
        <div class="qrBox g-flex"
             ref="qrBox"
             style="margin-top: 20px">
          <div style="width: 50%; padding-left: 5% ">
            <p>温馨提示：</p>
            <p>1.请您扫描下方二维码，保存您的电子普通发票。</p>
            <p>2.二维码是获取发票的唯一凭证，请妥善保管。</p>
          </div>
          <div id="qrCode"
               style=" width: 40%; margin-left: 3%"
               ref="qrCodeBox"></div>
        </div>
        <div style='width:100%;margin: 30px 0px 10px'
             class="g-flex g-flex-center ">
          <el-button style="width: 120px;"
                     @click.stop="printQr">
            打印二维码
          </el-button>
        </div>

      </el-dialog>
    </template>
  </div>
</template>

<script>
import request from '@/utils/request';
import api from '@/api/index';
import invoice from './invoice';
import redFlush from './redFlush';
import QRCode from 'qrcodejs2'
export default {
  props: {
    currentRow: [Object]
  },
  data() {
    return {
      orderId: '',
      invoiceType: '3',// 发票类型
      invoiceVisible: false,// 开发票
      invoiceInfo: null,
      redFlushVisible: false, // 红冲弹窗
      isShowQrCode: false,
      userInfo: null, // 用户信息
    };
  },

  components: {
    invoice,
    redFlush
  },

  computed: {},
  created() {
    this.orderId = this.currentRow.order_id
    this.init();
  },
  methods: {
    onInvoiceHandle(type) {
      this.invoiceType = type;
      this.getuserinfo();

    },
    getuserinfo() {
      let data = {
        customer_id: this.currentRow.customer_id,
      }
      request({
        url: api.customerBizList,
        method: 'post',
        data: data,
      })
        .then((res) => {
          this.invoiceVisible = true;
          this.userInfo = {
            email: res.data[0].email ? res.data[0].email : '',
            mobile_phone: res.data[0].link_mobile
              ? res.data[0].link_mobile
              : '',
          }
        })
        .catch(() => { })
    },
    // 发票红冲
    onRedFlusHandle(type) {
      this.invoiceType = type;
      this.redFlushVisible = true;
    },
    init() {
      let params = {
        sid: this.currentRow.order_id
      }
      request({
        url: api.invoiceStatus,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          this.invoiceInfo = res.data
        } else {
          this.$alert(res.msg, '提示', {
            confirmButtonText: '确定',
          })
        }
      }).catch(err => {
        this.$alert(err.msg, '提示', {
          confirmButtonText: '确定',
        })
      })
    },
    printQr() {
      this.$print(this.$refs.qrBox)
    },
    //查看发票
    queryProgressInvoice(type) {
      let invoice_type = type == '3' ? 'MS' : 'MF'
      let data = {
        order_id: this.currentRow.order_id,
        invoice_type: invoice_type,
      }
      request({
        url: api.query,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code != 200) {
            this.$alert(res.msg, '提示', {
              confirmButtonText: '确定'
            })
            return;
          }
          if (res.data.applyStatus == '1') {
            this.$alert('开票中，请稍后再试！', '提示', {
              confirmButtonText: '确定',
              callback: (action) => {

              },
            })
          } else {
            if (res.data.url) {
              this.showQrcode(res.data.url)
            }
          }
        })
        .catch(() => { })
    },
    showQrcode(url) {
      this.isShowQrCode = true
      this.$nextTick(() => {
        document.getElementById('qrCode').innerHTML = ''
        // const h = this.$createElement;
        let qrBox = this.$refs.qrCodeBox
        new QRCode(qrBox, {
          text: url,
          width: 200,
          height: 200,
          colorDark: '#333333', //二维码颜色
          colorLight: '#ffffff', //二维码背景色
          correctLevel: QRCode.CorrectLevel.L, //容错率，L/M/H
        })
      })
    },
  }
}
</script>
<style >
.invoice-list {
  margin: 15px;
}
.divider {
  width: 100%;
  margin-top: 24px;
  margin-bottom: 32px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}
</style>