<template>
  <div>
    <el-dialog title="发票红冲"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               width="60%"
               :append-to-body='true'
               custom-class="shipment-dialog">
      <div class="form-info">
        <el-form :model="ruleForm"
                 :rules="rules"
                 ref="ruleForm"
                 label-width="140px"
                 size="medium"
                 class="dt-form dt-form--max">
          <el-row :span="20">
            <el-col :span="10">
              <el-form-item label="订单编号:">
                <span class="rg">{{ orderId }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="发票类型:">
                <span class="rg"
                      v-if="invoiceType == 3">服务费发票</span>
                <span class="rg"
                      v-if="invoiceType == 4">滞纳金发票</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :span="20">
            <el-col :span="20">
              <el-form-item label="红冲原因:"
                            prop="remark">
                <el-input type="textarea"
                          :autosize="{ minRows: 4, maxRows: 4}"
                          v-model="ruleForm.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="editBtn">
          <el-button type="primary"
                     style="width: 150px"
                     @click="toredflush">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: Number,
    },
    invoiceType: {
      type: String,
      default: 3
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      ruleForm: {
        remark: ''
      },
      rules: {
        remark: [
          { required: true, message: "请输入红冲原因", trigger: "blur" },
        ],
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogFormVisible = this.visible;
    })
  },
  methods: {
    toredflush() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.goredflush();
        } else {
          console.log("error submit!!");
          return false;
        }
      });

    },
    goredflush() {
      let order_source = this.invoiceType == '3' ? 'MS' : 'MF'
      this.startLoading();
      let data = {
        order_id: this.orderId,
        remark: this.ruleForm.remark,
        order_source: order_source
      }
      request({
        url: api.redFlush,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message({
              message: '操作成功',
              type: 'success'
            });
            this.endLoading();
            this.$emit('on-refresh')
            this.dialogFormVisible = false
          }
        })
        .catch((err) => {
          this.$message({
            message: err.msg,
            type: 'error'
          });
          this.endLoading();

        })
    }
  },
  watch: {
    visible: function (val) {

      this.$nextTick(() => {
        console.log(val);
        this.dialogVisible = val;
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    }
  },
}
</script>

<style lang="scss" scoped>
.form-info {
  padding: 10px 35px;
}
.editBtn {
  width: 100%;
  text-align: center;
}
</style>