<template>
  <el-dialog title="开票信息"
             :visible.sync="dialogFormVisible"
             :close-on-click-modal="false"
             :center="true"
             width="70%"
             :append-to-body='true'
             custom-class="shipment-dialog">
    <div class="form-info">
      <el-form :model="ruleForm"
               :rules="rules"
               ref="ruleForm"
               label-width="140px"
               size="medium"
               class="dt-form dt-form--max">
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="订单编号:">
              <span class="rg">{{ orderId }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="发票类型:">
              <span class="rg"
                    v-if="ruleForm.invoice_apply_type == 3">服务费发票</span>
              <span class="rg"
                    v-if="ruleForm.invoice_apply_type == 4">滞纳金发票</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="购方名称:"
                          prop="buyer_name">
              <el-input v-model="ruleForm.buyer_name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="购方纳税人识别号:"
                          prop="buyer_taxpayer_num">
              <el-input v-model="ruleForm.buyer_taxpayer_num"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="购方地址:"
                          prop="buyer_address">
              <el-input v-model="ruleForm.buyer_address"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="购方电话:"
                          prop="buyer_phone">
              <el-input v-model="ruleForm.buyer_phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="	购方开户行名称:"
                          prop="buyer_bank_name">
              <el-input v-model="ruleForm.buyer_bank_name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="	购方开户行账号:"
                          prop="buyer_bank_account">
              <el-input v-model="ruleForm.buyer_bank_account"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="10">
            <el-form-item label="	邮箱:"
                          prop="email">
              <el-input v-model="ruleForm.email"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="	手机号:"
                          prop="mobile_phone">
              <el-input v-model="ruleForm.mobile_phone"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="20">
            <el-form-item label="	备注:"
                          prop="remark">
              <el-input type="textarea"
                        v-model="ruleForm.remark"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :span="20">
          <el-col :span="20">
            <el-form-item label="">
              <span class="sip">开票需邮箱手机号至少填写一项</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="editBtn">
        <el-button type="primary"
                   style="width: 150px"
                   @click="open">开发票</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
// import { validPhone } from "@/utils/validate"
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    customerid: {
      type: Number,
    },
    orderId: {
      type: Number,
    },
    userinfo: {
      type: Object,
      default: {
        email: '',
        mobile_phone: ''
      }
    },

    invoiceType: {
      type: String,
      default: 3
    }
  },
  data() {
    // const validatephone = (rule, value, callback) => {
    //   if (!validPhone(value)) {
    //     callback(new Error("请输入正确的手机号码"))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      dialogFormVisible: false,
      ruleForm: {
        buyer_name: "",
        buyer_taxpayer_num: "",
        buyer_address: "",
        buyer_phone: "",
        buyer_bank_name: "",
        buyer_bank_account: "",
        email: "",
        mobile_phone: "",
        remark: "",
        invoice_apply_type: 3
      },
      rules: {
        buyer_name: [
          { required: true, message: "请输入购方名称", trigger: "blur" },
        ],
        email: [
          { message: "请输入正确邮箱", pattern: "^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$", trigger: "blur" },
        ],
        mobile_phone: [
          { message: "请输入正确手机号", trigger: 'blur', pattern: "^1[0-9]{10}$" }
        ]
      },
    }
  },
  created() {
    this.$nextTick(() => {
      this.ruleForm.invoice_apply_type = this.invoiceType
      this.dialogFormVisible = this.visible;
      this.ruleForm.email = this.userinfo.email
      this.ruleForm.mobile_phone = this.userinfo.mobile_phone
    })
  },
  methods: {
    open() {
      if (this.ruleForm.email == '' && this.ruleForm.mobile_phone == "") {
        this.$message({
          message: '请填写邮箱或手机号',
          type: 'warning'
        });
        return
      }
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.invoice();
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    replace() {
      this.$refs["ruleForm"].resetFields();
    },
    invoice() {
      this.startLoading();
      this.ruleForm.customer_id = this.customer_id;
      this.ruleForm.order_id = this.orderId;
      let data = this.ruleForm
      request({
        url: api.applyInvoice,
        method: 'post',
        data: data,
      })
        .then((res) => {
            this.endLoading();
          if (res.code == 200) {
            this.$message({
              message: '开票成功',
              type: 'success'
            });
            this.$emit('on-refresh')
            this.dialogFormVisible = false
          }
        })
        .catch((err) => {
          this.$message({
            message: err.msg,
            type: 'error'
          });
          this.endLoading();
        })
    }
  },
  watch: {
    visible: function (val) {

      this.$nextTick(() => {
        console.log(val);
        this.dialogVisible = val;
      })
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    }
  },
}
</script>

<style lang="scss" scoped>
.form-info {
  padding: 10px 35px;
}
.editBtn {
  width: 100%;
  text-align: center;
  margin-top: 50px;
}
.sip {
  font-weight: 600;
  color: #f56c6c;
}
</style>