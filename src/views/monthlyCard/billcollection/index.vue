<template>
  <div class="user">
    <dart-search
      ref="searchForm2"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search2"
      :rules="rules2"
    >
      <template slot="search-form">
        <div style="margin: 20px">ETC消费月结账单回款报表(2023年9月启用)</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search2.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm2')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm2')"
              >重置</el-button
            >
            <stampDownload
              :name="search2.name"
              :fileName="search2.name"
              :billMonth="search2.billMonth"
              title="月月行（账期产品）ETC消费月结账单回款报表"
            ></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <div style="margin: 20px">ETC消费月结账单回款报表(旧)</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm1')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm1')"
              >重置</el-button
            >
            <stampDownload
              :name="search.name"
              :fileName="search.name"
              :billMonth="search.billMonth"
              title="月月行（账期产品）ETC消费月结账单回款报表"
            ></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import stampDownload from '../components/stampDownload'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        name: 'monthReceived', //报表名称
        billMonth: '', // 消费月份
      },
      search2: {
        name: 'monthReceived2', //报表名称
        billMonth: '', // 消费月份
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' },
        ],
      },
      rules2: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' },
        ],
      },
      pickerOptions2: {
        disabledDate(time) {
          // console.log('time.getTime()', time.getTime())
          const b = time.getTime() < moment('2023-09') // 限制不能超过今天
          return b
        },
      },
      pickerOptions: {
        disabledDate(time) {
          // console.log('time.getTime()', time.getTime())
          const b = time.getTime() > moment('2023-08') // 限制不能超过今天
          return b
        },
      },
    }
  },
  created() {
    // this.search.billMonth = moment(new Date())
    //   .subtract(1, 'months')
    //   .format('YYYY-MM')
  },
  mounted() {},
  methods: {
    onCustomerBizSearch: _.debounce(function (query) {
      if (query !== '') {
        this.getCustomerBizList(query)
      } else {
        this.customerBizList = []
      }
    }, 500),
    onClearHandle() {
      this.customerBizList = []
    },
    onSearchHandle(type) {
      this.$refs[type].$children[0].validate((valid) => {
        if (valid) {
          this.sendReportRequest(type)
        } else {
          return false
        }
      })
    },
    onResultHandle(type) {
      // let nameStr = ''
      // if (type == 'old') {
      //   nameStr = 'searchForm1'
      // } else if (type == 'new') {
      //   nameStr = 'searchForm2'
      // }
      this.$nextTick(function () {
        this.$refs[type].resetForm()
      })
    },

    sendReportRequest(type) {
      this.loading = true
      let data = {}
      if (type == 'searchForm1') {
        data = {
          // billMonth: moment(this.search.billMonth).add(1, 'months').format('YYYY-MM'),
          billMonth: moment(this.search.billMonth).format('YYYY-MM'),
          name: this.search.name,
        }
      } else if (type == 'searchForm2') {
        data = {
          // billMonth: moment(this.search.billMonth).add(1, 'months').format('YYYY-MM'),
          billMonth: moment(this.search2.billMonth).format('YYYY-MM'),
          name: this.search2.name,
        }
      }
      request({
        url: api.report,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
    getCustomerBizList(query) {
      let params = {}
      let reg = /^[0-9]\d*$/ //包括零的正整数
      let data = Math.abs(query)
      if (reg.test(query) && data.toString().length <= 11) {
        if (data.toString().length <= 8) {
          //编号
          params.customer_id = data
        } else if (data.toString().length == 11) {
          //手机号
          params.phone = data
        }
      } else {
        params.customer_name = query
      }
      this.loading = true
      request({
        url: api.customerBizList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.customerBizList = res.data
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
