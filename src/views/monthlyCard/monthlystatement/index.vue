<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="消费月份"
                          prop="billMonth">
          <el-date-picker v-model="search.billMonth"
                          type="month"
                          value-format="yyyy-MM"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <stampDownload :name='search.name'
                           :fileName='search.name'
                           :billMonth='search.billMonth'
                           title="月月行（账期产品）ETC消费月结账单报表"></stampDownload>
          </div>

        </dart-search-item>

      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import stampDownload from '../components/stampDownload'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        name: 'monthSettleReport', //报表名称
        billMonth: '', // 消费月份
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.search.billMonth = moment(new Date())
      .subtract(1, 'months')
      .format('YYYY-MM')
  },
  mounted() { },
  methods: {
    onCustomerBizSearch: _.debounce(function (query) {
      if (query !== '') {
        this.getCustomerBizList(query)
      } else {
        this.customerBizList = []
      }
    }, 500),
    onClearHandle() {
      this.customerBizList = []
    },
    onSearchHandle() {
      this.sendReportRequest()
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },

    sendReportRequest() {
      this.loading = true
      let data = {
        billMonth: moment(this.search.billMonth).format('YYYY-MM'),
        name: this.search.name,
      }
      request({
        url: api.report,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => { })
    },
    getCustomerBizList(query) {
      let params = {}
      let reg = /^[0-9]\d*$/ //包括零的正整数
      let data = Math.abs(query)
      if (reg.test(query) && data.toString().length <= 11) {
        if (data.toString().length <= 8) {
          //编号
          params.customer_id = data
        } else if (data.toString().length == 11) {
          //手机号
          params.phone = data
        }
      } else {
        params.customer_name = query
      }
      this.loading = true
      request({
        url: api.customerBizList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.customerBizList = res.data
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
