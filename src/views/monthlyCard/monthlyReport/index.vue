<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="消费月份"
                          prop="billMonth">
          <el-date-picker v-model="search.billMonth"
                          type="month"
                          value-format="yyyy-MM"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="用户名"
                          prop='custMastId'>
          <!-- <el-select v-model="search.custMastId"
                     filterable
                     remote
                     clearable
                     reserve-keyword
                     placeholder="请输入用户名称或编号,手机号"
                     @clear='onClearHandle'
                     :remote-method="onCustomerBizSearch"
                     :loading="loading">
            <el-option v-for="item in customerBizList"
                       :key="item.customer_id"
                       :label="item.customer_name"
                       :value="item.customer_id">
            </el-option>
          </el-select> -->
          <el-select v-model="search.custMastId"
                     filterable
                     clearable
                     placeholder="请输入或选择用户名">
            <el-option v-for="item in customerBizList"
                       :key="item.customerId"
                       :label="item.customerNameId"
                       :value="item.customerId">
            </el-option>
          </el-select>
        </dart-search-item>
        <!-- <dart-search-item label="车牌号码"
                          prop='vipCarNo'>
          <el-input v-model="search.vipCarNo"
                    placeholder="请输入内容"></el-input>
        </dart-search-item> -->
        <!-- <dart-search-item label="车牌号码"
                          prop='vipCarNo'>
          <el-input v-model="search.vipCarNo"
                    placeholder="请输入内容"></el-input>
        </dart-search-item> -->
        <!-- <dart-search-item label="ETC卡号"
                          prop='vipCardNo'>
          <el-input v-model="search.vipCardNo"
                    placeholder="请输入内容"></el-input>
        </dart-search-item> -->
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import { mapGetters } from 'vuex'

var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        name: 'monthBillView', //报表名称
        custMastId: '', // etc用户ID
        custName: '', // ETC用户名称
        billMonth: '', // 消费月份
        transStart: '', // 统计结束日期
        transEnd: '', // 统计开始日期
        makeBy: '',
        makeDay: '',
        // "vipCardNo": "", // ETC卡号
        // "vipCarNo": "" // 车牌号码
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' },
        ],
      },
    }
  },
  computed: {
    ...mapGetters(['realName']),
  },
  created() {
    this.search.makeBy = this.realName
    this.search.billMonth = moment(new Date())
      .subtract(1, 'months')
      .format('YYYY-MM')
    this.search.transStart = moment(this.search.billMonth)
      .startOf('month')
      .format('YYYY-MM-DD')
    this.search.transEnd = moment(this.search.billMonth)
      .endOf('month')
      .format('YYYY-MM-DD')
    this.getCustomerBizList()
  },
  mounted() {},
  methods: {
    onCustomerBizSearch: _.debounce(function (query) {
      if (query !== '') {
        this.getCustomerBizList(query)
      } else {
        this.customerBizList = []
      }
    }, 500),
    onClearHandle() {
      this.customerBizList = []
    },
    onSearchHandle() {
      let _self = this
      let custMasItem = _.filter(this.customerBizList, function (item) {
        return item.customerId == _self.search.custMastId
      })
      this.search.custName = ''
      if (custMasItem && custMasItem.length) {
        this.search.custName = custMasItem[0].customerName
      }
      this.search.transStart = moment(this.search.billMonth)
        .startOf('month')
        .format('YYYY-MM-DD')
      this.search.transEnd = moment(this.search.billMonth)
        .endOf('month')
        .format('YYYY-MM-DD')
      this.sendReportRequest()
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm1'].resetForm()
      })
    },

    sendReportRequest() {
      this.loading = true
      this.search.makeDay = moment().startOf('day').format('YYYY-MM-DD')
      let params = JSON.parse(JSON.stringify(this.search))
      
      if (params.custName) {
        params.name = 'monthBillOne'
      }
      request({
        url: api.report,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
    getCustomerBizList(query) {
      let params = {}
      let reg = /^[0-9]\d*$/ //包括零的正整数
      let data = Math.abs(query)
      if (reg.test(query) && data.toString().length <= 11) {
        if (data.toString().length <= 8) {
          //编号
          params.customer_id = data
        } else if (data.toString().length == 11) {
          //手机号
          params.phone = data
        }
      } else {
        params.customer_name = query
      }
      this.loading = true
      request({
        url: api.getVipCustomerList,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            let params = JSON.parse(JSON.stringify(res.data)).map((item) => {
              return {
                customerNameId: '(' + item.customerId + ')' + item.customerName,
                customerName: item.customerName,
                customerId: item.customerId,
              }
            })
            this.customerBizList = params
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
