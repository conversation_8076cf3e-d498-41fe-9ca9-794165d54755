<template>
  <div class="user">
    <dart-search ref="searchForm"
                 :formSpan='24'
                 :searchOperation='false'
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="统计日期"
                          prop="transEnd">
          <el-date-picker v-model="search.transEnd"
                          type="date"
                          value-format="yyyy-MM-dd"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       @click="onSearchHandle">搜索</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var moment = require('moment')

export default {
  name: 'monthUnpaySettle',
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: {
        transEnd: '', // 统计日期
        name: 'monthUnpaySettle', // 报表名称
      },
      rules: {
        transEnd: [
          { required: true, message: '请选择统计日期', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    // 设置默认日期为今天
    this.search.transEnd = moment(new Date()).format('YYYY-MM-DD')
  },
  methods: {
    onSearchHandle() {
      console.log(this.$refs.searchForm)
      this.$refs.searchForm.$children[0].validate((valid) => {
        if (valid) {
        //   console.log('Search triggered with date:', this.search.transEnd);
          this.sendReportRequest()
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    onResultHandle() {
      this.$nextTick(() => {
        this.$refs.searchForm.resetForm();
        // 重置后将日期设置回今天
        this.search.transEnd = moment(new Date()).format('YYYY-MM-DD');
      });
    },
    // 发送报表请求
    sendReportRequest() {
      this.loading = true
      let data = {
        transEnd: this.search.transEnd,
        name: this.search.name
      }

      request({
        url: api.report,
        method: 'post',
        data: data,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
              clientWidth +
              ',height=' +
              clientHeight +
              ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    margin-top: 20px; // 添加一些间距
    width: 100%;
    text-align: center;
    img {
      width: 50%;
      max-width: 600px; // 限制最大宽度
    }
  }
  // 确保按钮在同一行显示
  .g-flex {
    display: flex;
    gap: 10px; // 按钮间距
  }
}

</style>
