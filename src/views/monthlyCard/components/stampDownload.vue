<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:报表盖章并下载
  * @author:zhangys
  * @date:2022/06/01 10:03:33
!-->
<template>
  <div style="margin-left: 30px">
    <el-button type="primary" @click="stampDownload">报表盖章并下载</el-button>
  </div>
</template>

<script>
var moment = require('moment')
import request from '@/utils/request'
import api from '@/api/index'
export default {
  name: '',
  props: {
    name: {
      type: String,
      default: '',
      require: true,
    },
    fileName: {
      type: String,
      default: '',
      require: true,
    },
    keyword: {
      type: String,
      default: '公司',
    },
    billMonth: {
      type: String,
      default: '',
      require: true,
    },
    title: {
      type: String,
      default: 'report',
    },
  },
  components: {},
  data() {
    return {
      param: {},
      loading: false,
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    stampDownload() {
      if (!this.billMonth) {
        this.$message({
          message: '请先选择消费月份！',
          type: 'error',
        })
        return
      }
      this.param.keyword = this.keyword
      let months = 1
      //ETC消费月结账单报表
      if (this.name == 'monthSettleReport') {
        months = 0
      }
      this.param.billMonth = moment(this.billMonth)
        .add(months, 'months')
        .format('YYYY-MM')
      let params = {
        name: this.name,
        fileName: this.fileName,
        param: this.param,
      }
      this.loading = true
      let m = this.$message({
        message: '报表下载中......',
        type: 'info',
        duration: 0,
      })
      request({
        url: api.yyxStamp,
        method: 'post',
        data: params,
        responseType: 'blob',
      })
        .then((res) => {
          const link = document.createElement('a')
          let blob = new Blob([res], { type: 'application/pdf' }) //构造一个blob对象来处理数据
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${this.title}.pdf` //下载的文件名
          document.body.appendChild(link)
          link.click() // 执行下载
          document.body.removeChild(link) // 释放标签
          this.$message.success('下载成功')
          m.close()
        })
        .catch((err) => {
          console.log(err)
        })
    },
  },
}
</script>

<style lang='scss' scoped>
</style>