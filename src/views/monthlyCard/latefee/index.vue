<template>
  <div class="user">
    <dart-search
      ref="searchForm2"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search2"
      :rules="rules2"
    >
      <template slot="search-form">
        <div style="margin: 20px">服务费分润及滞纳金报表(2023年9月启用)</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search2.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm2')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm2')"
              >重置</el-button
            >
            <stampDownload
              :name="search2.name"
              :fileName="search2.name"
              :billMonth="search2.billMonth"
              title="月月行（账期产品）服务费分润及滞纳金报表"
            ></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <div style="margin: 20px">服务费分润及滞纳金报表(旧)</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm1')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm1')"
              >重置</el-button
            >
            <stampDownload
              :name="search.name"
              :fileName="search.name"
              :billMonth="search.billMonth"
              title="月月行（账期产品）服务费分润及滞纳金报表"
            ></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>

    <dart-search
      ref="searchForm3"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search3"
      :rules="rules3"
    >
      <template slot="search-form">
        <div style="margin: 20px">服务费1 (1、2型客车)明细数据报表</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search3.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm3')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm3')"
              >重置</el-button
            >
            <!-- <stampDownload
              :name="search3.name"
              :fileName="search3.name"
              :billMonth="search3.billMonth"
              title="服务费1(1、2型客车)明细数据报表"
            ></stampDownload> -->
          </div>
        </dart-search-item>
      </template>
    </dart-search>

    <dart-search
      ref="searchForm4"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search4"
      :rules="rules4"
    >
      <template slot="search-form">
        <div style="margin: 20px">服务费2 (3、4型客车及货车)明细数据报表</div>
        <dart-search-item label="统计月份" prop="billMonth">
          <el-date-picker
            v-model="search4.billMonth"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
            :picker-options="pickerOptions2"
          >
          </el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('searchForm4')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle('searchForm4')"
              >重置</el-button
            >
            <!-- <stampDownload
              :name="search4.name"
              :fileName="search4.name"
              :billMonth="search4.billMonth"
              title="服务费2(3、4型客车及货车)明细数据报表"
            ></stampDownload> -->
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <!-- <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="24"
    ></reportForms> -->
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import stampDownload from '../components/stampDownload'
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
var _ = require('lodash')
var moment = require('moment')
export default {
  name: 'Latefee',
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        name: 'monthServiceForfeit', //报表名称
        billMonth: '' // 消费月份
      },
      search2: {
        name: 'monthServiceForfeit2', //报表名称
        billMonth: '' // 消费月份
      },
      search3: {
        name: 'monthServiceCustCarDetail', //报表名称
        billMonth: '' // 消费月份
      },
      search4: {
        name: 'monthServiceTruckDetail', //报表名称
        billMonth: '' // 消费月份
      },
      optiondata: [],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' }
        ]
      },
      rules2: {
        billMonth: [
          { required: true, message: '请选择消费月份', trigger: 'change' }
        ]
      },
      rules3: {
        billMonth: [
          { required: true, message: '请选择月份', trigger: 'change' }
        ]
      },
      rules4: {
        billMonth: [
          { required: true, message: '请选择月份', trigger: 'change' }
        ]
      },
      pickerOptions2: {
        disabledDate(time) {
          // console.log('time.getTime()', time.getTime())
          const b = time.getTime() < moment('2023-09') // 限制不能超过今天
          return b
        }
      },
      pickerOptions: {
        disabledDate(time) {
          // console.log('time.getTime()', time.getTime())
          const b = time.getTime() > moment('2023-08') // 限制不能超过今天
          return b
        }
      },
      reportList: [
        {
          id: 1,
          name: 'monthReceivedReport',
          title: '账单回款情况统计报表'
        }
      ],
      tableHeight: 0,
      rules: {
        billMonth: [
          { required: true, message: '请选择账单月份', trigger: 'change' }
        ],
        transStart: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        transEnd: [{ required: true, message: '请选择日期', trigger: 'change' }]
      },
      pickerOptions3: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    formConfig() {
      return [
        {
          type: 'datePicker',
          field: 'billMonth',
          label: '账单月份',
          placeholder: '请选择账单月份',
          valueFormat: 'yyyy-MM',
          customType: 'month',
          // pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'transStart',
          label: '回款开始日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          // pickerOptions: this.pickerOptions,
          default: ''
        },
        {
          type: 'datePicker',
          field: 'transEnd',
          label: '回款结束日期',
          placeholder: '请选择日期',
          valueFormat: 'yyyy-MM-dd',
          // pickerOptions: this.pickerOptions,
          default: ''
        }
      ]
    }
  },
  created() {
    // this.search.billMonth = moment(new Date())
    //   .subtract(1, 'months')
    //   .format('YYYY-MM')
  },
  mounted() {},
  methods: {
    onCustomerBizSearch: _.debounce(function(query) {
      if (query !== '') {
        this.getCustomerBizList(query)
      } else {
        this.customerBizList = []
      }
    }, 500),
    onClearHandle() {
      this.customerBizList = []
    },
    onSearchHandle(type) {
      this.$refs[type].$children[0].validate(valid => {
        if (valid) {
          this.sendReportRequest(type)
        } else {
          return false
        }
      })
    },
    onResultHandle(type) {
      this.$nextTick(function() {
        this.$refs[type].resetForm()
      })
    },

    sendReportRequest(type) {
      this.loading = true
      let data = {}
      if (type == 'searchForm1') {
        data = {
          // billMonth: moment(this.search.billMonth).add(1, 'months').format('YYYY-MM'),
          billMonth: moment(this.search.billMonth).format('YYYY-MM'),
          name: this.search.name
        }
      } else if (type == 'searchForm2') {
        data = {
          // billMonth: moment(this.search.billMonth).add(1, 'months').format('YYYY-MM'),
          billMonth: moment(this.search2.billMonth).format('YYYY-MM'),
          name: this.search2.name
        }
      } else if (type == 'searchForm3') {
        data = {
          currentMonth:  moment(this.search3.billMonth).format('YYYY-MM'),
          preThreeMonth: moment(this.search3.billMonth).subtract(3, 'months').format('YYYY-MM'),
          preFourMonth: moment(this.search3.billMonth).subtract(4, 'months').format('YYYY-MM'),
          preMonth: moment(this.search3.billMonth).subtract(1, 'months').format('YYYY-MM'),
          name: this.search3.name
        }
      } else if (type == 'searchForm4') {
        data = {
          currentMonth:  moment(this.search4.billMonth).format('YYYY-MM'),
          preThreeMonth: moment(this.search4.billMonth).subtract(3, 'months').format('YYYY-MM'),
          preFourMonth: moment(this.search4.billMonth).subtract(4, 'months').format('YYYY-MM'),
          preMonth: moment(this.search4.billMonth).subtract(1, 'months').format('YYYY-MM'),
          name: this.search4.name
        }
      }
      request({
        url: api.report,
        method: 'post',
        data: data
      })
        .then(res => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
    getCustomerBizList(query) {
      let params = {}
      let reg = /^[0-9]\d*$/ //包括零的正整数
      let data = Math.abs(query)
      if (reg.test(query) && data.toString().length <= 11) {
        if (data.toString().length <= 8) {
          //编号
          params.customer_id = data
        } else if (data.toString().length == 11) {
          //手机号
          params.phone = data
        }
      } else {
        params.customer_name = query
      }
      this.loading = true
      request({
        url: api.customerBizList,
        method: 'post',
        data: params
      })
        .then(res => {
          this.loading = false
          if (res.code == 200) {
            this.customerBizList = res.data
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.transStart).isAfter(fromData.transEnd)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
