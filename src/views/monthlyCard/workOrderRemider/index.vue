<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:工单提醒
  * @author:zhangys
  * @date:2023/04/03 13:52:11
-->
<template>
  <ePage style="padding:20px">
    <div slot="report-search"
         style="background-color: #fff;">
      <el-tabs v-model="activeIndex"
               type="border-card">
        <el-tab-pane label="未还款提醒设置"
                     name="settingReminder">
          <nonPayment></nonPayment>
        </el-tab-pane>

        <el-tab-pane label="逾期提醒"
                     name="overdueReminder">
          <overdue></overdue>
        </el-tab-pane>
      </el-tabs>
    </div>
  </ePage>
</template>

<script>
import ePage from '../../reportstatistics/components/ePage.vue'
import nonPayment from './nonPayment'
import overdue from './overdue'
export default {
  data() {
    return {
      activeIndex: 'settingReminder',
      options: [],
      search: {},
    }
  },

  components: { ePage, nonPayment, overdue },

  computed: {},
  created() {},
  methods: {},
}
</script>
<style lang="scss" scoped>
.report-search .el-tabs--border-card {
  box-shadow: none !important;
}
.report-search .el-tabs__content {
  padding: 0 !important;
}
</style>
