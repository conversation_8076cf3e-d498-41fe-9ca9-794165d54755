<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:逾期提醒
  * @author:zhangys
  * @date:2023/04/03 17:54:39
-->
<template>
  <div>
    <dart-search ref="search"
                 class="search"
                 :formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1"
                 :rules="rules">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="起始日："
                          prop="warnStart">
          <el-select v-model="search.warnStart"
                     placeholder="请选择">
            <el-option v-for="(item, index) in startDay"
                       :label="item.label"
                       :value="item.value"
                       :key="index"></el-option>
          </el-select>

        </dart-search-item>
        <dart-search-item label="结束日："
                          prop="warnEnd">
          <el-select v-model="search.warnEnd"
                     placeholder="请选择">
            <el-option v-for="(item, index) in startDay"
                       :label="item.label"
                       :value="item.value"
                       :key="index"></el-option>
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :colElementNum='3'>
          <div class="g-flex g-flex-end">

            <el-button type="primary"
                       @click="onUpdateHandle">修改</el-button>

          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import float from '@/common/method/float.js'
export default {
  name: '',
  props: {},
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: { warnStart: '', warnEnd: '', warnDay: '14', warnType: '2' },
      startDay: [
        { label: '1日', value: 1 },
        { label: '2日', value: 2 },
        { label: '3日', value: 3 },
        { label: '4日', value: 4 },
        { label: '5日', value: 5 },
        { label: '6日', value: 6 },
        { label: '7日', value: 7 },
        { label: '8日', value: 8 },
        { label: '9日', value: 9 },
        { label: '10日', value: 10 },
        { label: '11日', value: 11 },
        { label: '12日', value: 12 },
        { label: '13日', value: 13 },
        { label: '14日', value: 14 },
      ],

      rules: {
        warnStart: [
          { required: true, message: '请选择起始日', trigger: 'change' },
        ],
        warnEnd: [{ required: true, message: '请选择结束日', trigger: 'blur' }],
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getInfo()
  },

  methods: {
    onUpdateHandle() {
      this.$refs['search'].$children[0].validate((valid) => {
        if (valid) {
          this.update()
        } else {
          return false
        }
      })
    },
    validate() {
      if (
        float.mul(this.search.warnEnd, 1) < float.mul(this.search.warnStart, 1)
      ) {
        this.$message.error('结束日需大于等于起始日')
        return false
      }
      return true
    },
    update() {
      if (!this.validate()) return
      let params = JSON.parse(JSON.stringify(this.search))
      params.warnDay = '14'
      this.startLoading()
      this.$request({
        url: this.$interfaces.yyxWarnUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('修改成功')
            this.getInfo()
            this.endLoading()

            this.remark = ''
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    getInfo() {
      let params = {
        warnType: this.search.warnType,
      }

      this.startLoading()
      this.$request({
        url: this.$interfaces.yyxWarnInfo,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.search = res.data
            this.endLoading()

            this.remark = ''
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang='scss' scoped>
</style>