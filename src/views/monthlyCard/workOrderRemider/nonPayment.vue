<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:未还款提醒设置
  * @author:zhang<PERSON>
  * @date:2023/04/03 17:54:39
-->
<template>
  <div>
    <dart-search ref="search"
                 class="search"
                 :formSpan='24'
                 label-position="right"
                 :searchOperation='false'
                 :model="search"
                 :fontWidth="1"
                 :rules="rules">
      <template slot="search-form"
                style="padding-left: 10px">
        <dart-search-item label="起始日："
                          prop="warnStart">
          <el-select v-model="search.warnStart"
                     placeholder="请选择">
            <el-option v-for="(item, index) in startDay"
                       :label="item.label"
                       :value="item.value"
                       :key="index"></el-option>
          </el-select>

        </dart-search-item>
        <dart-search-item label="提醒天数："
                          prop="warnDay">
          <el-input v-model="search.warnDay"
                    placeholder=""></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :colElementNum='3'>
          <div class="g-flex g-flex-end">

            <el-button type="primary"
                       @click="onUpdateHandle">修改</el-button>

          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import float from '@/common/method/float.js'
export default {
  name: '',
  props: {},
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      search: { warnStart: '', warnEnd: '31', warnDay: '', warnType: '1' },
      startDay: [
        { label: '16日', value: 16 },
        { label: '17日', value: 17 },
        { label: '18日', value: 18 },
        { label: '19日', value: 19 },
        { label: '20日', value: 20 },
        { label: '21日', value: 21 },
        { label: '22日', value: 22 },
        { label: '23日', value: 23 },
        { label: '24日', value: 24 },
        { label: '25日', value: 25 },
        { label: '26日', value: 26 },
        { label: '27日', value: 27 },
        { label: '28日', value: 28 },
      ],
      rules: {
        warnStart: [
          { required: true, message: '请选择起始日', trigger: 'change' },
        ],
        warnDay: [
          { required: true, message: '请输入提醒天数', trigger: 'blur' },
        ],
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getInfo()
  },

  methods: {
    onUpdateHandle() {
      this.$refs['search'].$children[0].validate((valid) => {
        if (valid) {
          this.update()
        } else {
          return false
        }
      })
    },
    validate() {
      if (float.add(this.search.warnStart, this.search.warnDay) > 31) {
        this.$message.error('起始日+提醒天数不得超过31')
        return false
      }
      return true
    },
    update() {
      if (!this.validate()) return
      let params = JSON.parse(JSON.stringify(this.search))
      params.warnEnd = '31'
      this.startLoading()
      this.$request({
        url: this.$interfaces.yyxWarnUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('修改成功')
            this.getInfo()
            this.endLoading()

            this.remark = ''
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    getInfo() {
      let params = {
        warnType: this.search.warnType,
      }

      this.startLoading()
      this.$request({
        url: this.$interfaces.yyxWarnInfo,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.search = res.data
            this.endLoading()

            this.remark = ''
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang='scss' scoped>
</style>