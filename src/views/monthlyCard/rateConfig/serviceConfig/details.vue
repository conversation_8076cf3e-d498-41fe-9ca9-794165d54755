<template>
  <div style="padding: 15px;">
    <el-table :data="tableData"
              :align="center"
              :header-align="center"
              :row-style="{ height: '48px' }"
              :cell-style="{ padding: '0px' }"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }"
              row-key="id"
              border>
      <el-table-column prop="forfeitPer"
                       align="center"
                       label="费率类型">
        <template slot-scope="scope">
          <span v-if="scope.row.serviceType ==0">固定费率</span>
          <span v-if="scope.row.serviceType ==1">费率制</span>
        </template>
      </el-table-column>
      <el-table-column prop="forfeitPer"
                       align="center"
                       min-width="100"
                       label="服务费费率">
        <template slot-scope="scope">
          <span v-if="scope.row.serviceType ==0">{{fixedRateFilter(scope.row.serviceFixed)}}</span>
          <span v-if="scope.row.serviceType ==1">{{ rateFilter(scope.row.servicePer)  }}</span>

        </template>
      </el-table-column>
      <el-table-column prop="isTrunk"
                       align="center"
                       label="客货类型">
        <template slot-scope="scope">
          {{ getVehicleType(scope.row.isTrunk) }}
        </template>
      </el-table-column>
      <el-table-column prop="carType"
                       align="center"
                       label="车型">
        <template slot-scope="scope">
          {{ getCarType(scope.row.carType) }}
        </template>
      </el-table-column>
      <el-table-column prop="effectiveStart"
                       align="center"
                       min-width="160"
                       label="生效时间" />
      <el-table-column prop="effectiveEnd"
                       align="center"
                       min-width="160"
                       label="失效时间" />

      <el-table-column prop="createByStr"
                       align="center"
                       min-width="180"
                       label="操作人" />
      <el-table-column prop="createTime"
                       align="center"
                       min-width="160"
                       label="操作时间" />
      <el-table-column prop="enable"
                       align="center"
                       label="滞纳金费率状态">
        <template slot-scope="scope">
          <span v-if="scope.row.enable == 0">未启用</span>
          <span v-if="scope.row.enable == 1">已启用</span>
          <span v-if="scope.row.enable == 2">已废弃</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="g-flex g-flex-start "
         style="margin: 10px 0;"
         v-if="total>search.pageSize">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import float from '@/common/method/float.js'
import { getVehicleType, getCarType } from '@/common/method/formatOptions'

export default {
  props: {
    rateConfigInfo: {
      type: Object,
      default() {
        return {}
      },
    }
  },
  data() {
    return {
      tableData: [],
      center: 'center',
      search: {
        isTrunk: '',
        carType: '',
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },

  components: {},

  computed: {},
  created() {
    this.search.isTrunk = this.rateConfigInfo.isTrunk
    this.search.carType = this.rateConfigInfo.carType
    this.getServiceConfigList();
  },
  methods: {
    getVehicleType, getCarType,
    changePage(page) {
      this.search.pageNum = page
      this.getServiceConfigList()
    },
    getServiceConfigList() {
      this.tableData = [];
      this.$request({
        url: this.$interfaces.serviceConfigList,
        method: 'post',
        data: this.search
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.search.pageNum = res.data.current
            this.search.pageSize = res.data.size
          }
        })
        .catch((error) => {

        })
    },
    //费率制
    rateFilter(val) {
      if (!val) return ''
      let value = float.mul(val, 100)
      return value + '%'
    },
    // 固定费率
    fixedRateFilter(val) {
      if (!val) return ''
      let value = float.div(val, 100)
      return value + '元'
    }
  }
}
</script>
<style lang='sass'>
</style>