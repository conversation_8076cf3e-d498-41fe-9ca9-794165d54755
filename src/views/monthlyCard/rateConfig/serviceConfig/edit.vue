<template>
  <div class="edit-page">
    <div class="edit-page_bd">
      <el-table :data="tableData"
                :align="center"
                :header-align="center"
                :row-style="{ height: '48px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id"
                border>

        <el-table-column prop="isTrunk"
                         align="center"
                         label="客货类型">
          <template slot-scope="scope">
            {{ getVehicleType(scope.row.isTrunk) }}
          </template>
        </el-table-column>
        <el-table-column prop="carType"
                         align="center"
                         label="车型">
          <template slot-scope="scope">
            {{ getCarType(scope.row.carType) }}
          </template>
        </el-table-column>
        <el-table-column prop="forfeitPer"
                         align="center"
                         label="费率类型">
          <template slot-scope="scope">
            <el-select v-model="scope.row.serviceType "
                       size="medium"
                       placeholder="请选择">
              <el-option v-for="item in serviceTypeOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="forfeitPer"
                         align="center"
                         min-width="100"
                         label="服务费费率">
          <template slot-scope="scope">

            <div v-if="scope.row.serviceType ==0">
              <el-input placeholder="请输入费率"
                        size="medium"
                        v-model="scope.row.serviceFixed">
                <template slot="append">元</template>
              </el-input>
            </div>
            <div v-if="scope.row.serviceType ==1">
              <el-input placeholder="请输入费率"
                        size="medium"
                        v-model="scope.row.servicePer">
                <template slot="append">%</template>
              </el-input>
            </div>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <div class="page-drawer-footer">
      <el-button size='medium'
                 @click="onCancelHandle">返回</el-button>
      <el-button size="medium"
                 @click="onEditHandle"
                 type="primary">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getVehicleType, getCarType } from '@/common/method/formatOptions'
import float from '@/common/method/float.js'
export default {
  props: {
    list: {
      type: Array,
      default() {
        return []
      },
    },
    multipleSelection: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      center: 'center',
      tableData: [],
      serviceTypeOptions: [
        {
          value: 0,
          label: '固定费率',
        },
        {
          value: 1,
          label: '费率制',
        },
      ],
      isLoading: false,
    }
  },

  components: {},

  computed: {},
  created() {
    console.log(this.multipleSelection, '<<---------this.multipleSelection')
    this.tableData = JSON.parse(JSON.stringify(this.multipleSelection))
    for (let i = 0; i < this.tableData.length; i++) {
      // 固定费率 百分比
      if (this.tableData[i].serviceType == 0) {
        this.tableData[i].serviceFixed = float.div(
          this.tableData[i].serviceFixed,
          100
        )
      }
      // 费率制 分
      if (this.tableData[i].serviceType == 1) {
        this.tableData[i].servicePer = float.mul(
          this.tableData[i].servicePer,
          100
        )
      }
    }
  },
  methods: {
    getVehicleType,
    getCarType,
    onEditHandle() {
      if (!this.onValidHandle()) return
      let params = this.formatParams()
      if (this.isLoading) return
      this.isLoading = true
      this.$request({
        url: this.$interfaces.serviceConfigUpdate,
        method: 'post',
        data: {
          req: params,
        },
      })
        .then((res) => {
          this.isLoading = false
          if (res.code == 200) {
            this.$message({
              message: '修改成功',
              type: 'success',
            })
            this.$emit('on-refresh')
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch((error) => {
          this.isLoading = false
        })
    },
    formatParams() {
      let temList = JSON.parse(JSON.stringify(this.list))
      let formData = JSON.parse(JSON.stringify(this.tableData))
      let params = []

      for (let j = 0; j < formData.length; j++) {
        // 固定费率 百分比
        if (formData[j].serviceType == 0) {
          formData[j].serviceFixed = float.mul(formData[j].serviceFixed, 100)
        }
        // 费率制 分
        if (formData[j].serviceType == 1) {
          formData[j].servicePer = float.div(formData[j].servicePer, 100)
        }
      }
      for (let i = 0; i < formData.length; i++) {
        let obj = {
          isTrunk: formData[i].isTrunk,
          carType: formData[i].carType,
          serviceType: formData[i].serviceType,
        }
        if (formData[i].serviceType == 0) {
          obj.serviceFixed = formData[i].serviceFixed
        }
        if (formData[i].serviceType == 1) {
          obj.servicePer = formData[i].servicePer
        }
        params.push(obj)
      }
      return params
    },
    onValidHandle() {
      let item = null
      for (let i = 0; i < this.tableData.length; i++) {
        if (
          this.tableData[i].serviceType == 0 &&
          !this.tableData[i].serviceFixed
        ) {
          item = this.tableData[i]
          break
        }
        if (
          this.tableData[i].serviceType == 1 &&
          !this.tableData[i].servicePer
        ) {
          item = this.tableData[i]
          break
        }
      }
      if (item && Object.keys(item).length) {
        let msg =
          this.getVehicleType(item.isTrunk) +
          '【' +
          this.getCarType(item.carType) +
          '】' +
          '未填写费率，请填写！'
        this.$message({
          showClose: true,
          message: msg,
          type: 'error',
        })
        return false
      }
      return true
    },
    onCancelHandle() {
      this.$emit('on-cancel')
    },
    //费率制
    rateFilter(val) {
      if (!val) return ''
      let value = float.mul(val, 100)
      return value + '%'
    },
    // 固定费率
    fixedRateFilter(val) {
      if (!val) return ''
      let value = float.sub(val, 100)
      return value
    },
  },
}
</script>
<style lang="scss" scoped>
.edit-page {
  width: 100%;
  padding: 15px;
  height: 100%;
  word-wrap: break-word;
  position: absolute;
  z-index: 10;
  overflow-y: auto;
  flex-flow: column;
  display: flex;
}
.edit-page_bd {
  flex: 1;
  overflow-y: scroll;
}
.page-drawer-footer {
  width: 100%;
  height: 53px;
  border-top: 1px solid #e8e8e8;
  padding-right: 15px;
  text-align: right;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
