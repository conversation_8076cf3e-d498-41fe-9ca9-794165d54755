<template>
  <div class="downnav">
    <el-table :data="tableData"
              :align="center"
              :header-align="center"
              :row-style="{ height: '48px' }"
              :cell-style="{ padding: '0px' }"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }"
              row-key="id"
              height="300"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange">
      <el-table-column type="selection"
                       width="55">
      </el-table-column>
      <el-table-column prop="forfeitPer"
                       align="center"
                       label="费率类型">
        <template slot-scope="scope">
          <span v-if="scope.row.serviceType ==0">固定费率</span>
          <span v-if="scope.row.serviceType ==1">费率制</span>
        </template>
      </el-table-column>
      <el-table-column prop="forfeitPer"
                       align="center"
                       min-width="100"
                       label="服务费费率">
        <template slot-scope="scope">
          <span v-if="scope.row.serviceType ==0">{{fixedRateFilter(scope.row.serviceFixed)}}</span>
          <span v-if="scope.row.serviceType ==1">{{ rateFilter(scope.row.servicePer)  }}</span>

        </template>
      </el-table-column>
      <el-table-column prop="isTrunk"
                       align="center"
                       label="客货类型">
        <template slot-scope="scope">
          {{ getVehicleType(scope.row.isTrunk) }}
        </template>
      </el-table-column>
      <el-table-column prop="carType"
                       align="center"
                       label="车型">
        <template slot-scope="scope">
          {{ getCarType(scope.row.carType) }}
        </template>
      </el-table-column>
      <el-table-column prop="effectiveStart"
                       align="center"
                       min-width="160"
                       label="生效时间" />
      <el-table-column prop="effectiveEnd"
                       align="center"
                       min-width="160"
                       label="失效时间" />
      <el-table-column prop="createByStr"
                       align="center"
                       label="操作人" />
      <el-table-column prop="createTime"
                       align="center"
                       label="操作时间" />
      <el-table-column fixed="right"
                       label="操作"
                       header-align="center"
                       min-width="120"
                       align="center">
        <template slot-scope="scope">
          <el-button size="mini"
                     type="primary"
                     @click="detailsHandle(scope.row)">更新记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width:100%;margin-top: 10px;">
      <el-button size="medium"
                 style="width:100%;border-style: dashed;"
                 @click="onDialogVisibleHandle">修改配置</el-button>
    </div>
    <!-- 服务费费率更新 -->
    <dartSlide :visible.sync="editSlideVisiable"
               title="服务费费率编辑"
               v-transfer-dom
               width="70%"
               :maskClosable="true">
      <editPage @on-cancel='editSlideVisiable=false'
                @on-refresh='onRefreshHandle'
                :multipleSelection='multipleSelection'
                :list="tableData"
                v-if="editSlideVisiable"></editPage>
    </dartSlide>
    <!-- 修改记录 -->
    <dartSlide :visible.sync="detailsSlideVisiable"
               title="修改记录"
               v-transfer-dom
               width="70%"
               :maskClosable="true">
      <recordList v-if="detailsSlideVisiable"
                  :rateConfigInfo='rateConfigRow'></recordList>
    </dartSlide>
  </div>
</template>

<script>
import dartSlide from '@/components/dart/Slide/index.vue'
import recordList from './details.vue'
import editPage from './edit.vue'
import float from '@/common/method/float.js'
import { getVehicleType, getCarType } from '@/common/method/formatOptions'
export default {
  data() {
    return {
      center: 'center',
      detailsSlideVisiable: false, //更新记录弹窗
      editSlideVisiable: false, // 编辑费率
      tableData: [],
      isLoading: false,
      multipleSelection: [],
      rateConfigRow: {},
    }
  },

  components: {
    dartSlide,
    recordList,
    editPage,
  },

  computed: {},
  created() {
    this.getServiceConfigView()
  },
  watch: {
    dialogFormVisible(val) {
      if (!val) {
        this.ruleForm.forfeitPer = ''
      }
    },
  },
  methods: {
    getVehicleType,
    getCarType,
    onRefreshHandle() {
      this.getServiceConfigView()
      this.editSlideVisiable = false
    },
    getServiceConfigView() {
      this.tableData = []
      this.$request({
        url: this.$interfaces.serviceConfigView,
        method: 'post',
        data: {},
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.tableData = res.data
          }
        })
        .catch((error) => {})
    },
    // 表单提交
    submitFormHandle(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.forfeitConfigUpdate()
        } else {
          return false
        }
      })
    },
    forfeitConfigUpdate() {
      if (this.isLoading) return
      this.isLoading = true
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      params.forfeitPer = float.div(params.forfeitPer, 100)
      this.$request({
        url: this.$interfaces.forfeitConfigUpdate,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.isLoading = false
          if (res.code == 200) {
            this.getServiceConfigView()
            this.dialogFormVisible = false
            this.$message({
              message: '修改成功',
              type: 'success',
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch((error) => {
          this.isLoading = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val, '<<---------val')
    },
    onDialogVisibleHandle() {
      if (!this.tableData.length) return
      if (!this.multipleSelection.length) {
        this.$message({
          message: '请选择需要修改服务费费率配置内容',
          type: 'warning',
        })
        return
      }
      this.editSlideVisiable = true
    },
    // 打开修改记录页面
    detailsHandle(row) {
      this.rateConfigRow = row
      this.detailsSlideVisiable = true
    },
    //费率制
    rateFilter(val) {
      if (!val) return ''
      let value = float.mul(val, 100)
      return value + '%'
    },
    // 固定费率
    fixedRateFilter(val) {
      if (!val) return ''
      let value = float.div(val, 100)
      return value + '元'
    },
  },
}
</script>
<style lang='sass'>
</style>