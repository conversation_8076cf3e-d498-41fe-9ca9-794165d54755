<template>
  <div class="account-page">
    <div class="account-page_bd">
      <div class="down">
        <div class="thetable">
          <div class="title">滞纳金费率配置</div>
          <forfeitConfig class="downnav"></forfeitConfig>
        </div>
      </div>
      <div class="down">
        <div class="thetable">
          <div class="title">服务费费率配置</div>
           <serviceConfig class="downnav"></serviceConfig>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import forfeitConfig from './forfeitConfig/index'
import serviceConfig from './serviceConfig/index'
export default {
  data() {
    return {

    };
  },

  components: {
    serviceConfig,
    forfeitConfig
  },

  computed: {},
  created() {

  },
  methods: {

  }
}
</script>
<style lang="scss" scoped>
.down {
  padding: 10px 15px;
  .thetable {
    background-color: #fff;
    padding-bottom: 20px;
    .title {
      margin: 0 0 20px;
      padding: 16px 24px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .downnav {
    padding: 0px 24px;
    .itembox {
      line-height: 40px;
      .item {
        margin: auto;
        font-size: 14px;
        span {
          display: inline-block;
          padding-right: 10px;
          color: #606266;
          font-weight: 600;
          min-width: 100px;
          text-align: right;
        }
      }
    }
  }
}
.account-page {
  width: 100%;
  height: 100%;
  word-wrap: break-word;
  z-index: 10;
  overflow-y: auto;
  flex-flow: column;
  display: flex;
}
.account-page_bd {
  flex: 1;
  background: #f5f7f9;
  overflow-y: scroll;
}
.page-drawer-footer {
  width: 100%;
  height: 53px;
  border-top: 1px solid #e8e8e8;
  padding-right: 15px;
  text-align: right;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>