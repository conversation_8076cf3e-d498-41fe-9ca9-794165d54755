<template>
  <div style="padding: 15px;">
    <el-table :data="tableData"
              :align="center"
              :header-align="center"
              :row-style="{ height: '48px' }"
              :cell-style="{ padding: '0px' }"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }"
              row-key="id"
              border>
      <el-table-column prop="forfeitPer"
                       align="center"
                       label="滞纳金费率">
        <template slot-scope="scope">
          {{ rateFilter(scope.row.forfeitPer)  }}
        </template>
      </el-table-column>
      <el-table-column prop="effectiveStart"
                       align="center"
                       label="生效时间" />
      <el-table-column prop="effectiveEnd"
                       align="center"
                       label="失效时间" />
      <el-table-column prop="createByStr"
                       align="center"
                       label="操作人" />
      <el-table-column prop="createTime"
                       align="center"
                       label="操作时间" />
      <el-table-column prop="enable"
                       align="center"
                       label="滞纳金费率状态">
        <template slot-scope="scope">
          <span v-if="scope.row.enable == 0">未启用</span>
          <span v-if="scope.row.enable == 1">已启用</span>
          <span v-if="scope.row.enable == 2">已废弃</span>
        </template>
      </el-table-column>
    </el-table>
    <div class="g-flex g-flex-start "
         style="margin: 10px 0;">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="total, prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
  </div>
</template>

<script>
import float from '@/common/method/float.js'
export default {
  data() {
    return {
      tableData: [],
      center: 'center',
      search: {

        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },

  components: {},

  computed: {},
  created() {
    this.getForfeitConfigList();
  },
  methods: {
    changePage(page) {
      this.search.pageNum = page
      this.getForfeitConfigList()
    },
    getForfeitConfigList() {
      this.tableData = [];
      this.$request({
        url: this.$interfaces.forfeitConfigList,
        method: 'post',
        data: this.search
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.tableData = res.data.records
            this.total = res.data.total
            this.search.pageNum = res.data.current
            this.search.pageSize = res.data.size
          }
        })
        .catch((error) => {

        })
    },
    rateFilter(val) {
      if (!val) return ''
      let value = float.mul(val, 100)
      return value + '%'
    }
  }
}
</script>
<style lang='sass'>
</style>