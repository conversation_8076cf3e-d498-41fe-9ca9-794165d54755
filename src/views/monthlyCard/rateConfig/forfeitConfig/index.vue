<template>
  <div class="downnav">
    <el-table :data="tableData"
              :align="center"
              :header-align="center"
              :row-style="{ height: '48px' }"
              :cell-style="{ padding: '0px' }"
              :header-row-style="{ height: '54px' }"
              :header-cell-style="{ padding: '0px' }"
              row-key="id"
              border>
      <el-table-column prop="forfeitPer"
                       align="center"
                       label="滞纳金费率">
        <template slot-scope="scope">
          {{ rateFilter(scope.row.forfeitPer)  }}
        </template>
      </el-table-column>
      <el-table-column prop="effectiveStart"
                       align="center"
                       label="生效时间" />
      <el-table-column prop="effectiveEnd"
                       align="center"
                       label="失效时间" />
       <el-table-column prop="createByStr"
                       align="center"
                       label="操作人" />
      <el-table-column prop="createTime"
                       align="center"
                       label="操作时间" />
      <el-table-column align="center"
                       label="操作">
        <template slot-scope="scope">
          <el-button size="mini"
                     type="primary"
                     @click="detailsHandle(scope.row)">更新记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="width:100%;margin-top: 10px;">
      <el-button size="medium"
                 style="width:100%;border-style: dashed;"
                 @click="onDialogVisibleHandle">修改配置</el-button>
    </div>
    <!-- 更新配置 -->
    <el-dialog title="修改滞纳金费率"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               append-to-body
               center
               width="550px">
      <el-form ref="ruleForm"
               :model="ruleForm"
               :rules="rules"
               label-width="120px"
               class="demo-ruleForm">
        <el-row :xs="24"
                :sm="24">
          <el-col :span="16"
                  :offset="4">
            <el-form-item label="滞纳金费率"
                          prop="forfeitPer">
              <el-input placeholder="请输入滞纳金费率"
                        v-model="ruleForm.forfeitPer">
                <template slot="append">%</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

      </el-form>
      <template slot="footer">
        <el-button type="primary"
                   size="medium"
                   @click="submitFormHandle('ruleForm')">提交</el-button>
        <el-button size="medium"
                   @click="dialogFormVisible=false">取消</el-button>
      </template>
    </el-dialog>
    <!-- 修改记录 -->
    <dartSlide :visible.sync="detailsSlideVisiable"
               title="修改记录"
               v-transfer-dom
               width="70%"
               :maskClosable="true">
      <recordList v-if="detailsSlideVisiable"></recordList>
    </dartSlide>
  </div>
</template>

<script>
import dartSlide from '@/components/dart/Slide/index.vue'
import recordList from './details.vue'
import float from '@/common/method/float.js'
export default {
  data() {
    return {
      center: 'center',
      detailsSlideVisiable: false, //更新记录弹窗
      dialogFormVisible: false, // 编辑费率
      tableData: [],
      ruleForm: {
        forfeitPer: '',
      },
      rules: {

        forfeitPer: [
          { required: true, message: '滞纳金费率不能为空!', trigger: 'blur' },
        ]
      },
      isLoading: false
    };
  },

  components: {
    dartSlide,
    recordList
  },

  computed: {},
  created() {
    this.getForfeitConfigView();
  },
  watch: {
    dialogFormVisible(val) {
      if (!val) {
        this.ruleForm.forfeitPer = '';
      }
    }
  },
  methods: {
    getForfeitConfigView() {
      this.tableData = [];
      this.$request({
        url: this.$interfaces.forfeitConfigView,
        method: 'post',
        data: {}
      })
        .then((res) => {
          if (res.code == 200 && res.data) {
            this.tableData = res.data;
          }

        })
        .catch((error) => {

        })
    },
    // 表单提交
    submitFormHandle(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {

          this.forfeitConfigUpdate();
        } else {

          return false
        }
      })
    },
    forfeitConfigUpdate() {
      if (this.isLoading) return;
      this.isLoading = true;
      let params = JSON.parse(JSON.stringify(this.ruleForm));
      params.forfeitPer = float.div(params.forfeitPer, 100);
      this.$request({
        url: this.$interfaces.forfeitConfigUpdate,
        method: 'post',
        data: params
      })
        .then((res) => {
          this.isLoading = false
          if (res.code == 200) {
            this.getForfeitConfigView();
            this.dialogFormVisible = false;
            this.$message({
              message: '修改成功',
              type: 'success',
            })
          } else {
            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch((error) => {
          this.isLoading = false
        })
    },
    onDialogVisibleHandle() {
      if (!this.tableData.length) return;
      this.ruleForm.forfeitPer = this.tableData[0].forfeitPer;
      this.ruleForm.forfeitPer = float.mul(this.ruleForm.forfeitPer, 100)
      this.dialogFormVisible = true;

    },
    // 打开修改记录页面
    detailsHandle() {
      this.detailsSlideVisiable = true;
    },
    rateFilter(val) {
      if (!val) return ''
      let value = float.mul(val, 100)
      return value + '%'
    }
  }
}
</script>
<style lang='sass'>
</style>