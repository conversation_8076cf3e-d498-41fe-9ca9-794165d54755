

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'cardNo',
      label: '卡号',
      width: 180
    },
    {
      prop: 'fee',
      label: '交易金额',
      formatter: (row) => {
        return moneyFilter(row)
      }
    },
    {
      prop: 'transFee',
      label: '扣费金额',
      formatter: (row) => {
        return moneyFilter(row)
      }
    },
    {
      prop: 'time',
      label: '交易时间',
      width: 180
    },
    {
      prop: 'transactionId',
      label: '交易编号',
      width: 260
    },
    {
      prop: 'serviceproviderid',
      label: '通行省份',
      formatter: (row) => {
        //  9999999945020001 = 省内 ， ！=9999999945020001 = 省外
        return row == null ?'全部': row != '9999999945020001' ? '省外' : '省内'
      }
    },
    {
      prop: 'extime',
      label: '出口时间',
      width: 180
    },
    {
      prop: 'passId',
      label: '通行标识编号',
      width: 240
    },
    {
      prop: 'handleStatus',
      label: '处理状态',
      formatter: (row) => {
        return row == '0' ? '初始化' : row == '1' ? '已审核' : '已归集'
      }
    },
    {
      prop: 'refillStatus',
      label: '是否圈存',
      // formatter: (row) => {
      //   return convertDataToNames(row, vcTradeTypesMap)
      // }
      // formatter: (row) => {
      //   return row == '0' ? '未圈存' : '已圈存确认'
      // } 
    },
    // {
    //   prop: 'action',
    //   width: 120,
    //   label: '操作'
    // }
  ]
}

function moneyFilter(val) {
  if (val) {
    return (val / 100).toFixed(2)
  } else {
    return '0.00'
  }
}

//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号',
      default: '',
    },
    {
      type: 'dateRange',
      field: 'transTime',
      keys: ['transTimeFrom', 'transTimeTo'],
      label: '交易时间：',
      default: []
    },
    {
      type: 'select',
      field: 'serviceProviderId',
      label: '通行省份',
      placeholder: '通行省份',
      default: '',
      options: [
        {
          label: '全部',
          value: '0'
        },
        {
          label: '省内',
          value: '1'
        },
        {
          label: '省外',
          value: '2'
        },
      ]
    },
    {
      type: 'dateRange',
      field: 'exTime',
      keys: ['exTimeFrom', 'exTimeTo'],
      label: '出口时间：',
      default: []
    },
    {
      type: 'select',
      field: 'handleStatus',
      label: '处理状态',
      placeholder: '处理状态',
      default: '',
      options: [
        {
          label: '初始化',
          value: '0'
        },
        {
          label: '已审核',
          value: '1'
        },
        {
          label: '已归集',
          value: '2'
        },
      ]
    },
    {
      type: 'select',
      field: 'refillStatus',
      label: '是否圈存',
      placeholder: '是否圈存',
      default: '',
      options: [
        {
          label: '未圈存',
          value: '0'
        },
        {
          label: '已圈存写卡',
          value: '1'
        },
        {
          label: '已圈存确认',
          value: '2'
        },
      ]
    },
  ]
}
