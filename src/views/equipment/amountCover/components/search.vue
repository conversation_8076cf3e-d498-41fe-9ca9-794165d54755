<template>
  <div class="search-diaolog">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
    >
    </SearchForm>

    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="handleSubmit" type="primary" size="mini"
        >确定</el-button
      >
      <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import { listForm } from '../model'
import SearchForm from '@/components/my-table/search-form.vue'

export default {
  components: {
    SearchForm
  },
  mixins: [layerMix],
  data() {
    return {
      timeField:['OrderSubmitDate','OrderSubmitDate3']
    }
  },
  computed: {
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    onSearchHandle(formData) {
      let params = JSON.parse(JSON.stringify(formData))
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      this.timeField.forEach(item => {
        delete params[item]
      })
      this.getParam('callBack')(params, this.layerid)
    },
    handleCloseIcon() {
      this.closeDialog()
    },
    handleSubmit(){
      this.$refs.SearchForm.onSearchHandle()
    }
  }
}
</script>

<style lang="scss" scoped>
.search-diaolog{
  ::v-deep .btn-wrapper{
    display: none;
  }
}
</style>