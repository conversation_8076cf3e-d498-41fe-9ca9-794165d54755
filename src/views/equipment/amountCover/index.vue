<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >报表</el-button
      >
    </SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" v-permisaction="['cardAccount:cardAmountReturnCheck']" @click="audit">审核</el-button>
        <el-button type="primary" size="mini" v-permisaction="['cardAccount:cardAmountReturnCheck']" @click="batchAudit"
          >批量审核</el-button
        >
        <el-button
          type="primary"
          size="mini"
          @click="collection"
          v-permisaction="['cardAccount:cardAmountReturnSum']"
          :disabled="!isCollection"
          >全部数据归集生成待退款</el-button
        >
        <el-button type="primary" size="mini" v-permisaction="['cardAccount:cardAmountReturnSumCheck']" @click="batchColletion"
          >数据归集生成待退款</el-button
        >
        <el-button size="mini" v-permisaction="['cardAccount:importAmountReturnList']" type="primary" @click="importDialog" 
          ><i class="el-icon-upload"></i> 导入</el-button
        >
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import layerMix from '@/utils/layerMixins'
import { listColoumns, listForm } from './model'
import {
  getCardAmounList,
  cardAmountReturnCheck,
  caExport,
  cardAmountReturnSum,
  cardAmountReturnCount,
  cardAmountReturnSumCheck
} from '@/api/equipment'
import { decode } from 'js-base64'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin, layerMix],
  data() {
    return {
      tableData: [],
      api: getCardAmounList,
      timeField: ['transTime', 'exTime'],
      isCollection: false
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 审核
    audit() {
      if (this.selectArr.length != 1) {
        this.$message.warning('请选择一条数据')
        return
      }
      this.$confirm('您确定审核这条记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let params = {
          cardAmountReturnIds: []
        }

        params.cardAmountReturnIds.push(this.selectArr[0].cardAmountReturnIdStr)
        let res = await cardAmountReturnCheck(params)
        if (res.code == 200) {
          this.clearSelection()
          this.getTableData()
          this.checkCount()
          this.$message({
            type: 'success',
            message: '审核成功!'
          })
        }
      })
    },
    //批量审核
    batchAudit() {
      // this.$openPage(
      //   '@/views/equipment/amountCover/components/search',
      //   '批量审核条件',
      //   {
      //     callBack: (res, lid) => {
      //       // this.addSubmit(res, lid)
      //       console.log(res, lid, 'res')
      //     }
      //   },
      //   {
      //     // offset: ['50%', '50%']
      //     area: ['75%', '280px']
      //   }
      // )
      if (this.selectArr.length <= 1) {
        this.$message.warning('请选择多条数据')
        return
      }
      this.$confirm('您确定审核这些记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let count = this.selectArr.length
        let params = {}
        params.cardAmountReturnIds = this.selectArr.map(
          item => item.cardAmountReturnIdStr
        )
        let res = await cardAmountReturnCheck(params)
        if (res.code == 200) {
          this.clearSelection()
          this.getTableData()
          this.checkCount()
          this.batchAuditSubmit(res, count)
        }
      })
    },
    async batchAuditSubmit(params, count) {
      this.$alert(
        `您的操作已成功！<p style="padding-top:5px;">审核成功记录数：${count}</p>`,
        '提示',
        {
          dangerouslyUseHTMLString: true,
          showClose: false,
          confirmButtonText: '确定'
        }
      )
    },
    // 全量归集生成待退款
    collection() {
      this.$confirm('您确定归集生成待退款？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await cardAmountReturnSum()
        if (res.code == 200) {
          let { returnSum, totAmount } = res.data
          // 归集成功记录数：0
          this.$alert(
            `您的操作已成功！<p style="padding-top:5px;">归集成功记录数：${returnSum}</p> <p style="padding-top:5px;">归集成功金额：${this.moneyFilter(
              totAmount
            )}</p>`,
            '提示',
            {
              dangerouslyUseHTMLString: true,
              showClose: false,
              confirmButtonText: '确定'
            }
          )
          this.getTableData()
          this.checkCount()
        }
      })
    },
    // 批量归集
    batchColletion() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请选择数据')
        return
      }
      this.$confirm('您确定归集生成待退款？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let count = this.selectArr.length
        let params = {}
        params.cardAmountReturnIds = this.selectArr.map(
          item => item.cardAmountReturnIdStr
        )
        let res = await cardAmountReturnSumCheck(params)
        if (res.code == 200) {
          // 归集成功记录数：0
          this.$alert(
            `您的操作已成功！<p style="padding-top:5px;">归集成功记录数：${count}</p>`,
            '提示',
            {
              dangerouslyUseHTMLString: true,
              showClose: false,
              confirmButtonText: '确定'
            }
          )
          this.getTableData()
          this.checkCount()
        }
      })
    },
    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = {
        card_no: query.cardNo,
        startDateTime: query.transTimeFrom,
        endDateTime: query.transTimeTo,
        startDateTime1: query.exTimeFrom,
        endDateTime1: query.exTimeTo,
        handleStatus: query.handleStatus
      }
      params.name = 'cardAmountReturnReport'
      this.sendReportRequest(params)
    },
    // 报表请求
    sendReportRequest(params) {
      this.loading = true
      this.$store.dispatch('report/report', params).then(res => {
        this.loading = false
        let url = res
        let decodeUrl = decode(url)
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      })
    },
    async checkCount() {
      let { data } = await cardAmountReturnCount()
      this.isCollection = data > 0
    },
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
    // 导入
    importDialog() {
      this.$openPage(
        '@/views/equipment/amountCover/components/import-dialog',
        '导入文件',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
            this.getTableData()
            this.checkCount()
          }
        },
        {
          area: ['45%', '400px'],
          offset: ['50%', '50%']
        }
      )
    }
  },
  created() {
    this.getTableData()
    this.checkCount()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  .choose-footer {
    text-align: center;
  }
}
</style>