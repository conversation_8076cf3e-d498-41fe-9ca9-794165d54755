

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'terminalCode',
      label: '设备编号',
      // width: 200,
    },
    {
      prop: 'terminalName',
      label: '设备名称',
    },
    {
      prop: 'manufacturerCode',
      label: '厂商编号',
      // width: 200,
    },
    {
      prop: 'manufacturerName',
      label: '厂商名称',
    },
    {
      prop: 'model',
      label: '设备型号',
    },
    {
      prop: 'status',
      label: '设备状态',
      formatter:(row) => {
        return _this.queryGroup.statusObj[Number(row)]
      }
    },
    {
      prop: 'currentVersion',
      label: '软件版本',
    },
    {
      prop: 'nextVersion',
      label: '应升级版本',
    },
    {
      prop: 'action',
      width: 200,
      label: '操作'
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'terminalCode',
      label: '设备编号：',
      default: '',
    },
    {
      type: 'input',
      field: 'manufacturerCode',
      label: '厂商编号：',
      default: '',
    },
    {
      type: 'input',
      field: 'terminalModel',
      label: '设备型号：',
      default: '',
    },
    {
      type: 'select',
      field: 'status',
      label: '状态：',
      isCollapse: true,
      options: _this.queryGroup.statusArr
    },
    {
      type: 'input',
      field: 'currentVersion',
      label: '软件版本：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'nextVersion',
      label: '应升级版本：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'terminalName',
      label: '设备名称',
      isCollapse: true,
      default: '',
    },
  ]
}
