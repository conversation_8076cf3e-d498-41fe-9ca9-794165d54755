<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
      <!-- <el-row :gutter="24">
        <el-col :span="12"></el-col>
        <el-col :span="12"></el-col>
      </el-row>-->

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="设备ID：" prop="terminalMastId">
            <el-input disabled v-model="formData.terminalMastId"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备编号：" prop="terminalCode">
            <el-input disabled v-model="formData.terminalCode"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="厂商：" prop="manufacturerName">
        <el-input disabled v-model="formData.manufacturerName">
          <el-button
            slot="append"
            @click="chooseFactory"
            icon="el-icon-search"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="设备型号：" prop="terminalModel">
            <el-input v-model="formData.terminalModel"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称：" prop="terminalName">
            <el-input v-model="formData.terminalName"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="当前软件版本：" prop="currentVersion">
            <el-input v-model="formData.currentVersion"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应升级版本：" prop="nextVersion">
            <el-input v-model="formData.nextVersion"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注：" prop="remark">
        <el-input type="textarea" v-model="formData.remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {},
      rules: {
        terminalName: [
          { required: true, message: '设备名称不能为空', trigger: 'blur' }
        ],
        manufacturerName: [{ required: true, message: '厂商不能为空', trigger: 'blur' }],
        terminalModel: [
          { required: true, message: '设备型号不能为空', trigger: 'blur' }
        ],
        currentVersion: [
          { required: true, message: '当前软件版本不能为空', trigger: 'blur' }
        ],
        nextVersion: [
          { required: true, message: '应升级版本不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        console.log(this.formData)
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    chooseFactory() {
      this.$openPage(
        '@/views/equipment/factory/index',
        '新增厂商',
        {
          callBack: res => {
            this.$set(this.formData, 'manufacturerName', res.manufacturerName)
            this.$set(this.formData, 'manufacturerMastId', res.manufacturerMastId)
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    },
    close() {
      this.closeDialog()
    }
  },
  created() {
    let formData = this.getParam('formData')
    this.formData = formData
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
}
</style>
