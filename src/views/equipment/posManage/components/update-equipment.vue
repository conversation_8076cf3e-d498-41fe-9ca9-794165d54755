<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
      <el-form-item label="厂商：" prop="factory">
        <el-input disabled v-model="formData.factory">
          <el-button slot="append" @click="chooseFactory" icon="el-icon-search"></el-button>
        </el-input>
      </el-form-item>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="当前软件版本：" prop="nowVersion">
            <el-input v-model="formData.nowVersion"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应升级版本：" prop="version">
            <el-input v-model="formData.version"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <el-button type="primary" @click="handleSubmit">更新</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      dialogVisible: false,
      formData: {},
      rules: {
        nowVersion: [
          { required: true, message: '当前软件版本不能为空', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '应升级版本不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        console.log(this.formData)
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    chooseFactory() {
      this.$openPage(
        '@/views/equipment/factory/index',
        '新增厂商',
        {
          callBack: res => {
            console.log(res,'reeess')
            this.$set(this.formData, 'manufacturerName', res.manufacturerName)
            this.$set(this.formData, 'manufacturerMastId', res.manufacturerMastId)
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    },
    close() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
}
</style>
