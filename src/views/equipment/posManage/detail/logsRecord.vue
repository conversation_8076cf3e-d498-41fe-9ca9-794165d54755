
<template>
  <div>
    <el-descriptions :column="1" border title="设备领用日志" style="padding:24px 16px 0px 16px"></el-descriptions>
    <div style="padding:0 16px 10px 16px">
      <el-table
        :data="tableData"
        align="center"
        header-align="center"
        border
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column prop="processNode_str" align="center" label="终端ID"></el-table-column>
        <el-table-column prop="handleType_str" align="center" label="操作类型" />
        <el-table-column prop="opTime" align="center" label="操作时间" />
        <el-table-column prop="opByName" align="center" label="操作人" />
        <el-table-column prop="remark" align="center" min-width="250" label="备注" />
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return { tableData: [] }
  },
  computed: {},
  watch: {
    orderInfo(val) {
      if (val && val.id) {
        this.getRecord()
      }
    }
  },
  created() {},
  methods: {
    getRecord() {
      let params = {
        id: this.orderInfo.id
      }
      this.$request({
        url: this.$interfaces.activateOperationRecord,
        method: 'post',
        data: params
      })
        .then(res => {
          if (res.code == 200) {
            this.tableData = res.data
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang='scss' scoped>
</style>