<template>
  <div class="detail-wrap">
    <!-- 设备详细信息 -->
    <div class="orderItem">
      <basicInfo :orderInfo="orderInfo" v-bind="$attrs"></basicInfo>
    </div>

    <!-- 设备领用日志 -->
    <!-- <div class="orderItem">
      <logRecord :orderInfo="orderInfo" />
    </div> -->

    <!-- 操作 -->
    <!-- <div class="orderItem">
      <div class="btns g-flex g-flex-center">
        <el-button type="primary" style="margin:0 20px">设备归还</el-button>
        <el-button type="primary" style="margin:0 20px">设备挂失</el-button>
        <el-button type="primary" style="margin:0 20px">设备禁用</el-button>
        <el-button type="primary" style="margin:0 20px">设备作废</el-button>
      </div>
    </div> -->
  </div>
</template>

<script>
import basicInfo from './basicInfo.vue'
import logRecord from './logsRecord.vue'
import { posDetail } from '@/api/equipment'

export default {
  components: {
    basicInfo,
    logRecord
  },
  props: {
    terminalMastId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      orderInfo: {
      }
    }
  },
  methods: {
    async initDeatail() {
      let params = {
        terminalMastId: this.terminalMastId
      }
      let { data } = await posDetail(params)
      this.orderInfo = data
    }
  },
  created(){
    this.initDeatail()
  }
}
</script>

<style lang="scss" scoped>
@import './common.css';

.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}

.btn-style {
  padding: 10px;
}
</style>
