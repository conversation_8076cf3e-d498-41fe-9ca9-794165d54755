
<template>
  <div>
    <el-descriptions
      :column="2"
      border
      size="medium"
      title="订单基础信息"
      class="descriptions-content"
    >
      <el-descriptions-item label="设备编号：">{{
        orderInfo.terminalCode
      }}</el-descriptions-item>
      <el-descriptions-item label="设备ID：">{{
        orderInfo.terminalMastId
      }}</el-descriptions-item>

      <el-descriptions-item label="厂商编号：">{{
        orderInfo.manufacturerCode
      }}</el-descriptions-item>

      <el-descriptions-item label="厂商名称：">{{
        orderInfo.manufacturerName
      }}</el-descriptions-item>

      <el-descriptions-item label="设备型号：">{{
        orderInfo.model
      }}</el-descriptions-item>

      <el-descriptions-item label="设备名称：">{{
        orderInfo.terminalName
      }}</el-descriptions-item>

      <el-descriptions-item label="设备状态：">{{
        queryGroup.statusObj[orderInfo.status]
      }}</el-descriptions-item>

      <el-descriptions-item label="创建时间：">{{
        orderInfo.createTime
      }}</el-descriptions-item>
      
      <el-descriptions-item label="备注">{{
        orderInfo.remark
      }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'

export default {
  name: '',
  props: {
    orderInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    queryGroup: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {},
  data() {
    return {}
  },
  computed: {},
  watch: {
    orderInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        this.orderInfo = val
      }
    }
  },
  created() {},
  methods: {
    getVehicleColor
  }
}
</script>

<style lang='scss' scoped>
@import './common.css';
</style>