<template>
  <div class="pos-page">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="add">新增</el-button>
        <!-- <el-button type="primary" size="mini" @click="updateEquipment"
          >批量更新设备版本</el-button
        > -->
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 设备编号 -->
        <template slot="terminalCode" slot-scope="{ scope }">
          <el-button type="text" size="mini" @click="openDetail(scope)">{{
            scope.terminalCode
          }}</el-button>
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            type="primary"
            size="mini"
            @click="handelRow(scope, 'edit')"
            >修改</el-button
          >
          <el-button size="mini" type="danger" @click="handelRow(scope, 'del')"
            >删除</el-button
          >
        </template>
      </my-table>
    </div>
    <dartSlide
      :visible.sync="slideVisible"
      title="POS设备详细信息"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
    >
      <detail
        :terminalMastId="terminalMastId"
        :queryGroup="queryGroup"
        v-if="slideVisible"
        :slideVisible="slideVisible"
      ></detail>
      <!-- @refreshList="getTableData" :isView="isView" -->
    </dartSlide>

    <!-- <import-dialog :visible.sync="importDialogVisible" @uploadSuccess="getTableData"></import-dialog> -->
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { posList, addPos, delPos, posDict } from '@/api/equipment'
import dartSlide from '@/components/dart/Slide/index.vue'
import detail from './detail/index.vue'

export default {
  components: {
    MyTable,
    SearchForm,
    dartSlide,
    detail
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: posList,
      slideVisible: false,
      equipmentId: null,
      importDialogVisible: false,
      pageSizeKey: 'pageSize',
      pageNumKey: 'page',
      dataKey: 'data',
      queryGroup: {
        statusObj: {},
        statusArr: []
      }
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    async initDict() {
      let { data } = await posDict()
      let statusObj = {}
      let statusArr = data.TEMINAL_MAST_STATUS.map(item => {
        statusObj[item.dictValue] = item.dictLabel
        return { label: item.dictLabel, value: item.dictValue }
      })

      this.queryGroup.statusObj = statusObj
      this.queryGroup.statusArr = statusArr
    },
    // 打开新增弹框
    add() {
      this.$openPage(
        '@/views/equipment/posManage/components/form-layer',
        '添加设备',
        {
          callBack: (res, lid) => {
            this.addSubmit(res, lid)
          }
        },
        {
          area: ['35%', '590px']
        }
      )
    },
    // 新增/编辑
    async addSubmit(params, lid) {
      let res = await addPos(params)
      if (res.code == 200) {
        this.$message.success('成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
    // 操作
    handelRow(row, type) {
      if (type == 'del') {
        let params = {
          terminalMastIds: [row.terminalMastId]
        }
        this.deletePos(params)
      } else if (type == 'edit') {
        this.$openPage(
          '@/views/equipment/posManage/components/edit-form',
          '修改设备信息',
          {
            formData: {
              ...row,
              terminalModel: row.model,
              // manufacturerMastId: row.manufacturerCode
            },
            callBack: (res, lid) => {
              this.addSubmit(res, lid)
              // console.log(res, lid)
            }
          },
          {
            area: ['60%', '480px']
          }
        )
      }
    },
    deletePos(params) {
      this.$confirm('请确认是否要删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await delPos(params)
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    },
    // 详情
    openDetail(row) {
      this.terminalMastId = row.terminalMastId
      this.slideVisible = true
    },
    // 更新版本
    updateEquipment() {
      this.$openPage(
        '@/views/equipment/posManage/components/update-equipment',
        '修改软件版本',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
            console.log(res, lid)
          }
        },
        {
          area: ['65%', '280px'],
          offset: ['50%', '350']
        }
      )
    }
  },
  created() {
    this.initDict()
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.pos-page {
  .top-btn {
    margin-bottom: 20px;
  }
  ::v-deep .fontWidth {
    // display: flex;
    div {
      // width: auto;
      .collapse {
        // display: none;
      }
    }
  }
}
</style>