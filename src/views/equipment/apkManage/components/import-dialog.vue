<template>
  <div class="authorizeDialog">
    <el-form ref="form" :model="formData" label-width="90px" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name"></el-input>
      </el-form-item>
      <el-form-item label="厂商：" prop="manufacturerName">
        <el-input disabled v-model="formData.manufacturerName">
          <el-button
            slot="append"
            @click="chooseFactory"
            icon="el-icon-search"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="终端编号" prop="terminalCode">
        <el-input v-model="formData.terminalCode"></el-input>
      </el-form-item>
      <el-form-item label="终端类型" prop="terminalType">
        <el-input v-model="formData.terminalType"></el-input>
      </el-form-item>
      <el-form-item label="apk版本号" prop="versionName">
        <el-input v-model="formData.versionName"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remarks">
        <el-input type="textarea" v-model="formData.remarks"></el-input>
      </el-form-item>
    </el-form>
    <fieldset class="fieldset">
      <legend>附件上传</legend>
      <div slot="tip" class="el-upload__tip">注意事项：</div>
      <div slot="tip" class="el-upload__tip">
        仅支持apk格式的文件
      </div>
      <el-upload
        class="upload"
        ref="upload"
        :on-remove="handleRemove"
        :auto-upload="false"
        action="action"
        accept=".apk"
        :file-list="fileList"
        :multiple="false"
        :on-change="onChange"
      >
        <!-- accept=".xls, .xlsx"  -->
        <el-button
          slot="trigger"
          :disabled="fileList.length >= 1"
          size="small"
          type="primary"
          >选取文件</el-button
        >
      </el-upload>
    </fieldset>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="handleSubmit" type="primary" size="mini"
        >确定</el-button
      >
      <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import api from '@/api/index'
import { getToken } from '@/utils/auth'
import layerMix from '@/utils/layerMixins'

export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {
        file: ''
      },
      fileList: [],
      rules: {
        name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
        manufacturerCode: [
          { required: true, message: '厂商编号不能为空', trigger: 'blur' }
        ],
        manufacturerName: [
          { required: true, message: '厂商不能为空', trigger: 'blur' }
        ],
        versionName: [
          { required: true, message: '版本号不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(async valid => {
        console.log(this.formData)
        if (valid) {
          let fullFilePath = await this.submitUpload()
          if (!fullFilePath) return
          this.formData.fullFilePath = fullFilePath
          console.log(fullFilePath, 'fullFilePath')

          this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    async submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请上传文件'
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['apk']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }

      if (this.fileList[0].isEdit) {
        return this.formData.fullFilePath
      }

      let fileCode = await this.upload()
      console.log(fileCode, 'fileCode')
      return new Promise((resolve, reject) => {
        resolve(fileCode)
      })
    },
    async upload() {
      this.startLoading()
      let formData = new FormData()
      formData.append('file', this.formData.file)
      console.log(formData)
      let url =
        process.env.VUE_APP_BASE_API + '/issue-web' + '/manufacturerApk/upload'
      let config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: getToken()
        }
      } //添加请求头
      let res = await axios.post(url, formData, config)

      this.endLoading()

      if (res.data.code !== 200) {
        this.endLoading()
        this.$message({
          type: 'error',
          message: res.data.msg
        })
        return new Promise((resolve, reject) => {
          reject(res.data.msg)
        })
      }
      // this.$refs.upload.clearFiles()

      // this.formData.file = ''
      console.log(res.data, 'res.data')
      return new Promise((resolve, reject) => {
        resolve(res.data.data.url)
      })
    },
    handleRemove() {
      this.formData.file = ''
      this.fileList = []
    },
    chooseFactory() {
      this.$openPage(
        '@/views/equipment/factory/index',
        '新增厂商',
        {
          callBack: res => {
            this.$set(this.formData, 'manufacturerName', res.manufacturerName)
            this.$set(this.formData, 'manufacturerCode', res.manufacturerCode)
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.closeDialog()
    }
  },
  created() {
    let formData = this.getParam('formData')
    if (formData) {
      this.formData = formData
      // 文件处理
      this.formData.file = this.formData.fullFilePath
      this.fileList.push({
        name: this.formData.fileName,
        status: 'success',
        isEdit: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.authorizeDialog {
  width: 100%;
  height: 100%;
  padding: 20px;
}
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>