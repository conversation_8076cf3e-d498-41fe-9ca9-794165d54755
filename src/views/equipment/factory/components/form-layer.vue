<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
      <el-form-item label="厂商编号" prop="manufacturerCode">
        <el-input v-model="formData.manufacturerCode"></el-input>
      </el-form-item>
      <el-form-item label="厂商名称" prop="manufacturerName">
        <el-input v-model="formData.manufacturerName"></el-input>
      </el-form-item>
      <el-form-item label="联系人" prop="manufacturerContactName">
        <el-input v-model="formData.manufacturerContactName"></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="manufacturerTel">
        <el-input v-model="formData.manufacturerTel"></el-input>
      </el-form-item>
      <el-form-item label="联系地址" prop="manufacturerAddress">
        <el-input v-model="formData.manufacturerAddress"></el-input>
      </el-form-item>
      <el-form-item label="邮政编码" prop="manufacturerPosscode">
        <el-input  v-model="formData.manufacturerPosscode"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">提交</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {},
      rules: {
        manufacturerName: [
          { required: true, message: '厂商名称不能为空', trigger: 'blur' }
        ],
        manufacturerCode: [{ required: true, message: '厂商编号不能为空', trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    close() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
}
</style>
