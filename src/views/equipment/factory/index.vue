<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="add">新增</el-button>
        <el-button type="primary" size="mini" @click="batchDelete">删除</el-button>
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="danger" @click="handelRow(scope, 'del')">删除</el-button>
        </template>
      </my-table>
    </div>
    <div class="choose-footer" v-if="$isDialogByOpenPage()">
      <el-button @click="closeDialog()">取消</el-button>
      <el-button type="primary" @click="submitCallback">确定</el-button>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import layerMix from '@/utils/layerMixins'
import { listColoumns, listForm } from './model'
import { factoryList, addFactory, delFactory } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin, layerMix],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: factoryList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'page',
      dataKey:'data'
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 打开新增弹框
    add() {
      this.$openPage(
        '@/views/equipment/factory/components/form-layer',
        '新增厂商',
        {
          callBack: (res, lid) => {
            this.addSubmit(res, lid)
          }
        },
        {
          area: ['35%', '520px']
        }
      )
    },
    // 新增
    async addSubmit(params, lid) {
      let res = await addFactory(params)
      if (res.code == 200) {
        this.$message.success('添加成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
    // 操作
    handelRow(row, type) {
      if (type == 'del') {
        let params = {
          manufacturerMastIds: [row.manufacturerMastId]
        }
        this.deleteFactory(params)
      }
    },
    // 批量删除
    batchDelete() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let params = {
        manufacturerMastIds: this.selectArr.map(item => item.manufacturerMastId)
      }
      this.deleteFactory(params)
      // console.log(params)
    },
    // 删除接口
    deleteFactory(params) {
      this.$confirm('请确认是否要删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await delFactory(params)
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    },
    submitCallback() {
      if (this.selectArr.length != 1) {
        this.$message.warning('只能选择一条数据')
        return
      }
      this.getParam('callBack')(this.selectArr[0])
      this.closeDialog()
    }
  },
  created() {
    // console.log(this.$isDialogByOpenPage(), this.getParam())

    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  ::v-deep .fontWidth {
    display: flex;
    div {
      width: auto;
      .collapse {
        display: none;
      }
    }
  }
  .choose-footer {
    text-align: center;
  }
}
</style>