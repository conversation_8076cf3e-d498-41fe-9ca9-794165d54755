
import moment from 'moment'
import float from '@/common/method/float.js'

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'sern',
      label: '生产序列号',
      width: 180
    },
    {
      prop: 'warnType',
      label: '操作类型',
    },
    {
      prop: 'custCode',
      label: '用户编号',
    },
    {
      prop: 'custName',
      label: '用户名称',
    },
    {
      prop: 'cardNo',
      label: '卡号',
      width: 180
    },
    {
      prop: 'amount',
      label: '金额',
      formatter: (row) => {
        return moneyFilter(row)
      },
      width: 100,
    },
    {
      prop: 'bankCardNo',
      label: '银行卡号',
      width: 170
    },
    {
      prop: 'carNo',
      label: '车牌',
    },
    {
      prop: 'carColor',
      label: '车牌颜色',
    },
    {
      prop: 'createDate',
      label: '创建时间',
      width: 160
    },
    {
      prop: 'updateDate',
      label: '更新时间',
      width: 160
    },
    {
      prop: 'delFlag',
      label: '完成情况',
    },
    {
      prop: 'sussess',
      label: '是否成功',
    },
    {
      prop: 'remark',
      label: '备注',
      width: 200
    },
    // {
    //   prop: 'action',
    //   width: 120,
    //   label: '操作'
    // }
  ]
}

const end = new Date()
const start = new Date()
start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)

function moneyFilter(val) {
  if (!val || val == '0') {
    return val
  }
  return float.div(val, 100)
}

//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'custCode',
      label: '用户编号',
      default: '',
    },
    {
      type: 'input',
      field: 'custName',
      label: '用户名称',
      default: '',
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车牌',
      default: '',
    },
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号',
      default: '',
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['createTimeFrom', 'createTimeTo'],
      label: '生成时间',
      default: [moment(start).format('YYYY-MM-DD HH:mm:ss'),moment(end).format('YYYY-MM-DD HH:mm:ss')]
    },
    {
      type: 'select',
      field: 'delFlag',
      label: '操作完成情况',
      placeholder: '操作完成情况',
      default: '0',
      options: [
        {
          label: '待重发',
          value: '0'
        },
        {
          label: '已完成',
          value: '1'
        },
      ]
    },
    {
      type: 'select',
      field: 'cardType',
      label: '卡片类型',
      isCollapse: true,
      placeholder: '卡片类型',
      default: '',
      options: [
        {
          label: '储值卡',
          value: '22'
        },
        {
          label: '记账卡',
          value: '23'
        },
      ]
    },
    {
      type: 'input',
      field: 'custIdNo',
      label: '证件号码',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'sern',
      isCollapse: true,
      label: '生产序列号',
      default: '',
    },
    {
      type: 'select',
      field: 'bankId',
      label: '银行名称',
      placeholder: '银行名称',
      isCollapse: true,
      default: '',
      props:{
        filterable:true,
      },
      options: _this.activateChannelStatus
    },
    {
      type: 'input',
      field: 'bankCardNo',
      isCollapse: true,
      label: '银行卡号',
      default: '',
    },
    {
      type: 'select',
      field: 'warnType',
      label: '操作类型',
      placeholder: '操作类型',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '批扣',
          value: '1'
        },
        {
          label: '白名单',
          value: '2'
        },
        {
          label: '黑名单',
          value: '3'
        },
        {
          label: '兜底',
          value: '4'
        },
      ]
    },

    {
      type: 'select',
      field: 'sussess',
      label: '操作是否成功',
      placeholder: '操作是否成功',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '失败',
          value: '0'
        },
        {
          label: '成功',
          value: '1'
        },
      ]
    },

  ]
}
