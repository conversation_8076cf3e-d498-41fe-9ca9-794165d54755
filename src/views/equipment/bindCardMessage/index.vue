<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
      :rules="rules"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="primary" @click="handelRow(scope, 'del')"
            >重发</el-button
          >
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { mapActions } from 'vuex'
import { getCardBindingAsk } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: getCardBindingAsk,
      timeField: ['OrderSubmitDate'],
      activateChannelStatus: [],
      rules: {
        OrderSubmitDate: [
          {
            required: true,
            message: '生成时间不能为空',
            trigger: 'blur'
          }
        ]
      }
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    ...mapActions(['setApplyChannelStatus']),
    getActivateStatusOptions() {
      this.$request({
        url: this.$interfaces.activateBindChannel,
        method: 'post',
        data: {}
      })
        .then(res => {
          if (res.code == 200) {
            let channelOPtions = res.data.BINDING_BANK_TYPE.map(item => {
              return {
                value: item.fieldValue,
                label: item.fieldNameDisplay
              }
            })
            channelOPtions.unshift({ value: '', label: '全部' })
            this.activateChannelStatus = channelOPtions
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    }
  },
  created() {
    this.getActivateStatusOptions()
  },
  mounted() {
    // this.$refs.SearchForm.onSearchHandle()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
}
</style>