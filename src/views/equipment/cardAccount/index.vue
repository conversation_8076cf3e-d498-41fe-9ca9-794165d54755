<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >导出</el-button
      >
    </SearchForm>
    <div class="table">
      <div class="top-btn">
        <!-- <el-button type="primary" size="mini" @click="add"
          >卡帐信息修改</el-button
        >
        <el-button type="primary" size="mini" @click="batchDelete"
          >预付记账卡修改</el-button
        > -->
        <!-- <el-button
          type="warning"
          size="mini"
          native-type="submit"
          @click="exportHandle"
          >导出</el-button
        > -->
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <!-- <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template> -->
      </my-table>
    </div>

  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { getCardAccountPage, addFactory, caExport } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: getCardAccountPage,
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 打开新增弹框
    add() {
      this.$openPage(
        '@/views/equipment/factory/components/form-layer',
        '新增厂商',
        {
          callBack: (res, lid) => {
            this.addSubmit(res, lid)
          }
        },
        {
          area: ['35%', '520px']
        }
      )
    },
    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = {
        [this.pageSizeKey]: this.pageSize,
        [this.pageNumKey]: this.pageNum,
        ...query
      }
      let fileObj = {
        fileName: '卡帐信息.xlsx'
      }
      this.exportFile(params, caExport, fileObj)
    }
  },
  created() {
    // this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  .choose-footer {
    text-align: center;
  }
}
</style>