

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'cardNo',
      label: '卡号',
      width: 200
    },
    {
      prop: 'cardType',
      label: '卡类型',
    },
    {
      prop: 'custName',
      label: '用户名称',
      width: 160
    },
    {
      prop: 'carNo',
      label: '车牌',
    },
    {
      prop: 'custIdNo',
      label: '证件号码',
      width: 200
    },
    {
      prop: 'custContact',
      label: '联系人',
    },
    {
      prop: 'amountStr',
      label: '卡帐余额',
      // width: 200,
    },
    {
      prop: 'frozenAmountStr',
      label: '冻结金额',
    },
    {
      prop: 'availableAmountStr',
      label: '可用余额',
    },
    {
      prop: 'eWalletAmountStr',
      label: '电子钱包余额',
    },
    {
      prop: 'amountUpdateTimeStr',
      label: '卡账余额更新时间',
      width: 160
    },
    {
      prop: 'status',
      label: '卡帐状态',
    },
    // {
    //   prop: 'action',
    //   width: 120,
    //   label: '操作'
    // }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'custCode',
      label: '用户编号',
      default: '',
    },
    {
      type: 'input',
      field: 'custName',
      label: '用户名称',
      default: '',
    },
    {
      type: 'input',
      field: 'custContact',
      label: '联系人',
      default: '',
    },
    {
      type: 'input',
      field: 'custIdNo',
      label: '证件号码',
      default: '',
    },
    {
      type: 'input',
      field: 'custMobile',
      label: '手机',
      default: '',
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车牌',
      default: '',
    },
    {
      type: 'select',
      field: 'cardType',
      label: '卡片类型',
      placeholder: '卡片类型',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '储值卡',
          value: '22'
        },
        {
          label: '记账卡',
          value: '23'
        },
      ]
    },
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'cardAccountStatus',
      label: '卡帐状态',
      placeholder: '卡帐状态',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '正常',
          value: '0'
        },
        {
          label: '已清算',
          value: '1'
        },
      ]
    },
    // {
    //   type: 'select',
    //   field: 'otherStatus',
    //   label: '卡账清算状态',
    //   placeholder: '卡账清算状态',
    //   isCollapse: true,
    //   default: '',
    //   options: [
    //     {
    //       label: '银联',
    //       value: 'CUP'
    //     },
    //     {
    //       label: '微信',
    //       value: 'S_WECHAT'
    //     },
    //   ]
    // },
  ]
}
