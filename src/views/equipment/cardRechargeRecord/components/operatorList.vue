<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
      </my-table>
    </div>
    <div class="choose-footer" v-if="$isDialogByOpenPage()">
      <el-button @click="closeDialog()">取消</el-button>
      <el-button type="primary" @click="submitCallback">确定</el-button>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import layerMix from '@/utils/layerMixins'
import { operatorListColoumns, operatorListForm } from '../model'
import { getEtcUserPage } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin, layerMix],
  data() {
    return {
      tableData: [],
      api: getEtcUserPage,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageNum',
      dataKey: 'data'
    }
  },
  computed: {
    listColoumns() {
      return operatorListColoumns(this)
    },
    formConfig() {
      return operatorListForm(this)
    }
  },
  methods: {
    // 操作
    handelRow(row, type) {
      if (type == 'del') {
        let params = {
          manufacturerMastIds: [row.manufacturerMastId]
        }
        this.deleteFactory(params)
      }
    },
    submitCallback() {
      if (this.getParam('multiple')) {
        if (this.selectArr.length <= 0) {
          this.$message.warning('请选择数据')
          return
        }
        if (this.selectArr.length > this.getParam('multiple')) {
          this.$message.warning(`最多只能选择${this.getParam('multiple')}条数据`)
          return
        }
        this.getParam('callBack')(this.selectArr)

      } else {
        if (this.selectArr.length != 1) {
          this.$message.warning('只能选择一条数据')
          return
        }
        this.getParam('callBack')(this.selectArr[0])
      }

      this.closeDialog()
    }
  },
  created() {
    console.log(this.$isDialogByOpenPage(), this.getParam('query'))
    if(this.getParam('query')){
      this.pageParams = this.getParam('query')
    }
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  ::v-deep .fontWidth {
    display: flex;
    div {
      width: auto;
      .collapse {
        display: none;
      }
    }
  }
  .choose-footer {
    text-align: center;
  }
}
</style>