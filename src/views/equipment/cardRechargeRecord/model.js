

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'recordId',
      label: '充值编号',
    },
    {
      prop: 'custName',
      label: '用户名称',
    },
    {
      prop: 'custContact',
      label: '联系人',
    },
    {
      prop: 'cardNo',
      label: '卡号',
      width: 200,
    },
    {
      prop: 'carNo',
      label: '车牌',
    },
    // {
    //   prop: 'codeNo',
    //   label: '证件号码',
    // },
    // {
    //   prop: 'manufacturerTel',
    //   label: '手机',
    // },
    {
      prop: 'paidAmountStr',
      label: '实收金额',
    },
    {
      prop: 'giftAmountStr',
      label: '赠送金额',
    },
    {
      prop: 'rechargeAmountStr',
      label: '充值金额',
    },
    {
      prop: 'optName',
      label: '操作人',
    },
    {
      prop: 'deptName',
      label: '操作部门',
      width: 200
    },
    {
      prop: 'optTime',
      label: '操作时间',
      width: 160
    },
    {
      prop: 'cancelFlag',
      label: '冲正标识',
    },
    // {
    //   prop: 'action',
    //   width: 120,
    //   label: '操作'
    // }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'custName',
      label: '用户名称：',
      default: '',
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车牌：',
      default: '',
    },
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号：',
      default: '',
    },
    {
      type: 'input',
      field: 'recordId',
      label: '充值记录编号：',
      default: '',
    },
    {
      type: 'select',
      field: 'optSubtype',
      label: '充值类型：',
      placeholder: '充值类型',
      default: '',
      options: [
        {
          label: '划拨',
          value: '1'
        },
        {
          label: '直冲',
          value: '2'
        },
      ]
    },
    {
      type: 'select',
      field: 'cancelFlag',
      label: '是否已冲正：',
      placeholder: '是否已冲正',
      default: '',
      options: [
        {
          label: '有效',
          value: '0'
        },
        {
          label: '已冲正',
          value: '1'
        },
      ]
    },
    {
      type: 'input',
      field: 'custContact',
      label: '联系人',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'custIdNo',
      label: '证件号码',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'custMobile',
      label: '手机',
      isCollapse: true,
      default: '',
    },
    {
      type: 'slot',
      field: 'optby',
      isCollapse: true,
      label: '操作员',
      default: '',
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['preDate', 'lastDate'],
      isCollapse: true,
      label: '操作时间：',
      default: []
    },
  ]
}


//操作人表格
export const operatorListColoumns = (_this) => {
  return [
    {
      prop: 'username',
      label: '用户名',
    },
    {
      prop: 'userCode',
      label: '用户编号',
    },
    {
      prop: 'realName',
      label: '姓名',
    },
    {
      prop: 'deptName',
      label: '部门',
      width: 200,
    },
    {
      prop: 'mobile',
      label: '手机号码',
    },

    {
      prop: 'email',
      label: 'Email',
    },
    {
      prop: 'status',
      label: '状态',
    },
  ]
}

//操作人搜索表单
export const operatorListForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'username',
      label: '用户名：',
      default: '',
    },
    {
      type: 'input',
      field: 'realName',
      label: '姓名：',
      default: '',
    },
    // {
    //   type: 'input',
    //   field: 'cardNo',
    //   label: '卡号：',
    //   default: '',
    // },
  ]
}