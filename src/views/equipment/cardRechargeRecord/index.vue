<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <template slot="input" slot-scope="{ scope }">
        <el-input  clearable v-model="scope.optbyName" disabled>
          <el-button
            slot="append"
            @click="chooseOperator(scope)"
            icon="el-icon-search"
          ></el-button>
        </el-input>
      </template>

      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >导出</el-button
      >
    </SearchForm>
    <div class="table">
      <!-- <div class="top-btn">
        <el-button
          type="warning"
          size="mini"
          native-type="submit"
          @click="exportHandle"
          >导出</el-button
        >
      </div> -->
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <!-- <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template> -->
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { getCardAccountRechargePage,carExport } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: getCardAccountRechargePage,
      timeField:['OrderSubmitDate','custContactName']
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = {
        [this.pageSizeKey]: this.pageSize,
        [this.pageNumKey]: this.pageNum,
        ...query
      }
      let fileObj = {
        fileName:'卡帐充值记录.xlsx'
      }
      this.exportFile(params, carExport,fileObj)
    },
    chooseOperator(row) {
      console.log(row)
        this.$openPage(
        '@/views/equipment/cardRechargeRecord/components/operatorList',
        '选择操作人',
        {
          callBack: res => {
            console.log(res,444)
            this.$set(row, 'optby', res.etcUserId)
            this.$set(row, 'optbyName', res.realName)            
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    }
  },
  created() {
    // this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  .choose-footer {
    text-align: center;
  }
  ::v-deep .el-input.is-disabled .el-input__inner{
    background-color:#fff;
    color: #606266;
  }
}
</style>