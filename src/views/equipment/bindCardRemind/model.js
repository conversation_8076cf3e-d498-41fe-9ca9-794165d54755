

//表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'warnType',
      label: '类型',
    },
    {
      prop: 'custCode',
      label: '用户编号',
    },
    {
      prop: 'custName',
      label: '用户名称',
    },
    {
      prop: 'cardType',
      label: '卡类型',
    },
    {
      prop: 'cardNo',
      label: '卡号',
      width: 180
    },
    {
      prop: 'bankCardType',
      label: '卡类型',
    },
    {
      prop: 'bankCardNo',
      label: '银行卡号',
      width: 180
    },
    {
      prop: 'carNo',
      label: '车牌',
    },
    {
      prop: 'carColor',
      label: '车牌颜色',
    },
    {
      prop: 'reminderTimes',
      label: '提醒次数',
    },
    {
      prop: 'createDate',
      label: '提醒生成时间',
      width: 180
    },
    {
      prop: 'sussess',
      label: '是否成功',
    },
    {
      prop: 'content',
      label: '内容',
      width: 300
    },
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'custCode',
      label: '用户编号',
      default: '',
    },
    {
      type: 'input',
      field: 'custName',
      label: '用户名称',
      default: '',
    },
    {
      type: 'input',
      field: 'custContact',
      label: '联系人',
      default: '',
    },
    {
      type: 'input',
      field: 'cardNo',
      label: '卡号',
      default: '',
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车牌',
      default: '',
    },
    {
      type: 'input',
      field: 'custMobile',
      label: '手机',
      default: '',
    },

    {
      type: 'input',
      field: 'custIdNo',
      label: '证件号码',
      isCollapse: true,
      default: '',
    },
    {
      type: 'input',
      field: 'bankCardNo',
      isCollapse: true,
      label: '银行卡号',
      default: '',
    },
    {
      type: 'select',
      field: 'bankCardType',
      label: '银行卡类型',
      placeholder: '银行卡类型',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '借记卡',
          value: '1'
        },
        {
          label: '借贷卡',
          value: '2'
        },
        {
          label: '对公账户',
          value: '3'
        },
      ]
    },
    {
      type: 'select',
      field: 'cardType',
      label: '卡片类型',
      placeholder: '卡片类型',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '储值卡',
          value: '22'
        },
        {
          label: '记账卡',
          value: '23'
        },
      ]
    },
    {
      type: 'select',
      field: 'warnType',
      label: '提醒类型',
      placeholder: '提醒类型',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '批扣',
          value: '1'
        },
        {
          label: '白名单',
          value: '2'
        },
        {
          label: '黑名单',
          value: '3'
        },
        {
          label: '兜底',
          value: '4'
        },
      ]
    },
    {
      type: 'select',
      field: 'sussess',
      label: '操作是否成功',
      placeholder: '操作是否成功',
      isCollapse: true,
      default: '',
      options: [
        {
          label: '失败',
          value: '0'
        },
        {
          label: '成功',
          value: '1'
        },
      ]
    },
  ]
}
