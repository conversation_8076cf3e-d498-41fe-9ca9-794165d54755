<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）车辆办理情况日报表 2022.7.04需求 盖章并下载
  * @author:zhangys&zcq
  * @date:2022/05/11 10:58:55
!-->
<template>
  <div class="user">
    <div class="section-1">
      <reportExport
        title="车辆办理情况日报表"
        reportName="hsDdpCarHandleDay"
        timeName="ddpCarTime"
        :receiveTime.sync="receiveTime"
      >
        <template v-slot:button>
          <stampDownload
            name="hsDdpCarHandleDay"
            fileName="hsDdpCarHandleDay"
            :billMonth="receiveTime"
            timeName="ddpCarTime"
            title="日日通（担保产品）车辆办理情况日报表"
            keyword="报表日期"
          ></stampDownload>
        </template>
      </reportExport>
    </div>
    <div class="section-2">
      <reportExport
        title="车辆办理情况新发明细"
        reportName="hsDdpIssueDetail"
        timeName="ddpCarTime"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        title="车辆办理情况注销明细"
        reportName="hsDdpCancelDetail"
        timeName="ddpCarTime"
      >
      </reportExport>
    </div>
  </div>
</template>

<script>
import { decode } from 'js-base64'
import request from '@/utils/request'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import stampDownload from './components/stampDownload'
import reportExport from './components/reportExport'

import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
    reportExport,
  },
  data() {
    return {
      receiveTime: '',
      // loading: false,
      // search: {
      //   thedate: '',
      // },
      // tableHeight: 0,
      // rules: {
      //   thedate: [
      //     { required: true, message: '请选择统计日期', trigger: 'change' },
      //   ],
      // },
    }
  },
  methods: {
    // downReport(val) {
    //   this.downReportDate = val
    // },
  },
  // created() {
  //   this.search.thedate = moment().subtract(1, 'day').format('YYYY-MM-DD')
  // },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
