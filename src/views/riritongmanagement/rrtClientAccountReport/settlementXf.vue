<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）-代收ETC保证金结算通知书（卡账）
  * @author:zhangys
  * @date:2023/05/19 10:06:51
-->
<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item
          :key="item.fieldKey"
          :label="item.fieldLabel"
          :prop="item.fieldKey"
        >
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="3">
        <div class="g-flex g-flex-end">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
          <!-- <stampDownload
            name="dailyAccessConsumerReport"
            fileName="dailyAccessConsumerReport"
            :isTimeQuantum="true"
            starDateName="ddpETCStartTime"
            endDateName="ddpETCEndTime"
            :startDate="search.ddpETCStartTime"
            :endDate="search.ddpETCEndTime"
            rangeTime="days"
            timeName="ddpETCTime"
            title="日日通（担保产品）-代收ETC保证金结算通知书（卡账）"
            keyword="资金流出单位"
          ></stampDownload> -->
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import {
  queryReport,
  queryDeptOrg
} from '@/views/reportstatistics/components/service'
import ePage from '@/views/reportstatistics/components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'
import stampDownload from '../../riritongmanagement/components/stampDownload'

export default {
  data() {
    return {
      loading: false,
      rules: {
        // ddpETCTime: [
        //   { required: true, message: '请选择日期', trigger: 'change' }
        // ],
        ddpETCStartTime: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        ddpETCEndTime: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      search: {
        name: 'dailyAccessConsumerReport ', // 报表名称
        // ddpETCTime: '',
        ddpETCStartTime: '',
        ddpETCEndTime: ''
      },
      // formProperties: {
      //   ddpETCTime: {
      //     ...datePickerSchema.datePicker,
      //     fieldLabel: '选择日期',
      //     fieldKey: 'ddpETCTime',
      //     fieldProps: {
      //       ...datePickerSchema.datePicker.fieldProps,
      //       pickerOptions: datePickerOptions
      //     }
      //   }
      // },
      formProperties: {
        ddpETCStartTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择开始日期',
          fieldKey: 'ddpETCStartTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            // pickerOptions: datePickerOptions
          }
        },
        ddpETCEndTime: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择结束日期',
          fieldKey: 'ddpETCEndTime',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            // pickerOptions: datePickerOptions
          }
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
    stampDownload
  },

  computed: {},
  // created() {
  //   this.search.ddpETCTime = moment()
  //     .subtract(1, 'day')
  //     .format('YYYY-MM-DD')
  // },
  methods: {
    beforeReportHandle() {
      let check = true
      if (
        moment(this.search.ddpETCStartTime).isAfter(this.search.ddpETCEndTime)
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      if (
        moment(this.search.ddpETCEndTime).diff(
          moment(this.search.ddpETCStartTime),
          'days'
        ) > 6
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不能大于一个星期'
        })
        check = false
      }
      return check
    },
    onSearchHandle() {
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let check = this.beforeReportHandle()
          if (!check) return

          let params = JSON.parse(JSON.stringify(this.search))
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
