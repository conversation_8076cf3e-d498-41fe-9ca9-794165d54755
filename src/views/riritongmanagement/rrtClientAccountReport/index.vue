<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:
  * @author:zhangys
  * @date:2023/05/19 13:13:59
-->
<template>
  <ePage style="padding:20px">
    <div slot="report-search" style="background-color: #fff;">
      <el-tabs v-model="activeIndex" type="border-card">
        <el-tab-pane
          label="日日通(担保产品)-ETC消费结算通知书(卡账+客账)"
          name="consumptionSettlement"
        >
          <consumptionSettlement></consumptionSettlement>
        </el-tab-pane>

        <el-tab-pane
          label="日日通(担保产品)-代收ETC保证金结算通知书(卡账)"
          name="marginSettlementCard"
        >
          <marginSettlementCard></marginSettlementCard>
        </el-tab-pane>
        <el-tab-pane
          label="日日通(担保产品)-代收ETC保证金结算通知书(卡账+客账)"
          name="marginSettlement"
        >
          <marginSettlement></marginSettlement>
        </el-tab-pane>
        <el-tab-pane
          label="日日通(担保产品)-ETC充值保证金收款统计报表"
          name="depositCollect"
        >
          <depositCollect></depositCollect>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="report-search" style="background-color: #fff;margin-top:30px;">
      <el-tabs v-model="activeIndexDiff" type="border-card">
        <el-tab-pane
          label="日日通(担保产品)-代收ETC保证金结算通知书(轧差后报表，不展示轧差数据)"
          name="jhOverdueSuretyReport"
        >
          <marginSettlement reportName="jhOverdueSuretyReport" timeKey="notice_date"  title="日日通(担保产品)-代收ETC保证金结算通知书(轧差后报表，不展示轧差数据)"></marginSettlement>
        </el-tab-pane>
        <el-tab-pane label="日日通(担保产品)-轧差数据统计报表" name="jhOverdueOffsetReport">
          <depositCollectNew :reportName="'jhOverdueOffsetReport'" :timeKey="['JHStartTime','JHEndTime']"></depositCollectNew>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div slot="report-search" style="background-color: #fff;margin-top:30px;">
      <el-tabs v-model="activeIndexXf" type="border-card">
        <el-tab-pane
          label="捷通自营日日通-ETC消费报表(卡账+客账)"
          name="dailyAccessConsumerReport"
        >
          <settlementXf reportName="dailyAccessConsumerReport" timeKey="notice_date"  title="日日通(担保产品)-代收ETC保证金结算通知书(轧差后报表，不展示轧差数据)"></settlementXf>
        </el-tab-pane>
      </el-tabs>
    </div>
  </ePage>
</template>

<script>
import ePage from '@/views/reportstatistics/components/ePage.vue'

import consumptionSettlement from './consumptionSettlement'
import marginSettlementCard from './marginSettlementCard'
import marginSettlement from './marginSettlement'
import settlementXf from './settlementXf'
import depositCollect from './depositCollect'
import depositCollectNew from './depositCollectNew.vue'
export default {
  data() {
    return {
      activeIndex: 'consumptionSettlement',
      activeIndexDiff: 'jhOverdueSuretyReport',
      activeIndexXf: 'dailyAccessConsumerReport',
      options: []
    }
  },

  components: {
    ePage,
    consumptionSettlement,
    marginSettlementCard,
    depositCollect,
    marginSettlement,
    depositCollectNew,
    settlementXf
  },

  computed: {},
  created() {},
  methods: {}
}
</script>
<style>
.report-search .el-tabs--border-card {
  box-shadow: none !important;
}
.report-search .el-tabs__content {
  padding: 0 !important;
}
</style>
