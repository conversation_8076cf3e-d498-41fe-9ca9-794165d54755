<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通(担保产品)-轧差数据统计报表
-->
<template>

  <!-- 搜索栏 -->
  <dart-search slot="report-search"
               ref="searchForm"
               :formSpan="24"
               :labelTextLength="8"
               :searchOperation="false"
               :fontWidth="1"
               label-position="right"
               :model="search"
               :rules="rules">
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item :key="item.fieldKey"
                          :label="item.fieldLabel"
                          :prop="item.fieldKey">
          <template v-if="item.element != 'custom'">
            <searchField :fieldProps="item.fieldProps"
                         :fieldOptions="item"
                         :ref="item.ref"
                         v-model="search[item.fieldKey]"></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true"
                        :colElementNum="3">
        <div class="g-flex g-flex-end">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>

</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema,
} from '@/components/schemaQuery/schema'
import {
  queryReport,
  queryDeptOrg,
} from '@/views/reportstatistics/components/service'
import ePage from '@/views/reportstatistics/components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'

export default {
    props:{
    reportName:{
      type:String,
      default:'clientCardBatchRechargeNewReport'
    },
    timeKey:{
      type:Array
    }
  },
  data() {
    return {
      loading: false,
      rules: {
        trans_date_sta: [
          { required: true, message: '请选择开始日期', trigger: 'change' },
        ],
        trans_date_end: [
          { required: true, message: '请选择结束日期', trigger: 'change' },
        ],
      },
      search: {
        name: this.reportName, // 报表名称
        trans_date_sta: '',
        trans_date_end: '',
      },
      formProperties: {
        trans_date_sta: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择开始日期',
          fieldKey: 'trans_date_sta',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
        trans_date_end: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择结束日期',
          fieldKey: 'trans_date_end',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions,
          },
        },
      },
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage,
  },

  computed: {},
  created() {},
  methods: {
    onValid() {
      if (
        moment(this.search.trans_date_sta).isAfter(this.search.trans_date_end)
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return
      }

      return true
    },
    onSearchHandle() {
      this.$refs['searchForm'].$children[0].validate((valid) => {
        if (valid) {
          if (!this.onValid()) return
          let params = JSON.parse(JSON.stringify(this.search))
          params.notice_date = params.ddpETCTime
          if(this.timeKey){
            params[this.timeKey[0]] =  params.trans_date_sta
            params[this.timeKey[1]] =  params.trans_date_end
            delete params.trans_date_sta
            delete params.trans_date_end
          }
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function () {
        this.$refs['searchForm'].resetForm()
      })
    },
  },
}
</script>
<style lang="sass"></style>
