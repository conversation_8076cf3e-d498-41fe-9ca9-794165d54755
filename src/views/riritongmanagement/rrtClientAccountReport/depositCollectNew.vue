<template>
  <!-- 搜索栏 -->
  <dart-search
    slot="report-search"
    ref="searchForm"
    :formSpan="24"
    :labelTextLength="8"
    :searchOperation="false"
    :fontWidth="1"
    label-position="right"
    :model="search"
    :rules="rules"
  >
    <template slot="search-form">
      <template v-for="item in formProperties">
        <dart-search-item
          :key="item.fieldKey"
          :label="item.fieldLabel"
          :prop="item.fieldKey"
        >
          <template v-if="item.element != 'custom'">
            <searchField
              :fieldProps="item.fieldProps"
              :fieldOptions="item"
              :ref="item.ref"
              v-model="search[item.fieldKey]"
            ></searchField>
          </template>
        </dart-search-item>
      </template>

      <dart-search-item :is-button="true" :colElementNum="3">
        <div class="g-flex g-flex-end">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </div>
      </dart-search-item>
    </template>
  </dart-search>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import {
  datePickerSchema,
  cascaderSchema,
  inputSchema,
  selectSchema,
  customSchema
} from '@/components/schemaQuery/schema'
import {
  queryReport,
  queryDeptOrg
} from '@/views/reportstatistics/components/service'
import ePage from '@/views/reportstatistics/components/ePage.vue'
var moment = require('moment')
import { datePickerOptions } from '@/components/schemaQuery/tool'
import { getCycle } from '@/api/infoUpload'

let checkNum = (rule, value, callback) => {
  if (!value) {
    callback()
  }
  let reg = /^[1-9]\d*$/
  if (!reg.test(value)) {
    callback(new Error('请输正整数'))
  } else {
    callback()
  }
}

export default {
  props: {
    reportName: {
      type: String,
      default: 'clientCardBatchRechargeNewReport'
    },
    timeKey: {
      type: Array
    }
  },
  data() {
    return {
      loading: false,
      rules: {
        JHCurrentId: [
          // { required: true, message: '周期不能为空', trigger: 'change' }
          // { validator: checkNum, trigger: 'blur' }
        ]
      },
      search: {
        name: this.reportName, // 报表名称
        trans_date_sta: '',
        trans_date_end: '',
        JHCardNo: '',
        JHCurrentId: ''
      },
      cycleOptions: []
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField,
    ePage
  },

  computed: {
    formProperties() {
      return {
        trans_date_sta: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择开始日期',
          fieldKey: 'trans_date_sta',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        trans_date_end: {
          ...datePickerSchema.datePicker,
          fieldLabel: '选择结束日期',
          fieldKey: 'trans_date_end',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps,
            pickerOptions: datePickerOptions
          }
        },
        JHCardNo: {
          ...inputSchema,
          fieldLabel: '卡号',
          fieldKey: 'JHCardNo',
          fieldProps: {
            clearable: true,
            type: 'text',
            placeholder: '请输入卡号'
          }
        },
        JHCurrentId: {
          ...selectSchema,
          fieldLabel: '周期',
          fieldKey: 'JHCurrentId',
          fieldProps: {
            // 传给渲染的组件的 props
            clearable: true,
            placeholder: '请选择',
            options: this.cycleOptions
          }
        }
      }
    }
  },
  created() {
    this.getOptions()
  },
  methods: {
    onValid() {
      let {
        trans_date_sta,
        trans_date_end,
        JHCurrentId,
        JHCardNo
      } = this.search
      if (moment(trans_date_sta).isAfter(trans_date_end)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (!(trans_date_sta && trans_date_end) && !JHCardNo && !JHCurrentId) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '时间范围,卡号，周期不能都为空'
        })
        return
      }

      return true
    },
    onSearchHandle() {
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          if (!this.onValid()) return
          let params = JSON.parse(JSON.stringify(this.search))
          // 周期处理
          let selectObj = this.cycleOptions.filter(
            item => item.currentLastId == params.JHCurrentId
          )[0]
          if (selectObj) {
            params.JHStartTime = selectObj.jHStartTime
            params.JHEndTime = selectObj.jHEndTime
          }

          // params.notice_date = params.ddpETCTime
          // params.JHCycle = Number(params.JHCycle) || ''
          if (this.timeKey) {
            params[this.timeKey[0]] =
              params.trans_date_sta && params.trans_date_end
                ? params.trans_date_sta
                : params[this.timeKey[0]]
            params[this.timeKey[1]] =
              params.trans_date_sta && params.trans_date_end
                ? params.trans_date_end
                : params[this.timeKey[1]]
            delete params.trans_date_sta
            delete params.trans_date_end
          }
          console.log(params)
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    },
    async getOptions(params = {}) {
      let res = await getCycle(params)
      let data = res.data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.currentLastId
        }
      })
      this.cycleOptions = data
    }
  }
}
</script>
<style lang="sass"></style>
