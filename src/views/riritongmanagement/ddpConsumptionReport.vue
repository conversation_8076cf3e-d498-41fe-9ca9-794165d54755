<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）ETC消费结算月报表 reportName:consumeSettleMonthReport
  * @author:zhangys
  * @date:2022/05/19 15:10:25
!-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="统计日期"
                          prop="searchDate">
          <el-date-picker v-model="search.searchDate"
                          type="month"
                          value-format="yyyy-MM"
                          :clearable='false'
                          placeholder="选择日期">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
          </div>

        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      search: {
        name: 'consumeSettleMonthReport', //报表名称
        ddp_year: '',
        ddp_month: '',
        searchDate: '',
      },
      tableHeight: 0,
      rules: {
        searchDate: [
          { required: true, message: '请选择统计日期', trigger: 'change' },
        ],
      },
    }
  },
  created() {
    this.search.searchDate = moment(new Date())
      .subtract(1, 'months')
      .format('YYYY-MM')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      let times = this.search.searchDate.split('-')
      this.search.ddp_year = times[0]
      this.search.ddp_month = times[1]
      delete this.search.searchDate
      this.sendReportRequest()
    },

    sendReportRequest() {
      this.loading = true
      request({
        url: api.report,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
