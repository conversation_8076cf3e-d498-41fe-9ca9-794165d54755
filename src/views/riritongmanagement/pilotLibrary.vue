<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通0元预存试点库
  * @author:zhang<PERSON>
  * @date:2023/01/30 10:55:31
-->
<template>
  <div class="toll-record">
    <div class="search">
      <dart-search :formSpan="24"
                   :gutter="20"
                   ref="searchForm1"
                   label-position="right"
                   :model="search"
                   :fontWidth="2">
        <template slot="search-form"
                  style="padding-left: 10px">

          <dart-search-item label="车牌号："
                            prop="carNo">
            <el-input v-model="search.carNo"
                      clearable
                      placeholder="请输入车牌号"></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色："
                            prop="carColor">
            <el-select v-model="search.carColor"
                       clearable
                       placeholder="请选择">
              <el-option v-for="item in licenseColorOption"
                         :key="item.index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="用户名称："
                            prop="userName">
            <el-input v-model="search.userName"
                      clearable
                      placeholder="请输入用户名称"></el-input>
          </dart-search-item>
          <div class="collapse-wrapper"
               v-show="isCollapse">
            <dart-search-item label="手机号："
                              prop="mobile">
              <el-input v-model="search.mobile"
                        clearable
                        placeholder="请输入手机号码"></el-input>
            </dart-search-item>
            <dart-search-item label="创建时间起始："
                              prop="startTime">
              <el-date-picker type="datetime"
                              placeholder="选择日期时间"
                              v-model="search.startTime"
                              :picker-options="pickerOptions">
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="创建时间截至："
                              prop="endTime">
              <el-date-picker type="datetime"
                              placeholder="选择日期时间"
                              default-time="23:59:59"
                              v-model="search.endTime"
                              :picker-options="pickerOptions">
              </el-date-picker>
            </dart-search-item>
            <dart-search-item label="ETC卡号："
                              prop="cardNo">
              <el-input v-model="search.cardNo"
                        clearable
                        placeholder="请输入ETC卡号"></el-input>
            </dart-search-item>
            <dart-search-item label="卡片状态："
                              prop="state">
              <el-select v-model="search.cardStatus"
                         clearable
                         placeholder="请选择">
                <el-option v-for="item in cpuStatus"
                           :key="item.index"
                           :label="item.label"
                           :value="item.value" />
              </el-select>
            </dart-search-item>

          </div>
          <dart-search-item :is-button="true"
                            :span="24">
            <div class="btn-wrapper">
              <el-button type="primary"
                         size="mini"
                         native-type="submit"
                         @click="onSearchHandle"><i class="el-icon-search"></i> 搜索</el-button>
              <el-button size="mini"
                         @click="onReSetHandle">重置</el-button>
              <span class="collapse"
                    v-if="!isCollapse"
                    @click="isCollapse = true">展开</span>
              <span class="collapse"
                    v-else
                    @click="isCollapse = false">收起</span>
            </div>
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table v-loading="loading"
                :data="tableData"
                :align="center"
                :header-align="center"
                border
                height="100%"
                style="width: 100%; margin-bottom: 20px"
                :row-style="{ height: '40px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '40px' }"
                :header-cell-style="{ padding: '0px' }">
        <el-table-column prop="userName"
                         align="center"
                         width="140"
                         label="用户名称" />
        <el-table-column prop="mobile"
                         align="center"
                         width="120"
                         label="手机号码" />
        <el-table-column prop="cardNo"
                         align="center"
                         width="220"
                         label="ETC卡号" />
        <el-table-column prop="cardStatus"
                         align="center"
                         width="100"
                         label="ETC卡状态">
          <template slot-scope="scope">
            {{getCpuStatus(scope.row.cardStatus)}}
          </template>
        </el-table-column>
        <el-table-column prop="carNo"
                         align="center"
                         width="130"
                         label="车牌号" />
        <el-table-column prop="carColor"
                         align="center"
                         label="车牌颜色">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.carColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="createTime"
                         align="center"
                         width="160"
                         label="办理时间" />
        <el-table-column prop="cardAccountAmount"
                         align="center"
                         label="卡账余额(元)"
                         width="120">
          <template slot-scope="scope">
            {{moneyFilter(scope.row.cardAccountAmount)}}
          </template>
        </el-table-column>
        <el-table-column prop="signCode"
                         align="center"
                         width="200"
                         label="签约编号">
          <template slot-scope="scope">
            <div style="color:#409eff;cursor:pointer;text-decoration:underline">
              <span @click="previewContact(scope.row)">{{scope.row.signCode}}</span>
              <el-tooltip class="item"
                          effect="dark"
                          content="点击预览电子协议"
                          placement="top"
                          v-if="scope.row.signCode">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="productType"
                         align="center"
                         width="190"
                         label="产品类型">
          <template slot-scope="scope">
            {{scope.row.productType=='8'?'捷通日日通0元预存记账卡':'未知'}}
          </template>
        </el-table-column>
        <el-table-column fixed="right"
                         label="操作"
                         header-align="center"
                         width="140"
                         align="center">
          <template slot-scope="scope">
            <el-button type="primary"
                       size="mini"
                       @click="removeConfirm(scope.row)">卡类型转换</el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>
    <div class="pagination">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="search.pageNum"
                     :page-sizes="[10, 20, 50]"
                     :page-size="search.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    
    <!-- 合同明细 -->
    <dartSlide
      :visible.sync="detailVisible"
      title="合同明细"
      v-transfer-dom
      width="60%"
      :maskClosable="true"
    >
      <contract-detail :detailItem="detailItem" :detailVisible="detailVisible" />
    </dartSlide>
  </div>
</template>
  
  <script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import { licenseColorOption, cpuStatus } from '@/common/const/optionsData.js'
import dartSlide from '@/components/dart/Slide/index.vue';
import contractDetail from './components/contractDetail.vue';
import request from '@/utils/request'
import api from '@/api/index'
import float from '@/common/method/float.js'
import { getCpuStatus, getVehicleColor } from '@/common/method/formatOptions'
var moment = require('moment')
export default {
  name: 'PilotLibrary',
  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    contractDetail,
  },
  data() {
    return {
      loading: false,
      isCollapse: false,
      center: 'center',
      total: '',
      search: {
        carColor: '', //车牌颜色
        carNo: '', //车牌
        cardNo: '', //卡号
        mobile: '',
        userName: '',
        cardStatus: '',
        startTime: '', //申请开始时间
        endTime: '', //申请截止时间
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      detailVisible: false, // 合同明细抽屉状态
      detailItem: {},

      licenseColorOption,
      cpuStatus,
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        },
      },
    }
  },
  watch: {},
  created() {
    this.getPilotLibraryList()
  },
  methods: {
    getCpuStatus,
    getVehicleColor,
    getPilotLibraryList() {
      if (!this.validate()) return
      this.loading = true
      let params = JSON.parse(JSON.stringify(this.search))
      params.startTime = params.startTime
        ? moment(params.startTime).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.endTime = params.endTime
        ? moment(params.endTime).format('YYYY-MM-DD HH:mm:ss')
        : ''

      request({
        url: api.pilotLibrary,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch((err) => {
          this.loading = false
        })
    },
    validate() {
      if (
        (!this.search.startTime && this.search.endTime) ||
        (this.search.startTime && !this.search.endTime)
      ) {
        this.$message({
          message: '请选择完整时间段',
          type: 'warning',
        })
        return false
      }
      if (
        this.search.startTime &&
        this.search.endTime &&
        moment(this.search.startTime).isAfter(this.search.endTime)
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常',
        })
        return false
      }
      return true
    },
    removeConfirm(value) {
      let _this = this
      const h = _this.$createElement
      _this.$msgbox({
        title: '提示',
        message: h('div', null, [
          h(
            'p',
            {
              style: 'font-size: 16px;font-weight: 500;padding-bottom: 10px;',
            },
            '卡种类型转换72小时后，车辆将移出试点库，移出后将无法进入试点库，是否确认转换'
          ),
        ]),
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        showClose: false,
        callback(action) {
          if (action == 'confirm') {
            _this.removeHandle(value)
          }
        },
      })
    },
    removeHandle(value) {
      this.loading = true
      let params = {
        id: value.id,
        mobile:value.mobile
      }

      request({
        url: api.removePilotLibrary,
        method: 'post',
        data: params,
      })
        .then((res) => {
          this.loading = false
          if (res.code == 200) {
            this.getPilotLibraryList()
            this.$message({
              message: '操作成功！',
              type: 'success',
            })
          }
        })
        .catch((err) => {
          this.loading = false
        })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getPilotLibraryList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getPilotLibraryList()
    },
    //重置
    onReSetHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
      this.getPilotLibraryList()
    },
    onSearchHandle() {
      this.search.pageNum = 1
      this.getPilotLibraryList()
    },
    moneyFilter(val) {
      let value = val
      if (value == 0 || !val) return val
      value = float.div(val, 100)
      return value
    },
    previewContact(value) {
      if (value.signVersion === 'V2') {
        console.log('-------------', value);
        this.detailVisible = true;
        this.detailItem = { ...value };
      } else {
        let params = {
            conId: value.signCode,
            vehicleCode: value.carNo,
            vehicleColor: value.carColor,
        }
        request({
            url: api.previewSign,
            method: 'post',
            data: params,
        })
            .then((res) => {
            // this.loading = false
            console.log('返回的合同', res)
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
                res.data.viewUrl,
                '_blank',
                'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
            })
            .catch((err) => {
            // this.loading = false
            console.log(err)
            })
      }
    },
  },
}
</script>
  
  <style lang="scss" scoped>
// .toll-record {
//   padding: 20px;
//   .table {
//     margin: 0px 0 10px 0;
//   }
.toll-record {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    margin-top: 10px;
    color: red;
  }
  .btn-wrapper {
    margin-left: 40px;
    margin-top: 10px;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
    width: calc(100% - 150px) !important;
  }
  ::v-deep.dart-search-wrapper .dart-search-container .el-form-item__label {
    width: 140px !important;
    white-space: nowrap;
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
  