<template>
  <div class="user">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      v-permisaction="[item.permisaction]"
      :formConfig="item.formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="item.rules"
      :ref="item.ref"
      @onSearchHandle="onSearchHandle"
      :btnSpan="item.btnSpan || 6"
      :isDownload="item.isDownload"
      :stampConfig="item.stampConfig"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import moment from 'moment'
import { reportListFn } from './model'
import { getCycle } from '@/api/infoUpload'
import { getDict } from '@/api/dict'

export default {
  components: {
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      },
      tableHeight: 0,
      timeField: ['invStaTime'],
      cycleOptions: [],
      typeCycleOptions: [],
      reportDownload: false
    }
  },
  computed: {
    reportList() {
      return reportListFn(this)
    }
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.invStaTime).isAfter(fromData.invEndTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      return check
    },
    async getOptions(params = {}) {
      let res = await getCycle(params)
      let data = res.data.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.currentLastId
        }
      })
      if (Object.getOwnPropertyNames(params).length === 0) {
        this.cycleOptions = data
      } else {
        this.typeCycleOptions = data
      }
    },
    //
    async getDict() {
      let params = {
        businessType: 'DOWN_STAMP'
      }
      let { data } = await getDict(params)
      if (data[0].dictCode == 1) {
        this.reportDownload = true
      }
    }
  },
  created() {
    this.getDict()
    this.getOptions()
    this.getOptions({ type: '0' })
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  ::v-deep .el-form-item__label {
    width: 170px !important;
  }
  ::v-deep .dart-search-wrapper .el-form-item__content {
    width: calc(100% - 170px);
  }
}
</style>