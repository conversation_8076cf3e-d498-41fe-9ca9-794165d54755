import { staticStampDownload } from '@/api/dict'

export const reportListFn = (state) => {
  return [
    {
      id: 1,
      name: 'jhOverdueConsumeTotalReport',
      title: '日日通x吉鸿担保产品—逾期消费数据汇总统计表',
      ref: 'jhOverdueConsumeTotalReport',
      permisaction: 'ReportQuery1',
      rules: {
        JHCurrentId: [
          { required: true, message: '请选择周期', trigger: 'change' }
        ]
      },
      formConfig: [
        {
          type: 'select',
          field: 'JHCurrentId',
          label: '逾期还款统计周期',
          default: '',
          options: state.typeCycleOptions,
          callBack: (val) => {
            let that = state.$refs.jhOverdueConsumeTotalReport[0]
            let selectObj = state.typeCycleOptions.filter(item => item.currentLastId == val)[0]
            that.search.JHStartTime = selectObj.jHStartTime
            that.search.JHEndTime = selectObj.jHEndTime
            that.search.jHCurrentNum = selectObj.jHCurrentNum

          }
        },
      ]
    },
    {
      id: 2,
      name: 'jhOverdueUserReport',
      title: '日日通x吉鸿担保产品—逾期用户明细表',
      ref: 'jhOverdueUserReport',
      permisaction: 'ReportQuery2',
      rules: {
        JHCurrentId: [
          { required: true, message: '请选择周期', trigger: 'change' }
        ]
      },
      formConfig: [
        {
          type: 'select',
          field: 'JHCurrentId',
          label: '逾期还款统计周期',
          default: '',
          options: state.typeCycleOptions,
          callBack: (val) => {
            let that = state.$refs.jhOverdueUserReport[0]
            let selectObj = state.typeCycleOptions.filter(item => item.currentLastId == val)[0]
            that.search.JHStartTime = selectObj.jHStartTime
            that.search.JHEndTime = selectObj.jHEndTime
            that.search.jHCurrentNum = selectObj.jHCurrentNum
          }
        },
      ]
    },
    {
      id: 3,
      name: 'jhOverdueUserRefundReport',
      title: '日日通x吉鸿担保产品—逾期用户还款明细表',
      ref: 'jhOverdueUserRefundReport',
      permisaction: 'ReportQuery3',
      formConfig: [
        {
          type: 'select',
          field: 'JHCurrentId',
          label: '逾期还款统计周期',
          default: '',
          options: state.cycleOptions,
          callBack: (val) => {
            let that = state.$refs.jhOverdueUserRefundReport[0]
            let selectObj = state.cycleOptions.filter(item => item.currentLastId == val)[0]
            that.search.JHStartTime = selectObj.jHStartTime
            that.search.JHEndTime = selectObj.jHEndTime
          }
        },
      ],
      rules: {
        JHCurrentId: [
          { required: true, message: '请选择周期', trigger: 'change' }
        ]
      }
    },
    {
      id: 4,
      name: 'jhOverdueCheckConsumeTotalReport',
      title: '日日通x吉鸿担保产品—逾期消费数据结算报表（用于与吉鸿公司进行结算）',
      ref: 'jhOverdueCheckConsumeTotalReport',
      permisaction: 'report',
      isDownload: true,
      btnSpan: 8,
      stampConfig: {
        name: 'jhOverdueCheckConsumeTotalReport', //报表名称
        fileName: 'jhOverdueCheckConsumeTotalReport',
        keyword: '逾期'
      },
      formConfig: [
        {
          type: 'select',
          field: 'JHCurrentId',
          label: '逾期还款统计周期',
          default: '',
          options: state.typeCycleOptions,
          callBack: (val) => {
            let that = state.$refs.jhOverdueCheckConsumeTotalReport[0]
            let selectObj = state.typeCycleOptions.filter(item => item.currentLastId == val)[0]
            that.search.JHStartTime = selectObj.jHStartTime
            that.search.JHEndTime = selectObj.jHEndTime
            that.search.jHCurrentNum = selectObj.jHCurrentNum
          }
        },
      ],
      rules: {
        JHCurrentId: [
          { required: true, message: '请选择周期', trigger: 'change' }
        ]
      }
    },
    {
      id: 5,
      name: 'jhOverdueCheckConsumeDetailReport',
      title: '日日通x吉鸿担保产品—逾期消费数据结算明细报表（用于与吉鸿公司进行结算）',
      ref: 'jhOverdueCheckConsumeDetailReport',
      permisaction: 'report',
      isDownload: state.reportDownload,
      btnSpan: state.reportDownload ? 8 : '',
      stampConfig: {
        name: 'jhOverdueCheckConsumeDetailReport', //报表名称
        fileName: 'jhOverdueCheckConsumeDetailReport',
        keyword: '逾期',
        apiMethod:staticStampDownload
      },
      formConfig: [
        {
          type: 'select',
          field: 'JHCurrentId',
          label: '逾期还款统计周期',
          default: '',
          options: state.typeCycleOptions,
          callBack: (val) => {
            let that = state.$refs.jhOverdueCheckConsumeDetailReport[0]
            let selectObj = state.typeCycleOptions.filter(item => item.currentLastId == val)[0]
            that.search.JHStartTime = selectObj.jHStartTime
            that.search.JHEndTime = selectObj.jHEndTime
            that.search.jHCurrentNum = selectObj.jHCurrentNum
          }
        },
      ],
      rules: {
        JHCurrentId: [
          { required: true, message: '请选择周期', trigger: 'change' }
        ]
      }
    }
  ]
}