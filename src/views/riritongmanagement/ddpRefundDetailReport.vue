<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:ETC保证金结算通知书-退款明细 reportName:hsDdpRechargeRefundDetail
  * @author:zhangys
  * @date:2022/05/11 10:57:54
!-->
<template>
    <div class="user">
        <dart-search ref="searchForm1" :formSpan="24" :searchOperation="false" :fontWidth="1" label-position="right"
            :model="search" :rules="rules">
            <template slot="search-form">
                <dart-search-item label="统计日期" prop="thedate">
                    <el-date-picker v-model="search.thedate" type="date" :clearable="false" placeholder="选择日期">
                    </el-date-picker>
                </dart-search-item>
                <dart-search-item isButton>
                    <div class="g-flex">
                        <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索
                        </el-button>
                        <el-button size="mini" @click="onResultHandle">重置</el-button>
                    </div>
                </dart-search-item>
            </template>
        </dart-search>
        <div class="list" :style="`height:${tableHeight}px`">
            <img src="@/image/bg-left.png" />
        </div>
    </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
    components: {
        dartSearch,
        dartSearchItem,
    },
    data() {
        return {
            loading: false,
            search: {
                thedate: '',
            },
            tableHeight: 0,
            rules: {
                thedate: [
                    { required: true, message: '请选择统计日期', trigger: 'change' },
                ],
            },
        }
    },
    created() {
        this.search.thedate = moment().subtract(1, 'day').format('YYYY-MM-DD')
    },
    mounted() { },
    methods: {
        onSearchHandle() {
            this.$refs['searchForm1'].$children[0].validate((valid) => {
                if (valid) {
                    this.sendReportRequest()
                } else {
                    return false
                }
            })
        },
        onResultHandle() {
            this.$nextTick(function () {
                this.$refs['searchForm1'].resetForm()
            })
        },

        sendReportRequest() {
            this.loading = true
            let data = {
                ddpETCTime: moment(this.search.thedate).format('YYYY-MM-DD'),
                name: 'hsDdpRechargeRefundDetail',
            }

            request({
                url: api.report,
                method: 'post',
                data: data,
            })
                .then((res) => {
                    if (res.code == 200) {
                        let url = res.data
                        let decodeUrl = decode(url)
                        // console.log(decodeUrl,'地址')
                        let clientWidth = document.documentElement.clientWidth
                        let clientHeight = document.documentElement.clientHeight
                        window.open(
                            decodeUrl,
                            '_blank',
                            'width=' +
                            clientWidth +
                            ',height=' +
                            clientHeight +
                            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
                        )
                    }
                })
                .catch(() => { })
        },
    },
}
</script>

<style lang="scss" scoped>
.user {
    padding: 20px;

    .list {
        width: 100%;
        text-align: center;

        img {
            width: 50%;
        }
    }
}
</style>
