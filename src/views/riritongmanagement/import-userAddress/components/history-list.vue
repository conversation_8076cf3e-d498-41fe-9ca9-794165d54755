<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        showIndex
        tableHeight="350"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            type="text"
            size="mini"
            @click="handelRow(scope, 'downLoad')"
            >下载附件</el-button
          >
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import { chekTemplateList } from '@/api/infoUpload'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      // listColoumns,
      api: chekTemplateList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageNum',
      dataKey: 'data'
    }
  },
  computed: {
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 操作
    handelRow(row, type) {
      if (type == 'downLoad') {
        let { fileUrl } = row
        window.open(fileUrl)
      }
    },
    /**
     * 下载
     */
    async downloadClick(row) {
      try {
        console.log(fileUrl,row)
        const response = await fetch(fileUrl)
        // window.open(fileUrl)
        let blob = await response.blob()
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = '' //下载的文件名
        document.body.appendChild(link)
        link.click() // 执行下载
        document.body.removeChild(link) // 释放标签
      } catch (err) {
        console.error(err, 1231)
      }
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .table {
    margin-top: 0;
    padding-top: 0;
  }
  ::v-deep .fontWidth {
    display: flex;
    label {
      width: auto !important;
      margin-left: 18px;
    }
    .el-col-24 {
      padding: 5px !important;
    }
    div {
      width: auto;
    }
    .el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>