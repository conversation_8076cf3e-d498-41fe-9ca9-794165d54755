<template>
  <div class="conent-wrap" v-loading="loading">
    <div class="wrap" style="margin-bottom: 15px;">
      <div class="wrap-title">用户地址复核导入</div>
      <el-form ref="form" :model="formData" label-width="90px" :rules="rules">
        <el-form-item label="上传附件" prop="file">
          <el-upload
            style="width:60%;"
            class="upload"
            ref="upload"
            drag
            action="action"
            :on-remove="handleRemove"
            accept=".xlsx,.xls"
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="onChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <!-- <div class="el-upload__tip" slot="tip">
              支持上传 Rar、Zip、Excel、Word、PDF等格式，rar不支持预览
            </div> -->
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="bottom-btn g-flex">
        <!-- <el-button @click="handleSubmit" :disabled="fileList.length <= 0" type="primary" size="mini"
          >上传</el-button
        > -->
        <el-button
          size="small"
          type="primary"
          @click="handleSubmit"
          :disabled="fileList.length <= 0"
          ><i class="el-icon-upload"></i> 导入</el-button
        >
        <el-button size="small" type="warning" @click="downloadExcel"
          ><i class="el-icon-download"></i> 下载导入样表</el-button
        >
      </div>
    </div>

    <div class="wrap">
      <div class="wrap-title" style="margin-bottom: 0;">历史导入记录</div>
      <HistoryList ref="HistoryList"></HistoryList>
    </div>
  </div>
</template>

<script>
import HistoryList from './components/history-list.vue'
import { downloadTemplate, uploadTemplateData } from '@/api/infoUpload'

export default {
  components: {
    HistoryList
  },
  data() {
    return {
      formData: {
        file: ''
      },
      fileList: [],
      rules: {
        file: [{ required: true, message: '附件不能为空', trigger: 'blur' }]
      },
      loading: false
    }
  },
  methods: {
    handleSubmit() {
      console.log(this.fileList, 'fileList')
      this.$refs.form.validate(async valid => {
        console.log(this.formData)
        if (valid) {
          let params = new FormData()
          params.append('file', this.formData.file)
          // let fullFilePath = await this.submitUpload()
          // if (!fullFilePath) return
          this.loading = true
          let res = await uploadTemplateData(params)
          this.loading = false
          if (res.code == 200) {
            this.$refs.HistoryList.getTableData()
            this.$alert('上传成功', '提示', {
              dangerouslyUseHTMLString: true,
              showClose: false,
              confirmButtonText: '确定'
            })
            this.formData = {}
            this.handleRemove()
          }
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    handleExceed(files, fileList) {
      this.$message.error('文件数量超出,仅上传一个文件')
    },
    /**
     * 文件变化
     */
    onChange(files) {
      this.$refs.upload.clearFiles()
      this.fileList.push({ ...files, name: files.name, status: 'success' })
      this.formData.file = files.raw
    },
    /**
     * 文件删除
     */
    handleRemove(val) {
      console.log(val, 'handleRemove')
      this.formData.file = ''
      this.fileList = []
    },
    /**
     * 样表下载
     */
    async downloadExcel() {
      this.startLoading()
      try {
        let res = await downloadTemplate()
        let fileObj = {
          fileName: '复核导入模版.xls'
        }
        const link = document.createElement('a')
        let blob = new Blob([res]) //构造一个blob对象来处理数据
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = `${fileObj.fileName}` //下载的文件名
        document.body.appendChild(link)
        link.click() // 执行下载
        document.body.removeChild(link) // 释放标签
        this.endLoading()
      } catch (err) {
        console.log(err)
        this.endLoading()
      }
    }
  },
  created() {
    // this.initDeatail()
  }
}
</script>

<style lang="scss" scoped>
.conent-wrap {
  padding: 10px 24px;
  .wrap {
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    .wrap-title {
      margin: 0 0 20px;
      padding: 5px 10px 10px 10px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  ::v-deep .el-upload__tip {
    margin-top: 0;
    line-height: 20px;
  }
  .bottom-btn {
    margin: 20px 0 10px 0;
    padding-left: 150px;
  }
}
</style>