<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）车辆办理情况日报表 2022.7.04需求 盖章并下载
  * @author:zhangys&zcq
  * @date:2022/05/11 10:58:55
!-->
<template>
  <div class="report-export">
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <div class="search-title">{{ title }}</div>
        <dart-search-item label="统计日期" prop="searchDate" v-if="!type">
          <el-date-picker
            v-model="search.searchDate"
            type="date"
            :clearable="false"
            placeholder="选择日期"
            @change="dayChange"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item
          :label="customLabel ? customLabel : '统计日期'"
          prop="searchDate"
          v-if="type == 'month'"
        >
          <el-date-picker
            v-model="search.searchDate"
            type="month"
            value-format="yyyy-MM"
            :clearable="false"
            placeholder="选择日期"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item
          label="选择日期"
          prop="searchDate"
          v-if="type == 'date'"
        >
          <el-date-picker
            v-model="search.searchDate"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="rangeChange"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item
          label="选择日期"
          prop="searchDate"
          v-if="type == 'datetimerange'"
        >
          <el-date-picker
            v-model="search.searchDate"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item
          label="选择年份"
          prop="yearValue"
          v-if="type == 'quarter'"
        >
          <el-date-picker
            v-model="search.yearValue"
            type="year"
            value-format="yyyy"
            :clearable="false"
            placeholder="选择年份"
            @change="yearChange"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item
          label="选择季度"
          prop="quarterValue"
          v-if="type == 'quarter'"
        >
          <el-select
            v-model="search.quarterValue"
            placeholder="选择季度"
            @change="quarterChange"
          >
            <el-option
              v-for="item in quarterList"
              :key="item.id"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </dart-search-item>
        <dart-search-item
          :label="customLabel ? customLabel : '统计日期'"
          prop="searchDate"
          v-if="type == 'monthRange'"
        >
          <el-date-picker
            v-model="search.searchDate"
            type="monthrange"
            value-format="yyyy-MM"
            clearable
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <slot name="button"></slot>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <!-- <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>-->
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
// import stampDownload from './components/stampDownload'

import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem
    // stampDownload,
  },
  props: {
    // thedate: {
    //   type: String,
    //   default: moment().subtract(1, 'day').format('YYYY-MM-DD'),
    // },
    title: {
      type: String
    },
    reportName: {
      type: String
    },
    type: {
      type: String, //日期选择默认不传，其他类型判断
      default: ''
    },
    timeName: {
      type: String,
      default: ''
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    },
    startDateName: {
      type: String,
      default: ''
    },
    endDateName: {
      type: String,
      default: ''
    },
    isCustomMonth: {
      // 自定义月份
      type: Boolean,
      default: false
    },
    customLabel: {
      type: String,
      default: ''
    },
    rangeTime: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      loading: false,
      search: {
        searchDate: '',
        quarterValue: [],
        yearValue: ''
      },
      tableHeight: 0,
      rules: {
        searchDate: [
          { required: true, message: '请选择统计日期', trigger: 'change' }
        ],
        yearValue: [
          { required: true, message: '请选择年份', trigger: 'change' }
        ],
        quarterValue: [
          { required: true, message: '请选择季度', trigger: 'change' }
        ]
      },
      quarterList: [
        {
          id: 1,
          value: ['-01', '-03'],
          label: '一季度'
        },
        {
          id: 2,
          value: ['-04', '-06'],
          label: '二季度'
        },
        {
          id: 3,
          value: ['-07', '-09'],
          label: '三季度'
        },
        {
          id: 4,
          value: ['-10', '-12'],
          label: '四季度'
        }
      ]
    }
  },
  created() {
    if (!this.type) {
      this.search.searchDate = moment()
        .subtract(1, 'day')
        .format('YYYY-MM-DD')
    } else if (this.type == 'month') {
      this.search.searchDate = moment(new Date())
        .subtract(1, 'months')
        .format('YYYY-MM')
    }
    this.$emit('update:receiveTime', this.search.searchDate)
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          let data = {
            name: this.reportName
          }

          switch (this.type) {
            case 'month':
              let times = this.search.searchDate.split('-')
              if (this.isCustomMonth) {
                data[this.timeName] = `${
                  this.search.searchDate
                }|${this.getPreviousMonthDate(this.search.searchDate)}`
              } else {
                data[this.startDateName] = times[0]
                data[this.endDateName] = times[1]
              }
              break
            case 'date':
              data[this.startDateName] = this.search.searchDate[0]
              data[this.endDateName] = this.search.searchDate[1]
              break
            case 'quarter':
              data[this.startDateName] =
                this.search.yearValue + this.search.quarterValue[0]
              data[this.endDateName] =
                this.search.yearValue + this.search.quarterValue[1]
              break
            case 'datetimerange':
              data[this.startDateName] = this.search.searchDate[0]
              data[this.endDateName] = this.search.searchDate[1]
              break
            case 'monthRange':
              data[this.startDateName] = this.search.searchDate[0]
              data[this.endDateName] = this.search.searchDate[1]
              break
            default:
              //默认调用日期选择
              data[this.timeName] = moment(this.search.searchDate).format(
                'YYYY-MM-DD'
              )
              break
          }
          let check = this.beforeReportHandle()
          if(!check) return
          //调用接口
          console.log('data=======>>>>', data)
          this.sendReportRequest(data)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm1'].resetForm()
      })
    },
    beforeReportHandle() {
      let check = true
      if (this.type == 'date' && this.rangeTime == 'days') {
        if (
          moment(this.search.searchDate[1]).diff(
            moment(this.search.searchDate[0]),
            'days'
          ) > 6
        ) {
          this.$msgbox({
            title: '提示',
            showClose: true,
            type: 'error',
            customClass: 'my_msgBox singelBtn',
            dangerouslyUseHTMLString: true,
            message: '统计日期时间段不能大于一个星期'
          })
          check = false
        }
      }
      return check
    },
    sendReportRequest(data) {
      this.loading = true
      request({
        url: api.report,
        method: 'post',
        data: data
      })
        .then(res => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
    dayChange() {
      console.log('change', this.search.searchDate)
      this.$emit('update:receiveTime', this.search.searchDate)
    },
    yearChange() {
      console.log('change', this.search.yearValue)
      this.$emit('update:yearValue', this.search.yearValue)
    },
    quarterChange() {
      console.log('change', this.search.quarterValue)
      this.$emit('update:quarterValue', this.search.quarterValue)
    },
    rangeChange(){
      console.log('change', this.search.searchDate)
      this.$emit('update:receiveTime', this.search.searchDate)
    },
    getPreviousMonthDate(dateString) {
      // 使用moment.js解析日期字符串，并将其减去一个月
      const previousMonthDate = moment(dateString + '-01').subtract(1, 'month')
      // 将结果格式化为所需的字符串格式
      const previousMonthDateString = previousMonthDate.format('YYYY-MM')

      return previousMonthDateString
    }
  }
}
</script>

<style lang="scss" scoped>
.report-export {
  // padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
  .search-title {
    padding: 0 0 10px 30px;
    font-size: 20px;
  }
}
</style>
