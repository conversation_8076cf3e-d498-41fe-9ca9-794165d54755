<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:2022.7.4日日通报表盖章并下载
  * @author:zhangys
  * @date:2022/07/04 10:03:33
!-->
<template>
  <div style="margin-left: 30px">
    <el-button type="primary" @click="stampDownload">报表盖章并下载</el-button>
  </div>
</template>

<script>
var moment = require('moment')
import request from '@/utils/request'
import api from '@/api/index'
export default {
  name: '',
  props: {
    name: {
      type: String,
      default: '',
      require: true
    },
    fileName: {
      type: String,
      default: '',
      require: true
    },
    keyword: {
      type: String,
      default: '负责人'
    },
    billMonth: {
      type: String,
      default: '',
      require: true
    },
    title: {
      type: String,
      default: 'report'
    },
    timeName: {
      type: String,
      default: ''
    },
    //如果入参是时间段，传以下参数
    isTimeQuantum: {
      type: Boolean,
      default: false
    },
    startDate: {
      type: String,
      default: ''
    },
    endDate: {
      type: String,
      default: ''
    },
    starDateName: {
      type: String,
      default: ''
    },
    endDateName: {
      type: String,
      default: ''
    },
    timeType: {
      type: String,
      default: 'YYYY-MM-DD'
    },
    rangeTime: {
      type: String,
      default: ''
    },
    customParams: {
      type: Object,
      default: () => {}
    }
  },
  components: {},
  data() {
    return {
      param: {},
      loading: false
    }
  },
  computed: {},
  watch: {},
  created() {},
  methods: {
    stampDownload() {
      this.param.keyword = this.keyword
      console.log(this.startDate)
      if (
        this.isTimeQuantum &&
        (this.startDate == 'undefined' || !this.startDate || !this.endDate)
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '请先选择统计日期'
        })
        return
      }
      if (!this.isTimeQuantum && !this.billMonth) {
        this.$message.warning('请先选择日期')
        return
      }
      if (this.isTimeQuantum && this.rangeTime == 'days') {
        if (moment(this.startDate).isAfter(this.endDate)) {
          this.$msgbox({
            title: '提示',
            showClose: true,
            type: 'error',
            customClass: 'my_msgBox singelBtn',
            dangerouslyUseHTMLString: true,
            message: '统计日期时间段异常'
          })
          return
        }
        if (moment(this.endDate).diff(moment(this.startDate), 'days') > 6) {
          this.$msgbox({
            title: '提示',
            showClose: true,
            type: 'error',
            customClass: 'my_msgBox singelBtn',
            dangerouslyUseHTMLString: true,
            message: '统计日期时间段不能大于一个星期'
          })
          return
        }
      }
      if (!this.isTimeQuantum) {
        this.param = {
          ...this.param,
          ...this.customParams
        }
        this.param[this.timeName] = moment(this.billMonth).format(this.timeType)
      } else {
        this.param[this.starDateName] = moment(this.startDate).format(
          this.timeType
        )
        this.param[this.endDateName] = moment(this.endDate).format(
          this.timeType
        )
      }
      let params = {
        name: this.name,
        fileName: this.fileName,
        param: this.param
      }
      this.loading = true
      let m = this.$message({
        message: '报表下载中......',
        type: 'info',
        duration: 0
      })
      request({
        url: api.yyxStamp,
        method: 'post',
        data: params,
        responseType: 'blob'
      })
        .then(res => {
          const link = document.createElement('a')
          let blob = new Blob([res], { type: 'application/pdf' }) //构造一个blob对象来处理数据
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = `${this.title}.pdf` //下载的文件名
          document.body.appendChild(link)
          link.click() // 执行下载
          document.body.removeChild(link) // 释放标签
          //   this.$message.success('下载成功')
          m.close()
        })
        .catch(err => {
          console.log(err)
        })
    }
  }
}
</script>

<style lang='scss' scoped>
</style>