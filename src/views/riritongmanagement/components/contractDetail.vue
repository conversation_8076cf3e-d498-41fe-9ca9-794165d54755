<!--
  * @projectName: gxetc-issue-manage-web
  * @desc: 合同明细
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/09/24 15:40:00
-->
<template>
	<div class="contract-detail">
		<el-table
			v-loading="loading"
			:data="tableData"
			:align="center"
			:header-align="center"
			border
			height="100%"
			style="width: 100%; margin-bottom: 20px"
			:row-style="{ height: '40px' }"
			:cell-style="{ padding: '0px' }"
			:header-row-style="{ height: '40px' }"
			:header-cell-style="{ padding: '0px' }"
		>
			<el-table-column
				prop="cid"
				align="center"
				width="160"
				label="编号"
				show-overflow-tooltip
			/>
			<el-table-column
				prop="contractId"
				align="center"
				width="160"
				label="合同编号"
				show-overflow-tooltip
			></el-table-column>
			<el-table-column
				prop="docId"
				align="center"
				width="200"
				show-overflow-tooltip
				label="文档编号"
			/>
			<el-table-column
				prop="createTime"
				align="center"
				width="180"
				label="办理日期"
			/>
			<el-table-column
				prop="delFlag"
				align="center"
				width="120"
				label="协议状态"
			>
				<template slot-scope="scope">
					{{ scope.row.delFlag == 0 ? '有效' : '失效' }}
				</template>
			</el-table-column>
			<el-table-column
				prop="signStatus"
				align="center"
				width="120"
				label="签章状态"
			>
				<template slot-scope="scope">
					{{ scope.row.signStatus == 0 ? '创建' : '签章完成' }}
				</template>
			</el-table-column>
			<el-table-column
				prop="templateId"
				align="center"
				width="120"
				label="模板文件Id"
			/>
			<el-table-column
				prop="templateVersion"
				align="center"
				width="120"
				label="模板文件版本"
			/>
			<el-table-column
				prop="remark"
				align="center"
				width="200"
				label="备注"
				show-overflow-tooltip
			/>
			<el-table-column
				fixed="right"
				label="操作"
				align="center"
				header-align="center"
			>
				<template slot-scope="scope">
					<el-button
						size="mini"
						type="primary"
						@click="previewF(scope.row)"
						>预览</el-button
					>
				</template>
			</el-table-column></el-table
		>
	</div>
</template>

<script>
import api from '@/api/index'
import request from '@/utils/request'

export default {
	name: 'contractDetail',
	components: {},
	props: {
		detailVisible: {
			type: Boolean,
			default: '',
		},
		detailItem: {
			type: Object,
			default() {
				return {}
			},
		},
	},
	data() {
		return {
			tableData: [],
			loading: false,
		}
	},
	watch: {
		detailVisible(val) {
			if (val) {
				this.getDetail()
			}
		},
	},
	computed: {},
	methods: {
		getDetail() {
			this.loading = true
			request({
				url: api.getSignDetai,
				method: 'post',
				data: {
					cid: this.detailItem.signCode,
				},
			})
				.then((res) => {
					this.loading = false
					this.tableData = res.data.docDetails || []
				})
				.catch((err) => {
					this.loading = false
					this.tableData = []
					console.log(err)
				})
		},
		previewF(val) {
			let clientWidth = document.documentElement.clientWidth
			let clientHeight = document.documentElement.clientHeight
			window.open(
				val.filePath,
				'_blank',
				'width=' +
					clientWidth +
					',height=' +
					clientHeight +
					',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
			)
		},
	},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.contract-detail {
	height: 100%;
	width: 100%;
	padding: 12px;
}
</style>