<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:ETC消费结算通知书
  * @author:zhang<PERSON>
  * @date:2022/12/13 09:42:23
!-->
<template>
  <div class="user">
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <dart-search-item label="统计开始日期" prop="clientStartTime">
          <el-date-picker
            v-model="search.clientStartTime"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item label="统计结束日期" prop="clientEndTime">
          <el-date-picker
            v-model="search.clientEndTime"
            type="date"
            :clearable="false"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <stampDownload
              name="clientDdpnConsumeReport"
              fileName="userBondBalanceReport"
              :billMonth="search.clientStartTime"
              isTimeQuantum
              :startDate="search.clientStartTime"
              :endDate="search.clientEndTime"
              :starDateName="'clientStartTime'"
              :endDateName="'clientEndTime'"
              title="客账ETC消费结算通知书"
              keyword="广西捷通"
            ></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import stampDownload from './components/stampDownload'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload
  },
  data() {
    return {
      loading: false,
      search: {
        clientStartTime: '',
        clientEndTime: ''
      },
      tableHeight: 0,
      rules: {
        clientStartTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' }
        ],
        clientEndTime: [
          { required: true, message: '请选择统计结束日期', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.search.clientStartTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD')
    this.search.clientEndTime = moment()
      .startOf('day')
      .format('YYYY-MM-DD')
  },
  mounted() {},
  methods: {
    onSearchHandle() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          if (
            moment(this.search.clientEndTime).diff(
              moment(this.search.clientStartTime),
              'months'
            ) > 2
          ) {
            this.$msgbox({
              title: '提示',
              showClose: true,
              type: 'error',
              customClass: 'my_msgBox singelBtn',
              dangerouslyUseHTMLString: true,
              message: '统计日期时间段不等大于三个月'
            })
            return
          }
          this.sendReportRequest()
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm1'].resetForm()
      })
    },

    sendReportRequest() {
      this.loading = true
      let data = {
        ...this.search,
        name: 'clientDdpnConsumeReport'
      }

      request({
        url: api.report,
        method: 'post',
        data: data
      })
        .then(res => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>

