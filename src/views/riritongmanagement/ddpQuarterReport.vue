<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）-ETC充值保证金通道费用季度报表 reportName:hsDdpRechargeQuarter
  * @author:dwz
  * @date:2022/07/05 15:23:25
!-->
<template>
  <div class="user">
    <dart-search ref="searchForm1"
                 :formSpan="24"
                 :searchOperation="false"
                 :fontWidth="1"
                 label-position="right"
                 :model="search">
      <template slot="search-form">
        <dart-search-item label="选择年份"
                          prop="searchDate">
          <el-date-picker v-model="yearValue"
                          type="year"
                          value-format="yyyy"
                          :clearable="false"
                          placeholder="选择年份">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item label="选择季度"
                          prop="">
          <el-select v-model="quarterValue"
                     placeholder="选择季度">
            <el-option v-for="item in quarterList"
                       :key="item.id"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">搜索</el-button>
            <stampDownload name='hsDdpRechargeQuarter'
                           fileName='hsDdpRechargeQuarter'
                           :isTimeQuantum='true'
                           :startDate='this.yearValue + this.quarterValue[0]'
                           :endDate='this.yearValue + this.quarterValue[1]'
                           starDateName='quStaTime'
                           endDateName='quEndTime'
                           title="日日通（担保产品）-ETC充值保证金通道费用季度报表"
                           keyword='报表日期'></stampDownload>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list"
         :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
import stampDownload from './components/stampDownload'

var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
  },
  data() {
    return {
      customerBizList: [],
      value: [],
      list: [],
      loading: false,
      states: [],
      quarterValue: [],
      yearValue: '',
      search: {
        name: 'hsDdpRechargeQuarter', //报表名称
        quStaTime: '',
        quEndTime: '',
      },
      tableHeight: 0,
      quarterList: [
        {
          id: 1,
          value: ['-01', '-03'],
          label: '一季度',
        },
        {
          id: 2,
          value: ['-04', '-06'],
          label: '二季度',
        },
        {
          id: 3,
          value: ['-07', '-09'],
          label: '三季度',
        },
        {
          id: 4,
          value: ['-10', '-12'],
          label: '四季度',
        },
      ],
      //   rules: {
      //     searchDate: [
      //       { required: true, message: '请选择统计日期', trigger: 'change' },
      //     ],
      //   },
    }
  },
  //   created() {
  //     this.search.searchDate = moment(new Date())
  //       .subtract(1, 'months')
  //       .format('YYYY-MM')
  //   },
  //   mounted() {},
  methods: {
    onSearchHandle() {
      //   let times = this.search.searchDate.split('-')
      //   this.search.ddp_year = times[0]
      //   this.search.ddp_month = times[1]
      //   delete this.search.searchDate
      //   this.sendReportRequest()
      console.log(this.quarterValue, this.yearValue)
      this.search.quStaTime = this.yearValue + this.quarterValue[0]
      this.search.quEndTime = this.yearValue + this.quarterValue[1]
      this.sendReportRequest()
    },

    sendReportRequest() {
      this.loading = true
      request({
        url: api.report,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
