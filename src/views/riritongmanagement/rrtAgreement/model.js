import { staticStampDownload } from '@/api/dict'

export const reportListFn = (state) => {
  return [
    {
      id: 1,
      name: 'JHconvertDetail',
      title: '吉鸿日日通用户转换协议签署明细报表',
      rules: {
        ddpETCStartTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ],
        ddpETCEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      },
      isDownload: true,
      btnSpan: 16,
      stampConfig: {
        name: 'JHconvertDetail', //报表名称
        fileName: 'JHconvertDetail',
        keyword: '统计单位'
      },
      formConfig: [
        {
          type: 'dateRange',
          field: 'OrderSubmitDate',
          keys: ['ddpETCStartTime', 'ddpETCEndTime'],
          valueFormat: 'YYYY-MM-DD',
          format:'YYYY-MM-DD',
          label: '选择日期：',
          default: []
        }
        // {
        //   type: 'datePicker',
        //   field: 'ddpETCStartTime',
        //   label: '开始日期',
        //   placeholder: '请选择日期',
        //   valueFormat: 'yyyy-MM-dd',
        //   // pickerOptions: state.pickerOptions,
        //   default: ''
        // },
        // {
        //   type: 'datePicker',
        //   field: 'ddpETCEndTime',
        //   label: '结束日期',
        //   placeholder: '请选择日期',
        //   // defaultTime:'23:59:59',
        //   valueFormat: 'yyyy-MM-dd',
        //   // pickerOptions: state.pickerOptions,
        //   default: ''
        // },
      ],
      rules: {
        OrderSubmitDate: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      }
    },
    {
      id: 2,
      name: 'JHconvertTotal',
      title: '吉鸿日日通用户转换协议总体情况统计报表',
      isDownload: true,
      btnSpan: 16,
      stampConfig: {
        name: 'JHconvertTotal', //报表名称
        fileName: 'JHconvertTotal',
        keyword: '统计单位'
      },
      formConfig: [
      {
          type: 'datePicker',
          field: 'ddpETCEndTime',
          label: '统计日期：',
          placeholder: '请选择日期',
          defaultTime:'23:59:59',
          // valueFormat: 'yyyy-MM-dd',
          // pickerOptions: state.pickerOptions,
          default: ''
        },
      ],
      rules: {
        ddpETCEndTime: [
          { required: true, message: '请选择日期', trigger: 'change' }
        ]
      }
    }
  ]
}