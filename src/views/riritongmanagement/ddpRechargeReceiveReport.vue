<template>
  <div class="user">
    <dart-search
      ref="searchForm1"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
    >
      <template slot="search-form">
        <dart-search-item label="选择日期" prop="">
          <el-date-picker
            v-model="value"
            type="daterange"
            value-format="yyyy-MM-dd"
            clearable
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </dart-search-item>

        <dart-search-item isButton>
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
import request from '@/utils/request'
import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      list: [],
      loading: false,
      states: [],
      value: [],
      search: {
        name: 'rechargeDepositCollectionReport', //报表名称
        trans_date_sta: '',
        trans_date_end: '',
      },
      tableHeight: 0,
      //   rules: {
      //     searchDate: [
      //       { required: true, message: '请选择统计日期', trigger: 'change' },
      //     ],
      //   },
    }
  },
  //   created() {
  //     this.search.searchDate = moment(new Date())
  //       .subtract(1, 'months')
  //       .format('YYYY-MM')
  //   },
  //   mounted() {},
  methods: {
    onSearchHandle() {
      if (this.value.length == 0) {
        this.$message({
          message: '请先选择日期',
          type: 'warning',
        })
        return
      }
      this.search.trans_date_sta = this.value[0]
      this.search.trans_date_end = this.value[1]
      this.sendReportRequest()
    },

    sendReportRequest() {
      console.log(this.search)
      this.loading = true
      request({
        url: api.report,
        method: 'post',
        data: this.search,
      })
        .then((res) => {
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {})
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
