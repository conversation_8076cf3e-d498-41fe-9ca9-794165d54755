<template>
  <div class="conent-wrap" v-loading="loading">
    <div class="wrap" style="margin-bottom: 15px;">
      <div class="wrap-title">上传事件</div>
      <el-form ref="form" :model="formData" label-width="90px" :rules="rules">
        <el-form-item label="事件名称" prop="eventName">
          <el-input
            v-model="formData.eventName"
            maxlength="30"
            show-word-limit
            style="width:50%;"
          ></el-input>
        </el-form-item>
        <el-form-item label="事件说明" prop="eventDetails">
          <el-input
            type="textarea"
            style="width:85%;"
            maxlength="500"
            show-word-limit
            rows="4"
            v-model="formData.eventDetails"
          ></el-input>
        </el-form-item>
        <el-form-item label="上传附件" prop="file">
          <el-upload
            style="width:60%;"
            class="upload"
            ref="upload"
            drag
            action="action"
            :on-remove="handleRemove"
            accept=".rar,.zip,.xlsx,xls,.pdf,.doc,.docx"
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :on-change="onChange"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              支持上传 Rar、Zip、Excel、Word、PDF等格式，rar不支持预览
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="handleSubmit" :disabled="!formData.eventName || !formData.eventDetails || fileList.length <= 0" type="primary" size="mini"
          >上传</el-button
        >
      </div>
    </div>

    <div class="wrap">
      <div class="wrap-title" style="margin-bottom: 0;">历史记录</div>
      <HistoryList ref="HistoryList"></HistoryList>
    </div>
  </div>
</template>

<script>
import HistoryList from './components/history-list.vue'
import { saveFile } from '@/api/equipment'

export default {
  components: {
    HistoryList
  },
  data() {
    return {
      formData: {
        file: ''
      },
      fileList: [],
      rules: {
        eventName: [
          { required: true, message: '事件名称不能为空', trigger: 'blur' }
        ],
        eventDetails: [
          { required: true, message: '事件说明不能为空', trigger: 'blur' }
        ],
        file: [{ required: true, message: '附件不能为空', trigger: 'blur' }]
      },
      loading:false
    }
  },
  methods: {
    handleSubmit() {
      console.log(this.fileList, 'fileList')
      this.$refs.form.validate(async valid => {
        console.log(this.formData)
        if (valid) {
          let params = new FormData()
          params.append('file', this.formData.file)
          params.append('eventName', this.formData.eventName)
          params.append('eventDetails', this.formData.eventDetails)
          // let fullFilePath = await this.submitUpload()
          // if (!fullFilePath) return
          this.loading = true
          let res = await saveFile(params)
          this.loading = false
          if (res.code == 200) {
            this.$refs.HistoryList.getTableData()
            this.$alert('事件上传成功', '提示', {
              dangerouslyUseHTMLString: true,
              showClose: false,
              confirmButtonText: '确定'
            })
            this.formData = {}
            this.handleRemove()
          }
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    handleExceed(files, fileList) {
     this.$message.error('文件数量超出')
    },
    /**
     * 文件变化
     */
    onChange(files) {
      this.$refs.upload.clearFiles()
      this.fileList.push({ ...files, name: files.name, status: 'success' })
      this.formData.file = files.raw
    },
    /**
     * 文件删除
     */
    handleRemove(val) {
      console.log(val, 'handleRemove')
      this.formData.file = ''
      this.fileList = []
    }
  },
  created() {
    // this.initDeatail()
  }
}
</script>

<style lang="scss" scoped>
.conent-wrap {
  padding: 10px 24px;
  .wrap {
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    .wrap-title {
      margin: 0 0 20px;
      padding: 5px 10px 10px 10px;
      font-weight: 600;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  ::v-deep .el-upload__tip {
    margin-top: 0;
    line-height: 20px;
  }
  .bottom-btn {
    margin-bottom: 5px;
  }
}
</style>