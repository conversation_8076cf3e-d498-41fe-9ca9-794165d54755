<template>
  <div>
    <ul
      class="file-list"
      v-for="item in myData"
      :key="item.id"
      :style="{ paddingLeft: item.level * 14 + 'px' }"
    >
      <li class="list-item" @click="downloadClick" :class="{ 'item-child': item.type != 'dir' }">
        <div class="item-content">
          <span>
            <span v-if="item.type != 'dir' && item.level != 0 ">-- </span
            ><i
              class="file-icon el-icon-document"
              :class="{ 'el-icon-folder-opened': item.type == 'dir' }"
            ></i
            >{{ item.title }}</span
          >
          <i class="el-icon-download"></i>
        </div>
      </li>
      <liTree
        v-if="item.children && item.children.length > 0"
        @downloadClick="downloadClick"
        :myData="item.children"
      ></liTree>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'liTree',
  props: {
    myData: {
      type: Array,
      default: () => []
    }
  },
  methods:{
    downloadClick(item){
      this.$emit('downloadClick',item)
    }
  }
}
</script>

<style lang="scss" scoped>
.file-list {
  display: block;
  margin-top: 0;
  ul {
    margin: 0;
    width: 100%;
    padding-left: 0;
    li {
      list-style-type: none;
      i {
        font-size: 18px;
      }
    }
  }
  .list-item {
    width: 100%;
    transition: all 0.3s;
    padding: 0 8px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .file-icon {
      padding-right: 3px;
    }
    &:hover {
      background: #f5f7fa;
      color: #409eff;
      cursor: pointer;
    }
    &.item-child {
      padding-left: 28px;
    }
    .item-content {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .el-icon-download {
        // float: right;
      }
    }
  }
}
</style>