<template>
  <div class="event-preview">
    <el-form ref="form" :model="formData" label-width="90px" :rules="rules">
      <el-form-item label="事件名称" prop="eventName">
        {{ formData.eventName }}
      </el-form-item>
      <el-form-item label="事件说明" prop="eventDetails">
        {{ formData.eventDetails }}
      </el-form-item>
      <el-form-item label="上传附件" prop="file">
        <liTree
          v-for="item in myData"
          :myData="myData"
          @downloadClick="downloadClick"
          :key="item.id"
        ></liTree>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import { queryDdpErrorData } from '@/api/equipment'
import JSZip from 'jszip'
import liTree from './li-tree.vue'

export default {
  mixins: [layerMix],
  components: {
    liTree
  },
  data() {
    return {
      formData: {},
      rules: {
        eventName: [
          { required: true, message: '事件名称不能为空', trigger: 'blur' }
        ],
        eventDetails: [
          { required: true, message: '事件说明不能为空', trigger: 'blur' }
        ],
        file: [{ required: true, message: '附件不能为空', trigger: 'blur' }]
      },
      myData: []
    }
  },
  methods: {
    /**
     * 读取zip文件
     */
    async getZipFile() {
      // let jszip = new JSZip()
      // // let zip = await jszip.loadAsync(path)
      // console.log(zip, 'zipzipzip')
      // const content = await getZipContent(url)
      let { fileName, fileType, uploadPath } = this.formData
      console.log(uploadPath, 'rrrrr')
      const response = await fetch(uploadPath)
      if (response.code == 999) {
        this.$message.error(response.msg)
        return
      }
      const buffer = await response.arrayBuffer()
      const content = new Uint8Array(buffer)
      const zip = new JSZip()
      try {
        const results = await zip.loadAsync(content)
        console.log(results, 'ressresss')
        let arr = []
        this.myData = this.transformData(results, arr)
      } catch (err) {
        this.$message.warning('压缩包格式有误，无法预览,请直接下载！！')
        console.error(err)
        let obj = {
          title: fileName,
          type: fileType,
          level: 0
        }
        this.myData.push(obj)
      }
    },
    transformData(obj, myData, level = 0) {
      let id = 0
      try {
        console.log(Object.keys(obj.files), 'Object.keys(obj.files)')
        if (Object.keys(obj.files).length == 0) {
          let fname = this.fileName.substring(0, this.fileName.lastIndexOf('.'))
          let rootData = {
            id: '0',
            parentId: '',
            key: '0',
            title: fname,
            fullName: fname + '/',
            type: 'dir',
            children: [],
            slots: { icon: 'folder' }
          }
          console.log('dirdirdirdirdir')
          myData.push(rootData)
        } else {
          console.log(obj, 'files')
          for (let key in obj.files) {
            let array = key.split('/').filter(item => item != '')
            if (array.length == level + 1) {
              console.log(array, level, 'array')
              if (obj.files[key].dir) {
                console.log('dirdirdirdirdir')
                if (level == 0) {
                  // 根 只有一个
                  let rootData = {
                    id: '0',
                    parentId: '',
                    key: '0',
                    title: array[level],
                    fullName: key,
                    type: 'dir',
                    children: [],
                    slots: { icon: 'folder' },
                    level
                  }
                  myData.push(rootData)
                  this.transformData(obj, rootData, level + 1)
                } else {
                  // 非根目录
                  if (
                    key.indexOf(myData.fullName) === 0 &&
                    key != myData.fullName &&
                    array.length == level + 1
                  ) {
                    let newData = {
                      id: myData.id + '-' + id,
                      key: myData.id + '-' + id,
                      parentId: myData.id,
                      title: array[level],
                      type: 'dir',
                      children: [],
                      fullName: key,
                      slots: { icon: 'folder' },
                      level
                    }
                    myData.children.push(newData)
                    id++
                    this.transformData(obj, newData, level + 1)
                  }
                }
              } else {
                // 文件
                if (
                  key.indexOf(myData.fullName) == 0 &&
                  key != myData.fullName
                ) {
                  let data = {
                    id: myData.id + '-' + id,
                    parentId: myData.id,
                    key: myData.id + '-' + id,
                    title: array[level],
                    type: array[level].replace(/.+\./, ''),
                    fullName: key,
                    level
                  }
                  if (['jpg', 'png', 'gif'].includes(data.type)) {
                    data.slots = { icon: 'image' }
                  } else if (data.type == 'mp3') {
                    data.slots = { icon: 'audio' }
                  } else if (data.type == 'mp4') {
                    data.slots = { icon: 'video' }
                  } else if (data.type == 'xlsx') {
                    data.slots = { icon: 'xml' }
                  } else {
                    data.slots = { icon: data.type }
                  }
                  console.log(myData, 'myData')
                  myData.children.push(data)
                  id++
                }
              }
            } else {
              // // jsZip不能正常读取到目录的情况下 处理
              if (myData.length <= 0) {
                let { fileName, fileType } = this.formData
                let rootData = {
                  id: '0',
                  parentId: '',
                  key: '0',
                  title: fileName,
                  fullName: key,
                  type: 'dir',
                  children: [],
                  slots: { icon: 'folder' },
                  level,
                  diy: true
                }
                myData.push(rootData)
              }
              if (myData[0] && myData[0].diy) {
                let data = {
                  id: key + '-' + id,
                  parentId: key,
                  key: key + '-' + id,
                  title: key,
                  type: array[level].replace(/.+\./, ''),
                  fullName: key,
                  level
                }
                myData[0].children.push(data)
              }
            }
          }
        }
      } catch (err) {
        console.log(err)
      }

      console.log(myData, 'myDatamyData')
      return myData
    },
    /**
     * 初始化
     */
    async initDeatail() {
      // let res = await queryDdpErrorData({ id: this.formData.id })
      // this.formData = res.data
      let { fileName, fileType } = this.formData
      if (fileType.includes('zip') || fileType.includes('rar')) {
        this.getZipFile()
      } else {
        let obj = {
          title: fileName,
          type: fileType,
          level: 0
        }
        this.myData.push(obj)
      }
    },
    /**
     * 下载
     */
    async downloadClick(item) {
      let { uploadPath, fileName } = this.formData
      const response = await fetch(uploadPath)
      // window.open(uploadPath)
      let blob = await response.blob()
      const link = document.createElement('a')
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = fileName //下载的文件名
      document.body.appendChild(link)
      link.click() // 执行下载
      document.body.removeChild(link) // 释放标签
    }
  },
  created() {
    let formData = this.getParam('formData')
    if (formData) {
      this.formData = formData
    }
    this.initDeatail()
  }
}
</script>

<style lang="scss" scoped>
.event-preview {
  width: 100%;
  height: 100%;
  padding: 20px;
}
</style>