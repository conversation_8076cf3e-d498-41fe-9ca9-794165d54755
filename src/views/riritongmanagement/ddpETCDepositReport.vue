<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:日日通（担保产品）车辆办理情况日报表 2022.7.04需求 盖章并下载
  * @author:zhangys&zcq
  * @date:2022/05/11 10:58:55
!-->
<template>
  <div class="user">
    <div class="section-1">
      <reportExport
        title="日报表"
        reportName="hsDdpEtcBondNotice"
        type="date"
        rangeTime="days"
        startDateName="ddpETCStartTime"
        endDateName="ddpETCEndTime"
        :receiveTime.sync="receiveTime"
      >
        <template v-slot:button>
          <stampDownload
            name="hsDdpEtcBondNotice"
            fileName="hsDdpEtcBondNotice"
            :isTimeQuantum="true"
            starDateName="ddpETCStartTime"
            endDateName="ddpETCEndTime"
            :startDate="receiveTime[0]"
            :endDate="receiveTime[1]"
            rangeTime="days"
            timeName="ddpETCTime"
            title="日日通（担保产品）ETC保证金结算通知书"
            keyword="资金流出单位"
          ></stampDownload>
        </template>
      </reportExport>
    </div>
    <div class="section-2">
      <reportExport
        title="月报表"
        startDateName="ddpMonthStartTime"
        endDateName="ddpMonthEndTime"
        reportName="hsDdpDepositMonthReport"
        type="monthRange"
      >
      </reportExport>
    </div>
    <div class="section-2">
      <reportExport
        title="充值明细"
        timeName="ddpETCTime"
        reportName="hsDdpRechargeDetail"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        title="充值退款明细"
        timeName="ddpETCTime"
        type="date"
        rangeTime="days"
        startDateName="ddpETCStartTime"
        endDateName="ddpETCEndTime"
        reportName="hsDdpRechargeRefundDetail"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        title="注销退款明细"
        timeName="ddpETCTime"
        type="date"
        rangeTime="days"
        startDateName="ddpETCStartTime"
        endDateName="ddpETCEndTime"
        reportName="hsDdpCancelRefundDetail"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        title="充值收款日报表"
        timeName="ddpETCTime"
        reportName="rechargeDepositCollectionReport"
      >
      </reportExport>
    </div>
    <div class="section-3">
      <reportExport
        type="quarter"
        title="充值保证金通道费用季度报表"
        reportName="hsDdpRechargeQuarter"
        startDateName="quStaTime"
        endDateName="quEndTime"
        :yearValue.sync="yearValue"
        :quarterValue.sync="quarterValue"
      >
        <template v-slot:button>
          <stampDownload
            name="hsDdpRechargeQuarter"
            fileName="hsDdpRechargeQuarter"
            :isTimeQuantum="true"
            timeType="YYYY-MM"
            starDateName="quStaTime"
            endDateName="quEndTime"
            :startDate="yearValue + quarterValue[0]"
            :endDate="yearValue + quarterValue[1]"
            title="日日通（担保产品）-ETC充值保证金通道费用季度报表"
            keyword="报表日期"
          ></stampDownload>
        </template>
      </reportExport>
    </div>
  </div>
</template>

<script>
import { decode } from 'js-base64'
import request from '@/utils/request'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import stampDownload from './components/stampDownload'
import reportExport from './components/reportExport'

import api from '@/api/index'
var _ = require('lodash')
var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    stampDownload,
    reportExport
  },
  data() {
    return {
      receiveTime: '',
      quarterValue: [],
      yearValue: ''
      // loading: false,
      // search: {
      //   thedate: '',
      // },
      // tableHeight: 0,
      // rules: {
      //   thedate: [
      //     { required: true, message: '请选择统计日期', trigger: 'change' },
      //   ],
      // },
    }
  },
  methods: {
    // downReport(val) {
    //   this.downReportDate = val
    // },
  }
  // created() {
  //   this.search.thedate = moment().subtract(1, 'day').format('YYYY-MM-DD')
  // },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>
