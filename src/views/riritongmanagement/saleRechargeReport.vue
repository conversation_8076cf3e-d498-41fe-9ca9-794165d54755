<template>
  <div class="user">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :formSpan="24"
      :gutter="20"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <!-- <div class="search-title">网点销售充值应收报表</div> -->
        <dart-search-item label="统计开始日期" prop="startTime">
          <el-date-picker
            v-model="search.startTime"
            type="datetime"
            :clearable="false"
            default-time="00:00:00"
            placeholder="选择日期"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item label="统计结束日期" prop="endTime">
          <el-date-picker
            v-model="search.endTime"
            type="datetime"
            :clearable="false"
            placeholder="选择日期"
            default-time="00:00:00"
          ></el-date-picker>
        </dart-search-item>
        <dart-search-item label="部门名称" prop="searchitem">
          <el-cascader
            v-model="search.searchitem"
            class="form-select"
            ref="deptNodes"
            :options="deptOptions"
            :expand-trigger="'click'"
            :props="{
              checkStrictly: true,
              value: 'id',
              label: 'name',
              emitPath: false,
            }"
          />
        </dart-search-item>
        <dart-search-item label="部门属性" prop="branchType">
          <el-select v-model="search.branchType" placeholder="请选择" clearable collapse-tags>
            <el-option
              v-for="item in branchTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true" :span="8">
          <el-button type="primary" size="mini" native-type="submit" @click="onSearchHandle">搜索</el-button>
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <!-- <div class="section-2">
      <reportExport
        title="网点销售充值应收实收款日报表"
        reportName="recoverPressReport"
        timeName="recoverMonth"
        :customLabel="'统计日期'"
      ></reportExport>
    </div>
    <div class="section-2">
      <reportExport
        title="网点销售充值应收实收款月报表"
        reportName="recoverPressReport"
        timeName="recoverMonth"
        :customLabel="'统计月份'"
        type="month"
      ></reportExport>
    </div>-->
    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import { departmenttype } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
import reportExport from './components/reportExport'

var moment = require('moment')
export default {
  components: {
    dartSearch,
    dartSearchItem,
    reportExport
  },
  created() {
    // this.search.startTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    // this.search.endTime = moment().startOf('day').format('YYYY-MM-DD HH:mm:ss')
    this.getgroup()
  },
  data() {
    return {
      departmenttype,
      groupArr: [],
      deptOptions: [], // 部门列表
      currentDept: null, // 当前选择部门节点
      search: {
        startTime: '',
        endTime: '',
        OPERATOR_DEPT: '',
        // searchitem: '',
        // name: '',
        branchDeptNo: '',
        branchLength: '',
        branchType: ''
      },
      tableHeight: 0,
      optiondata: [],
      rules: {
        startTime: [
          { required: true, message: '请选择统计开始日期', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择统计结束日期', trigger: 'change' }
        ]
        // searchitem: [
        //   { required: true, message: '请选择统计部门', trigger: 'change' },
        // ],
      },
      options: [],
      branchTypeOptions: [
        { value: '01', label: '自营' },
        { value: '02', label: '运营' },
        { value: '03', label: '一站式' },
        { value: '04', label: '合作' },
        { value: '05', label: '银行' },
        { value: '06', label: '线上' },
        { value: '99', label: '其他' }
      ],
      pickerOptions: {
        // 设置时间选择器的禁用时间
        disabledDate(time) {
          // 限制不能超过今天
          return time.getTime() > Date.now()
        }
      }
    }
  },
  methods: {
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this.search.endTime).diff(
          moment(this.search.startTime),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      console.log(this.search)
      this.sendReportRequest()
    },
    sendReportRequest() {
      this.$refs['searchForm1'].$children[0].validate(valid => {
        if (valid) {
          try {
            this.currentDept = this.$refs.deptNodes.getCheckedNodes()[0].data
            console.log(this.currentDept, '----this.currentDept+++++')
            this.changeitem(this.currentDept)
          } catch (e) {}
          let params = {
            name: 'outletSalesRechargeReport',
            startTime: moment(this.search.startTime).format(
              'YYYY-MM-DD HH:mm:ss'
            ),
            endTime: moment(this.search.endTime).format('YYYY-MM-DD HH:mm:ss'),
            OPERATOR_DEPT: this.search.OPERATOR_DEPT, //部门编号
            branchDeptNo: this.search.branchDeptNo,
            branchLength: this.search.branchLength
          }
          if (this.search.branchType != '') {
            params.branchType = this.search.branchType
          }
          console.log(params)
          this.$store
            .dispatch('report/report', params)
            .then(res => {
              let url = res
              let decodeUrl = decode(url)
              // console.log(decodeUrl,'地址')
              let clientWidth = document.documentElement.clientWidth
              let clientHeight = document.documentElement.clientHeight
              window.open(
                decodeUrl,
                '_blank',
                'width=' +
                  clientWidth +
                  ',height=' +
                  clientHeight +
                  ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
              )
            })
            .catch(() => {})
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$refs['searchForm1'].$children[0].resetFields()
      this.search.searchitem = nullbranchDeptNo
    },
    gettime(data) {
      const value = moment(data)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss')
      return value
    },
    changeitem(item) {
      let deptNum = item.deptNum
      switch (item.deptLevel) {
        case 1:
          this.search.branchDeptNo = deptNum.slice(9, 12)
          break
        case 2:
          this.search.branchDeptNo = deptNum.slice(9, 15)
          break
        case 3:
          this.search.branchDeptNo = deptNum.slice(9, 18)
          break
        case 4:
          this.search.branchDeptNo = deptNum.slice(9, 21)
          break
        case 5:
          this.search.branchDeptNo = deptNum.slice(9, 25)
          break
        case 6:
          this.search.branchDeptNo = deptNum.slice(9, 30)
          break
        case 7:
          this.search.branchDeptNo = deptNum.slice(9, 35)
          break
      }
      this.search.OPERATOR_DEPT = item.id.toString()
      this.search.branchLength = this.search.branchDeptNo.length.toString()
    },
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      })
        .then(res => {
          this.deptOptions = res.data
        })
        .catch(error => {
          console.log(error)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .search-title {
    padding: 0 0 10px 30px;
    font-size: 20px;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>