<template>
  <div class="audit-page">
    <div class="tab-item">
      <el-tabs @tab-click="handleClick" v-model="selectItem" type="border-card">
        <el-tab-pane
          :label="item.label"
          v-for="(item, index) in businessTypeOptions"
          :key="index"
          :name="item.value"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div>
      <auditTask v-if="selectItem == '1'"></auditTask>
      <auditCar v-if="selectItem == '2'"></auditCar>
    </div>
  </div>
</template>

<script>
import auditTask from './audit-task.vue'
import auditCar from './audit-car.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  components: {
    auditTask,
    auditCar
  },
  created() {
  },
  mounted() {},
  data() {
    return {
      businessTypeOptions: [
        { label: '稽核任务', value: '1' },
        { label: '稽核车辆', value: '2' },
      ],
      selectItem: '1'
    }
  },
  methods: {
    ...mapActions([
      'setApplyOrderStatus',
      'setApplyChannelStatus',
      'setActivateChannelStatus',
      'setAddress'
    ]),
    handleClick(val) {
      this.selectItem = this.businessTypeOptions[val.index].value
    },
  }
}
</script>

<style lang="scss" scoped>
.audit-page {
  height: 100%;
  position: relative;
  margin: 15px 20px;
  flex-flow: column;
  display: flex;
  .tab-item {
    ::v-deep .el-tabs--border-card {
      box-shadow: none;
    }
    ::v-deep .el-tabs__content {
      padding: 0;
    }
    ::v-deep .el-tabs--border-card {
      border-bottom: none;
    }
  }
  .table {
    width: 100%;
    flex: 1;
    background-color: #ffffff;
    text-align: center;
    margin: 0;
  }
  ::v-deep.el-table th,
  ::v-deep.el-table td {
    text-align: center;
  }
  .itembox {
    padding: 0 25px;
    line-height: 50px;
    .item {
      margin: auto;
      font-size: 14px;
      span {
        display: inline-block;
        padding-right: 10px;
        font-weight: 600;
        min-width: 75px;
      }
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .foot {
    margin-top: 20px;
    text-align: center;
  }
  .pagination {
    padding: 10px 0;
    background-color: #fff;
    text-align: center;
  }
  ::v-deep .el-dialog {
    min-width: 1150px;
  }
}
</style>
