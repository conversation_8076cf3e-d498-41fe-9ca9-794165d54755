<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="searchHandle"
      @onReSetHandle="onReSetHandle"
      :rules="carListFormRules"
    ></SearchForm>
    <div class="audit-wrap">
      <imgWrap :imgList="imgList" v-if="showWrap || showImg" />
      <auditCarWrap
        :searchParams="searchParams"
        @closeWrap="closeWrap"
        v-if="showWrap"
      />
      <div class="no-data" v-if="!showImg">暂无数据</div>
    </div>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="batchCheck"
          >复核通过</el-button
        >
        <el-button type="primary" size="mini" @click="auditReject"
          >复核驳回</el-button
        >
        <el-button
          type="warning"
          slot="btn"
          size="mini"
          native-type="submit"
          @click="exportHandle"
          >导出</el-button
        >
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            v-if="$route.query.type != 'mistake'"
            size="mini"
            type="text"
            :disabled="scope.modifyStatus != '待整改'"
            @click="handelRow(scope, 'enterInfo')"
            >录入整改信息</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-if="$route.query.type == 'mistake'"
            @click="handelRow(scope, 'mistake')"
            >编辑错误原因</el-button
          >
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { carListColoumns, carListForm, carListFormRules } from './model'
import {
  queryResultList,
  vehicleImage,
  passImage,
  resultCheck,
  vehicleExport,
  resultReject
} from '@/api/equipment'
import imgWrap from './components/img-wrap.vue'
import auditCarWrap from './components/auditCar-wrap.vue'

let imgName = {
  3: '行驶证图片',
  6: '车辆图片',
  12: '行驶证车辆照片',
  16: '车身照片'
}
export default {
  components: {
    MyTable,
    SearchForm,
    imgWrap,
    auditCarWrap
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: queryResultList,
      showWrap: false,
      pageSizeKey: 'pageSize',
      pageNumKey: 'page',
      dataKey: 'data',
      carListFormRules,
      imgList: [],
      showImg: false,
      searchParams: {}
    }
  },
  computed: {
    listColoumns() {
      return carListColoumns(this)
    },
    formConfig() {
      return carListForm(this)
    }
  },
  methods: {
    // 操作
    handelRow(row, type) {
      if (type == 'enterInfo') {
        this.$router.push({
          path: '/issue/add',
          query: {
            type: 'enterInfo',
            auditId: row.auditId,
            carNo: row.carNo,
            carColor: row.carColor
          }
        })
      } else if (type == 'mistake') {
        this.editReason()
      }
    },
    // 批量复核
    batchCheck() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let arr = JSON.parse(JSON.stringify(this.selectArr))
      let checkArr = arr.filter(item => (item.auditStatus != '整改完成待复核' && item.auditStatus != '整改中'))
      if (checkArr.length > 0) {
        this.$message.warning(
          '选中的数据中，有整改状态不为“整改完成待复核”和“整改中”，请重新选择！'
        )
        return
      }
      let params = {
        auditId: this.selectArr.map(item => item.auditId)
      }
      this.checkApi(params)
      console.log(params)
    },
    // 复核接口
    checkApi(params) {
      this.$confirm('请确认是否要复核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await resultCheck(params)
          if (res.code == 200) {
            this.$message.success('成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    },
    async searchHandle(params) {
      this.showImg = false
      this.showWrap = false
      this.searchParams = params
      this.currentFormData = params
      let res = await vehicleImage(params)
      this.getTableData()
      if (res.code == 200) {
        this.imgList = res.data.images.map(item => {
          return {
            ...item,
            name: imgName[item.code]
          }
        })
        let passImgRes = await passImage(params)
        console.log(passImgRes, 'passImgRes')
        if (passImgRes.type == 'application/json' && this.imgList.length <= 0)
          return // 如果缺少 车辆图片，行驶证图片 则返回
        let passImgUrl = window.URL.createObjectURL(passImgRes)
        let obj = {
          url: passImgUrl,
          code: 15,
          name: '车道通行图片'
        }
        this.imgList.unshift(obj)

        this.showImg = true
        if (res.data.isAudit) {
          this.showTip(res.data.auditStatus)
        } else {
          this.showWrap = true
          console.log(passImgRes)
        }
      }
    },

    blobToBase64(blob) {
      return new Promise((resolve, reject) => {
        const fileReader = new FileReader()
        fileReader.onload = e => {
          resolve(e.target.result)
        }
        fileReader.readAsDataURL(blob)
        fileReader.onerror = () => {
          reject(new Error('文件流异常'))
        }
      })
    },
    showTip(auditStatus) {
      this.$alert(
        `该车辆已经在其他稽核模型或专项稽核中进行稽核，无需重复稽核。<p style="padding-top:5px;">车辆稽核状态标识：${auditStatus}</p>`,
        '提示',
        {
          dangerouslyUseHTMLString: true,
          showClose: false,
          confirmButtonText: '确定'
        }
      )
    },
    exportHandle() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let params = {
        auditId: this.selectArr.map(item => item.auditId)
      }
      let fileObj = {
        fileName: 'ETC车辆数据.xlsx'
      }
      this.exportFile(params, vehicleExport, fileObj)
    },
    // 重置的回调
    onReSetHandle(formData) {
      this.currentFormData = formData
      // this.getTableData()
    },
    closeWrap() {
      this.showWrap = false
    },
    // 复核驳回
    auditReject() {
      if (this.selectArr.length != 1) {
        this.$message.warning('请选择一条数据')
        return
      }
      let row = this.selectArr[0]
      this.$openPage(
        '@/views/issue/releaseAudit/components/reason-layer',
        '复核驳回',
        {
          type: 'reject',
          callBack: (res, lid) => {
            let params = {
              auditId: row.auditId,
              remark: res.errorReason
            }
            console.log(params, '1231')
            this.rejectSubmitCallback(params, lid)
          }
        },
        {
          area: ['25%', '240px']
        }
      )
    },
    // 复核驳回
    async rejectSubmitCallback(params, lid) {
      let res = await resultReject(params)
      if (res.code == 200) {
        this.$message.success('成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    }
  },
  created() {
    // this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  ::v-deep .fontWidth {
    display: flex;
    div {
      width: auto;
    }
  }
  .choose-footer {
    text-align: center;
  }
  ::v-deep .dart-search-wrapper {
    margin-bottom: 0;
    border-bottom: 1px solid #ccc;
  }
  ::v-deep .dart-search-operation {
    display: none;
  }
  .no-data {
    padding: 10px 0;
    text-align: center;
    color: #909399;
  }
  .audit-wrap {
    padding: 10px;
    background-color: #fff;
  }
}
</style>