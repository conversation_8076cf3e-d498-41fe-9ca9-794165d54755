<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <template slot="input" slot-scope="{ scope }">
        <el-switch
          v-model="selfOperate"
          size="small"
          @change="handleSelfOperateChange(scope)"
        ></el-switch>
      </template>
    </SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="createTask"
          >创建任务</el-button
        >
        <el-button type="primary" size="mini" @click="specialAudit"
          >专项稽核</el-button
        >
        <el-button size="mini" type="primary" @click="importFile"
          ><i class="el-icon-upload"></i> 专项稽核批量导入</el-button
        >
        <el-button size="mini" type="warning" @click="downloadExcel('special')"
          ><i class="el-icon-download"></i> 批量导入表样下载</el-button
        >
        <el-button size="mini" type="primary" @click="importCorrectFile"
          ><i class="el-icon-upload"></i> 稽核结果修正批量导入</el-button
        >
        <el-button size="mini" type="warning" @click="downloadExcel('correct')"
          ><i class="el-icon-download"></i> 稽核结果修正批量导入表样下载</el-button
        >
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <!-- <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template> -->
        <!-- 操作 -->
        <!-- || !checkAuth([])  :disabled="!checkAuth([])" -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="text" :disabled="scope.taskStatus != '已完成' || !checkAuth(['issueAudit:resultList'])" @click="handelRow(scope, 'list')"
            >查看稽核结果列表</el-button
          >
          <el-button
            size="mini"
            type="text"
            :disabled="scope.workOrder == 1 || scope.taskStatus != '已完成' || !checkAuth(['issueAudit:errorEdit'])"
            @click="handelRow(scope, 'mistake')"
            >编辑错误原因</el-button
          >
          <el-button size="mini" type="text" :disabled="scope.workOrder == 1 || !checkAuth(['issueAudit:workOrder'])" @click="handelRow(scope, 'task')"
            >生成工单</el-button
          >
          <el-button size="mini" type="text" :disabled="scope.taskStatus != '已完成' || !checkAuth(['issueAudit:resultExport'])" @click="handelRow(scope, 'export')" 
            >导出</el-button
          >
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColoumns, listForm } from './model'
import {
  queryTaskList,
  createAuditTask,
  auditSpecial,
  downloadAuidtExcel,
  createWorkOrder,
  resultExport,
  correctAudit,
  correctDownloadExcel
} from '@/api/equipment'
import { getToken } from '@/utils/auth'
import { mapGetters } from 'vuex'


export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: queryTaskList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      dataKey: 'data',
      selfOperate: false,
    }
  },
  computed: {
    ...mapGetters(['realName']),
    listColoumns() {
      return listColoumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 操作
    handelRow(row, type) {
      if (type == 'export') {
        let params = {
          taskId: row.taskId
        }
        let fileObj = {
          fileName: '稽核结果数据.xlsx'
        }
        this.exportFile(params,resultExport,fileObj)
      } else if (type == 'list') {
        this.$router.push({
          path: '/issue/taskResult',
          query: {
            type: 'list',
            taskId:row.taskId
          }
        })
      } else if (type == 'mistake') {
        this.$router.push({
          path: '/issue/mistake',
          query: {
            type: 'mistake',
            taskId:row.taskId
          }
        })
      } else if (type == 'task') {
        this.createWorkOrderHandle(row)
      }
    },
    // 生成工单
    async createWorkOrderHandle(row) {
      let params = {
        taskId: row.taskId,
        taskName: row.taskName
      }
      let res = await createWorkOrder(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.getTableData()
      }
    },
    // 创建稽核任务
    createTask() {
      this.$openPage(
        '@/views/issue/releaseAudit/components/task-layer',
        '创建稽核任务',
        {
          callBack: (res, lid) => {
            this.createTaskApi(res, lid)
          }
        },
        {
          area: ['32%', '480px']
        }
      )
    },
    // 创建稽核任务
    async createTaskApi(params, lid) {
      let res = await createAuditTask(params)
      if (res.code == 200) {
        this.$message.success('创建成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },

    // 专项稽核
    specialAudit() {
      this.$openPage(
        '@/views/issue/releaseAudit/components/specialAudit-layer',
        '专项稽核',
        {
          callBack: (res, lid) => {
            // console.log(res,4444)
            this.createSpecialAudit(res, lid)
          }
        },
        {
          area: ['35%', '529px']
        }
      )
    },
    // 专项稽核
    async createSpecialAudit(params, lid) {
      let res = await auditSpecial(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
    importFile() {
      this.$openPage(
        '@/views/issue/releaseAudit/components/import-layer',
        '专项稽核批量导入',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
          }
        },
        {
          area: ['41%', '420px']
        }
      )
    },
    importCorrectFile(){
      this.$openPage(
        '@/views/issue/releaseAudit/components/import-correct-layer',
        '稽核结果修正批量导入',
        {
          area: ['41%', '320px']
        }
      )
    },
    //导出excel模板
    async downloadExcel(type) {
      let template = {
        special:{
          fileName:'专项稽核批量导入模版',
          api:downloadAuidtExcel
        },
        correct:{
          fileName:'稽核结果修正批量导入表样下载',
          api:correctDownloadExcel
        },
      }
      this.startLoading()
      try {
        // let res = await template[type].api()
        let fileObj = {
          fileName: `${template[type].fileName}.xlsx`
        }
        this.exportFile({}, template[type].api, fileObj)
        this.endLoading()
      } catch (err) {
        console.log(err)
        this.endLoading()
      }
    },
    checkAuth(auth){
      const permissions = this.$store.getters && this.$store.getters.permissions
      let hasPermissions = false // 本地开放全部权限，默认应为false
      for (let i of auth) {
        if (permissions.includes(i)) {
          hasPermissions = true
        }
      }
      return hasPermissions
    },
    handleSelfOperateChange(row) {  
      console.log(row,'row')
      if(this.selfOperate){
        this.$set(row, 'createBy', this.realName)
      }else{
        this.$set(row, 'createBy', '')
      }
      this.$refs.SearchForm.onSearchHandle()
    }
  },
  created() {
    this.getTableData()
  }
}
</script>


<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  ::v-deep .fontWidth {
    display: flex;
    div {
      width: auto;
    }
    div:nth-child(2) {
      .el-form-item__label {
        width: auto!important;
      }
    }
  }
  .choose-footer {
    text-align: center;
  }
}
</style>