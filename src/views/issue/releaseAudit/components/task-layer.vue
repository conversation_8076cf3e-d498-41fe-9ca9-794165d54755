<template>
  <div class="form-layer">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="选择发行时间区间稽核" name="timeRange">
        <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
          <el-form-item label="稽核任务名称" prop="taskName">
            <el-input v-model="formData.taskName"></el-input>
          </el-form-item>
          <el-form-item label="稽核时间" prop="aduitTime">
            <el-date-picker
              v-model="formData.aduitTime"
              type="datetimerange"
              range-separator="-"
              :start-placeholder="'开始时间'"
              :end-placeholder="'结束时间'"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSubmit">提交</el-button>
            <el-button @click="close">取消</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      
      <el-tab-pane label="指定车辆稽核" name="import">
        <importCarLayer ref="importLayer" @close="close" @uploadSuccess="handleUploadSuccess" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import importCarLayer from './importCar-layer.vue';

export default {
  components: {
    importCarLayer
  },
  mixins: [layerMix],
  data() {
    return {
      activeTab: 'timeRange',
      formData: {},
      rules: {
        taskName: [
          { required: true, message: '稽核任务名称不能为空', trigger: 'blur' }
        ],
        aduitTime: [
          { required: true, message: '稽核时间不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          let params = {
            taskName: this.formData.taskName,
            startTime: this.formData.aduitTime[0],
            endTime: this.formData.aduitTime[1]
          }
          this.getParam('callBack')(params, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    close() {
      this.closeDialog()
    },
    handleUploadSuccess() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  ::v-deep .el-range-editor {
    width: 100%;
  }
  
  // tab样式调整
  ::v-deep .el-tabs__nav {
    width: 100%;
    display: flex;
    
    .el-tabs__item {
      flex: 1;
      text-align: center;
      height: 40px;
      line-height: 40px;
      padding: 0;
      font-size: 14px;
      border: 1px solid #DCDFE6;
      margin-right: -1px;
      
      &.is-active {
        background-color: #fff;
      }
      
      &:first-child {
        border-radius: 4px 0 0 0;
      }
      
      &:last-child {
        border-radius: 0 4px 0 0;
      }
    }
  }
  
  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }
  
  ::v-deep .el-tabs__content {
    padding: 0 20px 20px 20px;
  }
}
</style>
