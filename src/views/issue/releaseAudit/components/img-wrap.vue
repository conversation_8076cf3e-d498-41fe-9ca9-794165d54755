<template>
  <div class="img-wrap">
    <div class="block" v-for="item in imgList" :key="item.id">
      <div class="demonstration">{{ item.name }}</div>
      <div class="img-box">
        <el-image
          style="width: 100%; height: 100%"
          :src="item.url"
          class="img"
          :preview-src-list="srcList"
        ></el-image>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    imgList: {
      type: Array,
      default: () => []
    },
    previewSrcKey: {
      type: String,
      default: 'url'
    }
  },
  data() {
    return {}
  },
  computed: {
    srcList() {
      return this.imgList.map(item => item[this.previewSrcKey])
    }
  }
}
</script>

<style lang="scss" scoped>
.img-wrap {
  display: flex;
  margin-bottom: 10px;
  padding-left: 20px;
  .block {
    margin-right: 20px;
    .demonstration {
      margin-bottom: 10px;
      font-size: 14px;
      color: #8492a6;
    }
    .img-box {
      height: 150px;
      width: 210px;
    }
  }
}
</style>