<template>
  <div class="form-layer">
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="demo-formData"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              placeholder="请输入任务名称"
              maxLength="50"
              v-model="formData.taskName"
              class="input-with-select"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <fieldset class="fieldset">
      <legend>附件上传</legend>
      <div slot="tip" class="el-upload__tip">注意事项：</div>
      <div slot="tip" class="el-upload__tip">
        仅支持xls、xlsx格式的Excel文件
      </div>
      <el-upload
        class="upload"
        ref="upload"
        :on-remove="handleRemove"
        :auto-upload="false"
        action="action"
        accept=".xls,.xlsx"
        :file-list="fileList"
        :multiple="false"
        :on-change="onChange"
      >
        <el-button slot="trigger" size="small" type="primary"
          >选取文件</el-button
        >
      </el-upload>
    </fieldset>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="handleSubmit" type="primary" size="mini"
        >生成稽核工单</el-button
      >
      <el-button size="mini" @click="close">关闭</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import config from '@/api/index'
import { getToken } from '@/utils/auth'
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {
        file: '',
        taskName: ''
      },
      fileList: [],
      rules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.submitUpload()
          // this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    close() {
      this.closeDialog()
    },
    verifyHandle() {
      if (!this.formData.file) {
        this.$message({
          type: 'warning',
          message: '请先添加文件'
        })
        return
      }
      return true
    },
    submitUpload() {
      if (!this.verifyHandle()) return
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }
      this.upload()
    },
    upload() {
      this.startLoading()
      let formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('taskName', this.formData.taskName)
      console.log(formData)
      let url =
        process.env.VUE_APP_BASE_API + '/issue-web/issueAudit/auditBatch'
      axios
        .post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: getToken()
          }
        })
        .then(res => {
          console.log('res', res)
          this.endLoading()

          if (res.data.code !== 200) {
            this.endLoading()
            this.$message({
              type: 'error',
              message: res.data.msg
            })
            return
          }
          this.$refs.upload.clearFiles()
          this.formData.taskName = ''
          this.formData.file = ''
          this.$message.success('操作成功')
          this.close()
          this.$emit('uploadSuccess')
        })
        .catch(err => {
          this.endLoading()
          this.$message({
            type: 'error',
            message: res.data.msg
          })
        })
    },
    handleRemove() {
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
  ::v-deep .el-range-editor {
    width: 100%;
  }
}

.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>
