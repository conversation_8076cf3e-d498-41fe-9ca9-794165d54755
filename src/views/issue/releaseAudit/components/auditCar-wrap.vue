<template>
  <div class="form-wrap">
    <el-form ref="form" :model="formData" label-width="100px" :rules="rules">
      <el-form-item label="稽核结果" prop="auditResult">
        <el-select
          v-model="formData.auditResult"
          placeholder="稽核结果"
          clearable
          style="width:100%"
        >
          <el-option
            v-for="item in auditResultOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="错误原因" prop="errorReason">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.errorReason"
        ></el-input>
      </el-form-item>
      <el-form-item style="margin-bottom:0;">
        <el-button
          type="primary"
          size="mini"
          :disabled="formData.auditResult != 6"
          @click="handleSubmit('操作成功')"
          >稽核通过</el-button
        >
        <el-button type="primary" size="mini" @click="handleSubmit('生成稽核工单成功')"
          >生成稽核工单</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { auditResultOption } from '@/common/const/optionsData'
import { vehicleCheck } from '@/api/equipment'

export default {
  props: {
    searchParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {},
      auditResultOption: [
        // { label: '审核通过', value: '0' },
        ...auditResultOption
      ],
      rules: {
        auditResult: [
          { required: true, message: '稽核结果不能为空', trigger: 'blur' }
        ],
        // errorReason: [
        //   { required: true, message: '错误原因不能为空', trigger: 'blur' }
        // ]
      }
    }
  },
  methods: {
    handleSubmit(tip) {
      let { carNo } = this.searchParams
      if (!carNo) {
        this.$message.error('请填写车牌号！')
        return
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          let params = {
            ...this.searchParams,
            ...this.formData
          }
          this.createTask(params,tip)
          // this.$emit('submit', this.formData)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    async createTask(params,tip) {
      let res = await vehicleCheck(params)
      if (res.code == 200) {
        this.$message.success(tip)
        this.$emit('closeWrap')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-wrap {
  width: 460px;
  height: 100%;
  ::v-deep .el-form-item {
    margin-bottom: 10px;
  }
  ::v-deep .el-range-editor {
    width: 100%;
  }
}
</style>
