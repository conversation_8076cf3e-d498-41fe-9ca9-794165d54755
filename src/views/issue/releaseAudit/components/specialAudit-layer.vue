<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="formData.taskName" />
      </el-form-item>
      <el-form-item label="车牌号码" prop="vehicleProvinceEnd">
        <el-input
          placeholder="请输入车牌号"
          maxLength="11"
          v-model="formData.vehicleProvinceEnd"
          class="input-with-select"
        >
          <el-select
            v-model="formData.vehicleProvincePre"
            style="width: 80px;"
            slot="prepend"
            placeholder="请选择"
          >
            <el-option
              v-for="(value, key) in provinces"
              :key="key"
              :label="key"
              :value="value"
            ></el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item label="车牌颜色" prop="carColor">
        <el-select
          v-model="formData.carColor"
          placeholder="请选择车牌颜色"
          clearable
          style="width:100%"
        >
          <el-option
            v-for="item in licenseColorOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="ETC卡号" prop="cardNo">
        <el-input v-model="formData.cardNo"></el-input>
      </el-form-item>
      <el-form-item label="稽核结果" prop="auditResult">
        <!-- <el-input v-model="formData.auditResult"></el-input> -->
        <el-select
          v-model="formData.auditResult"
          placeholder="稽核结果"
          clearable
          style="width:100%"
        >
          <el-option
            v-for="item in auditResultOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="错误原因" prop="errorReason" :required="formData.auditResult != 6">
        <el-input
          type="textarea"
          :rows="3"
          v-model="formData.errorReason"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">生成稽核工单</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  licenseColorOption,
  provinces,
  auditResultOption
} from '@/common/const/optionsData'
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {
        vehicleProvincePre: '桂'
      },
      provinces,
      licenseColorOption,
      auditResultOption,
      rules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' }
        ],
        vehicleProvinceEnd: [
          { required: true, message: '车牌号码不能为空', trigger: 'blur' }
        ],
        carColor: [
          { required: true, message: '车牌颜色不能为空', trigger: 'blur' }
        ],
        cardNo: [
          { required: true, message: 'ETC卡号不能为空', trigger: 'blur' }
        ],
        auditResult: [
          { required: true, message: '稽核结果不能为空', trigger: 'blur' }
        ],
        errorReason: [{ validator: this.checkErrorReason, trigger: 'blur' }]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          let params = {
            ...this.formData,
            carNo:
              this.formData.vehicleProvincePre +
              this.formData.vehicleProvinceEnd
          }
          delete params.vehicleProvincePre
          delete params.vehicleProvinceEnd

          this.getParam('callBack')(params, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    checkErrorReason(rule, value, callback) {
      if (!value && this.formData.auditResult != 6) {
        callback(new Error('错误原因不能为空'))
      } else {
        callback()
      }
    },
    close() {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
}
</style>
q