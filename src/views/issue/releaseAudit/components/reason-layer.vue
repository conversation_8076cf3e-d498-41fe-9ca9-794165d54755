<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="90px" :rules="rules">
      <el-form-item label="稽核结果" prop="auditResult" v-if="type != 'reject'">
        <el-select
          v-model="formData.auditResult"
          placeholder="稽核结果"
          clearable
          style="width:100%"
        >
          <el-option
            v-for="item in auditResultOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="labelText" prop="errorReason">
        <el-input
          type="textarea"
          :rows="4"
          v-model="formData.errorReason"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { auditResultOption } from '@/common/const/optionsData'
import layerMix from '@/utils/layerMixins'
export default {
  mixins: [layerMix],
  data() {
    return {
      formData: {},
      labelText: '错误原因',
      auditResultOption: [
        ...auditResultOption,
        { value: '7', label: '其他类型' }
      ],
      rules: {
        auditResult: [
          { required: true, message: '稽核结果不能为空', trigger: 'blur' }
        ],
        errorReason: [
          { required: true, message: `错误原因不能为空`, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交表单数据
          // this.$emit('submit', this.formData)
          this.getParam('callBack')(this.formData, this.layerid)
        } else {
          // 表单验证失败
          console.log('表单验证失败')
          return false
        }
      })
    },
    close() {
      this.closeDialog()
    }
  },
  created() {
    let formData = this.getParam('formData')
    let type = this.getParam('type')
    this.type = type
    if (formData) {
      this.formData = formData
    }
    console.log(type)
    if (type && type == 'reject') {
      this.labelText = '驳回原因'
      this.rules.errorReason[0].message = '驳回原因不能为空'
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
  ::v-deep .el-range-editor {
    width: 100%;
  }
}
</style>
