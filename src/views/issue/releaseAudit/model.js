
import {
  licenseColorOption
} from '@/common/const/optionsData.js'
import { getVehicleColor } from '@/common/method/formatOptions'

//稽核任务表格
export const listColoumns = (_this) => {
  return [
    {
      prop: 'taskId',
      label: '任务ID',
      // width: 200,
    },
    {
      prop: 'taskName',
      label: '任务名称',
      width: 180,
    },
    {
      prop: 'manufacturerContactName',
      label: '稽核时间区间',
      width: 320,
      formatter: (obj, row) => {
        return `${row.startTime ? row.startTime : ''} - ${row.endTime ? row.endTime : ''}`
      }
    },
    {
      prop: 'updateTime',
      label: '任务执行时间',
      width: 200,
    },
    {
      prop: 'taskStatus',
      label: '任务状态',
    },
    {
      prop: 'createBy',
      label: '操作人',
    },
    {
      prop: 'taskSource',
      label: '任务生成方式',
    },
    {
      prop: 'createTime',
      label: '任务生成时间/导入时间',
      width: 200,
    },
    {
      prop: 'action',
      width: 380,
      label: '操作',
      fixed: 'right',
    }
  ]
}


//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'taskName',
      label: '任务名称',
      default: '',
    },
    {
      type: 'slot',
      field: 'manufacturerName',
      label: '本人',
      default: 0,
    },
  ]
}




//稽核车辆表格
export const carListColoumns = (_this) => {
  return [
    {
      prop: 'custName',
      label: '用户名称',
      // width: 200,
    },
    {
      prop: 'custMobile',
      label: '手机号',
      width: 150
    },
    {
      prop: 'carNo',
      label: '车牌',
      width: 120,
    },
    {
      prop: 'carColorStr',
      label: '车牌颜色',
      // formatter: (row) => {
      //   return getVehicleColor(row)
      // }
    },
    {
      prop: 'isTrunk',
      label: '客货类型',
    },
    {
      prop: 'carStyle',
      label: '车辆使用性质',
      width: 150
    },
    {
      prop: 'carType',
      label: '车型',
    },
    {
      prop: 'vehicleType',
      label: '车辆类型',
    },
    {
      prop: 'cardNo',
      label: 'ETC卡号',
      width: 200,
    },
    {
      prop: 'obuNo',
      label: 'OBU号',
      width: 180
    },
    {
      prop: 'releasePerson',
      label: '发行人',
    },
    {
      prop: 'releaseDate',
      label: '发行时间',
      width: 180
    },
    {
      prop: 'branchCode',
      label: '发行网点',
      width: 180
    },
    {
      prop: 'auditResult',
      label: '稽核结果',
      width: 180
    },
    {
      prop: 'auditStatus',
      label: '整改状态',
    },
    {
      prop: 'modifyStatus',
      label: '车辆修改状态',
      width: 150
    },
    {
      prop: 'cardStatus',
      label: '卡状态',
      width: 130
    },
    {
      prop: 'obuStatus',
      label: 'OBU状态',
      width: 130
    },
    {
      prop: 'errorReason',
      label: '错误原因',
      width: 350,
      isCustomTooltip:true,
      wordWrap:true
    },
    {
      prop: 'action',
      width: 240,
      label: '操作',
      fixed: 'right'
    }
  ]
}


//ETC车辆首次通行车道图片稽核
export const carListForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'carNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'carColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      default: '',
      options: licenseColorOption
    },
  ]
}

export const carListFormRules = {
  carNo: [
    { required: true, message: '请输入车牌号', trigger: 'change' },
  ],
  carColor: [
    { required: true, message: '请输入车牌颜色', trigger: 'change' },
  ],
}