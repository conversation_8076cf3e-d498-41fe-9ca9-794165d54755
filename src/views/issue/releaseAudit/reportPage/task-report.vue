<template>
  <div>
    <!-- 搜索栏 -->
    <dart-search
      slot="report-search"
      ref="searchForm"
      :formSpan="24"
      :searchOperation="false"
      :fontWidth="1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <template v-for="item in formProperties">
          <dart-search-item
            :key="item.fieldKey"
            :label="item.fieldLabel"
            :prop="item.fieldKey"
          >
            <template v-if="item.element != 'custom'">
              <searchField
                :fieldProps="item.fieldProps"
                :fieldOptions="item"
                :ref="item.ref"
                v-model="search[item.fieldKey]"
              ></searchField>
            </template>
          </dart-search-item>
        </template>

        <dart-search-item :is-button="true" :colElementNum="3">
          <div class="g-flex">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import searchField from '@/components/schemaQuery/buildingBlock/base.vue'
import { datePickerSchema } from '@/components/schemaQuery/schema'
import { queryReport } from '@/views/reportstatistics/components/service'
import { datePickerOptions } from '@/components/schemaQuery/tool'
var moment = require('moment')

export default {
  data() {
    return {
      loading: false,
      rules: {
        logoutApplyStartDate: [
          { required: true, message: '请选择开始日期', trigger: 'change' }
        ],
        logoutApplyEndDate: [
          { required: true, message: '请选择结束日期', trigger: 'change' }
        ]
      },
      search: {
        name: 'issueAuditDetailReport', // 报表名称
        logoutApplyStartDate: '',
        logoutApplyEndDate: ''
      },
      formProperties: {
        logoutApplyStartDate: {
          ...datePickerSchema.datePicker,
          fieldLabel: '开始日期',
          fieldKey: 'logoutApplyStartDate',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps
            // pickerOptions: datePickerOptions
          }
        },
        logoutApplyEndDate: {
          ...datePickerSchema.datePicker,
          fieldLabel: '结束日期',
          fieldKey: 'logoutApplyEndDate',
          fieldProps: {
            ...datePickerSchema.datePicker.fieldProps
            // pickerOptions: datePickerOptions
          }
        }
      }
    }
  },

  components: {
    dartSearch,
    dartSearchItem,
    searchField
  },

  methods: {
    onValid() {
      if (moment(this.search.logoutApplyStartDate).isAfter(this.search.logoutApplyEndDate)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      if (
        moment(this.search.logoutApplyEndDate).diff(
          moment(this.search.logoutApplyStartDate),
          'months'
        ) > 2
      ) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不等大于三个月'
        })
        return
      }
      return true
    },

    formatParams() {
      let params = JSON.parse(JSON.stringify(this.search))
      delete params.deptID
      return params
    },
    onSearchHandle() {
      if (!this.onValid()) return
      this.$refs['searchForm'].$children[0].validate(valid => {
        if (valid) {
          let params = this.formatParams()
          queryReport(params)
        } else {
          return false
        }
      })
    },
    onResultHandle() {
      this.$nextTick(function() {
        this.$refs['searchForm'].resetForm()
      })
    }
  }
}
</script>
<style lang="sass"></style>
