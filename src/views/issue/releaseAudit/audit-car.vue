<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    ></SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="batchCheck"
          >复核通过</el-button
        >
        <el-button type="primary" size="mini" @click="auditReject"
          >复核驳回</el-button
        >
      </div>
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="true"
        @changeTableData="changeTableData"
        @selectChange="selectChange"
      >
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button
            v-if="$route.query.type != 'mistake'"
            size="mini"
            type="text"
            :disabled="scope.modifyStatus != '待整改'"
            @click="handelRow(scope, 'enterInfo')"
            >录入整改信息</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-if="$route.query.type == 'mistake'"
            @click="handelRow(scope, 'mistake')"
            >编辑错误原因</el-button
          >
          <el-button
            size="mini"
            type="text"
            v-if="!$route.query.type"
            @click="handelRow(scope, 'mistake')"
            >修改稽核结果</el-button
          >
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { carListColoumns, carListForm } from './model'
import { queryResultList, errorEdit, resultCheck,resultReject } from '@/api/equipment'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: queryResultList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      dataKey: 'data',
      pageParams: {}
    }
  },
  computed: {
    listColoumns() {
      return carListColoumns(this)
    },
    formConfig() {
      return carListForm(this)
    }
  },
  methods: {
    // 编辑错误弹框
    editReason(row) {
      this.$openPage(
        '@/views/issue/releaseAudit/components/reason-layer',
        '编辑错误原因',
        {
          formData: {
            errorReason: row.errorReason,
            auditResult: row.auditResultCode
          },
          callBack: (res, lid) => {
            let params = {
              auditId: row.auditId,
              ...res
            }
            this.resonSubmitCallback(params, lid)
          }
        },
        {
          area: ['25%', '300px']
        }
      )
    },
    // 错误原因编辑
    async resonSubmitCallback(params, lid) {
      let res = await errorEdit(params)
      if (res.code == 200) {
        this.$message.success('成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
    // 操作
    handelRow(row, type) {
      if (type == 'enterInfo') {
        this.$router.push({
          path: '/issue/add',
          query: {
            type: 'enterInfo',
            auditId: row.auditId,
            carNo: row.carNo,
            carColor: row.carColor
          }
        })
      } else if (type == 'mistake') {
        this.editReason(row)
      }
    },
    // 批量复核
    batchCheck() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let arr = JSON.parse(JSON.stringify(this.selectArr))
      let checkArr = arr.filter(item => (item.auditStatus != '整改完成待复核' && item.auditStatus != '整改中'))
      if (checkArr.length > 0) {
        this.$message.warning(
          '选中的数据中，有整改状态不为“整改完成待复核”和“整改中”，请重新选择！'
        )
        return
      }
      let params = {
        auditId: this.selectArr.map(item => item.auditId)
      }
      this.checkApi(params)
      console.log(params)
    },
    // 复核接口
    checkApi(params) {
      this.$confirm('请确认是否要复核', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          let res = await resultCheck(params)
          if (res.code == 200) {
            this.$message.success('成功')
            this.getTableData()
          }
        })
        .catch(() => {})
    },
    // 复核驳回
    auditReject() {
      if (this.selectArr.length != 1) {
        this.$message.warning('请选择一条数据')
        return
      }
      let row = this.selectArr[0]
      this.$openPage(
        '@/views/issue/releaseAudit/components/reason-layer',
        '复核驳回',
        {
          type: 'reject',
          callBack: (res, lid) => {
            let params = {
              auditId: row.auditId,
              remark: res.errorReason
            }
            console.log(params, '1231')
            this.rejectSubmitCallback(params, lid)
          }
        },
        {
          area: ['25%', '240px']
        }
      )
    },
    // 复核驳回
    async rejectSubmitCallback(params, lid) {
      let res = await resultReject(params)
      if (res.code == 200) {
        this.$message.success('成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    }
  },
  created() {
    let { type, taskId } = this.$route.query
    if (type == 'list' || type == 'mistake') {
      this.pageParams.taskId = taskId
      this.getTableData()
    }
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;
  .top-btn {
    margin-bottom: 10px;
  }
  ::v-deep .fontWidth {
    display: flex;
    div {
      width: auto;
    }
  }
  .choose-footer {
    text-align: center;
  }
}
</style>