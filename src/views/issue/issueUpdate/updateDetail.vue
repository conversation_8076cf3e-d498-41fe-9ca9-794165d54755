<template>
  <div class="account-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="visible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 资料补传详情 </span>
        </div>
      </template>

      <div class="img-wrapper">
        <div v-for="(item, index) in imgList" :key="index" class="img-list">
          <el-image
            :preview-src-list="srcList"
            :src="item.url"
            style="height: 200px; width: 300px"
          ></el-image>
          <span class="img-label">{{ item.label }}</span>
        </div>
      </div>
      <template slot="footer">
        <el-button @click="close()" size="mini">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    imgList: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      isFullscreen: false,
      showLoading: false,
      center: 'center',
    }
  },
  watch: {
    imgList(val) {
      let srcList = []
      for (let key in val) {
        srcList.push(val[key]['url'])
      }
      this.srcList = srcList
    },
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
  },
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}
::v-deep .form_dialog {
  .el-dialog--center {
    margin-top: 5vh !important;
  }
  .el-dialog.is-fullscreen {
    margin-top: 0 !important;
  }
}
.table {
  padding: 0;
  .el-table {
    margin-bottom: 0;
  }
}
.pagination {
  margin-top: 0;
}
.bottom-wrapper {
  margin-top: 50px;
}

.img-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.img-list {
  display: flex;
  margin-top: 30px;
  flex-direction: column;
  align-items: center;
}
.img-label {
  margin-top: 15px;
  font-size: 16px;
}
</style>
