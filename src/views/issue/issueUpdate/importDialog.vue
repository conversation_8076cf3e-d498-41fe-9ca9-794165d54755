<template>
  <div class="import-dialog" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :show-close="true"
      title="补传信息导入"
      :before-close="handleCloseIcon"
      width="45%"
    >
      <fieldset class="fieldset">
        <legend class="fieldset-legend">附件上传</legend>

        <div slot="tip" class="el-upload__tip">1、仅支持xls格式的Excel文件</div>
        <div slot="tip" class="el-upload__tip">
          2、导入工作薄按类型分别为：[ 资料补传 | 信息变更 ]
        </div>
        <div slot="tip" class="el-upload__tip">
          3、资料补传包含字段：[ 车牌 | 车牌颜色 | 身份证 | 行驶证 |
          行驶证车辆图片 | 车头照 | 营业执照 | 道路运输证 ]
        </div>
        <div slot="tip" class="el-upload__tip">
          3、信息变更包含字段：[ 车牌 | 车牌颜色 | 长 | 宽 | 高 | 车辆使用性质 |
          车型 | 客货标识 | 车型 | 座位数 | 总质量 | 车轴数 | 行驶证车辆类型 ]
        </div>
        <div slot="tip" class="el-upload__tip">
          <div
            style="font-size: 14px; cursor: pointer; color: #409eff"
            @click="downFile"
          >
            稽核补传信息导入模板
          </div>
        </div>
        <el-upload
          class="upload"
          ref="upload"
          :on-remove="handleRemove"
          :auto-upload="false"
          action="action"
          accept=".xls,.xlsx"
          :file-list="fileList"
          :multiple="false"
          :on-change="onChange"
        >
          <el-button slot="trigger" size="small" type="primary"
            >选取文件</el-button
          >
        </el-upload>
        <div class="form-box">
          <div class="label-title">整改定时任务功能设置</div>
          <el-radio-group v-model="formData.taskFlag" size="mini">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </div>
        <div class="form-box">
          <div class="label-title">限制对象</div>
          <el-radio-group v-model="formData.blackType" :disabled="formData.taskFlag == 0" size="mini">
            <el-radio :label="0">ETC卡片</el-radio>
            <el-radio :label="1">OBU</el-radio>
            <el-radio :label="2">ETC卡片及OBU</el-radio>
          </el-radio-group>
        </div>
        <div class="form-box">
          <div class="label-title">结束日期设置</div>
          <div class="label-content">
            <span>任务结束日期：</span>
            <el-date-picker
              v-model="formData.endTime"
              :disabled="formData.taskFlag == 0"
              type="date"
              size="mini"
              value-format="yyyy-MM-dd"
              :clearable="false"
              placeholder="选择日期"
            >
            </el-date-picker>
          </div>
        </div>
      </fieldset>

      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload" type="primary" size="mini"
          >确定</el-button
        >
        <el-button size="mini" @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dartSearchItem from '@/components/Search/searchItem'

export default {
  components: {
    dartSearchItem
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogFormVisible: false,
      showLoading: false,
      formData: {
        taskFlag: 0,
        file: ''
      },
      fileList: []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fileList = []
        this.formData.file = ''
        // this.formData.source = ''
      }
      this.responseDialogVisible = false
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  created() {
    // this.formData.taskFlag = 0
  },
  methods: {
    downFile() {
      this.$request({
        url: this.$interfaces.downTemplate,
        method: 'get',
        responseType: 'blob'
      })
        .then(res => {
          this.getBlob(res, 'application/vnd.ms-excel', '稽核补传导入模板')
        })
        .catch(error => {
          console.log(error)
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    moneyFilter(val) {
      let value = val
      if (!value) return value
      return this.toDecimal2(value)
    },
    toDecimal2(x) {
      var f = parseFloat(x)
      if (isNaN(f)) {
        return false
      }
      var f = Math.round(x * 100) / 100
      var s = f.toString()
      var rs = s.indexOf('.')
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + 2) {
        s += '0'
      }
      return s
    },
    submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件'
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型'
          })
          return
        }
      }

      let { taskFlag, endTime, blackType } = this.formData
      if (taskFlag == 1 && (!endTime || (!blackType && blackType != 0))) {
        this.$message({
          type: 'error',
          message: '请设置限制对象和结束日期！'
        })
        return
      }
      console.log(this.formData)
      this.upload()
    },

    upload() {
      this.showLoading = true
      let formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('taskFlag', this.formData.taskFlag)

      if (this.formData.taskFlag == 1) {
        formData.append('blackType', this.formData.blackType)
        formData.append('endTime', this.formData.endTime)
      }

      this.$request({
        url: this.$interfaces.updateDeviceUpload,
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      })
        .then(res => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = ''
          console.log('res', res)
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '导入成功'
            })
            this.$emit('uploadSuccess')
          }
        })
        .catch(error => {
          this.showLoading = false
          console.log(error)
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.fieldset-legend {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  width: 80px;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
.import-dialog {
  .form-box {
    margin: 10px 0 15px 0;
    .label-title {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 10px;
    }
    .label-content {
      span {
        margin-right: 10px;
        color: #606266;
        font-weight: 400;
        padding-left: 3px;
      }
      // ::v-deep .el-date-editor{
      //   // width: 100%;
      //   height: 100%;
      // }
    }
  }
}
</style>