<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌号：" prop="vehicleCode">
            <el-input
              v-model="search.vehicleCode"
              placeholder="请输入车牌号"
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="vehicleColor">
            <el-select
              v-model="search.vehicleColor"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="业务类型：" prop="bizType">
            <el-select v-model="search.bizType" placeholder="请选择" clearable>
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="客货标识：" prop="isTrunk">
            <el-select v-model="search.isTrunk" placeholder="请选择" clearable>
              <el-option
                v-for="item in vehicleType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="资料补传状态：" prop="source">
            <el-select
              v-model="search.imgStatus"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in imgStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="信息变更状态：" prop="source">
            <el-select
              v-model="search.infoStatus"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in infoStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px; margin-left: 35px"
            :span="24"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              ><i class="el-icon-search"></i> 搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <el-button size="mini" type="primary" v-permisaction="['entry:orderRaise']" @click="toAdd"
              ><i class="el-icon-plus"></i> 新增</el-button
            >
            <!-- <el-button
              size="mini"
              type="primary"
              @click="importDialogVisible = true"
              ><i class="el-icon-upload"></i> 导入</el-button
            > -->
            <el-button
              size="mini"
              type="primary"
              v-permisaction="['entry:orderUpload']"
              @click="importDialogVisible = true"
              ><i class="el-icon-upload"></i>上传记录</el-button
            >
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column
          prop="vehicleCode"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column prop="vehicleColor" align="center" label="车牌颜色">
          <template slot-scope="scope">
            {{ getType(licenseColorOption, scope.row.vehicleColor) }}
          </template>
        </el-table-column>
        <el-table-column align="center" min-width="200" label="长x宽x高(MM)">
          <template slot-scope="scope">
            {{ scope.row.vehicleLength }}
            {{ scope.row.vehicleWidth ? 'x' + scope.row.vehicleWidth : '' }}
            {{ scope.row.vehicleHeight ? 'x' + scope.row.vehicleHeight : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleAxles" align="center" label="车轴数" />
        <el-table-column
          prop="userCharacter"
          align="center"
          label="车辆使用性质"
        >
          <template slot-scope="scope">
            {{ getType(cartype, scope.row.userCharacter) }}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleNationalType" align="center" label="车型">
          <template slot-scope="scope">
            {{ getType(vehicleCatgoryType, scope.row.vehicleNationalType) }}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleSeat" align="center" label="座位数" />
        <el-table-column prop="totalWeight" align="center" label="总质量" />
        <el-table-column
          prop="vehicleType"
          align="center"
          label="行驶证车辆类型"
        />
        <!-- <el-table-column
          prop="carNoColor"
          align="center"
          label="行驶证车辆类型"
        >
          <template slot-scope="scope">
            {{ getType(typeList.carColors, scope.row.carNoColor) }}
          </template>
        </el-table-column> -->
        <el-table-column prop="bizType" align="center" label="业务类型">
          <template slot-scope="scope">
            {{ getType(typeList, scope.row.bizType) }}
          </template>
        </el-table-column>
        <el-table-column prop="imgStatus" align="center" label="资料补传状态">
          <template slot-scope="scope">
            {{ getType(imgStatus, scope.row.imgStatus) }}
          </template>
        </el-table-column>
        <el-table-column prop="infoStatus" align="center" label="信息更新状态">
          <template slot-scope="scope">
            {{ getType(infoStatus, scope.row.infoStatus) }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" align="center" label="录入人" />
        <el-table-column
          prop="createTime"
          align="center"
          label="录入时间"
          min-width="160"
        />
        <el-table-column
          prop="orderEndTime"
          align="center"
          label="修正时间"
          min-width="160"
        />
        <el-table-column prop="overdueFlag" align="center" label="是否超期">
          <template slot-scope="scope">
            {{
              scope.row.overdueFlag == 1 ? '是' : '否'
            }}
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              v-if="isShowUpdateBtn(scope.row)"
              @click="modifyOrder(scope.row)"
              v-permisaction="['entry:orderModify']"
              type="warning"
              size="mini"
              >修改</el-button
            >
            <el-button
              v-if="isShowImgBtn(scope.row)"
              @click="showImgs(scope.row)"
              v-permisaction="['entry:viewPictures']"
              type="primary"
              size="mini"
              >图片详情</el-button
            >
            <el-button
              v-if="isShowDelBtn(scope.row)"
              @click="delOrder(scope.row.id)"
              v-permisaction="['entry:orderDelete']"
              type="danger"
              size="mini"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="total > search.pageSize" class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <import-dialog
      :visible.sync="importDialogVisible"
      @uploadSuccess="updateList"
    >
    </import-dialog>
    <updateDetailDialog
      :visible.sync="updateDetailDialogVisible"
      :imgList="imgList"
    >
    </updateDetailDialog>
  </div>
</template>

<script>
import importDialog from './importDialog'
import updateDetailDialog from './updateDetail'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
// import { getToken } from '@/utils/auth'
// var moment = require('moment')
import {
  licenseColorOption,
  cartype,
  vehicleCatgoryType,
  vehicleType
} from '@/common/const/optionsData'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    importDialog,
    updateDetailDialog
  },
  data() {
    return {
      licenseColorOption,
      vehicleType,
      cartype,
      vehicleCatgoryType,
      loading: false,
      importDialogVisible: false,
      updateDetailDialogVisible: false,
      center: 'center',
      search: {
        infoStatus: '',
        imgStatus: '',
        vehicleType: '',
        vehicleColor: '',
        vehicleCode: '',
        bizType: '',
        isTrunk: '',
        pageIndex: 1,
        pageSize: 20
      },
      total: '',
      typeList: [
        {
          value: '1',
          label: '资料补传'
        },
        {
          value: '2',
          label: '信息变更'
        },
        {
          value: '3',
          label: '资料补传 + 信息变更'
        }
      ],
      imgStatus: [
        {
          value: '1',
          label: '待上传'
        },
        {
          value: '2',
          label: '已上传'
        }
      ],
      infoStatus: [
        {
          value: '1',
          label: '待变更'
        },
        {
          value: '2',
          label: '已变更'
        }
      ],
      tableData: [],
      paymentDetail: {},
      imgList: {},
      imgListLabel: {
        image1: '身份证人像面',
        image2: '单位营业执照副本',
        image3: '行驶证正页',
        image6: '行驶证车辆照片',
        image11: '身份证国徽面',
        image12: '行驶证副页',
        image15: '车头照片',
        image20: '道路运输证'
      }
    }
  },
  // computed: {
  // ...mapGetters(['refundSearch']),
  // },
  created() {
    // console.log('refundRearch', this.refundSearch)
    // if (Object.keys(this.refundSearch).length > 0) {
    //   this.search = this.refundSearch
    // }
    // this.getAuditBlackSearch()
    // this.getDictionaries()
    this.getList()
  },
  methods: {
    isShowUpdateBtn(item) {
      let flag = false
      if (item.bizType == '1') {
        if (item.imgStatus != '2') {
          flag = true
        }
      } else if (item.bizType == '2') {
        if (item.infoStatus != '2') {
          flag = true
        }
      } else if (item.bizType == '3') {
        if (item.infoStatus != '2' && item.imgStatus != '2') {
          flag = true
        }
      }
      return flag
    },
    isShowDelBtn(item) {
      let flag = false
      if (item.bizType == '1') {
        if (item.imgStatus != '2') {
          flag = true
        }
      } else if (item.bizType == '2') {
        if (item.infoStatus != '2') {
          flag = true
        }
      } else if (item.bizType == '3') {
        if (item.infoStatus != '2' || item.imgStatus != '2') {
          flag = true
        }
      }
      return flag
    },
    isShowImgBtn(item) {
      let flag = false
      if (item.bizType == '1' || item.bizType == '3') {
        if (item.imgStatus == '2') {
          flag = true
        }
      }
      return flag
    },
    showImgs(item) {
      let params = {
        id: item.id,
        vehicleCode: item.vehicleCode,
        vehicleColor: item.vehicleColor
      }
      this.$request({
        url: this.$interfaces.viewImgs,
        data: params,
        method: 'post'
      })
        .then(res => {
          let result = res.data
          for (var key in result) {
            if (result[key] == '') {
              delete result[key]
            }
          }
          for (let label in this.imgListLabel) {
            for (let key in result) {
              if (label == key) {
                result[key] = {
                  url: result[key],
                  label: this.imgListLabel[label]
                }
              }
            }
          }
          this.imgList = result
          this.updateDetailDialogVisible = true
        })
        .catch(error => {
          console.log(error)
        })
    },
    modifyOrder(item) {
      this.$router.push({
        path: './add',
        query: {
          detail: item
        }
      })
    },
    toAdd() {
      this.$router.push({
        path: './add'
      })
    },
    delOrder(id) {
      let params = { id: id }
      this.$request({
        url: this.$interfaces.delDevice,
        data: params,
        method: 'post'
      })
        .then(res => {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.getList()
        })
        .catch(error => {
          this.loading = false
          console.log(error)
        })
    },
    // 查询列表
    getList() {
      this.loading = true
      let params = this.search
      this.$request({
        url: this.$interfaces.getUpdateDeviceList,
        data: params,
        method: 'post'
      })
        .then(res => {
          this.loading = false
          console.log('this.$interfaces', res)
          this.tableData = res.data.records
          this.total = res.data.total
        })
        .catch(error => {
          this.loading = false
          console.log(error)
        })
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    changePage(page) {
      this.search.pageIndex = page
      this.getList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getList()
    },
    onSearchHandle() {
      this.search.pageIndex = 1
      console.log('当前页面audit', this.search.pageIndex)
      // //缓存搜索参数
      // this.$store
      //   .dispatch('containerRefund/setRefundSearch', this.search)
      //   .then((res) => {
      //     console.log('缓存过后的search', res)
      //   })
      this.getList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      // this.search.isJet = '1'
      this.search.pageIndex = 1
      this.search.pageSize = 20
      // //清除缓存
      // this.$store
      //   .dispatch('containerRefund/removeRefundSearch')
      //   .then((res) => {})
    },
    updateList() {
      this.search.pageIndex = 1
      this.search.pageSize = 20
      this.importDialogVisible = false
      this.getList()
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  display: flex;
}
::v-deep.dart-search-wrapper
  .dart-search-container
  .collapse-wrapper
  .el-col-24:nth-child(5)
  .el-form-item__label {
  width: 200px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  flex: 1;
}
.refund {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
