<template>
  <div class="hand-input">
    <div class="input-box">
      <div class="title-warpper">
        <div class="title">新增</div>
      </div>
      <div class="form-wrapper">
        <el-form
          :inline="true"
          ref="ruleForm"
          :rules="rules"
          :model="ruleForm"
          label-width="160px"
          class="form"
        >
          <el-row :xs="24" :sm="24">
            <el-col :span="12">
              <el-form-item label="车牌号：" prop="vehicleCode">
                <el-input
                  v-model="ruleForm.vehicleCode"
                  placeholder="车牌号"
                  :disabled="updateFlag"
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="车牌颜色：" prop="vehicleColor">
                <el-select
                  v-model="ruleForm.vehicleColor"
                  placeholder="请选择"
                  :disabled="updateFlag"
                  clearable
                >
                  <el-option
                    v-for="item in licenseColorOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24">
            <el-col :span="24">
              <el-form-item label="业务类型：" :key="detail.id">
                <!-- <el-checkbox label="资料补传"></el-checkbox>
                  <el-checkbox label="信息变更"></el-checkbox> -->
                <el-checkbox
                  v-for="item in workList"
                  :label="item.label"
                  :key="item.value"
                  :checked="item.checked"
                  @change="handleWorkChange($event, item)"
                ></el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :xs="24" :sm="24">
            <el-col :span="24">
              <el-form-item label="整改定时任务功能设置：" class="is-required">
                <el-radio-group
                  v-model="task.taskFlag"
                  :disabled="detail.overdueFlag == 1"
                  size="mini"
                >
                  <el-radio :label="1">开启</el-radio>
                  <el-radio :label="0">关闭</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24">
            <el-col :span="24">
              <el-form-item
                label="结束日期设置："
                :class="{ 'is-required': task.taskFlag == 1 }"
              >
                <el-date-picker
                  v-model="task.endTime"
                  type="date"
                  :disabled="task.taskFlag == 0 || detail.overdueFlag == 1"
                  value-format="yyyy-MM-dd"
                  size="mini"
                  :clearable="false"
                  placeholder="选择日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24">
            <el-col :span="24">
              <el-form-item
                label="限制对象："
                :class="{ 'is-required': task.taskFlag == 1 }"
              >
                <el-radio-group
                  :disabled="task.taskFlag == 0 || detail.overdueFlag == 1"
                  v-model="task.blackType"
                  size="mini"
                >
                  <el-radio :label="0">ETC卡片</el-radio>
                  <el-radio :label="1">OBU</el-radio>
                  <el-radio :label="2">ETC卡片及OBU</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :xs="24" :sm="24">
            <el-col :span="24">
              <el-form-item
                label="限制原因："
                :class="{ 'is-required': task.taskFlag == 1 }"
              >
                <el-input
                  :disabled="task.taskFlag == 0 || detail.overdueFlag == 1"
                  type="textarea"
                  :rows="3"
                  v-model="task.blackRemark"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 资料补传业务 -->
          <template v-if="workList[0].checked">
            <el-row :xs="24" :sm="24">
              <el-col :span="24">
                <el-form-item label="资料补传业务：" :key="detail.id">
                  <el-checkbox
                    v-for="item in imgList"
                    :label="item.label"
                    :key="item.value"
                    :checked="item.checked"
                    @change="handleImgChange($event, item)"
                  ></el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="补传状态：">
                  {{ statusList.imgStatus }}
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <!-- 信息变更业务 -->
          <template v-if="workList[1].checked">
            <el-row :xs="24" :sm="24">
              <el-col :span="24">
                <div class="title-warpper">
                  <div class="title">信息变更业务</div>
                </div>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="长宽高(MM)：">
                  <el-input
                    v-model="infModel.vehicleLength"
                    placeholder="长"
                    type="number"
                    clearable
                  ></el-input>
                  <el-input
                    v-model="infModel.vehicleWidth"
                    placeholder="宽"
                    type="number"
                    clearable
                  ></el-input>
                  <el-input
                    v-model="infModel.vehicleHeight"
                    placeholder="高"
                    type="number"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="车辆使用性质：">
                  <el-select
                    v-model="infModel.userCharacter"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in cartype"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="车型：" prop="">
                  <el-select
                    v-model="infModel.vehicleNationalType"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in vehicleCatgoryType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客货标识：" prop="">
                  <!-- <el-select
                    v-model="infModel.isTrunk"
                    placeholder="请选择"
                    clearable
                  >
                    <el-option
                      v-for="item in vehicleType"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select> -->
                  {{ getType(vehicleType, infModel.isTrunk) }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="座位数(座)：" prop="">
                  <el-input
                    v-model="infModel.vehicleSeat"
                    type="number"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总质量(KG)：" prop="">
                  <el-input
                    v-model="infModel.totalWeight"
                    type="number"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="车轴数(个)：" type="number" prop="">
                  <el-input
                    v-model="infModel.vehicleAxles"
                    type="number"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="行驶证车辆类型：" prop="">
                  <el-input v-model="infModel.vehicleType" clearable></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :xs="24" :sm="24">
              <el-col :span="12">
                <el-form-item label="变更状态：">
                  {{ statusList.infoStatus }}
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-form>
      </div>
      <div class="bottom-btn">
        <el-button
          type="primary"
          :disabled="isNoCar"
          @click="submitForm('ruleForm')"
          >{{ btnTitle }}</el-button
        >
        <el-button @click="back()">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
var _that
import {
  licenseColorOption,
  cartype,
  vehicleCatgoryType,
  vehicleType
} from '@/common/const/optionsData'
import { param } from '../../../utils'
export default {
  data() {
    return {
      licenseColorOption,
      cartype,
      vehicleCatgoryType,
      vehicleType,
      updateFlag: false,
      isNoCar: false,
      workList: [
        { label: '资料补传', value: '1', checked: false },
        { label: '信息变更', value: '2', checked: false }
      ],
      imgList: [
        { label: '身份证', value: '1', checked: false },
        // { label: '身份证国徽面', value: '11', checked: false },
        { label: '行驶证', value: '3', checked: false },
        // { label: '行驶证', value: '12', checked: false },
        { label: '行驶证车辆图片', value: '6', checked: false },
        { label: '车头照', value: '15', checked: false },
        { label: '营业执照', value: '2', checked: false },
        { label: '道路运输证', value: '20', checked: false }
      ],
      typeList: [],
      ruleForm: {
        vehicleCode: '',
        vehicleColor: ''
      },
      imaModel: {
        typeCode: []
      },
      statusList: {
        infoStatus: '待变更',
        imgStatus: '待补传'
      },
      infModel: {
        isTrunk: '', //客货
        totalWeight: '', //总质量
        userCharacter: '', //车辆使用性质
        vehicleAxles: '', //车轴数
        vehicleLength: '',
        vehicleWidth: '',
        vehicleHeight: '',
        vehicleNationalType: '', //车型
        vehicleSeat: '', //座位数
        vehicleType: '' //行驶证车辆类型
      },
      task: {
        taskFlag: 0,
        endTime: '',
        blackType: null,
        blackRemark: ''
      },
      btnTitle: '新增',
      detail: {},
      rules: {
        vehicleCode: [
          { required: true, message: '请输入车牌号', trigger: 'blur' }
        ],
        vehicleColor: [
          { required: true, message: '请选择车牌颜色', trigger: 'change' }
        ]
        // transactionDate: [
        //   {
        //     type: 'date',
        //     required: true,
        //     message: '请选择交易日期',
        //     trigger: 'change',
        //   },
        // ],
      }
    }
  },
  mounted() {
    _that = this
    console.log('this.$route.query', this.$route.query)
    if (
      this.$route.query.detail &&
      Object.keys(this.$route.query.detail).length > 0
    ) {
      this.detail = this.$route.query.detail
      this.updateFlag = true
      //回填数据
      this.btnTitle = '修改'
      this.setData(this.detail)
    }
    this.$nextTick(() => {
      let { type, carNo, carColor } = this.$route.query
      if (type == 'enterInfo') {
        this.ruleForm.vehicleCode = carNo
        setTimeout(() => {
          this.ruleForm.vehicleColor = carColor
        })
      }
    })
  },
  watch: {
    'ruleForm.vehicleCode': val => {
      if (!_that.updateFlag) {
        _that.infModel.isTrunk = ''
        if (val && _that.ruleForm.vehicleColor) {
          let vehicleCode = val.trim()
          if (vehicleCode.length == 7 || vehicleCode.length == 8) {
            setTimeout(() => {
              _that.getIsTrunk()
            }, 0)
          }
        }
      }
    },
    'ruleForm.vehicleColor': (val, old) => {
      if (!_that.ruleForm.vehicleCode) {
        if (val != old && old == '') {
          _that.$message({
            message: '请先填写车牌',
            type: 'warning'
          })
        }
        _that.ruleForm.vehicleColor = ''
        return
      } else {
        if (!_that.updateFlag) {
          setTimeout(() => {
            _that.getIsTrunk()
          }, 0)
        }
      }
    }
  },
  methods: {
    getIsTrunk(callback) {
      let params = {
        vehicleColor: this.ruleForm.vehicleColor,
        vehicleCode: this.ruleForm.vehicleCode
      }
      this.$request({
        url: this.$interfaces.getIsTrunk,
        data: params,
        method: 'post'
      })
        .then(res => {
          if (res) {
            this.infModel.isTrunk = res.data.vehicle_type
            this.isNoCar = false
            callback && callback(true)
          } else {
            this.isNoCar = true
            this.infModel.isTrunk = ''
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    setData(detail) {
      this.ruleForm.vehicleCode = detail.vehicleCode
      this.ruleForm.vehicleColor = detail.vehicleColor

      this.infModel.totalWeight = detail.totalWeight
      this.infModel.isTrunk = detail.isTrunk
      this.infModel.userCharacter = detail.userCharacter
      this.infModel.vehicleAxles = detail.vehicleAxles
      this.infModel.vehicleLength = detail.vehicleLength
      this.infModel.vehicleWidth = detail.vehicleWidth
      this.infModel.vehicleHeight = detail.vehicleHeight
      this.infModel.vehicleNationalType = detail.vehicleNationalType
      this.infModel.vehicleSeat = detail.vehicleSeat
      this.infModel.vehicleType = detail.vehicleType

      if (detail.imgStatus == 1) {
        // this.$set(this.workList[0], 'checked', true)
        this.workList[0].checked = true
      }

      if (detail.infoStatus == 1) {
        // this.$set(this.workList[1], 'checked', true)
        this.workList[1].checked = true
      }

      if (detail.remark) {
        let imgArr
        imgArr = JSON.parse(detail.remark)
        if (imgArr.length > 0) {
          imgArr.forEach(item => {
            this.imgList.forEach(item1 => {
              if (item == item1.value) {
                item1.checked = true
              }
            })
          })
        }
      }

      let { taskFlag, endTime, blackType, blackRemark } = detail
      this.task.taskFlag = taskFlag ? Number(taskFlag) : 0
      this.task.endTime = endTime
      this.task.blackType = blackType ? Number(blackType) : null
      this.task.blackRemark = blackRemark

      console.log(this.imgList)
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    handleWorkChange(e, item) {
      // if (!this.ruleForm.vehicleCode || !this.ruleForm.vehicleColor) {
      //   this.$message({
      //     message: '请先填写车辆信息!',
      //     type: 'warning',
      //   })
      //   item.checked = false
      //   console.log(this.workList)
      //   return
      // }
      item.checked = e

      if (
        this.ruleForm.vehicleCode &&
        this.ruleForm.vehicleColor &&
        e &&
        item.value == '2' &&
        !this.updateFlag
      ) {
        this.getIsTrunk()
      }

      console.log(e, item)
      //重置信息
      if (!e && item.value == '2') {
        for (const key in this.infModel) {
          this.infModel[key] = ''
        }
      }
      if (!e && item.value == '1') {
        this.imgList.forEach(item => {
          item.checked = false
        })
        this.imaModel.typeCode = []
      }
    },
    handleImgChange(e, item) {
      item.checked = e
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.getIsTrunk(nextFlag => {
            if (nextFlag) {
              this.submitData()
            }
          })
        } else {
          //   console.log('error submit!!')
          return false
        }
      })
    },
    async submitData() {
      let params = {
        bizType: '',
        imaModel: { typeCode: [] },
        infModel: {},
        ...this.ruleForm
      }
      params.vehicleCode = params.vehicleCode.toUpperCase()

      let workArr = []
      let typeCode = []

      //定时任务校验
      let { taskFlag, endTime, blackType, blackRemark } = this.task
      if (
        taskFlag == 1 &&
        (!endTime || (!blackType && blackType != 0) || !blackRemark)
      ) {
        let tipKey = !endTime
          ? 'endTime'
          : !blackType && blackType != 0
          ? 'blackType'
          : 'blackRemark'
        let tip = {
          blackType: '请设置限制对象！',
          endTime: '请选择结束日期！',
          blackRemark: '请填写限制原因！'
        }
        this.$message({
          type: 'error',
          message: tip[tipKey]
        })
        return
      }

      if (taskFlag == 1) {
        params.task = { ...this.task }
      } else {
        params.task = {
          taskFlag: 0,
          endTime: '',
          blackType: null,
          blackRemark: ''
        }
      }
      console.log('params', params)

      this.workList.forEach(item => {
        if (item.checked) {
          workArr.push(item.value)
        }
      })

      if (workArr.length == 0) {
        this.$message({
          message: '请先选择业务类型!',
          type: 'warning'
        })
        return
      }

      console.log(workArr)
      //判断业务类型
      if (workArr.length == 2) {
        if (workArr.includes('1') && workArr.includes('2')) {
          params.bizType = '3'
        }
      } else {
        if (workArr.includes('1')) {
          params.bizType = '1'
        } else if (workArr.includes('2')) {
          params.bizType = '2'
        }
      }
      //判断需要上传的资料code
      this.imgList.forEach(item => {
        if (item.checked) {
          typeCode.push(item.value)
          if (item.value == '1') {
            typeCode.push('11')
          }
          if (item.value == '3') {
            typeCode.push('12')
          }
        }
      })

      //判断是否对象属性空值
      let isBlankObj = await this._isBlankObject(this.infModel)

      //校验空数据
      if (params.bizType == '1') {
        if (typeCode.length == 0) {
          this.$message({
            message: '请先选择资料补传业务!',
            type: 'warning'
          })
          return
        }
      } else if (params.bizType == '2') {
        if (isBlankObj) {
          this.$message({
            message: '请先填写信息变更业务!',
            type: 'warning'
          })
          return
        }
      } else if (params.bizType == '3') {
        if (typeCode.length == 0) {
          this.$message({
            message: '请先选择资料补传业务!',
            type: 'warning'
          })
          return
        }
        if (isBlankObj) {
          this.$message({
            message: '请填写你要修改的信息!',
            type: 'warning'
          })
          return
        }
      }

      params.imaModel.typeCode = typeCode
      params.infModel = this.infModel

      this.submit(params)
    },
    //判断对象属性值是否都为空
    _isBlankObject(obj) {
      let newObj = JSON.parse(JSON.stringify(obj))
      delete newObj.isTrunk
      return new Promise(resolve => {
        for (const key in newObj) {
          if (newObj.hasOwnProperty(key)) {
            if (newObj[key] != null && newObj[key] != '') {
              resolve(false)
            }
          }
        }
        resolve(true)
      })
    },
    _trimObj(params) {
      return new Promise(resolve => {
        for (const key in params) {
          if (params[key] && typeof params[key] == 'object') {
            this._trimObj(params[key])
          } else if (params[key] && typeof params[key] == 'string') {
            console.log('修改前', params[key])
            params[key] = params[key].trim()
            console.log('修改后', params[key])
          }
          //最后一个跳出循环
          if (key == 'vehicleColor') {
            resolve(true)
          }
        }
      })
    },
    async submit(params) {
      let trimResult = await this._trimObj(params)
      let url = this.$interfaces.addUPdateDeviceOrder
      //切换成修改的url
      if (this.detail && Object.keys(this.detail).length > 0) {
        url = this.$interfaces.modifyDevice
        params.id = this.detail.id
      }
      if (trimResult) {
        this.$request({
          url: url,
          data: params,
          method: 'post'
        })
          .then(res => {
            console.log('新增新增', res)
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: this.btnTitle + '成功'
              })
              this.back()
            }
          })
          .catch(error => {
            console.log(error)
          })
      }
    },
    accMul(arg1, arg2) {
      var m = 0,
        s1 = arg1.toString(),
        s2 = arg2.toString()
      try {
        m += s1.split('.')[1].length
      } catch (e) {}
      try {
        m += s2.split('.')[1].length
      } catch (e) {}
      return (
        (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
        Math.pow(10, m)
      )
    },
    back() {
      setTimeout(() => {
        //关闭当前页面包屑
        this.$store.state.tagsView.visitedViews.splice(
          this.$store.state.tagsView.visitedViews.findIndex(
            item => item.path === this.$route.path
          ),
          1
        )
      console.log('当前路由', this.$route.path)
      })

      if (this.$route.query.type == 'enterInfo') {
        this.$router.back()
        return
      }
      this.$router.push({
        path: './update',
        replace: true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hand-input {
  height: 100%;
  position: relative;
  flex-flow: column;
  display: flex;
  .input-box {
    padding: 20px;
    margin: 20px;
    flex: 1;
    // height: 0;
    background-color: #fff;
  }
  .title-warpper {
    .title {
      font-size: 16px;
      border-left: 4px solid #09aff7;
      padding-left: 5px;
    }
  }
  .form-wrapper ::v-deep {
    margin-top: 20px;
    // padding-left: 20px;
    .el-form-item {
      display: flex;
      align-items: center;
    }
    .el-form-item__label {
      font-size: 12px;
      width: 120px;
    }
    .el-form-item__content {
      display: flex;
      flex: 1;
    }
    .el-select {
      flex: 1;
    }
    .el-input__inner {
      flex: 1;
    }
    .el-date-editor.el-input,
    .el-date-editor.el-input__inner,
    .el-input__inner {
      // width: 200px;
      height: 40px;
    }
    // .el-textarea__inner {
    //   width: 700px;
    // }
    .title {
      margin-bottom: 20px;
      font-size: 14px;
      border-left: 4px solid #ff6e00;
      padding-left: 5px;
    }
  }
  .bottom-btn {
    display: flex;
    justify-content: center;
  }
}
</style>
