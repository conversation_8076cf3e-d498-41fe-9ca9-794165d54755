<template>
  <div class="refund">
    <!-- <div class="search-list" v-if="!isShowHandle"> -->
    <div class="search-list">
      <dart-search
        :formSpan="24"
        :gutter="20"
        ref="searchForm1"
        label-position="right"
        :model="search"
        :fontWidth="2"
      >
        <template slot="search-form" style="padding-left: 10px">
          <dart-search-item label="车牌：" prop="carNo">
            <el-input
              v-model="search.carNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色：" prop="carColor">
            <el-select v-model="search.carColor" placeholder="请选择" clearable>
              <el-option
                v-for="item in licenseColorOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </dart-search-item>
          <dart-search-item label="卡号：" prop="cardNo">
            <el-input
              v-model="search.cardNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item label="OBU号：" prop="contractNo">
            <el-input
              v-model="search.contractNo"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <!-- <dart-search-item label="注销时间从：" prop="createTimeFrom">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              v-model="search.createTimeFrom"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="注销时间到：" prop="createTimeTo">
            <el-date-picker
              type="datetime"
              placeholder="选择日期时间"
              default-time="23:59:59"
              v-model="search.createTimeTo"
            >
            </el-date-picker>
          </dart-search-item> -->
          <dart-search-item label="注销时间：" prop="stopDate">
            <el-date-picker
              type="date"
              placeholder="选择日期时间"
              value-format="yyyy-MM-dd"
              v-model="search.stopDate"
            >
            </el-date-picker>
          </dart-search-item>
          <dart-search-item label="客户代码：" prop="custCode">
            <el-input
              v-model="search.custCode"
              placeholder=""
              clearable
            ></el-input>
          </dart-search-item>
          <dart-search-item
            :is-button="true"
            style="margin-top: 10px; margin-left: 35px"
            :span="24"
          >
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              ><i class="el-icon-search"></i> 搜索</el-button
            >
            <el-button size="mini" @click="onResultHandle">重置</el-button>
            <!-- <span class="collapse" v-if="!isCollapse" @click="isCollapse = true"
              >展开</span
            >
            <span class="collapse" v-else @click="isCollapse = false"
              >收起</span
            > -->
          </dart-search-item>
        </template>
      </dart-search>
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        :header-align="center"
        border
        height="100%"
        style="width: 100%"
        :row-style="{ height: '40px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '40px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column
          prop="cardNo"
          align="center"
          min-width="190"
          label="卡号"
        />
        <el-table-column
          prop="cardStatus"
          align="center"
          min-width="120"
          label="卡片状态"
        >
          <template slot-scope="scope">
            {{ getType(cardStatus, scope.row.cardStatus) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="custName"
          align="center"
          min-width="120"
          label="客户名称"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.custName }}
              </div>
              <span> {{ scope.row.custName }}</span>
            </el-tooltip>
          </template></el-table-column
        >
        <el-table-column
          prop="custMobile"
          align="center"
          min-width="120"
          label="手机"
        />
        <el-table-column
          prop="custAddress"
          align="center"
          min-width="160"
          label="联系地址"
        >
          <template slot-scope="scope">
            <el-tooltip class="tooltip-item" effect="dark" placement="top">
              <div slot="content">
                {{ scope.row.custAddress }}
              </div>
              <span> {{ scope.row.custAddress }}</span>
            </el-tooltip>
          </template></el-table-column
        >
        <el-table-column
          prop="releaseDate2"
          align="center"
          min-width="160"
          label="发行时间"
        />
        <el-table-column
          prop="stopDate"
          align="center"
          min-width="160"
          label="注销时间"
        />
        <el-table-column
          fixed="right"
          label="操作"
          header-align="center"
          min-width="280"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="toDetail(scope.row.cardMastId, scope.row)"
              >详情</el-button
            >
            <el-button
              type="primary"
              size="mini"
              :disabled="scope.row.cardStatus == 1"
              v-permisaction="['cancelVehicle:resume']"
              @click="cancelBind(scope.row)"
              >注销车辆重传</el-button
            >
            <el-button
              v-if="scope.row.cardStatus == '16'"
              v-permisaction="['bindingCardInterface:cardStop']"
              type="primary"
              size="mini"
              @click="cardHandle(scope.row.cardNo, 2)"
              >启用</el-button
            >
            <el-button
              v-if="scope.row.cardStatus != '16'"
              v-permisaction="['bindingCardInterface:cardStop']"
              type="danger"
              size="mini"
              @click="cardHandle(scope.row.cardNo, 1)"
              >停用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="changePage"
        :current-page="search.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="search.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
    <detailDialog
      ref="detail"
      :dialogFormVisible.sync="dialogDetail"
      :obuMastList="obuMastList"
      :cardDTO="cardDTO"
      :carListDTO="carListDTO"
      :cancelCustDTO="cancelCustDTO"
      :signInfo="signInfo"
    ></detailDialog>
  </div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
import detailDialog from './detailDialog'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { currentSign } from '@/api/issue'
import { cancelVehiclePost } from '@/api/bindManagement'

var moment = require('moment')
// import { mapGetters } from 'vuex'
export default {
  components: {
    dartSearch,
    dartSearchItem,
    detailDialog
  },
  data() {
    return {
      licenseColorOption,
      loading: false,
      dialogDetail: false,
      isCollapse: false,
      center: 'center',
      search: {
        carColor: '',
        carNo: '',
        cardNo: '',
        contractNo: '', //OBU号
        stopDate: '',
        custCode:'',
        pageNum: 1,
        pageSize: 20
      },
      signInfo: {},
      cardStatus: [
        {
          value: '0',
          label: '一发初始化'
        },
        {
          value: '1',
          label: '正常'
        },
        {
          value: '2',
          label: '挂失'
        },
        {
          value: '3',
          label: '已更换'
        },
        {
          value: '4',
          label: '有卡注销'
        },
        {
          value: '5',
          label: '过户'
        },
        {
          value: '7',
          label: '挂失已补领'
        },
        {
          value: '8',
          label: '坏卡'
        },
        {
          value: '9',
          label: '异常停用'
        },
        {
          value: '10',
          label: '已退货'
        },
        {
          value: '11',
          label: '已免费更换'
        },
        {
          value: '12',
          label: '无卡注销'
        },
        {
          value: '14',
          label: '合作机构黑名单'
        },
        {
          value: '15',
          label: '车型不符'
        },
        {
          value: '16',
          label: '卡片停用'
        }
      ],
      total: 0,
      tableData: [],
      carListDTO: [],
      cardDTO: {},
      obuMastList: [],
      cancelCustDTO: {}
    }
  },
  // computed: {
  //   ...mapGetters(['refundSearch']),
  // },
  created() {
    // console.log('refundRearch', this.refundSearch)
    // if (Object.keys(this.refundSearch).length > 0) {
    //   this.search = this.refundSearch
    // }
    // this.getCarInfoList()
  },
  methods: {
    // 查询版本信息
    getCarInfoList() {
      console.log(this.search)
      this.loading = true
      this.$request({
        url: this.$interfaces.getCarInfoList,
        method: 'post',
        data: this.search,
        timeout: 1 * 60 * 1000 //设置超时时间1分钟
      })
        .then(res => {
          this.loading = false
          // console.log(res, 'res')
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
        .catch(error => {})
    },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    getAuditBlackSearch() {
      this.loading = true
      let params = { ...this.search }
      params.createTimeFrom = params.createTimeFrom
        ? moment(params.createTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.createTimeTo = params.createTimeTo
        ? moment(params.createTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeFrom = params.payTimeFrom
        ? moment(params.payTimeFrom).format('YYYY-MM-DD HH:mm:ss')
        : ''
      params.payTimeTo = params.payTimeTo
        ? moment(params.payTimeTo).format('YYYY-MM-DD HH:mm:ss')
        : ''
      console.log('prams入参', params)
      this.$store
        .dispatch('bindManagement/auditBlackSearch', params)
        .then(res => {
          this.loading = false
          console.log('返回的申请列表', res)
          this.tableData = res.iPage.records
          this.total = res.iPage.total
          this.totalPrice = res.oweFee
          this.radio = []
        })
        .catch(err => {
          this.loading = false
          console.log('err', err)
        })
    },
    toDetail(id, row) {
      let params = { cardMastId: id }
      this.$request({
        url: this.$interfaces.getCarInfoDetail,
        method: 'post',
        data: params
      })
        .then(async res => {
          console.log(res, 'res')
          if (res.code == 200) {
            let signInfo = await this.getSign(row)
            this.signInfo = signInfo
            this.cardDTO = res.data.cardDTO
            this.carListDTO = res.data.carListDTO
            this.obuMastList = res.data.obuMastList
            this.cancelCustDTO = res.data.cancelCustDTO
            this.dialogDetail = true
          }
        })
        .catch(err => {
          console.log(err)
        })
    },
    cardHandle(cardNo, optCode) {
      this.$prompt('请输入备注', '备注', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(({ value }) => {
          let params = {
            cardNo: cardNo,
            optCode: optCode,
            stopReason: value
          }
          this.doCardHandle(params, res => {
            if (res.code == 200) {
              this.$message({
                type: 'success',
                message: '操作成功,请手动刷新页面更新状态'
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })
    },
    doCardHandle(params, callback) {
      this.startLoading()
      this.$request({
        url: this.$interfaces.cardStop,
        method: 'post',
        data: params
      })
        .then(res => {
          this.endLoading()
          console.log(res, 'res')
          callback(res)
        })
        .catch(error => {
          this.endLoading()
        })
    },
    changePage(page) {
      this.search.pageNum = page
      this.getCarInfoList()
    },
    handleSizeChange(pageSize) {
      this.search.pageSize = pageSize
      this.getCarInfoList()
    },
    onSearchHandle() {
      if (
        this.search.carNo == '' &&
        this.search.contractNo == '' &&
        this.search.cardNo == '' &&
        this.search.custCode == '' &&
        !this.search.stopDate
      ) {
        this.$message.warning('由于数据量庞大，请先输入搜索条件再搜索！')
        return
      }
      this.search.pageNum = 1
      // //缓存搜索参数
      // this.$store
      //   .dispatch('containerRefund/setRefundSearch', this.search)
      //   .then((res) => {
      //     console.log('缓存过后的search', res)
      //   })
      this.getCarInfoList()
    },
    //重置
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 20
      // //清除缓存
      // this.$store
      //   .dispatch('containerRefund/removeRefundSearch')
      //   .then((res) => {})
    },
    updateList() {
      this.search.pageNum = 1
      this.search.pageSize = 20
      this.dialogDetail = false
      this.getCarInfoList()
    },
    // 获取签约信息
    async getSign(row) {
      let { carNo, carColor } = row
      let params = {
        vehicle_code: carNo,
        vehicle_color: carColor
      }
      let res = await currentSign(params)
      console.log(res, 'currentSign')
      return new Promise((resolve, reject) => {
        resolve(res.data)
      })
    },
    // 重传
    async cancelBind(row) {
      let parmas = {
        carNo: row.carNo,
        carColor: row.carColor
      }
      let res = await cancelVehiclePost(parmas)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.getCarInfoList()
      }
    }
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep.dart-search-wrapper .dart-search-container .el-form-item {
  display: flex;
}
::v-deep.dart-search-wrapper
  .dart-search-container
  .collapse-wrapper
  .el-col-24:nth-child(5)
  .el-form-item__label {
  width: 200px !important;
}
::v-deep.dart-search-wrapper .dart-search-container .el-form-item__content {
  flex: 1;
}
.refund {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;

  .table {
    padding: 20px 20px 40px 20px;
    flex: 1;
    height: 0;
    background-color: #fff;
  }
  .pagination {
    margin: 10px 0;
  }
  .total-price {
    display: flex;
    align-items: center;
    margin-top: 10px;
    color: red;
    font-size: 14px;
  }
  .nowrap {
    white-space: nowrap;
  }
  .text {
    text-decoration: underline;
    &:hover {
      cursor: pointer;
    }
  }
  .tooltip-item {
    display: block;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
}
</style>
