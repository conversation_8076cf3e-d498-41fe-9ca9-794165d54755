<template>
  <div class="payment-detail" v-loading.fullscreen.lock="showLoading">
    <el-dialog
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :center="true"
      class="form_dialog"
      :fullscreen="isFullscreen"
      :show-close="false"
      width="80%"
      :close-on-press-escape="false"
    >
      <template slot="title">
        <div class="btn-wrapper">
          <i
            @click="isFullscreen = true"
            v-if="!isFullscreen"
            class="el-icon-full-screen"
          ></i>
          <i
            @click="isFullscreen = false"
            v-else
            class="el-icon-copy-document"
          ></i>
          <i @click="close()" class="el-icon-close"></i>
        </div>
        <div class="title-wrapper">
          <span class="title"> 车辆信息详情 </span>
        </div>
      </template>

      <div class="table" v-if="carListDTO.length > 0">
        <span class="title">车辆信息</span>
        <el-table
          :data="carListDTO"
          :align="center"
          :header-align="center"
          border
          height="100%"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '35px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '35px' }"
          :header-cell-style="{ padding: '0px' }"
        >
          <el-table-column prop="carNo" align="center" label="车牌号" />

          <el-table-column prop="carColor" align="center" label="车牌颜色">
            <template slot-scope="scope">
              {{ getType(licenseColorOption, scope.row.carColor) }}</template
            ></el-table-column
          >
          <el-table-column prop="isTrunk" align="center" label="客货标识">
            <template slot-scope="scope">
              {{ getType(vehicleType, scope.row.isTrunk) }}</template
            ></el-table-column
          >
          <el-table-column
            prop="carStyle"
            min-width="100"
            align="center"
            label="车辆使用性质"
          >
            <template slot-scope="scope">
              {{ getType(cartype, scope.row.carStyle) }}
            </template></el-table-column
          >
          <el-table-column prop="carType" align="center" label="车型">
            <template slot-scope="scope">
              {{ getType(vehicleCatgoryType, scope.row.carType) }}</template
            ></el-table-column
          >
          <el-table-column
            prop="drivingLicensepPath"
            align="center"
            label="行驶证正页"
          >
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.drivingLicensepPath"
                :preview-src-list="[scope.row.drivingLicensepPath]"
              >
                <div slot="placeholder"></div>
                <div slot="error"></div
              ></el-image> </template
          ></el-table-column>
          <el-table-column
            prop="drivingLicensebPath"
            align="center"
            label="行驶证副页"
          >
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.drivingLicensebPath"
                :preview-src-list="[scope.row.drivingLicensebPath]"
              >
                <div slot="placeholder"></div>
                <div slot="error"></div>
              </el-image> </template
          ></el-table-column>
          <el-table-column
            prop="drivingCarPath"
            align="center"
            label="车辆图片"
          >
            <template slot-scope="scope">
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.drivingCarPath"
                :preview-src-list="[scope.row.drivingCarPath]"
              >
                <div slot="placeholder"></div>
                <div slot="error"></div
              ></el-image> </template
          ></el-table-column>
        </el-table>
      </div>

      <!-- 签约信息 -->
      <div class="detail-wrapper" v-if="signInfo">
        <div class="section-1 margin-top">
          <el-descriptions
            title="签约信息"
            :column="3"
            border
            class="desc-wrapper"
          >
            <el-descriptions-item label="银行卡号">{{
              signInfo.bankAccount
            }}</el-descriptions-item>
            <el-descriptions-item label="绑定状态">{{
              signInfo.status_str
            }}</el-descriptions-item>
            <el-descriptions-item label="发行状态">{{
              signInfo.isIssue
            }}</el-descriptions-item>
            <el-descriptions-item label="绑定渠道">{{
              signInfo.bankCodeName
            }}</el-descriptions-item>

            <el-descriptions-item label="绑定银行">
                {{signInfo.bankPayName}}
            </el-descriptions-item>
            <el-descriptions-item label="绑定时间">{{
              signInfo.bindDate
            }}</el-descriptions-item>
            <el-descriptions-item label="签约时间">{{
              signInfo.signDate
            }}</el-descriptions-item>
            <el-descriptions-item label="发行时间">{{
              signInfo.issueDate
            }}</el-descriptions-item>
            <el-descriptions-item label="预绑定时间">{{
              signInfo.preBindDate
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="detail-wrapper" v-if="cardDTO">
        <div class="section-1 margin-top">
          <el-descriptions
            title="卡片信息"
            :column="4"
            border
            class="desc-wrapper"
          >
            <el-descriptions-item label="卡号">{{
              cardDTO.cardNo
            }}</el-descriptions-item>
            <el-descriptions-item label="卡片类型">{{
              cardDTO.cardType == '23' ? '记账卡' : '储值卡'
            }}</el-descriptions-item>
            <el-descriptions-item label="广西卡类型">{{
              getType(gxCardTypeAllOptions, cardDTO.gxCarType)
            }}</el-descriptions-item>
            <el-descriptions-item label="卡片状态">{{
              getType(cardStatus, cardDTO.cardStatus)
            }}</el-descriptions-item>
            <el-descriptions-item label="开卡网点">{{
              cardDTO.branchCodeStr
            }}</el-descriptions-item>

            <el-descriptions-item label="卡片发行时间">{{
              cardDTO.releaseDate2
            }}</el-descriptions-item>
            <el-descriptions-item label="卡片注销时间">{{
              cardDTO.stopDate
            }}</el-descriptions-item>
            <el-descriptions-item label="产品类型">{{
              getType(gxCardTypeAllOptions, cardDTO.productType)
            }}</el-descriptions-item>
            <el-descriptions-item label="绑定渠道">{{
              cardDTO.bindChannel
            }}</el-descriptions-item>
            <el-descriptions-item label="代扣渠道">{{
              cardDTO.payChannel
            }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="1" border class="desc-wrapper">
            <el-descriptions-item label="卡片黑名单记录">
              <div
                class="table"
                v-if="cardDTO.statusList && cardDTO.statusList.length > 0"
              >
                <!-- <span class="title">卡片黑名单记录</span> -->
                <el-table
                  :data="cardDTO.statusList"
                  :align="center"
                  :header-align="center"
                  border
                  height="100%"
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '35px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '35px' }"
                  :header-cell-style="{ padding: '0px' }"
                >
                  <el-table-column
                    prop="status"
                    min-width="100"
                    align="center"
                    label="状态"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.status == '1'">进入黑名单</span>
                      <span v-if="scope.row.status == '2'">解除黑名单</span>
                    </template></el-table-column
                  >
                  <el-table-column
                    prop="type"
                    min-width="100"
                    align="center"
                    label="类型"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.type == '1'">卡挂失</span>
                      <span v-if="scope.row.type == '2'">无卡挂起</span>
                      <span v-if="scope.row.type == '3'">无卡注销</span>
                      <span v-if="scope.row.type == '4'">欠费</span>
                      <span v-if="scope.row.type == '5'">合作机构黑名单 </span>
                      <span v-if="scope.row.type == '6'">车型不符</span>
                    </template></el-table-column
                  >
                  <el-table-column
                    prop="createByStr"
                    align="center"
                    label="创建人"
                    min-width="100"
                  />
                  <el-table-column
                    prop="createDeptStr"
                    align="center"
                    label="创建人部门"
                    min-width="160"
                  />
                  <el-table-column
                    prop="createTime"
                    align="center"
                    label="创建时间"
                    min-width="160"
                  />
                  <el-table-column
                    prop="updateByStr"
                    align="center"
                    label="更新人"
                    min-width="100"
                  />
                  <el-table-column
                    prop="updateDeptStr"
                    align="center"
                    label="更新人部门"
                    min-width="160"
                  />
                  <el-table-column
                    prop="updateTime"
                    align="center"
                    label="更新时间"
                    min-width="160"
                  />
                </el-table>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <div class="table" v-if="obuMastList.length > 0">
        <span class="title">OBU信息</span>
        <el-table
          :data="obuMastList"
          :align="center"
          :header-align="center"
          border
          height="100%"
          style="width: 100%; margin-bottom: 20px"
          :row-style="{ height: '35px' }"
          :cell-style="{ padding: '0px' }"
          :header-row-style="{ height: '35px' }"
          :header-cell-style="{ padding: '0px' }"
        >
          <el-table-column prop="contractNo" align="center" label="OBU号" />
          <el-table-column prop="obuStatus" align="center" label="OBU状态" />
          <el-table-column
            prop="releaseDate2"
            align="center"
            label="OBU发行时间"
            min-width="160"
          />
          <el-table-column
            prop="contractEndDate"
            align="center"
            label="OBU注销时间"
            min-width="100"
          />
        </el-table>
      </div>

      <div class="detail-wrapper" v-if="cardDTO">
        <div class="section-1 margin-top">
          <el-descriptions
            title="客户信息"
            :column="3"
            border
            class="desc-wrapper"
          >
            <el-descriptions-item label="所属客户">{{
              cancelCustDTO.custName
            }}</el-descriptions-item>
            <el-descriptions-item label="客户类型">{{
              cancelCustDTO.custType == '0' ? '个人' : '企业'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{
              cancelCustDTO.custContact
            }}</el-descriptions-item>
            <el-descriptions-item label="联系地址">{{
              cancelCustDTO.custAddress
            }}</el-descriptions-item>

            <el-descriptions-item label="证件类型">
              <span v-if="cancelCustDTO.custType == '0'">{{
                getType(personalOCRType, cancelCustDTO.custIdTypeCode)
              }}</span>
              <span v-if="cancelCustDTO.custType == '1'">{{
                getType(enterpriseOCRType, cancelCustDTO.custIdTypeCode)
              }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="证件号码">{{
              cancelCustDTO.custIdNo
            }}</el-descriptions-item>
            <el-descriptions-item label="固定电话">{{
              cancelCustDTO.custTel
            }}</el-descriptions-item>
            <el-descriptions-item label="手机">{{
              cancelCustDTO.custMobile
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              cancelCustDTO.custEmail
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  licenseColorOption,
  vehicleType,
  vehicleCatgoryType,
  cartype,
  personalOCRType,
  enterpriseOCRType,
  gxCardTypeAllOptions,
  gxCardTypeOptions
} from '@/common/const/optionsData'
export default {
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false
    },
    cardDTO: {
      type: Object
    },
    carListDTO: {
      type: Array
    },
    obuMastList: {
      type: Array
    },
    cancelCustDTO: {
      type: Object
    },
    signInfo: {
      type: Object
    }
  },
  data() {
    return {
      gxCardTypeAllOptions,
      personalOCRType,
      enterpriseOCRType,
      licenseColorOption,
      vehicleType,
      vehicleCatgoryType,
      cartype,
      gxCardTypeOptions,
      showLoading: false,
      center: 'center',
      isFullscreen: false,
      fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
      cardStatus: [
        {
          value: '0',
          label: '一发初始化'
        },
        {
          value: '1',
          label: '正常'
        },
        {
          value: '2',
          label: '挂失'
        },
        {
          value: '3',
          label: '已更换'
        },
        {
          value: '4',
          label: '有卡注销'
        },
        {
          value: '5',
          label: '过户'
        },
        {
          value: '7',
          label: '挂失已补领'
        },
        {
          value: '8',
          label: '坏卡'
        },
        {
          value: '9',
          label: '异常停用'
        },
        {
          value: '10',
          label: '已退货'
        },
        {
          value: '11',
          label: '已免费更换'
        },
        {
          value: '12',
          label: '无卡注销'
        },
        {
          value: '14',
          label: '合作机构黑名单'
        },
        {
          value: '15',
          label: '车型不符'
        },
        {
          value: '16',
          label: '卡片停用'
        }
      ]
    }
  },
  // mounted() {
  //   console.log(process.env.NODE_ENV)
  //   if (process.env.NODE_ENV == 'development') {
  //     this.url = 'https://etc-micro-portal.gxjettoll.cn:8443/issue-web'
  //   } else {
  //     this.url = 'https://portal.gxetc.com.cn/issue-web'
  //   }
  // },
  methods: {
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
    // accMul(arg1, arg2) {
    //   var m = 0,
    //     s1 = arg1.toString(),
    //     s2 = arg2.toString()
    //   try {
    //     m += s1.split('.')[1].length
    //   } catch (e) {}
    //   try {
    //     m += s2.split('.')[1].length
    //   } catch (e) {}
    //   return (
    //     (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) /
    //     Math.pow(10, m)
    //   )
    // },
    close() {
      this.$emit('update:dialogFormVisible', false)
    },

  },

  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    }
  },
}
</script>

<style lang="scss" scoped>
::v-deep.form_dialog .el-dialog--center {
  margin-top: 5vh !important;
  // height: 90%;
  // overflow-y: scroll;
}
::v-deep.form_dialog .el-dialog.is-fullscreen {
  margin-top: 0 !important;
}
.table {
  padding: 0;
  .title {
    display: block;
    // margin-bottom: 20px;
    font-size: 14px;
    font-weight: 700;
    color: #303133;
  }
}
.textarea {
  // overflow: hidden;
  border-color: #e8e8e8;
  padding: 10px;
  width: 100%;
}
.btn-wrapper {
  text-align: right;
  & > i {
    margin-right: 10px;
    font-size: 20px;
    color: #000000;
    &:last-child {
      margin-right: 0;
    }
    &:hover {
      cursor: pointer;
      color: #c6c6c6;
    }
  }
}

.margin-top {
  margin-top: 20px;
}
.desc-wrapper ::v-deep {
  .el-descriptions__header {
    margin-bottom: 0;
    .el-descriptions__title {
      font-size: 14px;
    }
  }
  .el-descriptions__body {
    .el-descriptions__table {
      border-collapse: inherit;
      .el-descriptions-row {
        .el-descriptions-item__content {
          white-space: nowrap;
          height: 30px;
          padding: 0 10px;
          .el-input__inner {
            width: 100px;
          }
        }
        .el-descriptions-item__label {
          width: 200px;
          height: 30px;
          padding: 0 10px;
          white-space: nowrap;
          background-color: #f5f7fa;
        }
      }
    }
  }
  .no-whitespace {
    white-space: normal;
  }
}

.form_dialog ::v-deep .el-dialog__body {
  padding-top: 0;
  overflow-x: auto;
}
.imgbox {
  display: inline-block;

  width: 200px;
  img {
    margin: 0 10px;
    height: 200px;
  }
}
.viewer-canvas {
  z-index: 9999 !important;
}
</style>
