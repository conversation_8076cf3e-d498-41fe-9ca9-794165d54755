<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查报表
  * @author: duan<PERSON><PERSON><PERSON>
  * @date: 2024/12/18 09:20:00
-->
<template>
	<div class="false-certificate-report">
		<dart-search
			:formSpan="24"
			:gutter="20"
			ref="falseCertificateReportRef"
			label-position="right"
			:model="formData"
			:fontWidth="2"
			:rules="rules"
		>
			<template slot="search-form">
				<div class="title">假证筛查导入查询记录表</div>
				<dart-search-item
					label="选择日期"
					prop="activateDate"
					:span="6"
				>
					<el-date-picker
						v-model="formData.activateDate"
						type="daterange"
						value-format="yyyy-MM-dd"
						clearable
						range-separator="至"
						start-placeholder="开始日期"
						end-placeholder="结束日期"
						:default-time="['00:00:00', '23:59:59']"
					>
					</el-date-picker>
				</dart-search-item>
				<dart-search-item :is-button="true">
					<el-button type="primary" @click="queryF()">查询</el-button>
					<el-button @click="onResultHandle">重置</el-button>
				</dart-search-item>
			</template>
		</dart-search>
	</div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { decode } from 'js-base64'
var moment = require('moment')

export default {
	components: {
		dartSearch,
		dartSearchItem,
	},
	data() {
		const dataValidator = (rule, value, callback) => {
			if (value && value.length) {
				const startDate = moment(value[0])
				const endDate = moment(value[1])
				const monthsDifference = endDate.diff(startDate, 'months')
				if (monthsDifference > 6) {
					callback(new Error('日期范围不得超过6个月'))
				} else {
					callback()
				}
			} else {
				callback(new Error('请选择日期'))
			}
		}
		return {
			formData: {
				activateDate: [new Date(), new Date()],
			},
			rules: {
				activateDate: [
					{
						required: true,
						trigger: 'change',
						validator: dataValidator,
					},
				],
			},
		}
	},
	computed: {},
	methods: {
		queryF() {
			this.$refs.falseCertificateReportRef.$children[0].validate(
				(valid) => {
					if (valid) {
						let params = {
							name: 'forgedLogReport',
							audit_start_date: moment(this.formData.activateDate[0]).format('YYYY-MM-DD'),
							audit_end_date: moment(this.formData.activateDate[1]).format('YYYY-MM-DD'),
						}

						this.$store
							.dispatch('report/report', params)
							.then((res) => {
								let url = res
								let decodeUrl = decode(url)
								let clientWidth =
									document.documentElement.clientWidth
								let clientHeight =
									document.documentElement.clientHeight
								window.open(
									decodeUrl,
									'_blank',
									'width=' +
										clientWidth +
										',height=' +
										clientHeight +
										',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
								)
							})
					}
				}
			)
		},
		onResultHandle() {
			this.formData.activateDate = [new Date(), new Date()]
		},
	},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.false-certificate-report {
	height: 100%;
	width: 100%;
	padding: 20px;
	flex-flow: column;
	display: flex;
	::v-deep .dart-search-wrapper {
		margin-bottom: 0;
	}
	.title {
		height: 60px;
		font-size: 20px;
		line-height: 40px;
		padding-left: 100px;
	}
}
</style>