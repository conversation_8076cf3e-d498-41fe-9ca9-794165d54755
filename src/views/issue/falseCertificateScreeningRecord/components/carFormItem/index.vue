<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表-车辆详情-表单展示详情
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/12/26 16:30:00
-->
<template>
	<div class="car-form-item">
		<div class="title">{{ itemTitle }}</div>
		<div class="detail">
			<el-row>
				<el-col :span="12">
					<div class="row">
						<span class="label">车牌号码：</span>
						<span class="value" :class="{ 'highlight': formInfo.cphmStatus }" :title="formInfo.cphm">{{ formInfo.cphm }}</span>
					</div>
				</el-col>
				<el-col :span="12">
					<div class="row">
						<span class="label">车牌颜色：</span>
						<span class="value" :class="{ 'highlight': formInfo.cpysStatus }" :title="formInfo.cpys">{{ formInfo.cpys }}</span>
					</div>
				</el-col>
			</el-row>
			<el-row>
				<el-col :span="12"
					><div class="row">
						<span class="label">车辆类型：</span>
						<span class="value" :class="{ 'highlight': formInfo.cllxStatus }" :title="formInfo.cllx">{{ formInfo.cllx }}</span>
					</div></el-col
				><el-col :span="12"
					><div class="row">
						<span class="label">核定载人数：</span>
						<span class="value" :class="{ 'highlight': formInfo.hdzrsStatus }" :title="formInfo.hdzrs">{{ formInfo.hdzrs }}</span>
					</div></el-col
				></el-row
			>
			<el-row>
				<el-col :span="12">
					<div class="row">
						<span class="label">车辆尺寸：</span>
						<span class="value" :class="{ 'highlight': formInfo.clccStatus }" :title="formInfo.clcc">{{ formInfo.clcc }}</span>
					</div></el-col
				><el-col :span="12">
					<div class="row">
						<span class="label">总质量：</span>
						<span class="value" :class="{ 'highlight': formInfo.zzlStatus }" :title="formInfo.zzl">{{ formInfo.zzl }}</span>
					</div></el-col
				></el-row
			>
            <el-row v-if="formInfo.type === 'etc'">
				<el-col :span="12">
					<div class="row">
						<span class="label">车辆使用性质：</span>
						<span class="value" :title="formInfo.clsyxz">{{ formInfo.clsyxz }}</span>
					</div></el-col
				><el-col :span="12">
					<div class="row">
						<span class="label">车型：</span>
						<span class="value" :title="formInfo.cx">{{ formInfo.cx }}</span>
					</div></el-col
				></el-row
			>
		</div>
	</div>
</template>

<script>
export default {
	name: 'carFormItem',
	props: {
		itemTitle: {
			type: String,
			default: '',
		},
        formInfo: {
			type: Object,
			default: () => {},
		},
	},
	components: {},
	data() {
		return {}
	},
	computed: {},
	methods: {},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.car-form-item {
	width: 100%;
	.title {
		height: 40px;
		line-height: 40px;
		text-align: center;
		font-weight: 700;
	}
	.detail {
		width: 100%;
		display: flex;
		flex-direction: column;
		.row {
			height: 30px;
			display: flex;
			align-items: center;
			width: 100%;
			.label {
				width: 110px;
				text-align: right;
				font-weight: bold;
			}
			.value {
				flex: 1;
				min-width: 0;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.highlight {
				color: #409eff;
			}
		}
	}
}
</style>