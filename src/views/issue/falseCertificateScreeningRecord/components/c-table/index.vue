<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表-车辆详情-表格
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/12/22 10:00:00
-->
<template>
	<div class="c-table">
		<div class="title">{{ title }}</div>
		<el-table
			:data="tableData"
			align="center"
			header-align="center"
			style="width: 100%"
			:row-style="{ height: '40px' }"
			:cell-style="{ padding: '0px' }"
			:header-row-style="{ height: '40px' }"
			:header-cell-style="{ padding: '0px' }"
			border
		>
			<el-table-column
				prop="cphm"
				align="center"
				label="车牌号码"
				min-width="90"
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.cphmStatus }">{{ scope.row.cphm }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="cpys"
				align="center"
				label="车牌颜色"
				min-width="90"
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.cpysStatus }">{{ scope.row.cpys }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="cllx"
				align="center"
				label="车辆类型"
				min-width="120"
                show-overflow-tooltip
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.cllxStatus }">{{ scope.row.cllx }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="hdzrs"
				align="center"
				label="核定载人数"
				min-width="100"
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.hdzrsStatus }">{{ scope.row.hdzrs }}</span>
				</template>
			</el-table-column>

			<el-table-column
				prop="clcc"
				align="center"
				label="车辆尺寸"
				min-width="140"
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.clccStatus }">{{ scope.row.clcc }}</span>
				</template>
			</el-table-column>
			<el-table-column
				prop="zzl"
				align="center"
				label="总质量"
				min-width="80"
			>
				<template slot-scope="scope">
					<span :class="{ 'highlight': scope.row.zzlStatus }">{{ scope.row.zzl }}</span>
				</template></el-table-column
			></el-table
		>
	</div>
</template>

<script>
export default {
	name: 'cTable',
	components: {},
	props: {
		tableData: {
			required: true,
			type: Array,
			default: [],
		},
		title: {
			required: true,
			type: String,
			default: '',
		},
	},
	data() {
		return {}
	},
	computed: {},
	methods: {},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.title {
	height: 40px;
	width: 100%;
	line-height: 40px;
	border: 1px solid #eee;
	border-bottom: none;
	text-align: center;
}
.highlight {
	color: #409eff;
}
</style>