<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表-批量导入弹窗
  * @author: duan<PERSON><PERSON><PERSON>
  * @date: 2024/12/05 10:20:00
-->
<template>
  <div class="form-layer" v-loading.fullscreen.lock="showLoading">
    <el-form
      ref="contrastCarImportFormRef"
      :model="formData"
      :rules="rules"
      label-width="80px"
      class="demo-formData"
    >
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="任务名称" prop="taskName">
            <el-input
              placeholder="请输入任务名称"
              maxLength="60"
              v-model="formData.taskName"
              class="input-with-select"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <fieldset class="fieldset">
      <legend>附件上传</legend>
      <div slot="tip" class="el-upload__tip margin-bottom-6">注意事项：</div>
      <div slot="tip" class="el-upload__tip">1.仅支持xlsx格式的Excel文件</div>
      <div slot="tip" class="el-upload__tip margin-bottom-12">
        2.车牌号和颜色必填，其他可不填
      </div>
      <el-upload
        class="upload"
        ref="upload"
        :on-remove="handleRemove"
        :auto-upload="false"
        action="action"
        accept=".xlsx"
        :file-list="fileList"
        :multiple="false"
        :on-change="onChange"
      >
        <el-button slot="trigger" size="small" type="primary"
          >选取文件</el-button
        >
      </el-upload>
    </fieldset>
    <div class="btn">
      <el-button type="primary" @click="createTaskF">生成任务</el-button>
      <el-button @click="close">关 闭</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import { getToken } from '@/utils/auth'

export default {
  name: 'multipleCarImport',
  mixins: [layerMix],
  components: {},
  data() {
    return {
      formData: {
        file: '',
        taskName: '',
      },
      rules: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
          },
        ],
      },
      fileList: [],
      showLoading: false,
    }
  },
  computed: {},
  methods: {
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleRemove() {
      this.formData.file = ''
      this.fileList = []
    },
    upload() {
      this.showLoading = true
      let formData = new FormData()
      formData.append('file', this.formData.file)
      formData.append('taskName', this.formData.taskName)

      this.$request({
        url: this.$interfaces.contrastCarSearch,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: getToken(),
        },
      })
        .then((res) => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = ''
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '批量导入成功',
            })
            this.getParam('callBack')('success', this.layerid)
            this.close()
          }
        })
        .catch((error) => {
          this.showLoading = false
        })
    },
    createTaskF() {
      this.$refs.contrastCarImportFormRef.validate((valid) => {
        if (valid) {
          if (!this.formData.file) {
            this.$message({
              type: 'error',
              message: '请先添加文件',
            })
            return
          }
          this.upload()
        }
        return false
      })
    },
    close() {
      this.closeDialog()
    },
  },
  created() {},
  mounted() {},
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: calc(100% - 40px);
  height: 100%;
  padding: 20px;
  position: relative;
  ::v-deep .el-range-editor {
    width: 100%;
  }
  .text {
    font-size: 18px;
    font-weight: 500;
    color: #606266;
  }
  .warning {
    font-size: 14px;
    color: #606266;
    margin-top: 12px;
  }
  .el-upload__tip {
    font-weight: 700;
    line-height: 20px;
  }
  .margin-bottom-12 {
    margin-bottom: 12px;
  }
  .margin-bottom-6 {
    margin-bottom: 6px;
  }
  .btn {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 20px;
    width: 100%;
  }
  .selector {
    margin-bottom: 20px;
  }
  .fieldset {
    border-width: 1px;
    border-style: solid;
    border-color: #e7e7e7;
  }
  .upload {
    padding: 20px;
  }
}
</style>