<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表-车辆详情
  * @author: duan<PERSON><PERSON><PERSON>
  * @date: 2024/12/9 10:00:00
-->
<template>
	<div>
		<el-dialog
			:visible.sync="visible"
			:close-on-click-modal="false"
			:center="true"
			class="form_dialog"
			:show-close="false"
			width="1000px"
		>
			<template slot="title">
				<div class="btn-wrapper">
					<i @click="close()" class="el-icon-close"></i>
				</div>
				<div class="title-wrapper">
					<span class="title">车辆信息对比报表</span>
				</div>
			</template>
			<div class="car-detail-container">
				<div class="compare-report">
					<div class="ga-container">
						<car-form-item
							item-title="公安(交管)信息"
							:form-info="gaFormDetail"
						/>
					</div>
					<div class="ga-container">
						<car-form-item
							item-title="ETC发行信息"
							:form-info="etcFormDetail"
						/>
					</div>
				</div>

				<div class="info-show">
					<div>
						<span class="label">经营范围：</span>
						<span class="value" :title="businessScope">{{
							businessScope
						}}</span>
					</div>
					<div>
						<span class="label">比对结果：</span>
						<span class="value" :title="compareResult">{{
							compareResult
						}}</span>
					</div>
				</div>
				<div class="img-show-list">
					<div
						class="drive-img"
						v-for="ele in imgList"
						:key="ele.title"
					>
						<div class="drive-title">{{ ele.title }}</div>
						<el-image
							:src="ele.url"
							style="height: 160px; width: 240px"
							fit="contain"
							:preview-src-list="[ele.url]"
						>
							<div slot="error" class="empty-text">
								<span>图片缺失/拉取失败</span>
							</div>
						</el-image>
					</div>
				</div>
			</div>
			<template slot="footer">
				<el-button @click="close()" type="primary" size="small"
					>返回</el-button
				>
			</template>
		</el-dialog>
	</div>
</template>

<script>
import { falseCertificateSearch } from '@/api/equipment'
import carFormItem from '../components/carFormItem/index.vue'

export default {
	name: 'carDetail',
	components: {
		carFormItem,
	},
	props: {
		visible: {
			type: Boolean,
			default: false,
		},
		detailInfo: {
			type: Object,
		},
	},
	data() {
		return {
			gaFormDetail: {},
			etcFormDetail: {},
			compareResult: '', // 比对结果
			businessScope: '', // 经营范围
			imgList: [
				{
					title: '行驶证图片',
					url: '',
					photo_code: '3',
				},
				{
					title: '道路运输证图片',
					url: '',
					photo_code: '20',
				},
				{
					title: '车辆图片',
					url: '',
					photo_code: '6',
				},
			],
		}
	},
	watch: {
		visible: {
			handler(val) {
				if (val && this.detailInfo) {
					const {
						manageCarNo,
						manageCarColorStr,
						manageVehicleType,
						manageSeatNum,
						manageCarLength,
						manageCarWidth,
						manageCarHeight,
						manageCarMass,
						carNo,
						carColorStr,
						vehicleType,
						seatNum,
						carLength,
						carWidth,
						carHeight,
						carMass,
						auditResultStr,
						carColor,
						custMastId,
                        carStyle_str,
                        carType_str,
                        manageMgrarea,
					} = this.detailInfo
					this.gaFormDetail = {
						cphm: manageCarNo,
						cphmStatus: manageCarNo !== carNo,
						cpys: manageCarColorStr,
						cpysStatus: manageCarColorStr !== carColorStr,
						cllx: manageVehicleType,
						cllxStatus: manageVehicleType !== vehicleType,
						hdzrs: manageSeatNum,
						hdzrsStatus: manageSeatNum !== seatNum,
						clcc:
							manageCarLength && manageCarWidth && manageCarHeight
								? `${manageCarLength}*${manageCarWidth}*${manageCarHeight}`
								: '',
						clccStatus:
							manageCarLength !== carLength ||
							manageCarWidth !== carWidth ||
							manageCarHeight !== carHeight,
						zzl: manageCarMass,
						zzlStatus: manageCarMass !== carMass,
                        type: 'ga',
					}
					this.etcFormDetail = {
						cphm: carNo,
						cphmStatus: manageCarNo !== carNo,
						cpys: carColorStr,
						cpysStatus: manageCarColorStr !== carColorStr,
						cllx: vehicleType,
						cllxStatus: manageVehicleType !== vehicleType,
						hdzrs: seatNum,
						hdzrsStatus: manageSeatNum !== seatNum,
						clcc:
							carLength && carWidth && carHeight
								? `${carLength}*${carWidth}*${carHeight}`
								: '',
						clccStatus:
							manageCarLength !== carLength ||
							manageCarWidth !== carWidth ||
							manageCarHeight !== carHeight,
						zzl: carMass,
						zzlStatus: manageCarMass !== carMass,
						clsyxz: carStyle_str,
						cx: carType_str,
                        type: 'etc',
					}
					this.compareResult = auditResultStr || ''
                    this.businessScope = manageMgrarea || ''
					this.getImageUrl({
						customer_id: custMastId,
						vehicle_code: carNo,
						vehicle_color: carColor,
						scene: '2',
					})
				}
			},
			immediate: true,
		},
	},
	computed: {},
	methods: {
		close() {
            // 重置 imgList，因为falseCertificateSearch接口不是每次都能返回三张图片
            // 当缺少图片时，会出现缓存的情况。
            // 例如：第一辆车查出来三张图，正常展示；第二辆车查出来只有两张图，但是没查出来
            //      的图会缓存第一辆车的图，因为没有重置

            this.imgList.forEach((ele) => {
                ele.url = '';
            });
			this.$emit('update:visible', false)
		},
		async getImageUrl(params) {
			let { data, code } = await falseCertificateSearch(params)
			if (code == 200) {
				if (data && data.length > 0) {
					data.forEach((ele) => {
						const target = this.imgList.find(
							(item) => item.photo_code === ele.photo_code
						)
						if (target) {
							target.url = ele.file_url
						}
					})
				}
			}
		},
	},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.btn-wrapper {
	text-align: right;
	& > i {
		margin-right: 10px;
		font-size: 20px;
		color: #000000;
		&:last-child {
			margin-right: 0;
		}
		&:hover {
			cursor: pointer;
			color: #c6c6c6;
		}
	}
}
::v-deep .form_dialog {
	.el-dialog__body {
		padding-bottom: 12px;
	}
	.el-dialog--center {
		margin-top: 5vh !important;
	}
	.el-dialog.is-fullscreen {
		margin-top: 0 !important;
	}
}
.car-detail-container {
	width: 100%;
	.compare-report {
		display: flex;
		.ga-container {
			width: 50%;
			border: 1px solid #eee;
			&:first-child {
				border-right: none;
			}
		}
	}
	.info-show {
		height: 40px;
		width: 100%;
		border: 1px solid #eee;
		display: flex;
		background-color: #f5f7fa;
		border-top: none;
		div {
			flex: 1;
			height: 40px;
			display: flex;
			align-items: center;
			.label {
				width: 82px;
				text-align: right;
				font-weight: bold;
			}
			.value {
				width: calc(100% - 82px);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
	}
	.img-show-list {
		width: 100%;
		display: flex;
		height: auto;
		border: 1px solid #eee;
		border-top: none;
		padding: 12px;
		justify-content: space-between;
		.drive-img {
			width: 240px;
			height: 200px;
			.drive-title {
				height: 40px;
				line-height: 40px;
				text-align: center;
			}
		}
	}
}
::v-deep .empty-text {
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 14px;
	color: #c0c4cc;
	vertical-align: middle;
	height: 100%;
	width: 100%;
	background: #f5f7fa;
}
.table {
	padding: 0;
	.el-table {
		margin-bottom: 0;
	}
}
.pagination {
	margin-top: 0;
}
.bottom-wrapper {
	margin-top: 50px;
}
</style>