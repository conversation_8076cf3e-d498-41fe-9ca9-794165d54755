<!--
  * @projectName: 广西ETC管理系统
  * @desc: 车辆列表
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/12/13 10:00:00
-->
<template>
	<div class="car-list">
		<dart-search
			:formSpan="24"
			:gutter="20"
			ref="carListFormRef"
			label-position="right"
			:model="formData"
			:fontWidth="2"
		>
			<template slot="search-form">
				<dart-search-item label="车牌号: " prop="carNo" :span="8">
					<el-input
						v-model.trim="formData.carNo"
						clearable
						placeholder="请输入车牌号"
					></el-input>
				</dart-search-item>
				<dart-search-item label="车牌颜色：" prop="carColor" :span="8">
					<el-select
						v-model="formData.carColor"
						placeholder="请选择"
						clearable
					>
						<el-option
							v-for="item in licenseColorOption"
							:key="item.value"
							:label="item.label"
							:value="item.value"
						/>
					</el-select>
				</dart-search-item>
				<dart-search-item :is-button="true" :span="8">
					<el-button type="primary" @click="queryF">查询</el-button>
				</dart-search-item>
			</template>
		</dart-search>

		<div class="table">
			<el-table
				v-loading="loading"
				:data="tableData"
				align="center"
				header-align="center"
				style="width: 100%; margin-bottom: 20px"
				:row-style="{ height: '54px' }"
				:cell-style="{ padding: '0px' }"
				:header-row-style="{ height: '54px' }"
				:header-cell-style="{ padding: '0px' }"
				row-key="auditId"
				border
				height="100%"
			>
				<el-table-column
					prop="custName"
					align="center"
					label="用户名称"
					min-width="160"
					show-overflow-tooltip
				/>
				<el-table-column
					prop="carNo"
					align="center"
					label="车牌号码"
					min-width="100"
				/>
				<el-table-column
					prop="carColorStr"
					align="center"
					label="车牌颜色"
					min-width="100"
				/>
				<el-table-column
					prop="vehicleType"
					align="center"
					label="车辆类型"
					min-width="140"
					show-overflow-tooltip
				/>
				<el-table-column
					prop="auditResultStr"
					align="center"
					label="稽核结果"
					min-width="500"
					show-overflow-tooltip
				/>
				<el-table-column
					prop="auditTime"
					align="center"
					label="稽核时间"
					min-width="170"
				/>
				<el-table-column
					align="center"
					header-align="center"
					width="240"
					fixed="right"
					label="操作"
				>
					<template slot-scope="scope">
						<el-button
							@click="checkCarDetailF(scope.row)"
							type="mini"
							>查看车辆详细信息</el-button
						>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination
				background
				:current-page="pageIndex"
				:page-size="pageSize"
				:page-sizes="[10, 20, 50]"
				layout="total, prev, pager, next, sizes, jumper"
				:total="total"
				@current-change="changePageNum"
				@size-change="changePageSize"
			/>
		</div>

		<CarDetail
			:visible.sync="carDetailVisible"
			:detail-info="carDetailInfo"
		/>
	</div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import CarDetail from '../carDetail/index.vue'
import { falseCertificateVehicleList } from '@/api/equipment'
import moment from 'moment'

export default {
	name: 'carList',
	components: {
		dartSearch,
		dartSearchItem,
		CarDetail,
	},
	data() {
		return {
			formData: {
				carNo: '',
				carColor: null,
				taskId: '',
			},
			licenseColorOption, // 车牌颜色集合
			loading: false,
			tableData: [],
			carDetailVisible: false,
			total: 0,
			pageIndex: 1,
			pageSize: 10,
			carDetailInfo: null, // 车辆详细信息
		}
	},
	computed: {},
	methods: {
		checkCarDetailF(val) {
			this.carDetailInfo = val
			this.carDetailVisible = true
		},
		async getCarList() {
			this.startLoading()
			let { data, code } = await falseCertificateVehicleList({
				...this.formData,
				carColor: this.formData.carColor
					? Number(this.formData.carColor)
					: null,
				pageIndex: this.pageIndex,
				pageSize: this.pageSize,
			})
			if (code == 200) {
				const { total, records } = data
				this.total = total
				this.tableData = records
					? records.map((ele) => ({
							...ele,
							auditTime: moment(ele.auditTime).format(
								'YYYY-MM-DD HH:ss:mm'
							),
					  }))
					: [];
			} else {
				this.total = 0
				this.tableData = []
			}
			this.endLoading()
		},
		queryF() {
			const { carNo, carColor } = this.formData
			if (carNo && !carColor) {
				this.$message.warning('请选择车牌颜色')
				return
			}
			if (!carNo && carColor) {
				this.$message.warning('请输入车牌号码')
				return
			}
			this.pageIndex = 1
			this.getCarList()
		},
		changePageNum(page) {
			this.pageIndex = page
			this.getCarList()
		},
		changePageSize(val) {
			this.pageIndex = 1
			this.pageSize = val
			this.getCarList()
		},
	},
	created() {},
	mounted() {
		const { carNo, carColor, taskId } = this.$route.query
		this.formData.carNo = carNo
		this.formData.carColor = carColor
		this.formData.taskId = taskId
		this.getCarList()
	},
}
</script>

<style lang="scss" scoped>
.car-list {
	height: 100%;
	width: 100%;
	padding: 20px;
	flex-flow: column;
	display: flex;
	::v-deep .dart-search-wrapper {
		margin-bottom: 0;
	}
	.table {
		flex: 1;
		min-height: 0;
		flex-flow: column;
		display: flex;
	}
}
</style>