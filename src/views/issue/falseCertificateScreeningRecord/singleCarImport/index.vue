<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表-单车辆导入弹窗
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/12/05 10:20:00
-->
<template>
	<div class="form-layer">
		<el-form
			ref="form"
			:model="formData"
			label-width="150px"
			:rules="rules"
		>
			<el-form-item label="任务名称:" prop="taskName">
				<el-input
					v-model.trim="formData.taskName"
					placeholder="请输入任务名称"
					clearable
				></el-input>
			</el-form-item>
			<el-form-item label="车牌号码" prop="carNo">
				<el-input
					placeholder="请输入车牌号"
					maxLength="11"
					v-model="formData.carNo"
					class="input-with-select"
				>
					<el-select
						v-model="formData.vehicleProvincePre"
						style="width: 80px"
						slot="prepend"
						placeholder="请选择"
					>
						<el-option
							v-for="(value, key) in provinces"
							:key="key"
							:label="key"
							:value="value"
						></el-option>
					</el-select>
				</el-input>
			</el-form-item>
			<el-form-item label="车牌颜色:" prop="carColor">
				<el-select
					v-model="formData.carColor"
					placeholder="请选择"
					clearable
					style="width: 100%"
				>
					<el-option
						v-for="item in licenseColorOption"
						:key="item.value"
						:label="item.label"
						:value="item.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="createTaskF"
					>生成任务</el-button
				>
			</el-form-item>
		</el-form>
	</div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import { licenseColorOption, provinces } from '@/common/const/optionsData'

export default {
	name: 'singleCarImport',
	components: {},
	mixins: [layerMix],
	data() {
		return {
            provinces,
			formData: {
				taskName: null,
				carNo: null,
				carColor: null,
                vehicleProvincePre: '桂',
			},
			rules: {
				taskName: [
					{
						required: true,
						message: '任务名称不能为空',
						trigger: 'blur',
					},
				],
				carNo: [
					{
						required: true,
						message: '车牌号不能为空',
						trigger: 'blur',
					},
				],
				carColor: [
					{
						required: true,
						message: '请选择车牌颜色',
						trigger: 'blur',
					},
				],
			},
			licenseColorOption, // 车牌颜色集合
		}
	},
	computed: {},
	methods: {
		createTaskF() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.getParam('callBack')(
						{
							taskName: this.formData.taskName,
                            carNo: `${this.formData.vehicleProvincePre}${this.formData.carNo}`,
							carColor: Number(this.formData.carColor),
						},
						this.layerid
					)
				} else {
					// 表单验证失败
					console.log('表单验证失败')
					return false
				}
			})
		},
		close() {
			this.closeDialog()
		},
	},
	created() {},
	mounted() {},
}
</script>

<style lang="scss" scoped>
.form-layer {
	width: 100%;
	height: 100%;
	padding: 20px;
	::v-deep .el-range-editor {
		width: 100%;
	}
	::v-deep .el-input-group__append {
		padding: 0 6px;
	}
	::v-deep .el-form-item:last-child {
		.el-form-item__content {
			margin-left: 0 !important;
			text-align: center;
		}
	}
}
</style>