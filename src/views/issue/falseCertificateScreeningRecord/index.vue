<!--
  * @projectName: 广西ETC管理系统
  * @desc: 假证筛查导入查询记录表
  * @author: du<PERSON><PERSON><PERSON><PERSON>
  * @date: 2024/12/04 17:20:00
-->
<template>
  <div class="false-certificate-screening-record">
    <dart-search
      :formSpan="24"
      :gutter="20"
      ref="falseCertificateFormRef"
      label-position="right"
      :model="formData"
      :fontWidth="2"
    >
      <template slot="search-form">
        <dart-search-item label="任务名称: " prop="taskName">
          <el-input
            v-model.trim="formData.taskName"
            clearable
            placeholder="请输入任务名称"
          ></el-input>
        </dart-search-item>
        <dart-search-item label="任务ID: " prop="taskId">
          <el-input
            v-model.trim="formData.taskId"
            clearable
            placeholder="请输入任务ID"
          ></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号: " prop="carNo">
          <el-input
            v-model.trim="formData.carNo"
            clearable
            placeholder="请输入车牌号"
          ></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色：" prop="carColor">
          <el-select v-model="formData.carColor" placeholder="请选择" clearable>
            <el-option
              v-for="item in licenseColorOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true" style="margin-left: 35px">
          <el-button type="primary" @click="queryF()">查询</el-button>
          <el-button @click="resetF()">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>

    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="singleCarImportF"
          >单辆导入</el-button
        >
        <el-button type="primary" size="mini" @click="multipleCarImportF"
          >批量导入</el-button
        >
        <el-button type="warning" size="mini" @click="templateDownloadF"
          >批量导入表样下载</el-button
        >
        <el-button type="primary" size="mini" @click="constratCarImportF"
          >交管数据批量查询</el-button
        >
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        align="center"
        header-align="center"
        style="width: 100%; margin-bottom: 20px"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="taskId"
        border
        height="100%"
      >
        <el-table-column
          prop="taskId"
          align="center"
          label="任务ID"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="taskName"
          align="center"
          label="任务名称"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          prop="createTime"
          align="center"
          label="任务生成时间/导入时间"
          min-width="170"
        />
        <el-table-column
          prop="updateTime"
          align="center"
          label="任务执行时间"
          min-width="170"
          show-overflow-tooltip
        />
        <el-table-column
          prop="taskStatus"
          align="center"
          label="任务状态"
          min-width="120"
        />
        <el-table-column
          prop="createByStr"
          align="center"
          label="操作人"
          min-width="120"
        />
        <el-table-column
          prop="taskSource"
          align="center"
          label="任务生成方式"
          min-width="120"
        />
        <el-table-column
          align="center"
          header-align="center"
          width="240"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <el-button :disabled="scope.row.bizType == '2'" @click="checkCarResultListF(scope.row)" type="mini"
              >查看车辆结果列表</el-button
            >
            <el-button
              @click="exportF(scope.row)"
              type="mini"
              :disabled="scope.row.taskStatus === '执行中'"
              >导出</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="pageIndex"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50]"
        layout="total, prev, pager, next, sizes, jumper"
        :total="total"
        @current-change="changePageNum"
        @size-change="changePageSize"
      />
    </div>
  </div>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import CarDetail from './carDetail/index.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import {
  falseCertificateResultList,
  falseCertificateVehicleCheck,
  falseCertificateResultExport,
} from '@/api/equipment'

export default {
  components: {
    dartSearch,
    dartSearchItem,
    CarDetail,
  },
  mixins: [tableListMixin],
  data() {
    return {
      formData: {
        taskId: null,
        carColor: null,
        carNo: '',
        taskName: '',
      },
      licenseColorOption, // 车牌颜色集合
      tableData: [],
      loading: false,
      total: 0,
      pageIndex: 1,
      pageSize: 10,
    }
  },
  computed: {},
  methods: {
    queryF() {
      const { carNo, carColor } = this.formData
      if (carNo && !carColor) {
        this.$message.warning('请选择车牌颜色')
        return
      }
      if (!carNo && carColor) {
        this.$message.warning('请输入车牌号码')
        return
      }
      this.pageIndex = 1
      this.getTableList()
    },
    resetF() {
      this.formData.taskId = null
      this.formData.carColor = null
      this.formData.carNo = null
      this.formData.taskName = null
      this.pageIndex = 1
      this.getTableList()
    },
    singleCarImportF() {
      this.$openPage(
        '@/views/issue/falseCertificateScreeningRecord/singleCarImport/index.vue',
        '单辆导入',
        {
          callBack: async (res, lid) => {
            let { code } = await falseCertificateVehicleCheck(res)
            if (code == 200) {
              this.$message.success('单辆导入成功')
              this.$layer.close(lid)
              this.getTableList()
            }
          },
        },
        {
          area: ['32%', '330px'],
        }
      )
    },
    multipleCarImportF() {
      this.$openPage(
        '@/views/issue/falseCertificateScreeningRecord/multipleCarImport/index.vue',
        '批量导入',
        {
          callBack: (res, lid) => {
            this.getTableList()
          },
        },
        {
          area: ['41%', '440px'],
        }
      )
    },
    constratCarImportF() {
      this.$openPage(
        '@/views/issue/falseCertificateScreeningRecord/dataContrastImport/index.vue',
        '交管数据批量查询',
        {
          callBack: (res, lid) => {
            this.getTableList()
          },
        },
        {
          area: ['41%', '440px'],
        }
      )
    },
    templateDownloadF() {
      this.startLoading()
      this.$request({
        url: this.$interfaces.falseCertificateDownload,
        method: 'post',
        responseType: 'blob',
      })
        .then((res) => {
          // 如果是流blob的形式
          const link = document.createElement('a')
          let blob = new Blob([res]) //构造一个blob对象来处理数据
          link.style.display = 'none'
          link.href = URL.createObjectURL(blob)
          link.download = '批量导入车辆模板.xlsx' //下载的文件名
          document.body.appendChild(link)
          link.click() // 执行下载
          document.body.removeChild(link) // 释放标签
          this.endLoading()
        })
        .catch((error) => {
          console.log(error)
          this.endLoading()
        })
    },
    changePageNum(page) {
      this.pageIndex = page
      this.getTableList()
    },
    changePageSize(val) {
      this.pageIndex = 1
      this.pageSize = val
      this.getTableList()
    },
    checkCarResultListF(row) {
      this.$router.push({
        path: '/issue/carResultList',
        query: {
          taskId: row.taskId,
          carNo: this.formData.carNo,
          carColor: this.formData.carColor,
        },
      })
    },
    exportF(val) {
      let params = {
        taskId: val.taskId,
        carNo: '',
        carColor: '',
      }
      let fileObj = {
        fileName: `${val.taskName}.xlsx`,
      }
      this.exportFile(params, falseCertificateResultExport, fileObj)
    },
    async getTableList() {
      this.startLoading()
      let { data, code } = await falseCertificateResultList({
        ...this.formData,
        carColor: this.formData.carColor
          ? Number(this.formData.carColor)
          : null,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
      })
      if (code == 200) {
        const { total, records } = data
        this.total = total
        this.tableData = records
      } else {
        this.total = 0
        this.tableData = []
      }
      this.endLoading()
    },
  },
  created() {},
  mounted() {},
}
</script>

<style lang="scss" scoped>
.false-certificate-screening-record {
  height: 100%;
  width: 100%;
  padding: 20px;
  flex-flow: column;
  display: flex;
  ::v-deep .dart-search-wrapper {
    margin-bottom: 0;
  }
  .table {
    flex: 1;
    min-height: 0;
    flex-flow: column;
    display: flex;
    .top-btn {
      margin-bottom: 20px;
    }
  }
}
</style>