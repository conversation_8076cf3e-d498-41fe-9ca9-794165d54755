<template>
  <div class="import-dialog"
       v-loading.fullscreen.lock="showLoading">
    <el-dialog :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               class="form_dialog"
               :show-close="true"
               title="失信名单导入"
               :before-close="handleCloseIcon"
               width="45%">
      <fieldset class="fieldset">
        <legend class="fieldset-legend">附件上传</legend>

        <div slot="tip"
             class="el-upload__tip">1、仅支持文件格式xls或xlsx的Excel文件 </div>
        <div slot="tip"
             class="el-upload__tip">
          导入字段包含：[ 序号 | 车牌号 | 车牌颜色 | 原因 ] 车牌颜色：蓝色，黄色，黑色，白色，渐变绿色，黄绿双拼色，蓝白渐变色
        </div>
        <div class="g-flex g-flex-align-center">
          <el-upload class="upload"
                     ref="upload"
                     :on-remove="handleRemove"
                     :auto-upload="false"
                     action="action"
                     accept=".xls,.xlsx"
                     :file-list="fileList"
                     :multiple="false"
                     :on-change="onChange">
            <el-button slot="trigger"
                       size="small"
                       type="primary">选取文件</el-button>
          </el-upload>
          <el-button size="small"
                     type="warning"
                     @click="downLoad">导入模板下载</el-button>
        </div>
      </fieldset>
      <div class="bottom-btn g-flex g-flex-center">
        <el-button @click="submitUpload"
                   type="primary"
                   size="mini">确定</el-button>
        <el-button size="mini"
                   @click="handleCloseIcon">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogFormVisible: false,

      showLoading: false,
      formData: {
        file: '',
      },
      fileList: [],
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fileList = []
        this.formData.file = ''
        // this.formData.source = ''
      }
      this.responseDialogVisible = false
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    },
  },
  methods: {
    downLoad() {
      axios
        .get('static/失信名单导入模板.xlsx', {
          responseType: 'blob', //重要
        })
        .then((response) => {
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')
          let fname = '失信名单导入模板.xlsx'
          link.href = url
          link.setAttribute('download', fname)
          document.body.appendChild(link)
          link.click()
        })
    },
    submitUpload() {
      if (!this.formData.file) {
        this.$message({
          type: 'error',
          message: '请先添加文件',
        })
        return
      }
      if (this.formData.file['name']) {
        let filePath = this.formData.file['name']
        //获取最后一个.的位置
        let index = filePath.lastIndexOf('.')
        //获取后缀
        let ext = filePath.substr(index + 1)

        console.log('ext', ext)
        let acceptType = ['xls', 'xlsx']

        if (acceptType.indexOf(ext.toLowerCase()) == -1) {
          //不符合文件类型
          this.$message({
            type: 'error',
            message: '不符合上传文件类型',
          })
          return
        }
      }

      this.upload()
    },

    upload() {
      this.showLoading = true
      let formData = new FormData()
      formData.append('file', this.formData.file)
      this.$request({
        url: this.$interfaces.breakFaithImport,
        method: 'post',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' },
      })
        .then((res) => {
          this.showLoading = false
          this.$refs.upload.clearFiles()
          this.formData.file = ''
          console.log('res', res)
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: '导入成功',
            })
            this.dialogFormVisible = false
            this.$emit('uploadSuccess')
          } else {
            this.$message.error(res.msg)
          }
        })

        .catch((error) => {
          this.showLoading = false
          console.log(error)
        })
    },
    handleRemove() {
      console.log('清空')
      this.formData.file = ''
    },
    onChange(files) {
      this.$refs.upload.clearFiles()
      if (this.fileList.length === 0) {
        this.fileList.push({ name: files.name, status: 'success' })
      } else {
        this.fileList = []
        this.fileList.push({ name: files.name, status: 'success' })
      }
      this.formData.file = files.raw
    },
    handleCloseIcon() {
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.selector {
  margin-bottom: 20px;
}
.fieldset {
  border-width: 1px;
  border-style: solid;
  border-color: #e7e7e7;
}
.fieldset-legend {
  font-size: 18px;
  font-weight: 500;
  color: #606266;
  width: 80px;
}
.upload {
  padding: 20px;
}
.el-upload__tip {
  font-weight: 700;
  line-height: 20px;
}
.bottom-btn {
  margin-top: 40px;
}
</style>