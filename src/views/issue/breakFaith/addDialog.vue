<template>
  <el-dialog title="新增失信名单"
             @closed='onClosedHandle'
             :visible.sync="dialogVisible"
             :close-on-click-modal="false"
             :center="true"
             width="480px"
             custom-class="add-cancel-dialog">
    <div class="form-info">
      <el-form :model="ruleForm"
               :rules="rules"
               ref="ruleForm"
               label-width="140px"
               size="medium"
               class="dt-form dt-form--max">
        <el-form-item label="车牌号："
                      prop="carNo">
          <el-input v-model="ruleForm.carNo"></el-input>
        </el-form-item>
        <el-form-item label="车牌颜色："
                      prop="carColor">
          <el-select v-model="ruleForm.carColor"
                     placeholder="请选择"
                     style="width:100%">
            <el-option v-for="(value,key) in licenseColorOption"
                       :label="value.label"
                       :value="value.value"
                       :key='key'>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="添加原因"
                      prop="badFaithReason">
          <el-input type="textarea"
                    v-model="ruleForm.badFaithReason"></el-input>
        </el-form-item>
      </el-form>

    </div>
    <div slot="footer"
         class="btn">
      <el-button @click="addHandle"
                 type="primary"
                 style="width: 100px;"
                 size="small">添加</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { licenseColorOption } from '@/common/const/optionsData.js'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: false,
      ruleForm: {
        carNo: '',
        carColor: '',
        carColor: '',
      },
      rules: {
        carNo: [{ required: true, message: '请输入车牌号码', trigger: 'blur' }],
        carColor: [
          { required: true, message: '请输入车牌颜色', trigger: 'blur' },
        ],
        badFaithReason: [
          { required: true, message: '请输入原因', trigger: 'blur' },
        ],
      },
      licenseColorOption,
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
  },
  methods: {
    addHandle() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.addMsgMonitor()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    addMsgMonitor() {
      this.startLoading()

      this.$request({
        url: this.$interfaces.breaKFaithAdd,
        method: 'post',
        data: this.ruleForm,
      })
        .then((res) => {
          this.endLoading()
          if (res.code == 200) {
            this.$message({
              message: '新增成功',
              type: 'success',
            })
            this.$emit('getList')
            this.dialogVisible = false
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    onClosedHandle() {
      this.dialogVisible = false
    },
  },
  watch: {
    visible: function (val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    },
  },
}
</script>

<style lang="scss">
.add-cancel-dialog .el-dialog__body {
  padding-bottom: 10px;
}
.form-info {
  padding: 0 25px;
}
.add-cancel-dialog .btn {
  width: 100%;
  text-align: center;
}
</style>