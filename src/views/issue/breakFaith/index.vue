<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:失信名单管理
  * @author:zhangys
  * @date:2023/03/28 13:52:44
-->
<template>
  <div class="container">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="form"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 :labelTextLength="8"
                 class="search">
      <template slot="search-form">

        <dart-search-item label="进入限制名单时间："
                          prop="">
          <el-date-picker v-model="inLimitDate"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          clearable
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item label="解除限制名单时间："
                          prop="">
          <el-date-picker v-model="outLimitDate"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          clearable
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="用户证件号："
                          prop="custIdNo">
          <el-input v-model="form.custIdNo"
                    clearable
                    placeholder=""></el-input>
        </dart-search-item>

        <div class="collapse-wrapper"
             v-show="isCollapse">
          <dart-search-item label="限制状态："
                            prop="type">
            <el-select clearable
                       v-model="form.type"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in limitStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="车牌号："
                            prop="carNo">
            <el-input v-model="form.carNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色："
                            prop="carColor">
            <el-select clearable
                       v-model="form.carColor"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in licenseColorOption"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="加入类型："
                            prop="limitType">
            <el-select clearable
                       v-model="form.limitType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in limitTypeOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

        </div>
        <dart-search-item :is-button="true"
                          :colElementNum='isCollapse ?1:1'>
          <div class="g-flex g-flex-end">
            <el-button type="primary"
                       size="mini"
                       @click="onSearchHandle">查询</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button type="primary"
                       size="mini"
                       @click="importHandle">导入</el-button>
            <el-button type="primary"
                       size="mini"
                       @click="exportHandle">导出</el-button>
            <el-button type="warning"
                       size="mini"
                       @click="add">添加</el-button>
            <el-button type="primary"
                       size="mini"
                       @click="batchDeleteConfirm">批量删除</el-button>
            <el-button type="primary"
                       size="mini"
                       @click="batchRescindConfirm">批量解除</el-button>
            <el-button type="text"
                       @click="isCollapse=!isCollapse"><span v-if="isCollapse">收起</span><span v-if="!isCollapse">展开</span></el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table :data="tableData"
                style="width: 100%;"
                height="100%"
                row-key="id"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection"
                         align="center">
        </el-table-column>
        <el-table-column prop="custName"
                         label="用户名称"
                         min-width="160"
                         align="center" />
        <el-table-column prop="carNo"
                         label="车牌号"
                         min-width="120"
                         align="center" />
        <el-table-column prop="carColor"
                         label="车牌颜色"
                         align="center"
                         min-width="100">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.carColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="custIdNo"
                         label="证件号"
                         min-width="160"
                         align="center" />
        <el-table-column prop="createdTime"
                         label="进入限制名单时间"
                         min-width="180"
                         align="center" />

        <el-table-column prop="badFaithReason"
                         label="进入原因"
                         min-width="180"
                         align="center" />

        <el-table-column prop="limitType"
                         label="加入类型"
                         align="center"
                         min-width="100">
          <template slot-scope="scope">
            {{scope.row.limitType == 1 ? '自助激活稽核' : '移动OBU稽核'}}
          </template>
        </el-table-column>
        <el-table-column prop="releaseTime"
                         label="解除限制名单时间"
                         min-width="180"
                         align="center" />
        <el-table-column prop="releaseReason"
                         label="解除原因"
                         min-width="180"
                         align="center" />

        <el-table-column label="操作"
                         fixed="right"
                         min-width="160"
                         align="center">
          <template slot-scope="scope">

            <el-button type="primary"
                       size="mini"
                       @click="rescindConfirm(scope.row)">解除</el-button>
            <el-button type="danger"
                       size="mini"
                       @click="deleteConfirm(scope.row)">删除</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="form.page"
                     :page-sizes="[10, 20, 50]"
                     :page-size="form.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <div>
      <addDialog :visible.sync="addVisible"
                 v-if="addVisible"
                 @getList="getList"></addDialog>
    </div>
    <importDialog :visible.sync="importDialogVisible"
                  @uploadSuccess="getList">
    </importDialog>
  </div>
</template>

<script>
import { getVehicleColor } from '@/common/method/formatOptions'
import {
  licenseColorOption,
  vehicleType,
  limitStatus,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import dartSlide from '@/components/dart/Slide/index.vue'
import addDialog from './addDialog'
import importDialog from './importDialog'
export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    addDialog,
    importDialog,
  },
  data() {
    return {
      licenseColorOption,
      form: {
        carColor: '',
        carNo: '',
        createdTimeEnd: '',
        createdTimeStart: '',
        custIdNo: '',
        limitStatus: '',
        orderByField: 'id',
        page: 1,
        pageSize: 10,
        releaseTimeEnd: '',
        releaseTimeStart: '',
        sort: 'desc',
      },
      isCollapse: false,
      inLimitDate: '',
      outLimitDate: '',

      orderStatusOptions: [],
      tableData: [],
      detailVisible: false,
      total: 0,
      addVisible: false,
      selectIds: [],
      importDialogVisible: false,
      selectItems: [],
      limitStatus,
      limitTypeOptions: [
        {
          label: '自助激活稽核',
          value: '1',
        },
        {
          label: '移动OBU稽核',
          value: '2',
        },        
      ],
    }
  },
  computed: {},
  watch: {
    inLimitDate(val) {
      if (!val) {
        this.form.createdTimeStart = ''
        this.form.createdTimeEnd = ''
      }
    },
    outLimitDate(val) {
      if (!val) {
        this.form.releaseTimeStart = ''
        this.form.releaseTimeEnd = ''
      }
    },
  },
  created() {
    this.getList()
  },
  methods: {
    getVehicleColor,
    onSearchHandle() {
      this.getList()
    },
    formatDate() {
      if (this.inLimitDate) {
        this.form.createdTimeStart = this.inLimitDate[0]
        this.form.createdTimeEnd = this.inLimitDate[1]
      }
      if (this.outLimitDate) {
        this.form.releaseTimeStart = this.outLimitDate[0]
        this.form.releaseTimeEnd = this.outLimitDate[1]
      }
    },
    onResultHandle() {
      this.form = {
        carColor: '',
        carNo: '',
        createdTimeEnd: '',
        createdTimeStart: '',
        custIdNo: '',
        limitStatus: '',
        orderByField: 'id',
        page: 1,
        pageSize: 20,
        releaseTimeEnd: '',
        releaseTimeStart: '',
        sort: 'desc',
      }
      this.inLimitDate = ''
      this.outLimitDate = ''
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getList()
    },
    changePage(e) {
      this.form.page = e
      this.getList()
    },
    toDetail(val, type) {
      this.detailVisible = true
    },
    //导出
    exportHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.breaKFaithExport,
        method: 'post',
        data: this.form,
        responseType: 'blob',
      })
        .then((res) => {
          this.getBlob(res, 'application/vnd.ms-excel', '失信名单列表')
          this.endLoading()
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
    // 导入
    importHandle() {
      this.importDialogVisible = true
    },
    getList() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.breakFaithList,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.data
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //添加
    add() {
      this.addVisible = true
    },
    //表格选中
    handleSelectionChange(val) {
      this.selectItems = val.map((item) => {
        return item.id
      })
    },
    //批量删除
    batchDeleteConfirm() {
      if (this.selectItems.length == 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.$confirm('请确认是否要删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let params = {
            ids: this.selectItems,
          }
          this.deleteHandle(params, 'batch')
        })
        .catch(() => {})
    },
    //删除确认弹框
    deleteConfirm(val) {
      this.$confirm('请确认是否要删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          let selectIds = []
          selectIds.push(val.id)
          let params = {
            ids: selectIds,
          }
          this.deleteHandle(params, 'one')
        })
        .catch(() => {})
    },

    //删除请求
    deleteHandle(params, val) {
      this.startLoading()

      this.$request({
        url: this.$interfaces.breakFaithDelete,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            if (val == 'batch') {
              this.selectItems = []
            }
            this.getList()
            this.$message.success('删除成功')
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    //批量解除
    batchRescindConfirm() {
      if (this.selectItems.length == 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.$prompt('解除原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        placeholder: '请输入解除原因',
      })
        .then(({ value }) => {
          if (!value) {
            this.$message({
              type: 'error',
              message: '请输入解除原因',
            })
            return
          }
          let params = {
            ids: this.selectItems,
            releaseReason: value,
          }
          this.rescindHandle(params, 'batch')
        })
        .catch(() => {})
    },
    //解除确认框
    rescindConfirm(val) {
      this.$prompt('解除原因', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        placeholder: '请输入解除原因',
      })
        .then(({ value }) => {
          if (!value) {
            this.$message({
              type: 'error',
              message: '请输入解除原因',
            })
            return
          }
          let selectIds = []
          selectIds.push(val.id)
          let params = {
            ids: selectIds,
            releaseReason: value,
          }
          this.rescindHandle(params, 'one')
        })
        .catch(() => {})
    },
    //解除请求
    rescindHandle(params, type) {
      this.startLoading()

      this.$request({
        url: this.$interfaces.breakFaithRelease,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            if (type == 'batch') {
              this.selectItems = []
            }
            this.getList()
            this.$message.success('解除成功')
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang='scss' scoped>
.container {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table {
    flex: 1;
    height: 0;
    background-color: #fff;
  }
}
</style>