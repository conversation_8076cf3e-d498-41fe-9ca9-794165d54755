<template>
  <div class="recheck-layer">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <!-- 运营公司选择 -->
      <el-form-item label="运营公司" prop="companyId">
        <el-select v-model="formData.companyId" placeholder="请选择运营公司" @change="handleCompanyChange">
          <el-option
            v-for="item in companyOptions"
            :key="item.id"
            :label="item.companyName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      
      <!-- 接收邮箱 -->
      <el-form-item label="接收邮箱" prop="emailAddress">
        <el-input
          v-model="formData.emailAddress"
          placeholder="选择运营公司后自动填充"
          disabled
        />
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submitForm">确定发送</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import { selectCompanyList } from '@/api/equipment'

export default {
  name: 'ReformLayer',
  mixins: [layerMix],
  data() {
    return {
      formData: {
        companyId: null,
        emailAddress: ''
      },
      companyOptions: [],
      rules: {
        companyId: [
          { required: true, message: '请选择运营公司', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getCompanyOptions()
  },
  methods: {
    // 获取运营公司选项
    async getCompanyOptions() {
      try {
        const res = await selectCompanyList({})
        this.companyOptions = res.data
        console.log(res, 333);
        
      } catch (error) {
        console.error('获取运营公司列表失败:', error)
      }
    },
    handleCompanyChange(selectedId) {
      const selectedCompany = this.companyOptions.find(item => item.id === selectedId);
      if (selectedCompany) {
        this.formData.emailAddress = selectedCompany.companyEmail;
      } else {
        this.formData.emailAddress = '';
      }
    },
    close() {
      this.closeDialog()
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let params = {
            emailAddress:this.formData.emailAddress
          }
          this.getParam('callBack')(params, this.layerid)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.recheck-layer {
  width: 100%;
  padding: 20px;

  .el-select {
    width: 100%;
  }

  .bottom-btn {
    margin-top: 30px;
    
    .el-button {
      min-width: 100px;
      margin: 0 10px;
    }
  }
}
</style>