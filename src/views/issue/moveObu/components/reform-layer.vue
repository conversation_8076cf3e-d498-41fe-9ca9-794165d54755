<template>
  <div class="recheck-layer">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="请选择整改结果" prop="correctionResult">
        <el-radio-group v-model="formData.correctionResult">
          <el-radio label="0">已注销</el-radio>
          <el-radio label="1">未整改已拉黑</el-radio>
          <el-radio label="2">已规范激活</el-radio>
          <el-radio label="3">已差错处理</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'

export default {
  name: 'ReformLayer',
  mixins: [layerMix],
  data() {
    return {
      formData: {
        correctionResult: '',
      },
      rules: {
        correctionResult: [
          { required: true, message: '请选择整改结果', trigger: 'change' }
        ],
      }
    }
  },
  methods: {
    close() {
      this.closeDialog()
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.getParam('callBack')(this.formData, this.layerid)
          this.close()
        }
      })
    }
  },
}
</script>

<style lang="scss" scoped>
.recheck-layer {
  width: 100%;
  padding: 30px;
  
  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    
    .el-radio {
      width: calc((100% - 30px) / 2); // 每行3个，减去两个间隔的15px
      margin-right: 0;
      
      &:nth-last-child(1),
      &:nth-last-child(2) {
        // 最后两个元素
        width: calc((100% - 30px) / 2); // 最后一行2个，减去中间间隔的15px
      }
    }
  }

  .bottom-btn {
    margin-top: 30px;
    
    .el-button {
      min-width: 100px;
    }
  }
}
</style>
