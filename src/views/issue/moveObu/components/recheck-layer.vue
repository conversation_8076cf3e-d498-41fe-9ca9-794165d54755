<template>
  <div class="recheck-layer">
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="请选择复核结果" prop="auditResult">
        <el-radio-group v-model="formData.auditResult">
          <el-radio label="0">正常</el-radio>
          <el-radio label="1">换牌不换车</el-radio>
          <el-radio label="2">换牌换车（车型不变）</el-radio>
          <el-radio label="3">换牌换车（车型变）</el-radio>
          <el-radio label="4">客货类型发行错误</el-radio>
          <el-radio label="5">使用移动OBU</el-radio>
          <el-radio label="6">复核时已注销</el-radio>
          <el-radio label="7">其他（请输入具体复核结果）</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注：" prop="auditDetails">
        <el-input
          type="textarea"
          v-model="formData.auditDetails"
          :rows="3"
          placeholder="请输入复核结果"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="bottom-btn g-flex g-flex-center">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'

export default {
  name: 'RecheckLayer',
  mixins: [layerMix],
  data() {
    return {
      formData: {
        auditResult: '',
        auditDetails: ''
      },
      rules: {
        auditResult: [
          { required: true, message: '请选择复核结果', trigger: 'change' }
        ],
        auditDetails: [
          { 
            required: false,
            validator: (rule, value, callback) => {
              if (this.formData.auditResult === '7' && !value) {
                callback(new Error('当复核结果为"其他"时,备注为必填项'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  methods: {
    close() {
      this.closeDialog()
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.getParam('callBack')(this.formData, this.layerid)
        }
      })
    }
  },
  watch: {
    'formData.auditResult'(val) {
      this.$refs.form.validateField('auditDetails')
    }
  }
}
</script>

<style lang="scss" scoped>
.recheck-layer {
  width: 100%;
  padding: 30px;
  
  .el-radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    
    .el-radio {
      width: calc((100% - 30px) / 3); // 每行3个，减去两个间隔的15px
      margin-right: 0;
      
      &:nth-last-child(1),
      &:nth-last-child(2) {
        // 最后两个元素
        width: calc((100% - 15px) / 2); // 最后一行2个，减去中间间隔的15px
      }
    }
  }

  .bottom-btn {
    margin-top: 30px;
    
    .el-button {
      min-width: 100px;
    }
  }
}
</style>
