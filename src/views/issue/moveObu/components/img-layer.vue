<template>
  <div class="img-preview-layer">
    <div v-if="imageList && imageList.length > 0" class="img-grid">
      <div 
        v-for="(img, index) in imageList" 
        :key="index"
        class="img-item"
      >
        <div class="img-title">{{ img.label }}</div>
        <div class="img-container">
          <el-image
            :src="img.url"
            fit="contain"
            :preview-src-list="imageUrls"
            :initial-index="index"
            @load="handleImageLoad"
            @error="handleImageError"
          >
            <div slot="error" class="image-error">
              <i class="el-icon-picture-outline"></i>
              <span>加载失败</span>
            </div>
            <div slot="placeholder" class="image-loading">
              <i class="el-icon-loading"></i>
              <span>加载中...</span>
            </div>
          </el-image>
        </div>
      </div>
    </div>
    <div v-else class="no-data-message">
      暂无图片数据
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'

const imgName = {
  3: '行驶证图片',
  6: '车辆图片',
  12: '行驶证车辆照片',
  15: '车道通行图片',
  16: '车身照片'
};

export default {
  name: 'ImgLayer',
  mixins: [layerMix],
  data() {
    return {
      imageList: []
    }
  },
  computed: {
    // 获取所有图片URL用于预览
    imageUrls() {
      return this.imageList.map(img => img.url)
    }
  },
  methods: {
    // 图片加载处理
    handleImageLoad() {
      // 图片加载成功的处理逻辑
    },
    handleImageError() {
      this.$message.error('图片加载失败')
    },
    close() {
      this.closeDialog()
    },
    // 初始化数据
    async init() {
      const images = this.getParam('images');
      
      let processedImages = [];

      if (images && Array.isArray(images)) {
        processedImages = images.map(img => {
          const code = img.code;  
          const label = imgName[code] || '未知图片'; 
          return {
            label: label,
            url: img.url,
            code: code 
          };
        }).filter(img => img.url); 
      } 
      
      this.imageList = processedImages;
    }
  },
  created() {
    this.init()
  }
}
</script>

<style lang="scss" scoped>
.img-preview-layer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  
  .img-grid {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    overflow-y: auto;
    padding-bottom: 20px;
    
    .img-item {
      width: calc((100% - 40px) / 3); // 3列布局，减去两个间隔的20px
      display: flex;
      flex-direction: column;
      
      .img-title {
        text-align: center;
        font-size: 14px;
        color: #333;
        margin-bottom: 10px;
        font-weight: bold;
      }
      
      .img-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f7fa;
        border-radius: 4px;
        overflow: hidden;
        min-height: 200px;
        cursor: pointer;
        
        .el-image {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }
  }

  .image-error,
  .image-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #909399;
    font-size: 14px;
    
    i {
      font-size: 24px;
      margin-bottom: 8px;
    }
  }
  
  .no-data-message {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #909399;
  }
}
</style>
