<template>
  <div class="tip-layer">
    <div class="title">
      请选择所需提醒的工作人员
    </div>
    <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
      <!-- 部门选择 -->
      <el-form-item label="部门" prop="deptId">
        <el-cascader
          v-model="formData.deptId"
          :options="deptOptions"
          :props="{
            value: 'id',
            label: 'name',
            children: 'children',
            expandTrigger: 'click',
            checkStrictly: true,
            emitPath: false
          }"
          placeholder="请选择部门"
          clearable
        />
      </el-form-item>
      
      <!-- 操作人员 -->
      <el-form-item label="人员" prop="recipientId">
        <el-input v-model="formData.operatorName" placeholder="请选择人员" disabled>
          <el-button
            slot="append"
            :disabled="!formData.deptId"
            @click="chooseOperator"
            icon="el-icon-search"
          ></el-button>
        </el-input>
      </el-form-item>
    </el-form>

    <!-- 底部按钮 -->
    <div class="bottom-btn g-flex g-flex-center">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'
import upmsRequest from '@/utils/upmsRequest'

export default {
  name: 'TipLayer',
  mixins: [layerMix],
  data() {
    return {
      formData: {
        deptId: 10,
        recipientId: '',
        operatorName: ''
      },
      deptOptions: [], // 部门选项
      rules: {
        deptId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        recipientId: [
          { required: true, message: '请选择人员', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getDeptOptions()
  },
  methods: {
    // 获取部门选项
    getDeptOptions() {
      upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      }).then(res => {
        // 处理部门树，设置可选状态
        const processDeptTree = (deptList, isUnderOperation = false) => {
          if (!deptList || !deptList.length) return []
          
          return deptList.map(dept => {
            // 复制部门对象，添加disabled属性
            const newDept = { ...dept }
            
            // 检查是否是运营部(id: 10)或在运营部子树下
            const isOperationDept = newDept.id === 10
            const currentIsUnderOperation = isOperationDept || isUnderOperation
            
            // 设置disabled属性
            newDept.disabled = !currentIsUnderOperation
            
            // 处理子部门
            if (newDept.children && newDept.children.length) {
              // 如果是运营部或已经在运营部下，传递标记，使所有子孙部门可选
              newDept.children = processDeptTree(newDept.children, currentIsUnderOperation)
            }
            
            return newDept
          })
        }
        
        this.deptOptions = processDeptTree(res.data)
      })
    },

    // 选择操作人员
    chooseOperator() {
      this.$openPage(
        '@/views/equipment/cardRechargeRecord/components/operatorList',
        '选择操作人',
        {
          query: {
            deptId: this.formData.deptId
          },
          callBack: res => {
            this.formData.recipientId = res.etcUserId
            this.formData.operatorName = res.realName            
          }
        },
        {
          area: ['60%', '600px']
        }
      )
    },

    close() {
      this.closeDialog()
    },

    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.getParam('callBack')(this.formData, this.layerid)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tip-layer {
  width: 100%;
  padding: 20px;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
  }
  .el-cascader {
    width: 100%;
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    background-color: #fff;
    color: #606266;
  }

  .bottom-btn {
    margin-top: 30px;
    
    .el-button {
      min-width: 100px;
      margin: 0 10px;
    }
  }
}
</style>
