<template>
  <div class="form-layer">
    <el-form ref="form" :model="formData" label-width="150px" :rules="rules">
      <el-form-item label="稽核任务名称" prop="taskName">
        <el-input v-model="formData.taskName"></el-input>
      </el-form-item>
      <el-form-item label="特情发生时间" prop="aduitTime">
        <el-date-picker v-model="formData.aduitTime" type="datetimerange" range-separator="-"
          :start-placeholder="'开始时间'" :end-placeholder="'结束时间'" value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSubmit" :loading="loading">提交</el-button>
        <el-button @click="close">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import layerMix from '@/utils/layerMixins'

export default {
  components: {
  },
  mixins: [layerMix],
  data () {
    return {
      activeTab: 'timeRange',
      formData: {},
      loading: false,
      rules: {
        taskName: [
          { required: true, message: '稽核任务名称不能为空', trigger: 'blur' }
        ],
        aduitTime: [
          { required: true, message: '特情发生时间不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async handleSubmit () {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let params = {
            taskName: this.formData.taskName,
            startTime: this.formData.aduitTime[0],
            endTime: this.formData.aduitTime[1]
          }
          this.loading = true;
          try {
            await this.getParam('callBack')(params, this.layerid)
          } catch (error) {
            console.error("Submission failed:", error);
          } finally {
            this.loading = false;
          }
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    close () {
      this.closeDialog()
    },
    handleUploadSuccess () {
      this.closeDialog()
    }
  }
}
</script>

<style lang="scss" scoped>
.form-layer {
  width: 100%;
  height: 100%;
  padding: 20px;
  ::v-deep .el-range-editor {
    width: 100%;
  }

  // tab样式调整
  ::v-deep .el-tabs__nav {
    width: 100%;
    display: flex;

    .el-tabs__item {
      flex: 1;
      text-align: center;
      height: 40px;
      line-height: 40px;
      padding: 0;
      font-size: 14px;
      border: 1px solid #DCDFE6;
      margin-right: -1px;

      &.is-active {
        background-color: #fff;
      }

      &:first-child {
        border-radius: 4px 0 0 0;
      }

      &:last-child {
        border-radius: 0 4px 0 0;
      }
    }
  }

  ::v-deep .el-tabs__header {
    margin-bottom: 20px;
  }

  ::v-deep .el-tabs__content {
    padding: 0 20px 20px 20px;
  }
}
</style>
