<template>
  <div class="traffic-table">
    <!-- 功能按钮区 -->
    <div class="table">
      <div class="top-btn">
        <!-- 通行信息 -->
      </div>
      <!-- 数据表格 -->
      <my-table ref="tableRef" v-loading="loading" tableHeight="400" :cloumns="listColumns" :tableData="tableData"
        :total="total" :pageSize="pageSize" :pageNum="pageNum" :hasPagination="true" @changeTableData="changeTableData">
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="text" @click="handleOperate(scope)">查看图片</el-button>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { selectTransactionList, obuPassImage } from '@/api/equipment'
import layerMix from '@/utils/layerMixins'

export default {
  name: 'MoveObuCorrect',
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin, layerMix],
  data() {
    return {
      tableData: [],
      api: selectTransactionList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      dataKey: 'data',
      pageSize: 5,
      carInfo: {}
    }
  },
  computed: {
    listColumns() {
      return [
        {
          prop: 'obuCarNo',
          label: 'OBU车牌',
          width: 120
        },
        {
          prop: 'carNo',
          label: '识别车牌',
          width: 120
        },
        {
          prop: 'tollLaneStr',
          label: '通行车道',
          width: 300
        },
        {
          prop: 'exTime',
          label: '出口时间',
          width: 180
        },
        {
          prop: 'passId',
          label: '通行标识编号',
          width: 200
        },
        {
          prop: 'action',
          label: '操作',
          slot: true,
          fixed: 'right',
          width: 140
        }
      ]
    },
  },
  methods: {
    async handleOperate(row) {
      let params = {
        passId: row.passId
      }
      let passImgRes = await obuPassImage(params)
      let images = []
      console.log(passImgRes, 'passImgRes')
      if (passImgRes.type == 'application/json') {
        images = []
      } else {
        let passImgUrl = window.URL.createObjectURL(passImgRes)
        let obj = {
          url: passImgUrl,
          code: 15,
          name: '车道通行图片'
        }
        images.push(obj)
      }

      let area = images.length > 0 ? ['1000px', '600px'] : ['500px', '300px'];
      this.$openPage(
        '@/views/issue/moveObu/components/img-layer',
        '通行图片',
        {
          images: images
        },
        {
          area: area
        }
      )
    },
    // 获取列表
    getTableData(query, callBack) {
      if (!this.api) {
        this.$message.error('没有写入列表请求方法')
        return
      }
      this.loading = true
      let params = {
        ...this.pageQuery,
        ...this.pageParams,
      }
      if (query) {
        params = Object.assign(params, query);
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      this.api(params).then(res => {
        if (res.code === 200) {
          this.loading = false
          this.tableData = res.data.map((item, idx) => {
            let obj = {
              ...item,
              // obuCarNo: this.carInfo.obuCarNo,
              carNo: this.carInfo.carNo
            }
            if (idx == 0) {
              obj.tollLaneStr = this.carInfo.tollLaneStr
            }
            return obj
          })
        }
      }).catch(() => {
        this.loading = false
      })
    },
  },
  created() {
    let query = this.getParam('formData')
    this.carInfo = query
    let params = {
      obuCarNo: query.obuCarNo,
      // obuCarColor: query.obuCarColor
    }
    this.pageParams = params
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.traffic-table {
  width: 100%;
  height: 100%;

  .top-btn {
    margin-bottom: 10px;
  }
}
</style>
