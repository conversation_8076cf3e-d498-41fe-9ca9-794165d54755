<template>
  <div class="move-obu-result">
    <!-- 搜索表单 -->
    <SearchForm ref="SearchForm" :formConfig="formConfig" collapse @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle" />

    <!-- 功能按钮区 -->
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="exportExcel">导出Excel</el-button>
        <el-button type="primary" size="mini" @click="exportReviewExcel">导出待复核数据</el-button>
        <el-button type="primary" size="mini" @click="importExcel">导入复核结果</el-button>

        <el-button type="primary" size="mini" @click="sendToCompany">发送至运营公司</el-button>
      </div>

      <!-- 数据表格 -->
      <my-table ref="tableRef" v-loading="loading" :cloumns="listColumns" :tableData="tableData" :total="total"
        :pageSize="pageSize" :pageNum="pageNum" :hasPagination="true" @changeTableData="changeTableData"
        @selectChange="selectChange">
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="text" @click="reCheck(scope)" :disabled="Boolean(scope.auditResult)">复核</el-button>
          <el-button size="mini" type="text" @click="handleOperate(scope, 'traffic')">通行信息</el-button>
          <el-button size="mini" type="text" @click="handleOperate(scope, 'image')">发行图片</el-button>
          <el-button size="mini" type="text" @click="singleExport(scope)">导出</el-button>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { resultListColumns, resultListForm } from './model'
import { taskInfoList, taskInfoExport, taskInfoMail, taskInfoCheck, obuVehicleImage } from '@/api/equipment'

export default {
  name: 'MoveObuResult',
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: taskInfoList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      timeField: ['auditTimeRange', 'firstDetectTimeRange', 'lastDetectTimeRange'],
      dataKey: 'data'
    }
  },
  computed: {
    listColumns() {
      return resultListColumns(this)
    },
    formConfig() {
      return resultListForm(this)
    }
  },
  methods: {
    // 导出Excel
    exportExcel() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      this.timeField.forEach(item => {
        delete query[item]
      })
      let params = {
        ...query,
        taskId: this.pageParams.taskId
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      let fileObj = {
        fileName: '稽核明细结果导出.xlsx'
      }
      this.exportFile(params, taskInfoExport, fileObj)
    },
    // 导出待复核数据
    exportReviewExcel() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      this.timeField.forEach(item => {
        delete query[item]
      })
      let params = {
        ...query,
        auditResultSign: '1',
        taskId: this.pageParams.taskId
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      let fileObj = {
        fileName: '待复核数据导出.xlsx'
      }
      this.exportFile(params, taskInfoExport, fileObj)
    },
    // 导入复核结果
    importExcel() {
      this.$openPage(
        '@/views/issue/moveObu/components/import-result-layer',
        '移动OBU复核结果批量导入',
        {
          downloadUrl: '/obuAudit/batchInfoCheck',
          callBack: (res, lid) => {
            this.getTableData()
          }
        },
        {
          area: ['41%', '380px']
        }
      )
    },
    // 发送至公司
    sendToCompany() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let query = {
        ids: this.selectArr.map(item => item.idStr)
      }
      this.$openPage(
        '@/views/issue/moveObu/components/send-layyer',
        '发送至运营公司',
        {
          formData: query,
          callBack: (res, lid) => {
            console.log(res, lid);
            let params = {
              ...query,
              ...res
            }
            this.sendToCompanyApi(params, lid)
          }
        },
        {
          area: ['30%', '330px']
        }
      )
    },

    // 发送至公司
    async sendToCompanyApi(params, lid) {
      let res = await taskInfoMail(params)
      if (res.code == 200) {
        this.$message.success('发送成功')
        this.$layer.close(lid)
        this.getTableData()
      } else {
        this.$message.error('发送失败')
      }
    },

    // 复核
    reCheck(row) {
      console.log(row);
      this.$openPage(
        '@/views/issue/moveObu/components/recheck-layer',
        '移动OBU复核结果',
        {
          rowData: row,
          callBack: (res, lid) => {
            let params = {
              id: row.idStr,
              auditResult: res.auditResult,
              auditDetails: res.auditDetails
            }
            this.taskInfoCheckApi(params, lid)
          }
        },
        {
          area: ['50%', '380px']
        },
      )
    },
    async taskInfoCheckApi(params, lid) {
      let res = await taskInfoCheck(params)
      if (res.code == 200) {
        this.$message.success('复核成功')
        this.$layer.close(lid)
        this.getTableData()
      } else {
        this.$message.error('复核失败')
      }
      console.log(res);
    },
    // 查看通行信息
    handleOperate(row, type) {
      if (type == 'traffic') {
        this.$openPage(
          '@/views/issue/moveObu/components/traffic-table',
          '通行信息',
          {
            formData: row,
          },
          {
            area: ['60%', '600px']
          },
        )
      } else if (type == 'image') {
        this.viewPassImage(row)
      }
    },
    // 发行图片
    async viewPassImage(row) {
      let params = {
        carNo: row.obuCarNo,
        carColor: row.obuCarColor
      }
      let res = await obuVehicleImage(params)
      if (res.code == 200) {
        let images = res.data.images
        let area = images.length > 0?['1000px', '600px']:['500px', '300px'];
          this.$openPage(
            '@/views/issue/moveObu/components/img-layer',
            '发行图片',
          {
            images:images
          },
          {
            area: area
          }
        )
      }
    },
    // 导出
    singleExport(row) {
      let params = {
        id: row.idStr,
        taskId: row.taskIdStr
      }
      let fileObj = {
        fileName: '稽核明细结果导出.xlsx'
      }
      this.exportFile(params, taskInfoExport, fileObj)
    }
  },
  created() {
    let { type, taskId } = this.$route.query
    if (type == 'list') {
      this.pageParams.taskId = taskId
    }
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.move-obu-result {
  width: 100%;
  height: 100%;

  .top-btn {
    margin-bottom: 10px;
  }
}
</style>
