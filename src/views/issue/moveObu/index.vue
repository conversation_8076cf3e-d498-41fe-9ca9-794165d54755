<template>
  <div class="audit-page">
    <div class="tab-item">
      <el-tabs @tab-click="handleClick" v-model="selectItem" type="border-card">
        <el-tab-pane
          :label="item.label"
          v-for="(item, index) in businessTypeOptions"
          :key="index"
          :name="item.value"
        >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div>
      <Task v-if="selectItem == '1'"></Task>
      <Result v-if="selectItem == '2'"></Result>
      <Correct v-if="selectItem == '3'"></Correct>
    </div>
  </div>
</template>

<script>
import Task from './task.vue'
import Result from './result.vue'
import Correct from './correct.vue'

export default {
  components: {
    Task,
    Result,
    Correct
  },
  created() {
  },
  mounted() {},
  data() {
    return {
      businessTypeOptions: [
        { label: '移动OBU稽核', value: '1' },
        { label: '稽核结果', value: '2' },
        { label: '整改明细', value: '3' },
      ],
      selectItem: '1'
    }
  },
  methods: {
    handleClick(val) {
      this.selectItem = this.businessTypeOptions[val.index].value
    },
  }
}
</script>

<style lang="scss" scoped>
.audit-page {
  height: 100%;
  position: relative;
  margin: 15px 20px;
  flex-flow: column;
  display: flex;
  .tab-item {
    ::v-deep .el-tabs--border-card {
      box-shadow: none;
    }
    ::v-deep .el-tabs__content {
      padding: 0;
    }
    ::v-deep .el-tabs--border-card {
      border-bottom: none;
    }
  }
  .table {
    width: 100%;
    flex: 1;
    background-color: #ffffff;
    text-align: center;
    margin: 0;
  }
  ::v-deep.el-table th,
  ::v-deep.el-table td {
    text-align: center;
  }
  .itembox {
    padding: 0 25px;
    line-height: 50px;
    .item {
      margin: auto;
      font-size: 14px;
      span {
        display: inline-block;
        padding-right: 10px;
        font-weight: 600;
        min-width: 75px;
      }
    }
    .nav {
      width: 100%;
      border: 1px solid rgb(202, 202, 202);
      padding: 10px;
      line-height: 14px;
      height: 160px;
      overflow-y: scroll;
    }
    ::-webkit-scrollbar {
      display: none;
    }
  }
  .foot {
    margin-top: 20px;
    text-align: center;
  }
  .pagination {
    padding: 10px 0;
    background-color: #fff;
    text-align: center;
  }
  ::v-deep .el-dialog {
    min-width: 1150px;
  }
}
</style>
