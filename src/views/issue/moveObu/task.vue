<template>
  <div class="toll-record">
    <SearchForm ref="SearchForm" :formConfig="formConfig" @onSearchHandle="onSearchHandle" :btnSpan="16"
      @onReSetHandle="onReSetHandle">
      <template slot="input" slot-scope="{ scope }">
        <el-switch v-model="selfOperate" size="small" @change="handleSelfOperateChange(scope)"></el-switch>
      </template>
    </SearchForm>
    <div class="table">
      <div class="top-btn">
        <el-button type="primary" size="mini" @click="createTask">创建任务</el-button>
        <el-button type="primary" size="mini" @click="obuAuditNotify">生成工单</el-button>
      </div>
      <my-table ref="tableRef" v-loading="loading" :cloumns="listColumns" :tableData="tableData" :total="total"
        :pageSize="pageSize" :pageNum="pageNum" :hasPagination="true" @changeTableData="changeTableData"
        @selectChange="selectChange">
        <template slot="selection">
          <el-table-column type="selection" align="center" width="55" />
        </template>
        <!-- 操作 -->
        <!-- || !checkAuth([])  :disabled="!checkAuth([])" -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="text" @click="handleDetail(scope)">查看稽核结果</el-button>
          <el-button size="mini" type="text" @click="handleCorrect(scope)">查看整改明细</el-button>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { listColumns, listForm } from './model'
import {
  obuTaskList,
  obuCreateTask,
  obuAuditNotify,
} from '@/api/equipment'
import { mapGetters } from 'vuex'

export default {
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: obuTaskList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      dataKey: 'data',
      timeField: ['taskTimeRange'],
      selfOperate: false,
    }
  },
  computed: {
    ...mapGetters(['realName']),
    listColumns() {
      return listColumns(this)
    },
    formConfig() {
      return listForm(this)
    }
  },
  methods: {
    // 创建稽核任务
    createTask() {
      this.$openPage(
        '@/views/issue/moveObu/components/obu-task-layer',
        '创建稽核任务',
        {
          callBack: (res, lid) => {
            return this.createTaskApi(res, lid)
          }
        },
        {
          area: ['32%', '350px']
        }
      )
    },
    // 创建稽核任务
    createTaskApi(params, lid) {
      return obuCreateTask(params).then(res => {
        if (res.code == 200) {
          this.$message.success('创建成功')
          this.$layer.close(lid)
          this.getTableData()
        } else {
          return Promise.reject(res)
        }
        return res
      })
    },


    importFile() {
      this.$openPage(
        '@/views/issue/releaseAudit/components/import-layer',
        '专项稽核批量导入',
        {
          callBack: (res, lid) => {
            // this.addSubmit(res, lid)
          }
        },
        {
          area: ['41%', '420px']
        }
      )
    },
    handleSelfOperateChange(row) {
      console.log(row, 'row')
      if (this.selfOperate) {
        this.$set(row, 'createBy', this.realName)
      } else {
        this.$set(row, 'createBy', '')
      }
      this.$refs.SearchForm.onSearchHandle()
    },
    handleDetail(scope) {
      console.log(scope.idStr, 'scope');
      
      this.$router.push({
        path: '/issue/obuResult',
        query: {
          type: 'list',
          taskId: scope.idStr
        }
      })
    },
    handleCorrect(scope) {
      console.log(scope.idStr, 'scope');

      this.$router.push({
        path: '/issue/obuCorrect',
        query: {
          type: 'correct',
          taskId: scope.idStr
        }
      })
    },
    obuAuditNotify() {
      if (this.selectArr.length <= 0) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      let taskNames = this.selectArr.map(item => item.taskName)

      this.$openPage(
        '@/views/issue/moveObu/components/workOrder-layer',
        '生成工单',
        {
          callBack: (res, lid) => {
            console.log(res, lid);
            let params = {
              taskNames,
              recipientId: res.recipientId,
            }
            this.obuAuditNotifyHandle(params, lid)
          }
        },
        {
          area: ['30%', '330px']
        }
      )
    },
    // 生成工单
    async obuAuditNotifyHandle(params, lid) {
      let res = await obuAuditNotify(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
  },
  created() {
    this.getTableData()
  }
}
</script>


<style lang="scss" scoped>
.toll-record {
  width: 100%;
  height: 100%;

  .top-btn {
    margin-bottom: 10px;
  }
  
  .choose-footer {
    text-align: center;
  }
}
</style>