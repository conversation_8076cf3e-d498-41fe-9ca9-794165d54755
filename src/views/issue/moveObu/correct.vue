<template>
  <div class="move-obu-correct">
    <!-- 搜索表单 -->
    <SearchForm ref="SearchForm" :formConfig="formConfig" collapse @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle" />

    <!-- 功能按钮区 -->
    <div class="table">
      <div class="top-btn">
        <el-button size="mini" type="primary" @click="exportExcel">导出Excel</el-button>
        <el-button type="primary" size="mini" @click="exportSpecialAudit">导出待整改明细</el-button>
        <el-button type="primary" size="mini" @click="importCorrectResult">导入整改结果</el-button>
      </div>
      <!-- 数据表格 -->
      <my-table ref="tableRef" v-loading="loading" :cloumns="listColumns" :tableData="tableData" :total="total"
        :pageSize="pageSize" :pageNum="pageNum" :hasPagination="true" @changeTableData="changeTableData"
        @selectChange="selectChange">
        <!-- 操作列 -->
        <template slot="action" slot-scope="{ scope }">
          <el-button size="mini" type="text" @click="handleCorrect(scope)">修改整改结果</el-button>
          <el-button size="mini" type="text" @click="handleOperate(scope)">发行图片</el-button>
        </template>
      </my-table>
    </div>
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import tableListMixin from '@/components/my-table/hook/tableMix'
import { correctListColumns, correctListForm  } from './model'
import { correctionInfoList, correctionInfoExport, obuVehicleImage, correctionInfoUpdate } from '@/api/equipment'
import upmsRequest from '@/utils/upmsRequest'

export default {
  name: 'MoveObuCorrect',
  components: {
    MyTable,
    SearchForm
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: correctionInfoList,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      timeField: ['auditTimeRange'],
      dataKey: 'data',
      deptOptions: []
    }
  },
  computed: {
    listColumns() {
      return correctListColumns(this)
    },
    formConfig() {
      return correctListForm(this)
    }
  },
  methods: {
    // 导出Excel
    exportExcel() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      this.timeField.forEach(item => {
        delete query[item]
      })
      let params = {
        ...query,
        taskId: this.pageParams.taskId
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      let fileObj = {
        fileName: '稽核整改数据.xlsx'
      }
      this.exportFile(params, correctionInfoExport, fileObj)
    },

    // 导出整改明细
    exportSpecialAudit() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      this.timeField.forEach(item => {
        delete query[item]
      })
      let params = {
        // ...query, // 只传correctionResultSign
        correctionResultSign: '1',
        taskId: this.pageParams.taskId
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      let fileObj = {
        fileName: '稽核整改明细数据.xlsx'
      }
      this.exportFile(params, correctionInfoExport, fileObj)
    },

    // 导入整改结果
    importCorrectResult() {
      this.$openPage(
        '@/views/issue/moveObu/components/import-result-layer',
        '整改结果导入',
        {
          downloadUrl: '/obuAudit/correctionBulkUpdate',
          callBack: (res, lid) => {
            console.log(res, lid);
            this.getTableData()
          }
        },
        {
          area: ['41%', '380px']
        }
      )
    },

    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      }).then(res => {
        console.log('发行网点', res)
        this.deptOptions = res.data
      })
    },
    // 修改整改结果
    handleCorrect(row) {
      this.$openPage(
        '@/views/issue/moveObu/components/reform-layer',
        '修改整改结果',
        {
          rowData: row,
          callBack: (res, lid) => {
            let params = {
              id: row.id,
              correctionResult: res.correctionResult
            }
            this.correctionInfoUpdateApi(params, lid)
          }
        },
        {
          area: ['35%', '240px']
        },
      )
    },
    async correctionInfoUpdateApi(params, lid) {
      let res = await correctionInfoUpdate(params)
      if (res.code == 200) {
        this.$message.success('修改成功')
        this.$layer.close(lid)
        this.getTableData()
      }
    },
    // 发行图片
    async handleOperate(row) {
      let params = {
        carNo: row.obuCarNo,
        carColor: row.obuCarColor
      }
      let res = await obuVehicleImage(params)
      if (res.code == 200) {
        let images = res.data.images
        let area = images.length > 0 ? ['1000px', '600px'] : ['500px', '300px'];
        this.$openPage(
          '@/views/issue/moveObu/components/img-layer',
          '发行图片',
          {
            images: images
          },
          {
            area: area
          }
        )
      }
    }
  },
  created() {
    this.getgroup()
    let { type, taskId } = this.$route.query
    if (type == 'correct') {
      this.pageParams.taskId = taskId
    }
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.move-obu-correct {
  width: 100%;
  height: 100%;

  .top-btn {
    margin-bottom: 10px;
  }
}
</style>
