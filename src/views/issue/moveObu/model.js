import {
  licenseColorOption
} from '@/common/const/optionsData.js'
import { getVehicleColor } from '@/common/method/formatOptions'

// OBU审核结果枚举
export const ObuAuditResultEnum = [
  { label: '全部', value: '' },
  { label: '正常', value: '0' },
  { label: '换牌不换车', value: '1' },
  { label: '换牌换车(车型不变)', value: '2' },
  { label: '换牌换车(车型变)', value: '3' },
  { label: '客货类型发行错误', value: '4' },
  { label: '使用移动OBU', value: '5' },
  { label: '复核时已注销', value: '6' },
  { label: '其他', value: '7' }
]

// OBU整改结果枚举
// 0: 已注销, 1: 未整改已拉黑, 2: 已规范激活, 3: 已差错处理
export const ObuCorrectionResultEnum = [
  { label: '全部', value: '' },
  { label: '已注销', value: '0' },
  { label: '未整改已拉黑', value: '1' },
  { label: '已规范激活', value: '2' },
  { label: '已差错处理', value: '3' },
  { label: '未录入整改结果', value: '9' }
]

//稽核任务表格
export const listColumns = (_this) => {
  return [
    {
      prop: 'idStr',
      label: '任务ID',
      width: 180
    },
    {
      prop: 'taskName',
      label: '任务名称',
      width: 180
    },
    {
      prop: 'auditTimeRange',
      label: '稽核时间区间',
      width: 320,
      formatter: (val, row) => {
        return row.startTime && row.endTime ? `${row.startTime} - ${row.endTime}` : '-'
      }
    },
    {
      prop: 'createdTime',
      label: '任务创建时间',
      width: 180
    },
    {
      prop: 'taskStatus',
      label: '任务状态',
      width: 140
    },
    {
      prop: 'createdBy',
      label: '操作人',
      width: 150
    },
    {
      prop: 'action',
      label: '操作',
      slot: true,
      fixed: 'right',
      width: 300
    }
  ]
}

//搜索表单
export const listForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'taskName',
      label: '任务名称',
      default: ''
    },
    {
      type: 'dateRange',
      field: 'taskTimeRange',
      keys: ['createTimeStart', 'createTimeEnd'],
      label: '任务创建时间',
      default: []
    },
    {
      type: 'select',
      field: 'taskStatus',
      label: '任务状态',
      default: '',
      options: [
        { label: '全部', value: '' },
        { label: '执行中', value: '1' },
        { label: '已完成', value: '2' }
      ]
    },
    {
      type: 'input',
      field: 'createBy',
      label: '任务创建人',
      default: ''
    }
  ]
}

// 表格列配置
export const resultListColumns = (_this) => {
  return [
    { prop: 'obuCarNo', label: 'OBU车牌', width: '120' },
    { prop: 'carNo', label: '车道识别车牌', width: '120' },
    { prop: 'obuCarColor', label: 'OBU车牌颜色', width: '120' },
    { prop: 'cardIssuer', label: '所属发行方', width: '120' },
    { prop: 'outOfTimeStart', label: '首次特情时间', width: '160' },
    { prop: 'outOfTimeEnd', label: '末次特情时间', width: '160' },
    { prop: 'tqCount', label: '稽核特情次数', width: '120' },
    { prop: 'taskName', label: '所属稽核任务', width: '120' },
    { prop: 'auditResult', label: '复核结果', width: '160' },
    { prop: 'auditDetails', label: '复核备注', width: '120' },
    { prop: 'auditTime', label: '复核时间', width: '160' },
    { prop: 'auditBy', label: '复核人', width: '100' },
    { prop: 'action', label: '操作', slot: true, fixed: 'right', width: '300' }
  ]
}

// 搜索表单配置
export const resultListForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'obuCarNo',
      label: 'OBU车牌'
    },
    {
      type: 'input',
      field: 'carNo',
      label: '车道识别车牌'
    },
    {
      type: 'select',
      field: 'obuCarColor',
      label: 'OBU车牌颜色',
      options: licenseColorOption
    },
    {
      type: 'input',
      field: 'cardIssuer',
      isCollapse: true,
      label: '所属发行方'
    },
    {
      type: 'dateRange',
      field: 'firstDetectTimeRange',
      isCollapse: true,
      keys: ['outOfTimeStartS', 'outOfTimeStartE'],
      label: '首次特情时间',
      default: []
    },
    {
      type: 'dateRange',
      field: 'lastDetectTimeRange',
      isCollapse: true,
      keys: ['outOfTimeEndS', 'outOfTimeEndE'],
      label: '末次特情时间',
      default: []
    },
    {
      type: 'input',
      field: 'taskName',
      isCollapse: true,
      label: '所属稽核任务'
    },
    {
      type: 'select',
      field: 'auditResult',
      label: '复核结果',
      default: '',
      isCollapse: true,
      options: ObuAuditResultEnum
    },
    {
      type: 'select',
      field: 'auditStatus',
      label: '复核状态',
      default: '',
      isCollapse: true,
      options: [
        { label: '全部', value: '' },
        { label: '待复核', value: '0' },
        { label: '已复核', value: '1' },
        { label: '已整改', value: '2' },
        { label: '稽核完成', value: '3' }
      ]
    },
    {
      type: 'dateRange',
      field: 'auditTimeRange',
      isCollapse: true,
      keys: ['auditTimeS', 'auditTimeE'],
      label: '复核时间',
      default: []
    },

  ]
}

// correct页面表格列配置
export const correctListColumns = (_this) => {
  return [
    { prop: 'obuCarNo', label: '车牌号', width: '120' },
    { prop: 'obuCarColor', label: '车牌颜色', width: '120' },
    { prop: 'cardNo', label: 'ETC卡号', width: '150' },
    { prop: 'cardStatus', label: 'ETC卡状态', width: '120' },
    { prop: 'obuNo', label: 'OBU号', width: '150' },
    { prop: 'obuStatus', label: 'OBU状态', width: '120' },
    { prop: 'carType', label: '车型', width: '120' },
    { prop: 'isTrunk', label: '客货标识', width: '120' },
    { prop: 'carSeat', label: '座位数', width: '150' },
    { prop: 'carDimension', label: '长x宽x高', width: '150' },
    { prop: 'cardType', label: '产品类型', width: '120' },
    { prop: 'vehicleType', label: '行驶证车辆类型', width: '150' },
    { prop: 'branchCode', label: '发行网点', width: '180' },
    { prop: 'releasePerson2', label: '发行人', width: '120' },
    { prop: 'releaseDate2', label: '发行时间', width: '150' },
    { prop: 'auditResult', label: '复核结果', width: '160' },
    { prop: 'auditDetails', label: '复核备注', width: '200' },
    { prop: 'auditTime', label: '复核时间', width: '160' },
    // { prop: 'issueAgencyCode', label: '所属稽核任务', width: '120' },
    { prop: 'correctionResult', label: '整改结果', width: '160' },
    { prop: 'correctionTime', label: '整改时间', width: '160' },
    { prop: 'action', label: '操作', slot: true, fixed: 'right', width: '180' }
  ]
}

// correct页面搜索表单配置
export const correctListForm = (_this) => {
  return [
    { type: 'input', label: '车牌号', field: 'obuCarNo' },
    { type: 'select', label: '车牌颜色', field: 'obuCarColor', options: licenseColorOption },
    { 
      type: 'select', 
      label: '复核结果', 
      field: 'auditResult', 
      default: '',
      options: ObuAuditResultEnum
    },
    { 
      type: 'select', 
      label: '整改结果', 
      field: 'correctionResult', 
      default: '',
      options: ObuCorrectionResultEnum, 
      isCollapse: true, 
    },
    {
      type: 'cascader',
      field: 'branchCode',
      label: '发行网点',
      isCollapse: true,
      default: '',
      options: _this.deptOptions,
      props: {
        props:{
          value: 'id',
          label: 'name',
          children: 'children',
          expandTrigger: 'click',
          checkStrictly: true,
          emitPath:false
        }
      }
    },
    { type: 'input', label: 'ETC卡号', field: 'cardNo' ,isCollapse: true,},
    { type: 'input', label: 'OBU号', field: 'obuNo', isCollapse: true, },
    {
      type: 'dateRange',
      field: 'auditTimeRange',
      isCollapse: true,
      keys: ['auditTimeS', 'auditTimeE'],
      label: '复核时间',
      default: []
    },
    // {
    //   type: 'input',
    //   field: 'issueAgencyCode',
    //   isCollapse: true,
    //   label: '所属稽核任务'
    // },
  ]
}