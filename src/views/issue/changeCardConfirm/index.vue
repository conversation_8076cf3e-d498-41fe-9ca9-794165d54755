<template>
  <div class="account-list">
    <dart-search
      :formSpan="24"
      :gutter="20"
      class="search"
      ref="searchForm1"
      label-position="right"
      :model="search"
      :fontWidth="2"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="车牌号码：" prop="vehicleCode">
          <el-input
            v-model="search.vehicleCode"
            placeholder=""
            clearable
          ></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色：" prop="vehicleColor">
          <el-select
            v-model="search.vehicleColor"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in licenseColorOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="新卡卡号：" prop="cardNo">
          <el-input
            v-model="search.newCpuCardId"
            placeholder=""
            clearable
            @input="
              if (search.newCpuCardId.length > 20)
                search.newCpuCardId = search.newCpuCardId.slice(0, 20)
            "
            type="number"
          ></el-input>
        </dart-search-item>
        <dart-search-item label="用户ID：" prop="customerId">
          <el-input
            v-model="search.customerId"
            placeholder=""
            clearable
            type="number"
          ></el-input>
        </dart-search-item>
        <dart-search-item :is-button="true" style="margin-left: 35px">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            ><i class="el-icon-search"></i> 搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        v-loading="loading"
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column
          prop="customerId"
          align="center"
          label="用户ID"
          min-width="180"
        />
        <el-table-column
          prop="vehicleColor_str"
          align="center"
          min-width="100"
          label="车牌颜色"
        />
        <el-table-column
          prop="vehicleNo"
          align="center"
          min-width="100"
          label="车牌号"
        />
        <el-table-column
          prop="oldCardNo"
          align="center"
          label="旧卡卡号"
          min-width="200"
        />
        <el-table-column
          prop="newCardNo"
          align="center"
          label="新卡卡号"
          min-width="200"
        />
        <el-table-column
          prop="newCardStatus_str"
          align="center"
          label="新卡状态"
        />
        <el-table-column
          prop="orderId"
          align="center"
          label="订单编号"
          min-width="180"
        />
        <el-table-column
          prop="orderTime"
          align="center"
          min-width="160"
          label="下单时间"
        />
        <el-table-column
          prop="branchName"
          align="center"
          min-width="160"
          label="操作网点"
        />
        <el-table-column
          prop="operatorName"
          align="center"
          min-width="160"
          label="操作人"
        />
        <el-table-column
          align="left"
          header-align="center"
          width="80"
          fixed="right"
          label="操作"
        >
          <template slot-scope="scope">
            <!-- <el-button @click="tochange(scope.row)" type="mini">修改</el-button>
            <el-button
              @click="torefund(scope.row)"
              v-if="scope.row.amout > 0"
              size="mini"
              type="primary"
              >账户退款</el-button
            > -->
            <el-button
              v-if="scope.row.newCardStatus == '0'"
              @click="changeCardConfirm(scope.row)"
              type="mini"
              >确认</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-start">
      <el-pagination
        background
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>
    <!-- <detail
      v-if="showvisible"
      :visible.sync="showvisible"
      :userdata="usernav"
      @getlist="getAccountList()"
    ></detail> -->

    <!-- 售后订单审批处理 -->
    <!-- <dartSlide
      :visible.sync="flowSlideVisiable"
      title="流水记录查询"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <tabs :userNo="userNo" :flowSlideVisiable="flowSlideVisiable"></tabs>
    </dartSlide> -->
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
// import dartSlide from '@/components/dart/Slide/index.vue'
import { licenseColorOption } from '@/common/const/optionsData'
import { _ignoreEmpty } from '@/utils/utils'
export default {
  name: 'changeCardConfirm',
  props: {
    // visible: {
    //   type: Boolean,
    //   default: false,
    // },
    // orderId: {
    //   type: String,
    // },
  },
  components: {
    dartSearch,
    dartSearchItem,
    // dartSlide,
  },
  created() {},
  data() {
    return {
      licenseColorOption,
      loading: false,
      // showvisible: false,
      center: 'center',
      search: {
        vehicleColor: '',
        vehicleCode: '',
        newCpuCardId: '',
        customerId: '',
        pageNum: 1,
        pageSize: 20,
      },
      total: 0,
      // userNoList: [],
      tableData: [],
      // usernav: {},
      // useroption: [
      //   { value: '1', label: '个人' },
      //   { value: '2', label: '单位' },
      // ],
      // flowSlideVisiable: false,
      // userNo: '',
    }
  },
  methods: {
    _ignoreEmpty,
    getCardConfirmList() {
      if (this.validata()) {
        let params = { ...this.search }
        // console.log('刷新了吗', this.orderId)
        for (const key in params) {
          if (params[key] == '') {
            delete params[key]
          }
        }
        params = this._ignoreEmpty(params)
        console.log('params', params)
        this.loading = true
        this.$request({
          url: this.$interfaces.changeNotCompleteList,
          method: 'post',
          data: params,
        })
          .then((res) => {
            console.log('列表信息', res.data.records)
            this.loading = false
            this.tableData = res.data.records
            this.total = res.data.total
            this.search.pageNum = res.data.current
            this.search.pageSize = res.data.size
          })
          .catch((error) => {
            this.loading = false
            console.log('err', error)
          })
      }
    },
    validata() {
      if (
        !this.search.vehicleCode &&
        !this.search.customerId &&
        !this.search.newCpuCardId
      ) {
        this.$message({
          type: 'warning',
          message: '用户ID、车牌、新卡卡号至少需要填写一个再查询',
        })
        return false
      }
      return true
    },
    confirm(row) {
      this.loading = true
      let params = {
        customer_id: row.customerId,
        vehicle_code: row.vehicleNo,
        vehicle_color: row.vehicleColor,
        orig_cpu_card_id: row.oldCardNo, //旧卡卡号
        new_cpu_card_id: row.newCardNo, //新卡卡号
      }
      this.$request({
        url: this.$interfaces.changeCardRepush,
        method: 'post',
        data: params,
      })
        .then((res) => {
          console.log('换卡确认', res)
          this.loading = false
          if (res.code == 200) {
            this.$message({
              type: 'success',
              message: res.msg,
            })
            this.getCardConfirmList()
          }
        })
        .catch((error) => {
          this.loading = false
          console.log('err', error)
        })
    },
    changeCardConfirm(row) {
      this.$confirm('将进行换卡确认操作, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.confirm(row)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作',
          })
        })
    },

    onSearchHandle() {
      this.search.pageNum = 1
      this.getCardConfirmList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    // handleSelectionChange(selection) {
    //   this.userNoList = []
    //   selection.forEach((item) => {
    //     if (!this.userNoList.includes(item.netUserNo)) {
    //       this.userNoList.push(item.netUserNo)
    //     }
    //   })
    // },
    changePage(page) {
      this.search.pageNum = page
      this.getCardConfirmList()
    },
    // tochange(nav) {
    //   this.usernav = nav
    //   this.showvisible = true
    // },
    close() {
      this.$emit('update:visible', false)
    },
    // torefund(item) {
    //   //跳转退费申请页面
    //   this.$router.push({
    //     path: './refund',
    //     query: {
    //       userNo: item.netUserNo,
    //       mobile: item.netMobile,
    //     },
    //   })
    // },
    // checkFlow(value) {
    //   this.userNo = value.netUserNo
    //   this.flowSlideVisiable = true
    // },
    getType(typeObj, value) {
      // console.log('typeObj', typeObj, value)
      for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
          return typeObj[i].label
        }
      }
      return ''
    },
  },
  filters: {
    moneyFilter(val) {
      if (val) {
        return (val / 100).toFixed(2)
      } else {
        return '0.00'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep input::-webkit-outer-spin-button,
::v-deep input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  margin: 10px 0;
}
</style>
