<template>
  <div class="page-container-children-content">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="search"
                 :rules="rules">
      <template slot="search-form">
        <dart-search-item label="任务名称"
                          prop="taskId">
          <el-select v-model="search.taskId"
                     placeholder="请选择">
            <el-option v-for="item in jobList"
                       :key="item.taskId"
                       :label="item.taskName"
                       :value="item.taskId" />
          </el-select>
        </dart-search-item>
        <dart-search-item :is-button="true"
                          :span="16">
          <el-button type="primary"
                     size="mini"
                     native-type="submit"
                     @click="onSearchHandle">搜索</el-button>
          <el-button size="mini"
                     @click="onResultHandle">重置</el-button>
        </dart-search-item>
      </template>
    </dart-search>

    <div class="card bd">
      <div class="card-body">
        <div class="descriptions">
          <div class="descriptions-view">
            <div id="main"
                 style="width: 920px;height:400px;" />
          </div>
        </div>
      </div>

    </div>
    <div class="card bd">
      <div class="card-body">
        <div class="descriptions">
          <div class="descriptions-header">
            <div class="descriptions-title">统计明细</div>
          </div>
          <div class="descriptions-view">
            <table>
              <tbody>
                <tr v-for="(item,index) in list"
                    :key="index"
                    class="descriptions-row">
                  <td v-for="(subItem,subIndex) in item"
                      :key="subIndex"
                      class="descriptions-item"
                      colspan="1">
                    <div class="descriptions-item-container"><span class="descriptions-item-label">{{ subItem.label }}</span><span class="descriptions-item-content">{{ subItem.value }}</span></div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts'
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
export default {
  components: {
    dartSearch,
    dartSearchItem
  },
  data() {
    return {
      myChart: null,
      search: {
        taskId: ''
      },
      rules: {
        taskId: [
          { required: true, message: '请选择任务', trigger: 'blur' }
        ]
      },
      jobList: [],
      list: [],
      data: [{
        label: '背面图片车牌不匹配',
        value: '',
        key: 'carBackImgNum'
      }, {
        label: '正面图片车牌不匹配',
        value: '',
        key: 'carFrontImgNum'
      }, {
        label: '车外形不匹配',
        value: '',
        key: 'carOutlineNum'
      }, {
        label: '车座位数不匹配',
        value: '',
        key: 'carSeatNum'
      }, {
        label: '背面图片无法识别',
        value: '',
        key: 'identifyBackImgNum'
      }, {
        label: '正面图片无法识别',
        value: '',
        key: 'identifyFrontImgNum'
      }, {
        label: '没有采集背面图片',
        value: '',
        key: 'nullBackImgNum'
      }, {
        label: '没有采集正面图片',
        value: '',
        key: 'nullFrontImgNum'
      }, {
        label: '无任何图片',
        value: '',
        key: 'nullImgNum'
      }, {
        label: '车辆品牌不匹配',
        value: '',
        key: 'carModel'
      }, {
        label: '背面图片地址在服务器上无法找到',
        value: '',
        key: 'nullSerBackImgNum'
      }, {
        label: '正面图片地址在服务器上无法找到',
        value: '',
        key: 'nullSerFrontImgNum'
      }]
    }
  },
  mounted() {

    this.myChart = echarts.init(document.getElementById('main'))
    this.getJobField();
  },
  methods: {
    getJobField() {
      this.$request({
        url: this.$interfaces.getJobField,
        method: 'post',
        data: {}
      }).then(res => {
        this.data = res.data;
        for (let i = 0; i < this.data.length; i++) {
          this.data[i].key = this.data[i].fieldId
          this.data[i].label = this.data[i].fieldName
        }
        this.list = this.group(this.data, 3)
        this.getAllJobList()
        this.initChat()
      })
    },
    // 获取列表
    getIssueStatistics() {

      this.$store
        .dispatch('issue/getIssueStatistics', this.search)
        .then(res => {
          if (res.code === 200) {
            let result = res.data || []
            console.log(result, ' seriesData.push(obj)');
            for (let i = 0; i < this.data.length; i++) {
              this.data[i].value = result[this.data[i].key]
            }
            let arr = this.data.filter((item) => {
              return item.value && item.value > 0
            })
            this.initChat()
            this.list = this.group(arr, 3)
          }
        })
        .catch(() => { })
    },
    getAllJobList() {
      this.$store
        .dispatch('issue/getAllJobList')
        .then(res => {
          this.jobList = res
          this.search.taskId = this.jobList[0].taskId || ''
          if (this.search.taskId) {
            this.getIssueStatistics()
          }
        })
        .catch(() => { })
    },
    group(array, subGroupLength) {
      let index = 0
      const newArray = []
      while (index < array.length) {
        newArray.push(array.slice(index, index += subGroupLength))
      }
      return newArray
    },
    onSearchHandle() {
      this.getIssueStatistics()
    },
    onResultHandle() {
      this.search.taskId = this.jobList[0].taskId || ''
      if (this.search.taskId) {
        this.getIssueStatistics()
      }
    },
    initChat() {
      const seriesData = []
      for (let i = 0; i < this.data.length; i++) {
        const obj = {
          name: this.data[i].label,
          value: this.data[i].value || 0
        }
        if (this.data[i].value) {
          seriesData.push(obj)
        }

      }
      /*   const data = {
          color: ['#c23531', '#2f4554', '#61a0a8'],
          tooltip: {},
          xAxis: {
            data: xAxisData
          },
          yAxis: {},
          series: [{
            name: '',
            type: 'line',
            data: seriesData,
            itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622']
                  return colorList[params.dataIndex]
                }
              }
            }
          }]
        } */
      const option = {
        title: {
          text: '发行稽核统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: '50%',
            data: seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
            /* itemStyle: {
              normal: {
                color: function (params) {
                  var colorList = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc', '#f6efa6', '#d88273', '#bf444c', '#fac858']
                  return colorList[params.dataIndex]
                }
              }
            } */
          }
        ]
      }
      this.myChart.setOption(option)
    }

  }
}
</script>

<style lang="scss">
.page-container-children-content {
  margin: 20px 20px 0px;
}
.page-container-children-content .bd {
  margin-top: 20px;
}
.card {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', 'tnum';
  position: relative;
  background: #fff;
  border-radius: 2px;
}
.card-body {
  padding: 20px;
}
.descriptions-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.descriptions-title {
  flex: auto;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 700;
  font-size: 16px;
  line-height: 1.5715;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.descriptions-view {
  width: 100%;
  overflow: hidden;
  border-radius: 2px;
}
.descriptions-row > td,
.descriptions-row > th {
  padding-bottom: 16px;
}
.descriptions-item {
  padding-bottom: 0;
  vertical-align: top;
}
.descriptions-item-container {
  display: flex;
}
.descriptions-item-container .descriptions-item-content,
.descriptions-item-container .descriptions-item-label {
  display: inline-flex;
  align-items: baseline;
}
.descriptions-item-label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5715;
  text-align: start;
}
.descriptions-item-content {
  display: table-cell;
  flex: 1 1;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  word-break: break-word;
  overflow-wrap: break-word;
}
.descriptions-item-label:after {
  content: ':';
  position: relative;
  top: -0.5px;
  margin: 0 8px 0 2px;
}
.descriptions-view table {
  width: 100%;
  table-layout: fixed;
}
</style>