<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:车牌解除占用功能
  * @author:zhang<PERSON>
  * @date:2023/04/14 15:07:37
-->
<template>
  <div class="account-list">
    <dart-search
      ref="searchForm1"
      class="search"
      :formSpan="24"
      label-position="right"
      :searchOperation="false"
      :model="search"
      :fontWidth="1"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item label="操作网点：" prop="branchNo">
          <el-cascader
            v-model="search.branchNo"
            class="form-select"
            ref="pidNodes"
            clearable
            filterable
            :options="deptOptions"
            :expand-trigger="'click'"
            :props="{
                checkStrictly: true,
                value: 'id',
                label: 'name',
            emitPath:false
              }"
          />
        </dart-search-item>
        <dart-search-item label="操作人：">
          <el-input v-model="search.operatorName" placeholder clearable></el-input>
        </dart-search-item>
        <dart-search-item label="车牌号：">
          <el-input v-model="search.vehicleNo" placeholder clearable></el-input>
        </dart-search-item>
        <dart-search-item label="车牌颜色：">
          <el-select clearable v-model="search.vehicleColor" placeholder="请选择" collapse-tags>
            <el-option
              v-for="(item,index) in licenseColorOption"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>
        <dart-search-item label="卡号：">
          <el-input v-model="search.cardNo" placeholder clearable></el-input>
        </dart-search-item>
        <dart-search-item label="OBU号：">
          <el-input v-model="search.obuNo" placeholder clearable></el-input>
        </dart-search-item>
        <template v-if="collapse">
          <dart-search-item label="开始时间：">
            <el-date-picker
              v-model="search.startTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间"
              clearable
            ></el-date-picker>
          </dart-search-item>
          <dart-search-item label="结束时间：">
            <el-date-picker
              v-model="search.endTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="23:59:59"
              placeholder="结束时间"
              clearable
            ></el-date-picker>
          </dart-search-item>
          <dart-search-item label="代扣机构：" prop="payOrg">
            <el-select
              v-model="search.payOrg"
              filterable
              remote
              clearable
              placeholder="请输入关键词"
              :loading="loading"
              :remote-method="remoteMethodPay"
            >
              <el-option
                v-for="item in payOrgOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              ></el-option>
            </el-select>
          </dart-search-item>
          <dart-search-item label="旧车主电话：">
            <el-input v-model="search.oCustMobile" placeholder clearable></el-input>
          </dart-search-item>
          <dart-search-item label="新车主电话：">
            <el-input v-model="search.nCustMobile" placeholder clearable></el-input>
          </dart-search-item>

          <dart-search-item label="签约渠道：" prop="signCode">
            <el-select
              v-model="search.signCode"
              filterable
              clearable
              remote
              placeholder="请输入关键词"
              :loading="loading"
              :remote-method="remoteMethod"
            >
              <el-option
                v-for="item in signCodeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.label"
              ></el-option>
            </el-select>
          </dart-search-item>
        </template>
        <dart-search-item :is-button="true" :colElementNum="1">
          <div class="g-flex g-flex-end">
            <el-button @click="onResultHandle">重置</el-button>
            <el-button type="primary" @click="onSearchHandle">查询</el-button>
            <el-button type="warning" @click="exportHandle">导出</el-button>
            <el-button type="text" @click="collapse=!collapse">
              <span v-if="collapse">收起</span>
              <span v-if="!collapse">展开</span>
            </el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table-box">
      <el-table
        :data="tableData"
        :align="center"
        height="100%"
        :header-align="center"
        style="width: 100%;"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
      >
        <el-table-column align="center" width="180" label="原车主姓名" prop="oCustName"></el-table-column>
        <el-table-column align="center" width="140" label="原联系电话" prop="oCustMobile"></el-table-column>
        <el-table-column align="center" width="180" label="新车主姓名" prop="nCustName"></el-table-column>

        <el-table-column align="center" width="140" label="新联系电话" prop="nCustMobile"></el-table-column>
        <el-table-column align="center" width="140" label="车牌号" prop="oCarNo"></el-table-column>
        <el-table-column align="center" width="180" label="卡号" prop="carNo"></el-table-column>
        <el-table-column align="center" width="180" label="obu号" prop="obuNo"></el-table-column>
        <el-table-column align="center" width="140" label="客货类型" prop="oCarIstruck">
          <template slot-scope="scope">{{ getVehicleType(scope.row.oCarIstruck) }}</template>
        </el-table-column>
        <el-table-column align="center" width="140" label="车型" prop="oCarClass">
          <template slot-scope="scope">{{ getCarType(scope.row.oCarClass) }}</template>
        </el-table-column>
        <el-table-column width="140" prop="signCode" label="签约渠道"></el-table-column>
        <el-table-column width="140" prop="payOrg" label="代扣机构"></el-table-column>

        <el-table-column align="center" width="180" label="解占时间" prop="createTime"></el-table-column>
        <el-table-column align="center" width="180" label="操作网点" prop="branchName"></el-table-column>
        <el-table-column align="center" width="140" label="操作人" prop="operatorName"></el-table-column>

        <el-table-column align="center" header-align="center" width="160" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="detailSlide(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination g-flex g-flex-start">
      <el-pagination
        background
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>

    <!-- 售后订单审批处理 -->
    <dartSlide
      :visible.sync="detailVisible"
      title="车牌解占详情"
      v-transfer-dom
      width="80%"
      :maskClosable="true"
    >
      <detail :detailItem="detailItem" :detailVisible="detailVisible"></detail>
    </dartSlide>
  </div>
</template>

<script>
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import dartSlide from '@/components/dart/Slide/index.vue'
import { licenseColorOption } from '@/common/const/optionsData.js'
import upmsRequest from '@/utils/upmsRequest'
import { getChannel } from '@/api/dict'
var moment = require('moment')
import { getCarType, getVehicleType } from '@/common/method/formatOptions.js'
import detail from './detail'
import { decode } from 'js-base64'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: String
    }
  },
  components: {
    dartSearch,
    dartSearchItem,
    dartSlide,
    detail
  },
  created() {
    this.getgroup()
    this.getDictionaries()
    this.getVehicleList()
  },
  data() {
    return {
      showvisible: false,
      center: 'center',
      search: {
        vehicleNo: '',
        vehicleColor: '',
        startTime: '',
        endTime: '',
        obuNo: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      detailVisible: false,
      licenseColorOption,
      tableData: [],
      detailItem: {},
      collapse: false,
      deptOptions: [],
      signCodeOptions: [],
      payOrgOptions: [],
      loading: false,
      typeList: {
        bindingBankType: [] //绑定机构
      }
    }
  },
  methods: {
    getCarType,
    getVehicleType,
    getVehicleList() {
      this.startLoading()

      this.$request({
        url: this.$interfaces.occupyVehicleList,
        method: 'post',
        data: this.search
      })
        .then(res => {
          this.endLoading()
          if (res.code == 200) {
            this.tableData = res.data.records
            this.total = res.data.total
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(error => {
          this.endLoading()
          console.log('err', err)
        })
    },
    //导出
    exportHandle() {
      let params = JSON.parse(JSON.stringify(this.search))
      params.name = 'removeVehicleListReport'
      delete params.pageNum
      delete params.pageSize
      this.startLoading()
      this.$request({
        url: this.$interfaces.report,
        method: 'post',
        data: params
      })
        .then(res => {
          this.endLoading()
          if (res.code == 200) {
            let url = res.data
            let decodeUrl = decode(url)
            // console.log(decodeUrl,'地址')
            let clientWidth = document.documentElement.clientWidth
            let clientHeight = document.documentElement.clientHeight
            window.open(
              decodeUrl,
              '_blank',
              'width=' +
                clientWidth +
                ',height=' +
                clientHeight +
                ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
            )
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    onSearchHandle() {
      if (moment(this.search.startTime).isAfter(this.search.endTime)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        return
      }
      this.search.pageNum = 1
      this.getVehicleList()
    },
    onResultHandle() {
      for (const key in this.search) {
        this.search[key] = ''
      }
      this.search.pageNum = 1
      this.search.pageSize = 10
    },
    changePage(page) {
      this.search.pageNum = page
      this.getVehicleList()
    },
    detailSlide(val) {
      this.detailVisible = true
      this.detailItem = val
    },
    getgroup() {
      return upmsRequest({
        url: '/dept/queryByDataScope',
        method: 'get'
      })
        .then(res => {
          console.log(res.data)
          this.deptOptions = res.data
        })
        .catch(error => {
          console.log(error)
        })
    },
    async remoteMethodPay(query) {
      if (query !== '') {
        this.loading = true
        let params = {
          name: query
        }
        let res = await getChannel(params)
        this.loading = false
        if (res.code == 200) {
          this.payOrgOptions = res.data.map(item => {
            return { label: item.channelName, value: item.channelCode }
          })
        }
      } else {
        this.payOrgOptions = []
      }
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        let params = {
          name: query
        }
        let res = await getChannel(params)
        this.loading = false
        if (res.code == 200) {
          this.signCodeOptions = res.data.map(item => {
            return { label: item.channelName, value: item.channelCode }
          })
          console.log(this.signCodeOptions)
        }
      } else {
        this.signCodeOptions = []
      }
    },
    getDictionaries() {
      this.$store
        .dispatch('bindManagement/dictionaries')
        .then(res => {
          console.log('字典列表', res)
          let typeList = res
          Object.keys(typeList).forEach(key => {
            this.filterTypeList(typeList[key], this.$data['typeList'][key])
          })
          console.log('typeList', this.typeList)
        })
        .catch(err => {})
    },
    filterTypeList(typeArr, lvTypeList) {
      // console.log('typeArr', typeArr)
      if (lvTypeList.length === 0) {
        // lvTypeList.push({
        //   label: '全部',
        //   value: '',
        // })
        typeArr.forEach(item => {
          // console.log('item', item)
          let lvObj = {
            label: item.fieldNameDisplay,
            value: item.fieldValue
          }
          lvTypeList.push(lvObj)
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.account-list {
  height: 100%;
  position: relative;
  padding: 0 20px;
  flex-flow: column;
  display: flex;
}
.account-list .search {
  margin-top: 20px;
}
.account-list .table-box {
  padding: 20px 20px 10px 20px;
  flex: 1;
  height: 0;
  background-color: #fff;
}
.account-list .pagination {
  margin: 10px 0;
}
</style>
