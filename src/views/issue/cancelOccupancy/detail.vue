
<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:线上发行——详情
  * @author:zhang<PERSON>
  * @date:2023/03/01 10:40:06
-->
<template>
  <div class="detail-wrap">
    <!-- 订单基础信息 -->
    <div class="orderItem">
      <el-descriptions :column="4"
                       border
                       size="medium"
                       title="车辆信息"
                       class="descriptions-content">

        <el-descriptions-item label="所属客户">
          {{ detail.oCustName }}
        </el-descriptions-item>
        <el-descriptions-item label="账户类型">
          {{ detail.oCustType=='1'?'单位':'个人' }}
        </el-descriptions-item>

        <el-descriptions-item label="联系人">
          {{ detail.oLinkName }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ detail.oCustMobile }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌号">
          {{ detail.oCarNo }}
        </el-descriptions-item>
        <el-descriptions-item label="车牌颜色">
          {{getVehicleColor(detail.oCarColor)  }}
        </el-descriptions-item>
        <el-descriptions-item label="车型">
          {{getCarType(detail.oCarClass)  }}
        </el-descriptions-item>
        <el-descriptions-item label="解占时间">
          {{ detail.createTime }}
        </el-descriptions-item>
        <el-descriptions-item label="操作网点">
          {{ detail.branchName }}
        </el-descriptions-item>
        <el-descriptions-item label="操作人">
          {{ detail.operatorName }}
        </el-descriptions-item>
        <el-descriptions-item label="授权人">
          {{ detail.authorizerName }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="sub-title">设备信息</div>
      <div style="padding:0 16px 10px 16px">
        <el-table :data="tableData"
                  align="center"
                  header-align="center"
                  border
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '54px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '54px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id">
          <el-table-column prop="goodType"
                           align="center"
                           label="设备类型"
                           width="160">
            <template slot-scope="scope">
              {{ scope.row.goodType == '1' ?'ETC卡':'OBU' }}
            </template>

          </el-table-column>
          <el-table-column prop="goodCode"
                           align="center"
                           label="设备号">
          </el-table-column>
          <el-table-column prop="productType"
                           align="center"
                           label="产品类型">
            <template slot-scope="scope">
              {{ getallGxCardType(scope.row.productType) }}
            </template>
          </el-table-column>

          <el-table-column prop="signCode"
                           align="center"
                           label="绑定渠道" />
          <el-table-column prop="payOrg"
                           align="center"
                           label="代扣渠道" />

        </el-table>
      </div>

      <div class="sub-title">车辆档案</div>
      <archives previewMode
                uploadType="CACHEIMGUPLAOD"
                :pictureSource="vehiclePicSource"
                style="padding: 0 16px;">
      </archives>

      <div class="sub-title"
           v-if="isShowArchives">激活档案</div>
      <archives previewMode
                uploadType="CACHEIMGUPLAOD"
                :pictureSource="activatePicSource"
                style="padding: 0 16px;">
      </archives>
    </div>

    <div class="orderItem">
      <div class="title">欠费记录</div>
      <div style="padding:0 16px 10px 16px">
        <el-table :data="oweTableData"
                  align="center"
                  header-align="center"
                  border
                  style="width: 100%; margin-bottom: 20px"
                  :row-style="{ height: '54px' }"
                  :cell-style="{ padding: '0px' }"
                  :header-row-style="{ height: '54px' }"
                  :header-cell-style="{ padding: '0px' }"
                  row-key="id">
          <el-table-column prop="id"
                           align="center"
                           label="序号">
          </el-table-column>
          <el-table-column prop="amount"
                           align="center"
                           label="金额(元)">
            <template slot-scope="scope">
              {{ moneyFilter(scope.row.amount) }}
            </template>
          </el-table-column>
          <el-table-column prop="source"
                           align="center"
                           label="来源">
          </el-table-column>
          <el-table-column prop="goodType"
                           align="center"
                           label="订单类型"
                           width="160">
            <template slot-scope="scope">
              {{ scope.row.orderType == '1' ?'补缴单':'退费单' }}
            </template>

          </el-table-column>
          <el-table-column prop="createTime"
                           align="center"
                           label="生成时间">
          </el-table-column>

          <el-table-column prop="operatorType"
                           align="center"
                           label="生成方式" />
          <el-table-column prop="status"
                           align="center"
                           label="处理状态" />

        </el-table>
      </div>

    </div>
    <div class="orderItem">
      <el-descriptions :column="3"
                       border
                       size="medium"
                       title="新车主信息"
                       class="descriptions-content">

        <el-descriptions-item label="新车主类型"
                              span="3">
          {{ detail.nCustType=='1'?'单位':'个人' }}
        </el-descriptions-item>

        <el-descriptions-item label="名称">
          {{ detail.nCustName }}
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">
          {{ detail.nCustIdno }}
        </el-descriptions-item>
        <el-descriptions-item label="联系电话">
          {{ detail.nCustMobile }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="sub-title">附件：</div>
      <archives previewMode
                uploadType="CACHEIMGUPLAOD"
                :pictureSource="cancelPicSource"
                style="padding: 0 16px;">
      </archives>
    </div>

    <div class="orderItem">
      <el-descriptions :column="1"
                       border
                       size="medium"
                       title="备注"
                       class="descriptions-content">
        <el-descriptions-item label="备注"
                              labelStyle="width:120px">
          {{ detail.remarks }}
        </el-descriptions-item>

      </el-descriptions>

    </div>
  </div>

</template>

<script>
import request from '@/utils/request'
import api from '@/api/index'
import {
  getCarType,
  getVehicleType,
  getVehicleColor,
  getallGxCardType,
} from '@/common/method/formatOptions.js'
import archives from './components/archives'
import float from '@/common/method/float.js'

export default {
  components: { archives },
  props: {
    detailVisible: {
      type: Boolean,
      default: '',
    },
    detailItem: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  watch: {
    detailVisible(val) {
      if (val) {
        this.getDetail()
      }
    },
  },
  data() {
    return {
      detail: {},
      tableData: [],
      oweTableData: [],
      vehiclePicSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '3',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '12',
          lable: '行驶证副页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '6',
          lable: '车辆照片',
        },
      ],
      activatePicSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '3',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '15',
          lable: '车头照片',
        },
      ],
      cancelPicSource: [
        {
          lable: '身份证正面',
          photo_code: '1',
          file_url: '',
          file_serial: '',
          type: '0',
          require: true,
        },
        {
          lable: '身份证反面',
          photo_code: '11',
          file_url: '',
          file_serial: '',
          type: '0',
          require: true,
        },
        {
          lable: '行驶证正副页',
          photo_code: '3',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '机动车登记证书',
          photo_code: '44',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '购车发票',
          photo_code: '45',
          file_url: '',
          file_serial: '',
          type: '3',
          require: false,
        },
        {
          lable: '回执单',
          photo_code: '46',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '营业执照',
          photo_code: '42',
          file_url: '',
          file_serial: '',
          type: '1',
          require: true,
        },
        {
          lable: '委托书',
          photo_code: '25',
          file_url: '',
          file_serial: '',
          type: '1',
          require: true,
        },
      ],
      isShowArchives: false,
    }
  },
  created() {},
  methods: {
    getCarType,
    getVehicleType,
    getVehicleColor,
    getallGxCardType,
    moneyFilter(val) {
      if (val == 0 || !val) {
        return val
      }
      return float.div(val, 100)
    },
    //获取订单详情
    getDetail() {
      let params = {
        id: this.detailItem.id,
      }

      this.startLoading()
      this.$request({
        url: this.$interfaces.occupyVehicleDetail,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.detail = res.data
            this.tableData = res.data.goodsList
            this.init()
            this.getActivateArchives()
            this.getVehicleArchives()
            this.getCancelArchives()
            this.getOweData()
            this.endLoading()
          } else {
            this.endLoading()

            this.$message({
              message: res.msg,
              type: 'error',
            })
          }
        })
        .catch(() => {
          this.endLoading()
        })
    },
    init() {
      this.vehiclePicSource = [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '3',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '12',
          lable: '行驶证副页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '6',
          lable: '车辆照片',
        },
      ]
      this.activatePicSource = [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '3',
          lable: '行驶证正页',
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: '15',
          lable: '车头照片',
        },
      ]
      this.cancelPicSource = [
        {
          lable: '身份证正面',
          photo_code: '1',
          file_url: '',
          file_serial: '',
          type: '0',
          require: true,
        },
        {
          lable: '身份证反面',
          photo_code: '11',
          file_url: '',
          file_serial: '',
          type: '0',
          require: true,
        },
        {
          lable: '行驶证正副页',
          photo_code: '3',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '机动车登记证书',
          photo_code: '44',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '购车发票',
          photo_code: '45',
          file_url: '',
          file_serial: '',
          type: '3',
          require: false,
        },
        {
          lable: '回执单',
          photo_code: '46',
          file_url: '',
          file_serial: '',
          type: '3',
          require: true,
        },
        {
          lable: '营业执照',
          photo_code: '42',
          file_url: '',
          file_serial: '',
          type: '1',
          require: true,
        },
        {
          lable: '委托书',
          photo_code: '25',
          file_url: '',
          file_serial: '',
          type: '1',
          require: true,
        },
      ]
    },
    //获取车辆档案
    getVehicleArchives() {
      let params = {
        customer_id: this.detail.oCustMastId,
        scene: '2',
        vehicle_code: this.detail.oCarNo,
        vehicle_color: this.detail.ocarColor,
      }
      this.$request({
        url: this.$interfaces.archives,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.length; i++) {
            for (let j = 0; j < this.vehiclePicSource.length; j++) {
              if (
                res.data[i].photo_code == this.vehiclePicSource[j].photo_code
              ) {
                this.vehiclePicSource[j].file_url = res.data[i].file_url
              }
            }
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //获取激活档案
    getActivateArchives() {
      let params = {
        customer_id: this.detail.oCustMastId,
        scene: '5',
        vehicle_code: this.detail.oCarNo,
        vehicle_color: this.detail.ocarColor,
      }
      this.$request({
        url: this.$interfaces.archives,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data.length != 0) {
            this.isShowArchives = true
          } else {
            this.isShowArchives = false
          }
          for (let i = 0; i < res.data.length; i++) {
            for (let j = 0; j < this.activatePicSource.length; j++) {
              if (
                res.data[i].photo_code == this.activatePicSource[j].photo_code
              ) {
                this.activatePicSource[j].file_url = res.data[i].file_url
              }
            }
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    //获取解占档案
    getCancelArchives() {
      let params = {
        customer_id: this.detail.oCustMastId,
        scene: '26',
        vehicle_code: this.detail.oCarNo,
        vehicle_color: this.detail.ocarColor,
      }
      this.$request({
        url: this.$interfaces.archives,
        method: 'post',
        data: params,
      }).then((res) => {
        if (res.code == 200) {
          for (let i = 0; i < res.data.length; i++) {
            for (let j = 0; j < this.cancelPicSource.length; j++) {
              if (
                res.data[i].photo_code == this.cancelPicSource[j].photo_code
              ) {
                this.cancelPicSource[j].file_url = res.data[i].file_url
              }
              if (this.detail.nCustType == '1') {
                if (this.cancelPicSource[i].photo_code == '1') {
                  this.$set(
                    this.cancelPicSource[i],
                    'lable',
                    '委托人身份证正面'
                  )
                }
                if (this.cancelPicSource[i].photo_code == '11') {
                  this.$set(
                    this.cancelPicSource[i],
                    'lable',
                    '委托人身份证反面'
                  )
                }
              }
            }
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getOweData() {
      let params = {
        id: this.detailItem.id,
      }
      this.$request({
        url: this.$interfaces.occupyOweData,
        method: 'post',
        data: params,
      })
        .then((res) => {
          if (res.code == 200) {
            this.oweTableData = res.data
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {})
    },
  },
}
</script>
<style lang="scss" scoped>
.foot {
  // padding-top: 20px ;
  margin: 0;
  text-align: center;
  line-height: 60px;
  background-color: #fff;
}
.el-steps--simple {
  background-color: #fff;
}
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-top: 20px;
}

.detail-wrap .orderItem:first-of-type {
  margin-top: 0px;
}

.detail-wrap .orderItem::-webkit-scrollbar {
  display: none;
}

.archives {
  padding: 0 20px;
  background-color: #fff;
}

.archives-box {
  display: flex;
  flex-wrap: wrap;
  -moz-box-pack: start;
  -ms-box-pack: start;
  -webkit-box-pack: start;
  -webkit-justify-content: flex-start;
  -moz-justify-content: flex-start;
  justify-content: flex-start;
}

.archives-box .archives-item {
  width: 240px;
  margin-right: 20px;
  margin-bottom: 20px;
}

.archives-box .archives-item .demonstration {
  display: block;
  color: #8492a6;
  width: 100%;
  text-align: center;
  font-size: 15px;
  margin-top: 10px;
}

.archives-box .archives-item .imgbox img {
  width: 240px;
  height: 180px;
}

.descriptions-content {
  padding: 16px !important;
}
.sub-title {
  padding: 10px 15px;
  font-size: 14px;
}
.title {
  padding: 15px;
  font-size: 16px;
  font-weight: bold;
}
</style>
