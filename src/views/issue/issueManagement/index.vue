<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:稽核管理
  * @author:zhangys
  * @date:2023/03/28 10:27:48
-->
<template>
  <div class="container">
    <dart-search ref="searchForm1"
                 label-position="right"
                 :model="form"
                 :formSpan='24'
                 :searchOperation='false'
                 :fontWidth="1"
                 :labelTextLength="8"
                 class="search">
      <template slot="search-form">

        <dart-search-item label="激活时间："
                          prop="">
          <el-date-picker v-model="activateDate"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          clearable
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>

        <dart-search-item label="加入时间："
                          prop="">
          <el-date-picker v-model="joinDate"
                          type="datetimerange"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          clearable
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期">
          </el-date-picker>
        </dart-search-item>
        <dart-search-item label="激活结果："
                          prop="auditResults">
          <el-select clearable
                     v-model="form.auditResults"
                     placeholder="请选择"
                     collapse-tags>
            <el-option v-for="(item,index) in activateResult"
                       :key="index"
                       :label="item.label"
                       :value="item.value" />
          </el-select>
        </dart-search-item>
        <div class="collapse-wrapper"
             v-show="isCollapse">
          <dart-search-item label="车辆类型："
                            prop="vehicleType">
            <el-select clearable
                       v-model="form.vehicleType"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in vehicleType"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车牌号："
                            prop="carNo">
            <el-input v-model="form.carNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="车牌颜色："
                            prop="carColor">
            <el-select clearable
                       v-model="form.carColor"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in licenseColorOption"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="车型："
                            prop="vehicleTypeKind">
            <el-select clearable
                       v-model="form.vehicleTypeKind"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in vehicleCatgoryType"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="ETC卡号："
                            prop="cardNo">
            <el-input v-model="form.cardNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="OBU号："
                            prop="obuNo">
            <el-input v-model="form.obuNo"
                      clearable
                      placeholder=""></el-input>
          </dart-search-item>
          <dart-search-item label="是否有通行记录："
                            prop="isHavePassRecord">
            <el-select clearable
                       v-model="form.isHavePassRecord"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in tollRecordStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
          <dart-search-item label="激活状态："
                            prop="auditStatus">
            <el-select clearable
                       v-model="form.auditStatus"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in activateStatus"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>

          <dart-search-item label="业务类型："
                            prop="businessSource">
            <el-select clearable
                       v-model="form.businessSource"
                       placeholder="请选择"
                       collapse-tags>
              <el-option v-for="(item,index) in businessTypeOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </dart-search-item>
        </div>
        <dart-search-item :is-button="true"
                          :colElementNum='1'>
          <div class="g-flex g-flex-end">
            <el-button type="primary"
                       size="mini"
                       native-type="submit"
                       @click="onSearchHandle">查询</el-button>
            <el-button size="mini"
                       @click="onResultHandle">重置</el-button>
            <el-button type="warning"
                       size="mini"
                       native-type="submit"
                       @click="exportHandle">导出</el-button>

            <el-button type="text"
                       @click="isCollapse=!isCollapse"><span v-if="isCollapse">收起</span><span v-if="!isCollapse">展开</span></el-button>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table :data="tableData"
                style="width: 100%;"
                height="100%"
                row-key="id">
        <el-table-column prop="carNo"
                         label="车牌号"
                         min-width="120"
                         align="center" />
        <el-table-column prop="carColor"
                         label="车牌颜色"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleColor(scope.row.carColor)}}
          </template>
        </el-table-column>
        <el-table-column prop="businessSource_str"
                  label="业务类型"
                  min-width="120"
                  align="center" />
        <el-table-column prop="cardNo"
                         label="ETC卡号"
                         min-width="180"
                         align="center" />
        <el-table-column prop="obuNo"
                         label="OBU号"
                         min-width="180"
                         align="center" />
        <el-table-column prop="vehicleType"
                         label="车辆类型"
                         min-width="100"
                         align="center">
          <template slot-scope="scope">
            {{getVehicleType(scope.row.vehicleType)}}
          </template>
        </el-table-column>
        <el-table-column prop="vehicleTypeKind"
                         label="车型"
                         min-width="100"
                         align="center">
          <template slot-scope="scope">
            {{getCarType(scope.row.vehicleTypeKind)}}
          </template>
        </el-table-column>
        <el-table-column prop="activationTime"
                         label="激活时间"
                         min-width="180"
                         align="center" />
        <el-table-column prop="numberOfActivations"
                         label="自助激活次数"
                         min-width="120"
                         align="center" />
        <el-table-column prop="numberOfPasses"
                         label="激活后通行次数"
                         min-width="120"
                         align="center" />
        <el-table-column prop="createdTime"
                         label="加入时间"
                         min-width="180"
                         align="center" />
        <el-table-column prop="isHavePassRecord_str"
                         label="是否有通行记录"
                         min-width="120"
                         align="center" />
        <el-table-column prop="auditStatus_str"
                         label="稽核状态"
                         min-width="120"
                         align="center" />
        <el-table-column prop="auditResults_str"
                         label="稽核结果"
                         min-width="120"
                         align="center" />
        <el-table-column label="操作"
                         fixed="right"
                         min-width="100"
                         align="center">
          <template slot-scope="scope">

            <el-button type="text"
                       @click="toDetail(scope.row,'deal')"
                       v-if="scope.row.auditStatus=='0'">稽核</el-button>
            <el-button type="text"
                       @click="toDetail(scope.row,'view')">详情</el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total>0">
      <el-pagination background
                     @size-change="handleSizeChange"
                     @current-change="changePage"
                     :current-page="form.page"
                     :page-sizes="[10, 20, 50]"
                     :page-size="form.pageSize"
                     layout="total, sizes, prev, pager, next, jumper"
                     :total="total">
      </el-pagination>
    </div>
    <dartSlide :visible.sync="detailVisible"
               title="稽核详情"
               v-transfer-dom
               width="90%"
               :maskClosable="true">
      <detail :itemInfo="itemInfo"
              :slideVisible="detailVisible"
              @onSearchHandle='getList'></detail>
    </dartSlide>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getVehicleType,
  getCarType,
} from '@/common/method/formatOptions'
import {
  licenseColorOption,
  vehicleType,
  vehicleCatgoryType,
} from '@/common/const/optionsData.js'
import dartSearch from '@/components/ProComponents/Search/search'
import dartSearchItem from '@/components/ProComponents/Search/searchItem'
import { mapGetters, mapActions } from 'vuex'
import request from '@/utils/request'
import api from '@/api/index'
import { decode } from 'js-base64'
import dartSlide from '@/components/dart/Slide/index.vue'
import detail from './detail'
export default {
  name: '',
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  components: { dartSearch, dartSearchItem, dartSlide, detail },
  data() {
    return {
      vehicleType,
      licenseColorOption,

      form: {
        activationTimeEnd: '',
        activationTimeStart: '',
        auditResults: '',
        auditStatus: '',
        carColor: '',
        carNo: '',
        cardNo: '',
        createdTimeEnd: '',
        createdTimeStart: '',
        custMastId: '',
        isHavePassRecord: '',
        netUserNo: '',
        obuNo: '',
        orderByField: 'id',
        page: 1,
        pageSize: 10,
        sort: 'desc',
        vehicleType: '',
        vehicleTypeKind: '',
      },
      isCollapse: false,
      activateDate: '',
      joinDate: '',

      tableData: [],
      detailVisible: false,
      total: 0,
      activateResult: [
        { label: '自助激活异常', value: '1' },
        { label: '自助激活正常', value: '0' },
      ],
      activateStatus: [
        { label: '未稽核', value: '0' },
        { label: '已稽核', value: '1' },
      ],
      tollRecordStatus: [
        { label: '否', value: '0' },
        { label: '是', value: '1' },
      ],
      businessTypeOptions:[
        { label: '全部', value: '' },
        { label: '线上发行', value: '1' },
        { label: '自助二次激活', value: '2' },
        { label: '线上更换', value: '3' },
        { label: '线上补办 ', value: '4' }
      ],
      vehicleCatgoryType,
      itemInfo: {},
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getList()
  },
  methods: {
    getVehicleColor,
    getVehicleType,
    getCarType,
    onSearchHandle() {
      this.form.page = 1
      this.getList()
    },
    getList(){
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditList,
        method: 'post',
        data: this.form,
      })
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.data
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    formatDate() {
      if (this.activateDate) {
        this.form.activationTimeStart = this.activateDate[0]
        this.form.activationTimeEnd = this.activateDate[1]
      }
      if (this.joinDate) {
        this.form.createdTimeStart = this.joinDate[0]
        this.form.createdTimeEnd = this.joinDate[1]
      }
    },
    onResultHandle() {
      this.form = {
        activationTimeEnd: '',
        activationTimeStart: '',
        auditResults: '',
        auditStatus: '',
        carColor: '',
        carNo: '',
        cardNo: '',
        createdTimeEnd: '',
        createdTimeStart: '',
        custMastId: '',
        isHavePassRecord: '',
        netUserNo: '',
        obuNo: '',
        orderByField: 'id',
        page: 1,
        pageSize: 10,
        sort: 'desc',
        vehicleType: '',
        vehicleTypeKind: '',
      }
      this.activateDate = ''
      this.joinDate = ''
    },
    handleSizeChange(e) {
      this.form.pageSize = e
      this.getList()
    },
    changePage(e) {
      this.form.page = e
      this.getList()
    },
    toDetail(val, type) {
      this.detailVisible = true
      this.itemInfo = val
      if (type == 'view') {
        this.itemInfo.isView = true
      } else {
        this.itemInfo.isView = false
      }
    },
    exportHandle() {
      this.formatDate()
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditExport,
        method: 'post',
        data: this.form,
        responseType: 'blob',
      })
        .then((res) => {
          this.getBlob(res, 'application/vnd.ms-excel', '稽核管理列表')
          this.endLoading()
        })
        .catch((err) => {
          this.endLoading()
        })
    },
    getBlob(blob, typeStr, fileName) {
      let link = document.createElement('a')
      link.href = URL.createObjectURL(new Blob([blob], { type: typeStr }))
      console.log(
        'URL.createObjectURL(new Blob([blob], { type: typeStr }))',
        URL.createObjectURL(new Blob([blob], { type: typeStr }))
      )
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      URL.revokeObjectURL(link.href)
    },
  },
}
</script>

<style lang='scss' scoped>
.container {
  height: 100%;
  position: relative;
  padding: 20px;
  flex-flow: column;
  display: flex;
  .table {
    flex: 1;
    height: 0;
    background-color: #fff;
  }
}
</style>