<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:激活档案查看
  * @author:zhangys
  * @date:2023/04/7 13:50:18
-->
<template>
  <el-dialog
    title="激活图片"
    @closed="onClosedHandle"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :center="true"
    append-to-body
  >
    <div style="height: 400px;overflow-y: scroll;">
      <archivesBox
        previewMode
        uploadType="CACHEIMGUPLAOD"
        :pictureSource="pictureSource"
        style="padding: 0 16px;"
      ></archivesBox>
    </div>
  </el-dialog>
</template>

<script>
import archivesBox from '@/views/workordermanage/component/photograph.vue'
import { getPlayInfo } from '@/api/paramsManagement'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    itemInfo: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  components: {
    archivesBox
  },
  data() {
    return {
      dialogVisible: false,
      pictureSource: [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseFrontUrl',
          lable: '行驶证正页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseSubpageUrl',
          lable: '行驶证副页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'frontPhotoUrl',
          lable: '车头照片'
        }
      ]
    }
  },
  created() {
    this.$nextTick(() => {
      this.dialogVisible = this.visible
    })
  },
  methods: {
    onClosedHandle() {
      this.dialogVisible = false
    },
    async init() {
      this.pictureSource = [
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseFrontUrl',
          lable: '行驶证正页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'drivingLicenseSubpageUrl',
          lable: '行驶证副页'
        },
        {
          isShow: true,
          file_url: '',
          file_serial: '',
          photo_code: 'frontPhotoUrl',
          lable: '车头45°照片'
        },
        {
          isShow: true,
          isVideo: true,
          file_url: '',
          file_serial: '',
          photo_code: 'obuVideoUrl',
          lable: 'ETC设备安装视频'
        }
      ]
      if (this.itemInfo.videoId) { // 如果有videoId 则去请求视频资源
        let { data } = await getPlayInfo({ videoId: this.itemInfo.videoId })
        let playInfo = data.playInfoList[0]
        this.itemInfo.obuVideoUrl = playInfo.playURL
      }

      for (let i = 0; i < this.pictureSource.length; i++) {
        console.log(this.itemInfo[this.pictureSource[i]['photo_code']])

        if (this.itemInfo[this.pictureSource[i]['photo_code']]) {
          this.pictureSource[i]['file_url'] = this.itemInfo[
            this.pictureSource[i]['photo_code']
          ]
        }
      }
    }
  },
  watch: {
    visible: function(val) {
      this.$nextTick(() => {
        this.dialogVisible = val
      })
      if (val) {
        this.init()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>