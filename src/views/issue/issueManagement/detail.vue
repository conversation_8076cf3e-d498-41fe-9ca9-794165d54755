<!--
  * @projectName:gxetc-issue-manage-web
  * @desc:稽核详情
  * @author:zhangys
  * @date:2023/03/28 10:58:24
-->
<template>
  <div class="detail-wrap">
    <div class="orderItem">
      <div class="title">稽核详情</div>
      <el-form class="nat-form nat-form-list" label-width="140px">
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="用户名称:">
              {{ detail.currentCustName }}
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="车牌号:">
              {{ detail.carNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌颜色:">
              {{ detail.carColor_str }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="ETC卡号:">
              {{ detail.currentCardNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="OBU号:">
              {{ detail.currentObuNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加入名单时间:">
              {{ detail.createdTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="历史加入次数:">
              {{ detail.numberOfHistory }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通行次数:">
              {{ detail.numberOfPasses }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="激活时间:">
              {{ detail.activationTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="稽核状态:">
              {{ detail.auditStatus_str }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="稽核结果:">
              {{ detail.auditResults_str }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="稽核时间:">
              {{ detail.auditTime }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="稽核人员:">
              {{ detail.updatedByName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="稽核备注:">
              {{ detail.remarks }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="激活次数:">
              {{ detail.numberOfActivations }}
            </el-form-item>
          </el-col>
        </el-row>
        <!-- 新增稽核后字段 -->
        <el-row :gutter="24">
          <el-col :span="8">
            <el-form-item label="加入稽核用户名:">
              {{ detail.custName}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加入稽核ETC卡:">
              {{ detail.cardNo}}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加入稽核OBU:">
              {{ detail.obuNo }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="orderItem">
      <div class="title">车辆通行记录</div>
      <div style="padding-top:10px">
        <el-form label-width="120px">
          <el-form-item label="通行时间:">
            <el-date-picker
              v-model="time"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>

            <el-button
              type="primary"
              style="margin-left:10px"
              @click="getTollRecord"
            >
              搜索</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <el-table
        :data="tollRecordData"
        height="400px"
        style="width:100%;margin-top: 10px;"
      >
        <el-table-column
          align="center"
          prop="pay_time"
          label="通行时间"
          width="170"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.pay_time }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="customer_name"
          label="用户名称"
          width="220"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="vehicle_code"
          label="车辆号码"
          width="120"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="cpu_card_id"
          label="ETC通行卡号"
          width="190"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="pay_way"
          label="付费方式"
          width="150"
        >
          <template slot-scope="scope">
            <span>{{
              scope.row.pay_way == '1'
                ? scope.row.pay_way == '2'
                  ? '出口ETC刷卡通行'
                  : 'ETC交易-停用'
                : '出口ETC通行'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="通行金额(元)" width="120">
          <template slot-scope="scope">
            <span>{{ (scope.row.pay_toll / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="服务费(元)" width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.serviceAmount || scope.row.serviceAmount == 0
                ? '--'
                : (scope.row.serviceAmount / 100).toFixed(2)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳金(元)" width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.overdueAmount || scope.row.overdueAmount == 0
                ? '--'
                : (scope.row.overdueAmount / 100).toFixed(2)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="滞纳天数(天)" width="120">
          <template slot-scope="scope">
            <span>{{
              !scope.row.overdueDay || scope.row.overdueDay == '0'
                ? '--'
                : scope.row.overdueDay
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="通行后卡面余额(元)" width="150">
          <template slot-scope="scope">
            <span>{{ (scope.row.after_wallet / 100).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="代收费(元)" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.pay_agency">{{
              (scope.row.pay_agency / 100).toFixed(2)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          prop="en_station"
          label="入口站点"
          width="160"
        >
        </el-table-column>
        <el-table-column
          align="center"
          prop="ex_station"
          label="出口站点"
          width="160"
        >
        </el-table-column>
      </el-table>
    </div>
    <div class="orderItem">
      <div class="title">激活记录列表</div>
      <div class="table">
        <el-table
          :data="tableData"
          style="width: 100%;"
          height="350px"
          row-key="id"
        >
          <el-table-column
            prop="classId"
            label="线上业务订单号"
            align="center"
          />
          <el-table-column
            prop="businessSource_str"
            label="业务类型"
            align="center"
          />
          <!-- <el-table-column prop="auditTime"
                           label="激活订单审核通过时间"
                           align="center" />
          <el-table-column prop="activateStatus_str"
                           label="激活状态"
                           align="center" /> -->
          <el-table-column
            prop="activateTime"
            label="激活成功时间"
            align="center"
          />
          <el-table-column prop="" label="激活附件材料" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="archivesPreview(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="pagination">
          <el-pagination
            background
            @size-change="handleTollSizeChange"
            @current-change="handleTollPageChange"
            :current-page="form.page"
            :page-sizes="[10, 20, 50]"
            :page-size="form.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="orderItem">
      <div class="title">稽核记录</div>
      <div class="table">
        <el-table
          :data="auditData"
          style="width: 100%;"
          height="350px"
          row-key="id"
        >
          <el-table-column prop="opByName" label="操作人" align="center" />
          <el-table-column prop="opType" label="操作" align="center">
            <template slot-scope="scope">
              {{ scope.row.opType == '1' ? '异常' : '正常' }}
            </template>
          </el-table-column>
          <el-table-column prop="opTime" label="操作时间" align="center" />
          <el-table-column prop="opRemarks" label="稽核备注" align="center" />
        </el-table>
      </div>
    </div>
    <div class="orderItem" v-if="!itemInfo.isView">
      <div class="title">稽核备注</div>
      <div style="padding:10px 20px">
        <el-input type="textarea" v-model="opRemarks"></el-input>
      </div>
    </div>

    <div class="orderItem" v-if="!itemInfo.isView">
      <div style="padding:10px 0 30px 0" class="g-flex g-flex-center ">
        <el-button
          type="danger"
          style="margin:0 20px"
          @click="operatorHandle('anomaly')"
          >自助激活异常</el-button
        >
        <el-button type="primary" @click="operatorHandle('normal')"
          >自助激活正常</el-button
        >
      </div>
    </div>
    <archives :visible.sync="archivesVisible" :itemInfo="recordItem"></archives>
  </div>
</template>

<script>
import {
  getVehicleColor,
  getVehicleType,
  getCarType
} from '@/common/method/formatOptions'
var moment = require('moment')
import archives from './archives'

export default {
  name: '',
  props: {
    itemInfo: {
      type: Object,
      default() {
        return {}
      }
    },
    slideVisible: {
      type: Boolean,
      default: false
    }
  },
  components: { archives },
  data() {
    return {
      total: null,
      tableData: [],
      detail: {},
      auditData: [],
      tollRecordData: [],
      tollRecordFormData: {
        customer_id: '',
        cpu_card_id: '',
        payStartDate: '',
        payEndDate: '',
        vehicleCode: '',
        vehicleColor: ''
      },
      time: '',
      opRemarks: '',
      form: {
        // id: '',
        page: 1,
        pageSize: 10
      },
      archivesVisible: false,
      recordItem: {}
    }
  },
  computed: {},
  watch: {
    slideVisible(val) {
      if (val) {
        this.getDetail()
        this.getAuditRecord()
        this.getTollRecord()
        this.getActivateRecord()
      }
      if (!val) {
        this.$emit('onSearchHandle')
      }
    }
  },
  created() {
    // this.tollRecordFormData.payStartDate = moment()
    //   .startOf('day')
    //   .format('YYYYMMDD')
    // this.tollRecordFormData.payEndDate = moment()
    //   .endOf('day')
    //   .format('YYYYMMDD')

    //初始化通行记录查询时间， 为最近一个月
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
    this.time = [
      moment(start).format('YYYY-MM-DD HH:mm:ss'),
      moment(end).format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  methods: {
    getVehicleColor,
    getVehicleType,
    getCarType,
    //获取详情
    getDetail() {
      let params = {
        id: this.itemInfo.id
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditDetail,
        method: 'post',
        data: params
      })
        .then(res => {
          if (res.code == 200) {
            this.detail = res.data
            // 获取完详情后 获取激活记录

            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    //获取通行记录
    getTollRecord() {
      this.tollRecordFormData.customer_id = this.itemInfo.custMastId + ''
      this.tollRecordFormData.cpu_card_id = this.itemInfo.cardNo
      this.tollRecordFormData.vehicleCode = this.itemInfo.carNo
      this.tollRecordFormData.vehicleColor = this.itemInfo.carColor

      if (this.time) {
        this.tollRecordFormData.payStartDate = moment(this.time[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.tollRecordFormData.payEndDate = moment(this.time[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      } else {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        this.tollRecordFormData.payStartDate = moment(start).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.tollRecordFormData.payEndDate = moment(end).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.hsKeyAuditListRecord,
        method: 'post',
        data: this.tollRecordFormData
      })
        .then(res => {
          if (res.code == 200) {
            this.endLoading()
            this.tollRecordData = res.data
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    archivesPreview(val) {
      this.archivesVisible = true
      this.recordItem = val
    },
    //获取稽核记录
    getAuditRecord() {
      let params = {
        keyAuditListId: this.itemInfo.id
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditOperatorRecord,
        method: 'post',
        data: params
      })
        .then(res => {
          if (res.code == 200) {
            this.auditData = res.data
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    //获取激活记录
    getActivateRecord() {
      let { carColor, carNo, custMastId } = this.itemInfo
      this.form.id = this.itemInfo.id
      let params = {
        carColor,
        carNo,
        custMastId,
        ...this.form
      }
      console.log(params)
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditActivateRecord,
        method: 'post',
        data: params
      })
        .then(res => {
          if (res.code == 200) {
            this.tableData = res.data.data
            this.total = res.data.total
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    //操作
    operatorHandle(type) {
      let params = {
        auditResults: '',
        id: this.itemInfo.id,
        opRemarks: this.opRemarks,
        revision: this.itemInfo.revision
      }
      if (type == 'normal') {
        params.auditResults = '0'
      }
      if (type == 'anomaly') {
        params.auditResults = '1'
      }
      if (type == 'anomaly' && !this.opRemarks) {
        this.$message.warning('请输入备注')
        return
      }
      this.startLoading()
      this.$request({
        url: this.$interfaces.auditJudge,
        method: 'post',
        data: params
      })
        .then(res => {
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.getDetail()
            this.endLoading()
          } else {
            this.endLoading()
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.endLoading()
        })
    },
    handleTollPageChange(val) {
      this.form.page = val
      this.getActivateRecord()
    },
    handleTollSizeChange(val) {
      this.form.pageSize = val
      this.getActivateRecord()
    }
  }
}
</script>

<style lang='scss' scoped>
.detail-wrap {
  padding: 10px 20px 0 20px;
  background-color: #fafafa;
}

.detail-wrap .orderItem {
  background-color: #fff;
  margin-top: 10px;
  .table {
    margin-top: 0px;
    padding: 10px;
  }
  .title {
    font-weight: 550;
    border-bottom: 1px solid #ebeef5;
    padding: 10px 20px;
  }
}
.nat-form.nat-form-list .el-form-item {
  margin-bottom: 0px;
}
</style>