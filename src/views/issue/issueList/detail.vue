<template>
  <div class="page-container-children-content">
    <div class="card bd">
      <div class="card-body">
        <div class="descriptions">
          <div class="descriptions-header">
            <div class="descriptions-title">稽核统计详情</div>
          </div>
          <div class="descriptions-view">
            <el-row :gutter="24"
                    class="descriptions-row">
              <el-col :span="12"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">稽核结果</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.sysAuditDetails }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24"
                    class="descriptions-row">
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">车牌</span>
                  <div class="descriptions-item-content"
                       :class="getClassFilter(detailInfo.carColor)">
                    <span class="vehicleCode"> {{ detailInfo.carNo }}</span>
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">车型</span>
                  <div class="descriptions-item-content">
                    {{ getCarTypeFilter(detailInfo.carType) }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">座位数</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.carSeatNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label"> 车轴数</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.axleCount }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24"
                    class="descriptions-row">
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label"> 发动机号</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.engineNum }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">识别代码</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.vin }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label"> 长x宽x高</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.outsideDimensions }}
                  </div>
                </div>
              </el-col>
              <el-col :span="6"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">车辆类型描述</span>
                  <div class="descriptions-item-content">
                    {{ detailInfo.vehicleType }}
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24"
                    class="descriptions-row">
              <el-col :span="12"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">行驶证正面</span>
                  <div class="descriptions-item-content car-image">
                    <el-image :src="detailInfo.drivingLicenseStr"
                              :preview-src-list="drivingLicenseStr">
                      <div slot="error"
                           class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
              </el-col>
              <el-col :span="12"
                      class="descriptions-item">
                <div class="descriptions-item-container">
                  <span class="descriptions-item-label">行驶证背面</span>
                  <div class="descriptions-item-content car-image">
                    <el-image :src="detailInfo.drivingLicenseBackStr"
                              :preview-src-list="drivingLicenseBackStr">
                      <div slot="error"
                           class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                    </el-image>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getClass, getCarType } from '@/common/method/formatOptions.js'
export default {
  components: {},
  data() {
    return {
      detailInfo: {},
      search: {
        ocrAuditId: ''
      },
      list: [],
      drivingLicenseStr: [],
      drivingLicenseBackStr: []

    }
  },

  computed: {},
  created() {
    this.search.ocrAuditId = this.$route.query.ocrAuditId
    if (this.search.ocrAuditId) {
      this.getIssueDetail()
    }
  },
  methods: {
    getClassFilter(type) {
      return getClass(type)
    },
    getCarTypeFilter(val) {
      return getCarType(val)
    },
    getIssueDetail() {
      this.$store
        .dispatch('issue/getIssueDetail', this.search)
        .then(res => {
          this.detailInfo = res.data
          this.detailInfo.drivingLicenseBackStr = this.detailInfo
            .drivingLicenseBackStr
            ? 'data:image/jpg;base64,' + this.detailInfo.drivingLicenseBackStr
            : ''
          this.detailInfo.drivingLicenseStr = this.detailInfo.drivingLicenseStr
            ? 'data:image/jpg;base64,' + this.detailInfo.drivingLicenseStr
            : ''
          if (this.detailInfo.drivingLicenseBackStr) {
            this.drivingLicenseBackStr = [this.detailInfo.drivingLicenseBackStr]
          }
          if (this.detailInfo.drivingLicenseStr) {
            this.drivingLicenseStr = [this.detailInfo.drivingLicenseStr]
          }
        })
        .catch(() => { })
    }
  }
}
</script>
<style lang="scss">
.car-image {
  width: 720px;
  margin: 0 auto;
}
.car-image__box .block,
.car-image__placeholder .block {
  width: 350px;
}
.car-image .block,
.car-image__box .block,
.car-image__placeholder .block {
  text-align: center;
  box-sizing: border-box;
  vertical-align: top;
}
.car-image .demonstration,
.car-image__box .demonstration,
.car-image__placeholder .demonstration {
  display: block;
  color: #8492a6;
  font-size: 14px;
  margin-bottom: 20px;
}
.car-image__box .el-image,
.car-image__placeholder .el-image {
  width: 300px;
  height: 200px;
}
.car-image__box .image-slot,
.car-image__placeholder .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}
.car-image__box .image-slot {
  font-size: 30px;
}
.car-image .el-image {
  width: 300px;
  height: 200px;
}
.car-image .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}
.car-image .image-slot {
  font-size: 30px;
}
.page-container-children-content {
  margin: 20px 20px 0px;
}
.page-container-children-content .bd {
  margin-top: 20px;
}
.card {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  font-feature-settings: 'tnum', 'tnum';
  position: relative;
  background: #fff;
  border-radius: 2px;
}
.card-body {
  padding: 20px;
}
.descriptions-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.descriptions-title {
  flex: auto;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 700;
  font-size: 16px;
  line-height: 1.5715;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.descriptions-view {
  width: 100%;
  overflow: hidden;
  border-radius: 2px;
}
.descriptions-row > .descriptions-item,
.descriptions-row > .descriptions-item {
  padding-bottom: 16px;
}
.descriptions-item {
  padding-bottom: 0;
  vertical-align: top;
}
.descriptions-item-container {
  display: flex;
}
.descriptions-item-container .descriptions-item-content,
.descriptions-item-container .descriptions-item-label {
  display: inline-flex;
  align-items: baseline;
}
.descriptions-item-label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5715;
  text-align: start;
}
.descriptions-item-content {
  display: table-cell;
  flex: 1 1;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  line-height: 1.5715;
  word-break: break-word;
  overflow-wrap: break-word;
}
.descriptions-item-label:after {
  content: ':';
  position: relative;
  top: -0.5px;
  margin: 0 8px 0 2px;
}
.descriptions-view table {
  width: 100%;
  table-layout: fixed;
}
.blue {
  color: #fff;

  .sign {
    background: #196fca;
  }

  .vehicleCode {
    background: #196fca;

    .arrowSpan {
      border-color: transparent transparent transparent #196fca;
    }
  }
}

.yellow {
  color: #000;

  .sign {
    background: #ffbc52;
  }

  .vehicleCode {
    background: #ffbc52;

    .arrowSpan {
      border-color: transparent transparent transparent #ffbc52;
    }
  }
}

.black {
  color: #fff;

  .sign {
    background: #4a4a4a;
  }

  .vehicleCode {
    background: #4a4a4a;

    .arrowSpan {
      border-color: transparent transparent transparent #4a4a4a;
    }
  }
}

.white {
  color: #000;

  .sign {
    background: #f3f3f3;
  }

  .vehicleCode {
    background: #f3f3f3;

    .arrowSpan {
      border-color: transparent transparent transparent #f3f3f3;
    }
  }
}

.gradualGreen {
  color: #000;

  .sign {
    background: linear-gradient(to top, #72ec72 20%, #f3fff3 90%);
  }

  .vehicleCode {
    background: linear-gradient(to top, #72ec72 20%, #f3fff3 90%);

    .arrowSpan {
      border-color: transparent transparent transparent #a4f3a4;
    }
  }
}

.yellowGreen {
  color: #000;

  .sign {
    background: #ffbc52;
  }

  .vehicleCode {
    background: #72ec72;

    .arrowSpan {
      border-color: transparent transparent transparent #72ec72;
    }
  }
}

.blueWhite {
  color: #000;

  .sign {
    background: linear-gradient(to top, #509aea 20%, #f3fff3 90%);
  }

  .vehicleCode {
    background: linear-gradient(to top, #509aea 20%, #f3fff3 90%);

    .arrowSpan {
      border-color: transparent transparent transparent #cee8f1;
    }
  }
}
.vehicleCode {
  padding: 2px 10px;
  border-radius: 5px;
}
</style>
