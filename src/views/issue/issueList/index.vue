<template>
  <div class="user">
    <dart-search
      ref="searchForm1"
      label-position="right"
      :model="search"
      :rules="rules"
    >
      <template slot="search-form">
        <dart-search-item label="任务名称" prop="taskId">
          <el-select v-model="search.taskId" placeholder="请选择">
            <el-option
              v-for="item in jobList"
              :key="item.taskId"
              :label="item.taskName"
              :value="item.taskId"
            />
          </el-select>
        </dart-search-item>

        <dart-search-item label="稽核结果类型">
          <el-select
            v-model="search.auditResultStatus"
            placeholder="请选择"
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in auditResultStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </dart-search-item>

        <dart-search-item :is-button="true">
          <el-button
            type="primary"
            size="mini"
            native-type="submit"
            @click="onSearchHandle"
            >搜索</el-button
          >
          <el-button size="mini" @click="onResultHandle">重置</el-button>
          <el-button size="mini" type="primary" @click="exportHandle"
            ><i class="el-icon-download"></i> 稽核结果导出</el-button
          >
        </dart-search-item>
      </template>
    </dart-search>
    <div class="table">
      <el-table
        :data="tableData"
        style="width: 100%"
        :row-style="{ height: '54px' }"
        :cell-style="{ padding: '0px' }"
        :header-row-style="{ height: '54px' }"
        :header-cell-style="{ padding: '0px' }"
        row-key="id"
      >
        <el-table-column prop="carNo" label="稽核车牌" width="100" />
        <el-table-column prop="carColor" label="车牌颜色">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column prop="sysAuditDate" label="稽核时间" width="180" />
        <el-table-column prop="issueDate" label="发行时间" />
        <el-table-column prop="sysAuditDetails" label="稽核结果" />
        <el-table-column prop="auditStatus_str" label="稽核状态" />
        <el-table-column fixed="right" label="操作" width="80">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="onDetailHandle(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="total > search.pageSize" class="pagination">
      <el-pagination
        background
        :current-page="search.pageNum"
        :page-size="search.pageSize"
        layout="prev, pager, next, jumper"
        :total="total"
        @current-change="changePage"
      />
    </div>
  </div>
</template>
<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import { auditResultStatusOptions } from '@/common/const/optionsData'
import { getCarType, getVehicleColor } from '@/common/method/formatOptions'
import { titleCase } from '@/utils/generator'
import { getToken } from '@/utils/auth'

export default {
  components: {
    dartSearch,
    dartSearchItem,
  },
  data() {
    return {
      auditResultStatusOptions: auditResultStatusOptions,
      tableData: [],
      jobList: [],
      rules: {
        taskId: [{ required: true, message: '请选择任务', trigger: 'blur' }],
      },
      ruleForm: {
        isAdmin: '否',
        roleDesc: '',
        roleName: '',
      },
      roleIdList: [],
      total: 0,
      search: {
        auditResultStatus: [],
        taskId: '',
        pageNum: 1,
        pageSize: 10,
      },
      row: {},

      dialogAddVisible: false,
      dialogEditVisible: false,
      permissDialog: false,
      currentTree: [],
    }
  },
  created() {
    this.getAllJobList()
  },
  methods: {
    getCarType,
    getVehicleColor,
    getAllJobList() {
      this.$store
        .dispatch('issue/getAllJobList')
        .then((res) => {
          this.jobList = res
          this.search.taskId = this.jobList[0].taskId || ''
          if (this.search.taskId) {
            this.getIssueList()
          }
        })
        .catch(() => {})
    },
    // 获取列表
    getIssueList() {
      this.$store
        .dispatch('issue/getIssueList', this.search)
        .then((res) => {
          console.log(res)
          this.tableData = res.records
          this.total = res.total
        })
        .catch(() => {})
    },
    // 分页搜素
    changePage(page) {
      this.search.pageNum = page
      this.getIssueList()
    },
    onSearchHandle() {
      this.getIssueList()
    },
    onResultHandle() {
      this.search.pageNum = 1
      this.search.taskId = this.jobList[0].taskId || ''
      this.search.auditResultStatus = []
      if (this.search.taskId) {
        this.getIssueList()
      }
    },
    onDetailHandle(row) {
      console.log(row)
      this.$router.push({
        path: '/issue/detail?ocrAuditId=' + row.ocrAuditId,
      })
    },

    //导出稽核结果
    exportHandle() {
      if (!this.search.taskId) {
        this.$message({
          message: '请先选择任务名称',
          type: 'warning',
        })
        return
      }
      let taskname = this.jobList.filter((item) => {
        if (item.taskId == this.search.taskId) {
          return item
        }
      })
      taskname = taskname[0].taskName
      this.$msgbox({
        title: '提示',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        message: `确认导出任务名称为'${taskname}'的结果吗`,
        callback: (action, instance) => {
          if (action == 'confirm') {
            this.exportResult(taskname)
          }
        },
      })
    },
    //导出稽核结果
    exportResult(taskname) {
      this.startLoading()
      let params = {
        taskId: this.search.taskId,
        taskName: taskname,
        Authorization: getToken(),
      }
      this.$store
        .dispatch('issue/exportIssueResult', params)
        .then((res) => {
          this.endLoading()
          let url =
            process.env.VUE_APP_BASE_API +
            '/issue-web/auditjob/downloadTemplateData?Authorization=Bearer ' +
            getToken() +
            '&taskId=' +
            params.taskId +
            '&taskName=' +
            params.taskName
          window.open(url)
        })
        .catch((err) => {
          this.endLoading()
          this.$message({
            message: err.msg,
            type: 'warning',
          })
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .form {
    ::v-deep.el-dialog {
      width: 620px;
      .el-dialog__body {
        .el-form-item__label {
          width: 120px !important;
        }
        .el-form-item__error {
          left: 20px;
        }
        .el-button {
          width: 100px;
          position: relative;
          margin-left: 20px;
        }
      }
    }
    .textareas {
      width: 300px;
      ::v-deep.el-input__count {
        background-color: transparent;
        bottom: -7px;
      }
    }
  }
  .form_dialog {
    .el-input {
      width: 300px;
    }
  }
  .table {
    margin: 0px 0 10px 0;
  }
  .permissDialog {
    ::v-deep.el-dialog {
      width: 700px;
    }
  }
  ::v-deep.el-table th,
  ::v-deep.el-table td {
    text-align: center;
  }
}
</style>
