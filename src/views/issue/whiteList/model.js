import {
  getVehicleColor,
  getVehicleType,
  getCarType,
} from '@/common/method/formatOptions'

import {
  licenseColorOptionAll,
  vehicleTypeAll,
  customerTypeAll,
  applyChannel,
} from '@/common/const/optionsData.js'
import { convertFormat } from '@/utils'

//转换结果
const bussnObj = {
  '0':'注销',
  '1':'解占',
  '2':'产品转换'
}

//使用标识
const useFlagObj = {
  '0':'未使用',
  '1':'已使用'
}

//产品转换表格
export const productListColoumns = (_this) => {
  return [
    {
      prop: 'vehicleNo',
      width: 120,
      label: '车牌号',
    },
    {
      prop: 'vehicleColor',
      width: 120,
      label: '车牌颜色',
      formatter: (row) => {
        return getVehicleColor(row)
      }
    },
    {
      prop: 'custName',
      width: 120,
      label: '用户名',
    },
    {
      prop: 'custType',
      width: 120,
      label: '用户类型',
      formatter: (row) => {
        return row == 1 ? '单位' : '个人'
      }
    },
    {
      prop: 'custIdNo',
      label: '证件号',
      width: 200,

    },
    {
      prop: 'cardNo',
      label: 'ETC卡号',
      width: 200,

    },
    {
      prop: 'obuNo',
      label: 'OBU号',
      width: 200,
    },
    {
      prop: 'orgId',
      label: '绑定关系',
      formatter: (row) => {
        return _this.queryGroup.payOrgIdObj[row]
      },
      width: 100,
    },
    {
      prop: 'payOrgId',
      label: '代扣渠道',
      formatter: (row) => {
        return _this.queryGroup.payOrgIdObj[row]
      },
      width: 120,
    },
    {
      prop: 'createTime',
      width: 180,
      label: '添加时间',
    },
    {
      prop: 'businessType',
      width: 100,
      label: '业务类型',
      formatter: (row) => {
        return bussnObj[row]
      }
    },
    {
      prop: 'useFlag',
      width: 120,
      label: '名单是否使用',
      formatter: (row) => {
        return useFlagObj[row]
      }
    },

    // {
    //   prop: 'action',
    //   fixed: 'right',
    //   width: 100,
    //   label: '操作'
    // }
  ]
}

//产品转换搜索表单
export const productListForm = (_this) => {
  return [
    {
      type: 'input',
      field: 'vehicleNo',
      label: '车牌号：',
      default: '',
    },
    {
      type: 'select',
      field: 'vehicleColor',
      label: '车牌颜色：',
      placeholder: '车牌颜色',
      options: licenseColorOptionAll
    },
    {
      type: 'input',
      field: 'custName',
      label: '用户名称：',
      default: '',
    },
    {
      type: 'select',
      field: 'custType',
      label: '用户类型：',
      options: customerTypeAll
    },
    {
      type: 'input',
      field: 'custIdNo',
      label: '证件号：',
      default: '',
    },
    {
      type: 'input',
      field: 'cardNo',
      label: 'ETC卡号：',
      default: '',
    },
    {
      type: 'input',
      field: 'obuNo',
      label: 'OBU号：',
      isCollapse: true,
      default: '',
    },
    {
      type: 'select',
      field: 'orgId',
      label: '绑定关系：',
      isCollapse: true,
      options: _this.activateChannelStatus
    },
    {
      type: 'select',
      field: 'payOrgId',
      label: '代扣渠道：',
      isCollapse: true,
      options: _this.activateChannelStatus
    },
    {
      type: 'dateRange',
      field: 'OrderSubmitDate',
      keys: ['insertDateStart', 'insertDateEnd'],
      isCollapse: true,
      label: '添加时间：',
      format:'YYYY-MM-DD',
      // defaultTime:['00:00:00', '00:00:00'],
      default: []
    },
    {
      type: 'select',
      field: 'businessType',
      label: '业务类型：',
      isCollapse: true,
      options: convertFormat(bussnObj)
    },
    {
      type: 'select',
      field: 'useFlag',
      label: '名单是否使用：',
      isCollapse: true,
      options: convertFormat(useFlagObj)
    },
  ]
}
