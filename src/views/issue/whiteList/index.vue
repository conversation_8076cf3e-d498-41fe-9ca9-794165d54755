<template>
  <div class="toll-record">
    <SearchForm
      ref="SearchForm"
      :formConfig="formConfig"
      collapse
      @onSearchHandle="onSearchHandle"
      @onReSetHandle="onReSetHandle"
    >
      <el-button
        type="warning"
        slot="btn"
        size="mini"
        native-type="submit"
        @click="exportHandle"
        >导出</el-button
      >
    </SearchForm>
    <div class="table">
      <my-table
        ref="tableRef"
        v-loading="loading"
        :cloumns="listColoumns"
        :tableData="tableData"
        :total="total"
        :pageSize="pageSize"
        :pageNum="pageNum"
        :hasPagination="false"
        @changeTableData="changeTableData"
      >
        <!-- 操作 -->
        <template slot="action" slot-scope="{ scope }">
          <div class="operator-td">
            <el-button type="text" @click="handelRow(scope, 'view')"
              >查看</el-button
            >
            <el-button type="text" @click="handelRow(scope, 'edit')"
              >处理</el-button
            >
          </div>
        </template>
      </my-table>
    </div>
    <div class="pagination" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      ></el-pagination>
    </div>

    <!-- <DartSlide
      :visible.sync="slideVisible"
      title="订单详情"
      v-transfer-dom
      width="90%"
      :maskClosable="true"
      @close="modelClose"
    >
      <Detail
        v-if="slideVisible"
        :orderId="orderId"
        :queryGroup="queryGroup"
        :slideVisible="slideVisible"
        :isView="isView"
        @refreshList="getTableData"
        @closeDartSlide="closeDartSlide"
      ></Detail>
    </DartSlide> -->
  </div>
</template>

<script>
import MyTable from '@/components/my-table'
import SearchForm from '@/components/my-table/search-form.vue'
import DartSlide from '@/components/dart/Slide/index.vue'
import {
  productListColoumns,
  productListForm,
  porductQueryExportConfig
} from './model'
import tableListMixin from '@/components/my-table/hook/tableMix'
import {
  getWhitelist,
  exportChannelCancelWhiteList
} from '@/api/workordermanage'
import { mapGetters, mapActions } from 'vuex'
import { convertFormat } from '@/utils'
import { decode } from 'js-base64'

export default {
  components: {
    MyTable,
    SearchForm,
    DartSlide
  },
  mixins: [tableListMixin],
  data() {
    return {
      tableData: [],
      api: getWhitelist,
      pageSizeKey: 'pageSize',
      pageNumKey: 'pageIndex',
      isView: false,
      slideVisible: false,
      orderId: '',
      queryGroup: {
        orgIdObj: {},
        payOrgIdObj: {}
      },
      timeField: ['OrderSubmitDate', 'orderUpdateDate']
    }
  },
  computed: {
    ...mapGetters(['applyChannelStatus', 'activateChannelStatus']),
    listColoumns() {
      return productListColoumns(this)
    },
    formConfig() {
      return productListForm(this)
    }
  },
  methods: {
    ...mapActions(['setApplyChannelStatus', 'setActivateChannelStatus']),
    async handelRow(row, type) {
      this.orderId = row.orderId
      if (type == 'view') {
        this.isView = true
      } else {
        this.isView = false
        await this.lockOrder()
      }
      this.slideVisible = true
    },
    closeDartSlide() {
      this.slideVisible = false
    },

    exportHandle() {
      let query = JSON.parse(JSON.stringify(this.$refs.SearchForm.search))
      let params = {
        [this.pageSizeKey]: this.pageSize,
        [this.pageNumKey]: this.pageNum,
        ...query
      }
      let fileObj = {
        fileName: '合作方车辆注销白名单.xlsx'
      }
      this.exportFile(params, exportChannelCancelWhiteList, fileObj)
    },
    handleSizeChange(val) {
      this.changeTableData(val, this.pageNum)
    },
    handleCurrentChange(val) {
      this.changeTableData(this.pageSize, val)
    },
    getActivateStatusOptions() {
      this.$request({
        url: this.$interfaces.activateBindChannel,
        method: 'post',
        data: {}
      })
        .then(res => {
          console.log(res, '<<---------res')
          if (res.code == 200) {
            let channelOPtions = res.data.BINDING_BANK_TYPE.map(item => {
              this.queryGroup.payOrgIdObj[item.fieldValue] =
                item.fieldNameDisplay
              return {
                value: item.fieldValue,
                label: item.fieldNameDisplay
              }
            })
            channelOPtions.unshift({ value: '', label: '全部' })
            this.setActivateChannelStatus(channelOPtions)
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    },
    getStatusOptions() {
      this.$request({
        url: this.$interfaces.searchInit,
        method: 'post',
        data: {}
      })
        .then(res => {
          if (res.code == 200) {
            let channelOPtions = res.data.channels.map(item => {
              this.queryGroup.orgIdObj[item.vcOrgId] = item.vcChannelName
              return {
                value: item.vcOrgId,
                label: item.vcChannelName
              }
            })
            channelOPtions.unshift({ value: '', label: '全部' })

            this.setApplyChannelStatus(channelOPtions)
          } else {
            this.$message({
              message: res.msg,
              type: 'error'
            })
          }
        })
        .catch(() => {})
    },
    async queryInit() {
      this.getActivateStatusOptions()
      // this.getStatusOptions()
      //  this.setApplyChannelStatus()
    }
  },
  created() {
    this.queryInit()
    this.getTableData()
  }
}
</script>

<style lang="scss" scoped>
.toll-record {
  height: 100%;
  position: relative;
  // padding: 20px;
  flex-flow: column;
  display: flex;
  .pagination {
    margin: 10px 0;
  }
}
</style>