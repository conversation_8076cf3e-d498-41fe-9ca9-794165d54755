<template>
  <div class="user">
    <reportForms
      v-for="item in reportList"
      :key="item.id"
      :formConfig="item.formConfig"
      :formTitle="item.title"
      :name="item.name"
      :rules="item.rules"
      @onSearchHandle="onSearchHandle"
      :btnSpan="item.btnSpan || 3"
    ></reportForms>

    <div class="list" :style="`height:${tableHeight}px`">
      <img src="@/image/bg-left.png" />
    </div>
  </div>
</template>

<script>
import reportMixin from '@/components/reportForms/hook/report-mixins'
import reportForms from '@/components/reportForms'
import moment from 'moment'
import { reportListFn } from './model'
export default {
  components: {
    reportForms
  },
  mixins: [reportMixin],
  data() {
    return {
      loading: false,
      tableHeight: 0
    }
  },
  computed: {
    reportList() {
      return reportListFn(this)
    }
  },
  methods: {
    beforeSearchHandle(fromData) {
      let check = true
      if (moment(fromData.stop_start).isAfter(fromData.stop_end)) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段异常'
        })
        check = false
      }
      if (moment(fromData.stop_end).diff(moment(fromData.stop_start), 'months') > 2) {
        this.$msgbox({
          title: '提示',
          showClose: true,
          type: 'error',
          customClass: 'my_msgBox singelBtn',
          dangerouslyUseHTMLString: true,
          message: '统计日期时间段不得超过三个月'
        })
        check = false
      }
      return check
    }
  }
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
  .list {
    width: 100%;
    text-align: center;
    img {
      width: 50%;
    }
  }
}
</style>