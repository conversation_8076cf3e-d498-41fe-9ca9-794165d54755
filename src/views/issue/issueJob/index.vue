<template>
  <div class="user">
    <div class="top_btn">
      <div class="search">
        <div class="search-list">
          <div class="conditions">
            <el-button type="primary"
                       class="btn"
                       size="mini"
                       @click="dialogAddVisible = true">添加任务</el-button>
            <el-button size="mini"
                       type="warning"
                       @click="exportExcel"><i class="el-icon-download"></i> EXCEL模板下载</el-button>
            <el-button size="mini"
                       type="primary"
                       @click="importDialogVisible = true"><i class="el-icon-upload"></i> 稽核任务导入</el-button>

          </div>
        </div>
      </div>
    </div>
    <div class="table">
      <el-table v-loading="loading"
                :data="tableData"
                :align="center"
                :header-align="center"
                style="width: 100%;margin-bottom: 20px;"
                :row-style="{ height: '54px' }"
                :cell-style="{ padding: '0px' }"
                :header-row-style="{ height: '54px' }"
                :header-cell-style="{ padding: '0px' }"
                row-key="id">
        <el-table-column prop="taskId"
                         label="任务ID" />
        <el-table-column prop="taskName"
                         label="任务名称"
                         width="160" />
        <el-table-column prop="carNo"
                         label="稽核车牌" />
        <el-table-column prop="carColor"
                         label="车牌颜色">
          <template slot-scope="scope">
            {{ getVehicleColor(scope.row.carColor) }}
          </template>
        </el-table-column>
        <el-table-column prop="carType"
                         label="车辆类型">
          <template slot-scope="scope">
            {{ getCarType(scope.row.carType) }}
          </template>
        </el-table-column>
        <el-table-column prop="startTime"
                         label="稽核开始时间"
                         width="160" />
        <el-table-column prop="endTime"
                         label="稽核结束时间"
                         width="160" />
        <el-table-column prop="auditResultStatus_str"
                         label="稽查结果状态"
                         width="140" />
        <el-table-column prop="status_str"
                         label="任务状态"
                         width="140" />
        <el-table-column prop="operator"
                         label="操作人" />

        <el-table-column fixed="right"
                         label="操作"
                         width="100">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status == 2"
                       type="text"
                       size="small"
                       @click="openJobHandle(scope.row)">启动任务</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="total > search.pageSize"
         class="pagination">
      <el-pagination background
                     :current-page="search.pageNum"
                     :page-size="search.pageSize"
                     layout="prev, pager, next, jumper"
                     :total="total"
                     @current-change="changePage" />
    </div>
    <addEle :visible.sync="dialogAddVisible"
            @on-submit="onUpdateList" />
    <import-dialog :visible.sync="importDialogVisible"
                   @uploadSuccess="uploadSuccess">
    </import-dialog>
  </div>
</template>
<script>
import addEle from './add.vue'
import { getCarType, getVehicleColor } from '@/common/method/formatOptions'
import importDialog from './importDialog'
import { decode } from 'js-base64'
import axios from 'axios'
import { getToken } from '@/utils/auth'

export default {
  components: {
    addEle,
    importDialog,
  },
  data() {
    return {
      center: 'center',
      loading: false,
      tableData: [],
      ruleForm: {
        isAdmin: '否',
        roleDesc: '',
        roleName: '',
      },
      roleIdList: [],
      total: 0,
      search: {
        pageNum: 1,
        pageSize: 20,
      },
      row: {},
      dialogAddVisible: false,
      currentTree: [],
      importDialogVisible: false,
    }
  },
  created() {
    this.getJobList()
  },
  methods: {
    getCarType,
    getVehicleColor,
    // 更新列表数据
    onUpdateList() {
      this.search.pageNum = 1
      this.getJobList()
    },
    // 获取列表
    getJobList() {
      this.$store
        .dispatch('issue/getJobList', this.search)
        .then((res) => {
          this.tableData = res.records
          this.total = res.total
        })
        .catch(() => {})
    },
    // 启动任务
    openJobHandle(row) {
      if (this.loading) return
      this.loading = true
      this.$store
        .dispatch('issue/openJob', row)
        .then((res) => {
          this.loading = false
          if (res.code === 200) {
            this.$message({
              message: '启动成功',
              type: 'success',
            })
            this.getJobList()
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 分页搜素
    changePage(page) {
      this.search.pageNum = page
      this.getJobList()
    },
    uploadSuccess() {
      this.importDialogVisible = false
      this.getJobList()
    },

    //导出excel模板
    exportExcel() {
      this.$store
        .dispatch('issue/exportExcelModel')
        .then((res) => {
          this.endLoading()
          let url =
            process.env.VUE_APP_BASE_API +
            '/issue-web/auditjob/downloadTemplate?Authorization=Bearer ' +
            getToken()
          window.open(url)
        })
        .catch(() => {
          this.endLoading()
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.user {
  padding: 20px;
  .form {
    ::v-deep.el-dialog {
      width: 620px;
      .el-dialog__body {
        .el-form-item__label {
          width: 120px !important;
        }
        .el-form-item__error {
          left: 20px;
        }
        .el-button {
          width: 100px;
          position: relative;
          margin-left: 20px;
        }
      }
    }
    .textareas {
      width: 300px;
      ::v-deep.el-input__count {
        background-color: transparent;
        bottom: -7px;
      }
    }
  }
  .form_dialog {
    .el-input {
      width: 300px;
    }
  }
  .table {
    margin: 20px 0;
  }
  ::v-deep.el-table th,
  ::v-deep.el-table td {
    text-align: center;
  }
  .permissDialog {
    ::v-deep.el-dialog {
      width: 700px;
    }
  }
}
</style>
