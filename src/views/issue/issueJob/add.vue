<template>
  <div class="form">
    <el-dialog title="任务配置"
               :visible.sync="dialogFormVisible"
               :close-on-click-modal="false"
               :center="true"
               custom-class="form_dialog">
      <el-form ref="ruleForm"
               :model="ruleForm"
               :rules="rules"
               label-width="120px"
               class="demo-ruleForm">
        <el-form-item label="任务名称"
                      prop="taskName">
          <el-input v-model="ruleForm.taskName" />
        </el-form-item>
        <el-form-item label="车牌号码"
                      prop="carNo">
          <el-input placeholder="请输入车牌号"
                    maxLength="11"
                    v-model="vehicleProvinceEnd"
                    class="input-with-select">
            <el-select v-model="vehicleProvincePre"
                       style="width: 80px;"
                       slot="prepend"
                       placeholder="请选择">
              <el-option v-for="(value,key) in provinces"
                         :key="key"
                         :label="key"
                         :value="value"></el-option>
            </el-select>
          </el-input>
        </el-form-item>
        <el-form-item label="车牌颜色"
                      prop="carColor">
          <el-select v-model="ruleForm.carColor"
                     placeholder="请选择车牌颜色"
                     clearable
                     style="width:100%">
            <el-option v-for="item in licenseColorOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车辆类型"
                      prop="carType">
          <el-select v-model="ruleForm.carType"
                     placeholder="请选择车辆类型"
                     clearable
                     style="width:100%">
            <el-option v-for="item in vehicleCatgoryType"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="稽核时间"
                      prop="dateRange">
          <dart-date-range v-model="ruleForm.dateRange"
                           type="datetime"
                           value-format="timestamp"
                           :default-time="defaultTime"></dart-date-range>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="submitForm('ruleForm')">提交</el-button>
          <el-button @click="dialogFormVisible = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  licenseColorOption,
  provinces,
  vehicleCatgoryType
} from '@/common/const/optionsData'
import DartDateRange from '@/components/DateRange/date-range'
var moment = require('moment')
export default {
  components: {
    DartDateRange
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      licenseColorOption: licenseColorOption,
      provinces: provinces,
      vehicleCatgoryType: vehicleCatgoryType,
      dialogFormVisible: false,
      pattern: '^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z0-9]{1}([京津沪渝桂蒙宁新藏冀晋辽吉黑苏浙皖赣闽鲁粤鄂湘豫川云贵陕甘青琼])?[A-NP-Z0-9]{1}[A-NP-Z0-9]{3}[A-NP-Z0-9挂学警港澳领试超外]{1}([A-NP-Z0-9外])?)|^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z0-9]{5}应急)|^(应急[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z0-9]{4})$|([A-Z0-9]{7})$',
      pickerOptions: {
        disabledDate(date) {
          const time = Number(
            moment(moment(new Date()).subtract(30, 'days')).format('x')
          )
          return date && (date.valueOf() > Date.now() || date.valueOf() < time)
        }
      },
      ruleForm: {
        taskName: '', // 任务名称
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        carNo: '桂', // 车牌号码
        carColor: '', // 车牌颜色
        carType: '', // 车辆类型
        dateRange: [] // 稽核时间
      },
      defaultTime: [],
      rules: {
        taskName: [
          { required: true, message: '任务名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    /* eslint-disable */
    vehicleProvincePre: {
      get: function () {
        if (this.ruleForm.carNo !== undefined) {
          let prefix = this.ruleForm.carNo.substr(0, 1)
          if (this.provinces[prefix]) {
            return prefix
          }
          return ''
        }
      },
      set: function (value) {
        if (this.ruleForm.carNo !== undefined)
          this.ruleForm.carNo = value + this.ruleForm.carNo.substr(1)
      }
    },
    vehicleProvinceEnd: {
      get() {
        if (this.ruleForm.carNo !== undefined)
          return this.ruleForm.carNo.substr(1)
      },
      set(value) {
        if (this.ruleForm.carNo !== undefined)
          this.ruleForm.carNo = (
            this.ruleForm.carNo.substr(0, 1) + value
          ).toUpperCase()
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogFormVisible = val
    },
    dialogFormVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  created() {
    let startTime = moment(moment(new Date()).subtract(30, 'days')).format('x')
    let endTime = moment(new Date()).format('x')
    this.ruleForm.dateRange = [Number(startTime), Number(endTime)]
  },
  methods: {
    // 车牌省份切换事件
    provinceChange(value) {
      // console.log("provinceChange:" + value);
    },
    // 表单提交
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.verifyHandle()) {
            this.addHandle();
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    verifyHandle() {
      const pattern = /^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z0-9]{1}[A-Z0-9]{1}([京津沪渝桂蒙宁新藏冀晋辽吉黑苏浙皖赣闽鲁粤鄂湘豫川云贵陕甘青琼])?[A-NP-Z0-9]{1}[A-NP-Z0-9]{3}[A-NP-Z0-9挂学警港澳领试超外]{1}([A-NP-Z0-9外])?)|^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z0-9]{5}应急)|^(应急[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z0-9]{4})$|([A-Z0-9]{7})$/
      if (this.ruleForm.carNo.length == 1 && this.ruleForm.dateRange.length === 0) {
        this.$message({
          message: '请选择车牌和稽核时间必填其一',
          type: 'error'
        });
        return;
      }
      if ((this.ruleForm.carNo && this.ruleForm.carNo.length > 1) || this.ruleForm.carColor) {
        if (!pattern.test(this.ruleForm.carNo)) {
          this.$message({
            message: '请输入正确车牌号码',
            type: 'error'
          })
          return;
        }
        if (!this.ruleForm.carColor) {
          this.$message({
            message: '请选择车牌颜色',
            type: 'error'
          })
          return;
        }
        return true;
      }
      if (!(this.ruleForm.dateRange[0] && this.ruleForm.dateRange[1])) {
        this.$message({
          message: '开始时间和结束时间必填',
          type: 'error'
        });
        return;
      }
      if (moment(this.ruleForm.dateRange[1]).diff(moment(this.ruleForm.dateRange[0]), 'days') > 30) {
        this.$message({
          message: '时间跨度需要小于一个月',
          type: 'error'
        });
        return
      }
      return true;
    },
    addHandle() {
      let params = JSON.parse(JSON.stringify(this.ruleForm))
      if (!!params.dateRange[0] && !!params.dateRange[1]) {
        params.startTime = !!params.dateRange[0] ? moment(params.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        ) : '';
        params.endTime = !!params.dateRange[1] ? moment(params.dateRange[1]).format('YYYY-MM-DD HH:mm:ss') : ''
      }

      delete params.dateRange;
      params.carNo = params.carNo.length === 1 ? '' : params.carNo;
      if (!params.carNo) {
        params.carType = '';
        params.carColor = '';
      }
      this.$store
        .dispatch('issue/addJob', params)
        .then((res) => {
          if (res.code === 200) {
            this.$message({
              message: '添加成功',
              type: 'success'
            })
            this.dialogFormVisible = false
            this.$emit('on-submit')
          }
        })
        .catch(() => { })
    }
  }
}
</script>
<style lang="sass"></style>
