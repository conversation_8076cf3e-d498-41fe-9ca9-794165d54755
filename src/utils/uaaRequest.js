import axios from 'axios'
import { Notification, MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_UAA_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent
    if (config.requestType === 'login') {
      // 登录请求，使用特殊的Authorization
      const str = config.auth.username + ':' + config.auth.password
      const base64Str = window.btoa(str)
      console.log('编码后的base64Str', base64Str)
      config.headers['Authorization'] = 'Bearer ' + base64Str
    } else if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getToken()
    }
    return config
  },
  (error) => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  (response) => {
    const code = response.data.code | response.data.status
    if (code === 401) {
      reLogin()
    } else if (code === 400 || code === 404) {
      Message({
        message: response.data.msg,
        type: 'error',
        duration: 5 * 1000
      })
    } else if (code !== 200 && code !== undefined && code !== 0) {
      Notification.error({
        title: response.data.msg
      })
      return Promise.reject('error')
    } else {
      // console.log('返回拦截', response)
      // if (response.data.data)
      return response.data
    }
  },
  (error) => {
    console.log('err ' + error) // for debug

    let message = error.message

    if (error.response && error.response.data) {
      message = error.response.data.msg || message
    }
    if (message.includes('token') || message.includes('token解析异常')) {
      reLogin()
    } else if (message.includes('Network Error') || message.includes('404')) {
      Message({
        message: message,
        type: 'error',
        duration: 5 * 1000
      })
    } else {
      Message({
        message: message,
        type: 'error',
        duration: 5 * 1000
      })
    }
    return Promise.reject(error)
  }
)

function reLogin() {
  // store.dispatch('user/resetToken')
  MessageBox.confirm(
    '登录状态失效，您可以继续留在该页面，或者重新登录',
    '系统提示',
    {
      confirmButtonText: '重新登录',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    store.dispatch('user/resetToken').then(() => {
      location.reload()
    })
    // store.dispatch('user/LogOut').then(() => {
    //   store.dispatch('user/resetToken')
    //   location.reload() // 为了重新实例化vue-router对象 避免bug
    // })
  })
}
export default service
