var CryptoJS = require("crypto-js");
let sortObj = function(paramObj) {
	const unordered = JSON.parse(JSON.stringify(paramObj));
	const ordered = {};
	Object.keys(unordered).sort().forEach((key) => {
		ordered[key] = unordered[key];
	});
	return ordered;
};
let formateObjToParamStr = function(paramObj) {
	const sdata = [];
	for (let key in paramObj) {
		if (paramObj[key]) {
			sdata.push(`${key}=${paramObj[key]}`);
		}
	}
	return sdata.join('&');
};
let verifyMd5Sign = function(param, md5Key) {
	let data = param;
	let sign = data.sign;
	delete data.sign;
	let md5Str = '';
	try {
		let obj = sortObj(data);
		obj.key = md5Key;
		let str = formateObjToParamStr(obj);
		md5Str = CryptoJS.MD5(str).toString();
		md5Str = md5Str.toLocaleUpperCase();
	} catch (e) {
		console.log(e);

	}
	return md5Str === sign
}
let md5Sign = function(params, md5Key) {
	let md5Str = '';
	if (!params) return md5Str;

	let data = sortObj(params);
	data.key = md5Key;
	const str = formateObjToParamStr(data);
	try {

		md5Str = CryptoJS.MD5(str).toString();

		md5Str = md5Str.toLocaleUpperCase();
	} catch (e) {

	}
	return md5Str
}
export {
	verifyMd5Sign,
	md5Sign
}
