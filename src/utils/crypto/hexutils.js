var DIGITS_LOWER = new Array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f');
var DIGITS_UPPER = new Array('0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F');
/**加密
 * @param data
 * */
export function Hex_encodeHex(data) {
    return Hex_encodeHex_Up(data, true);
}
/**解密
 * @param data
 * */
export function Hex_decodeHex(data) {
    return Hex_decodeHex_Up(data, true);
}

function Hex_decodeHex_Up(data, toLowerCase) {
    if (toLowerCase) {
        return toAscii(data, DIGITS_LOWER);
    } else {
        return toAscii(data, DIGITS_UPPER);
    }
}


function Hex_encodeHex_Up(data, toLowerCase) {
    if (toLowerCase) {
        return encodeHex(data, DIGITS_LOWER);
    } else {
        return encodeHex(data, DIGITS_UPPER);
    }
}
function encodeHex(data, toDigits) {
    var dataByte = stringToByte(data);
    var l = dataByte.length;
    var outChar = new Array();
    for (var i = 0, j = 0; i < l; i++) {
        outChar[j++] = toDigits[(0xF0 & dataByte[i]) >>> 4];
        outChar[j++] = toDigits[0x0F & dataByte[i]];
    }
    return arrAyToString(outChar);
}
function decodeHex(data, toDigits) {
    var charArray = stringToCharArray(data);
    var len = charArray.length;
    if ((len & 0x01) != 0) {
        throw new DOMException("Odd number of characters.");
    }
    var byteArray = new Array();
    for (var i = 0, j = 0; j < len; i++) {
        var f = toDigit(charArray[j], j) << 4;
        j++;
        f = f | toDigit(charArray[j], j);
        j++;
        byteArray[i] = String.fromCharCode((f & 0xFF));
    }
    return byteToString(byteArray);
}
function toDigit(char, index) {
    var radix = 16;
    var charInt = char.charCodeAt(0);
    return charInt * radix;
}
function arrAyToString(array) {
    var str = "";
    for (var i = 0; i < array.length; i++) {
        str = str + array[i];
    }
    return str;
}
function stringToCharArray(str) {
    var charArray = new Array();
    for (var i = 0; i < str.length; i++) {
        var c = str.charCodeAt(i);
        charArray.push(c);
    }
    return charArray;
}
function stringToByte(str) {
    var bytes = new Array();
    var len, c;
    len = str.length;
    for (var i = 0; i < len; i++) {
        c = str.charCodeAt(i);
        if (c >= 0x010000 && c <= 0x10FFFF) {
            bytes.push(((c >> 18) & 0x07) | 0xF0);
            bytes.push(((c >> 12) & 0x3F) | 0x80);
            bytes.push(((c >> 6) & 0x3F) | 0x80);
            bytes.push((c & 0x3F) | 0x80);
        } else if (c >= 0x000800 && c <= 0x00FFFF) {
            bytes.push(((c >> 12) & 0x0F) | 0xE0);
            bytes.push(((c >> 6) & 0x3F) | 0x80);
            bytes.push((c & 0x3F) | 0x80);
        } else if (c >= 0x000080 && c <= 0x0007FF) {
            bytes.push(((c >> 6) & 0x1F) | 0xC0);
            bytes.push((c & 0x3F) | 0x80);
        } else {
            bytes.push(c & 0xFF);
        }
    }
    return bytes;
}


function byteToString(arr) {
    if (typeof arr === 'string') {
        return arr;
    }
    var str = '',
        _arr = arr;
    for (var i = 0; i < _arr.length; i++) {
        var one = _arr[i].toString(2),
            v = one.match(/^1+?(?=0)/);
        if (v && one.length == 8) {
            var bytesLength = v[0].length;
            var store = _arr[i].toString(2).slice(7 - bytesLength);
            for (var st = 1; st < bytesLength; st++) {
                store += _arr[st + i].toString(2).slice(2);
            }
            str += String.fromCharCode(parseInt(store, 2));
            i += bytesLength - 1;
        } else {
            str += String.fromCharCode(_arr[i]);
        }
    }
    return str;
}
var symbols = " !\"#$%&'()*+,-./0123456789:;<=>?@";
var loAZ = "abcdefghijklmnopqrstuvwxyz";
symbols += loAZ.toUpperCase();
symbols += "[\\]^_`";
symbols += loAZ;
symbols += "{|}~";
//Hex to ASCII
function toAscii(str) {
    var valueStr = str.toLowerCase();
    var hex = "0123456789abcdef";
    var text = "";
    for (var i = 0; i < valueStr.length; i = i + 2) {
        var char1 = valueStr.charAt(i);
        if (char1 == ':') {
            i++;
            char1 = valueStr.charAt(i);
        }
        var char2 = valueStr.charAt(i + 1);
        var num1 = hex.indexOf(char1);
        var num2 = hex.indexOf(char2);
        var value = num1 << 4;
        value = value | num2;

        var valueInt = parseInt(value);
        var symbolIndex = valueInt - 32;
        var ch = '?';
        if (symbolIndex >= 0 && value <= 126) {
            ch = symbols.charAt(symbolIndex)
        }
        text += ch;
    }
    return text;
}
//ASCII to Hex
function toHex(str) {
    var valueStr = str;
    var hexChars = "0123456789abcdef";
    var text = "";
    for (i = 0; i < valueStr.length; i++) {
        var oneChar = valueStr.charAt(i);
        var asciiValue = symbols.indexOf(oneChar) + 32;
        var index1 = asciiValue % 16;
        var index2 = (asciiValue - index1) / 16;
        if (text != "") text += ":";
        text += hexChars.charAt(index2);
        text += hexChars.charAt(index1);
    }
    return text;
}

