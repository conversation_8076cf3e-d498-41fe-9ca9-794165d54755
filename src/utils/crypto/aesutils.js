import CryptoJS from "./crypto-js"
// import * as MD5 from 'node-forge'
// import {Hex_encodeHex,Hex_decodeHex} from './hexutils'
/**
 * 编码
 */
// export function Base64Coding(word){
//     var str = CryptoJS.enc.Utf8.parse(word);
//     return CryptoJS.enc.Base64.stringify(str);
// }
//
// /**
//  * 解码
// */
// export function Base64Decode(word){
//     var words = CryptoJS.enc.Base64.parse(word);
//     //var bass = new Base64();
//     //return bass.decode(word);
//     return words.toString(CryptoJS.enc.Utf8);
// }
// /**
//  * md5 加密
// */
// export function md5encrypt(pwd){
//     var md = MD5.md.md5.create();
//     md.update(pwd);
//     return md.digest().toHex();
// }
//
/**
 * 加密（依赖aes.js）
 * @param word 加密的字符串
 * @returns {*}
 */
export function encrypt(word, encryptKey) {
  if (encryptKey.length > 16) {
    encryptKey = encryptKey.substring(0, 16)
  }

  // console.log("请求明文：" + word);
  var key = CryptoJS.enc.Utf8.parse(encryptKey)
  var srcs = CryptoJS.enc.Utf8.parse(word)
  var encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}

/**
 * 解密
 * @param word 解密的字符串
 * @returns {*}
 */
export function decrypt(word, encryptKey) {
  if (encryptKey.length > 16) {
    encryptKey = encryptKey.substring(0, 16)
  }
  // console.log("解密密文：" + word);
  // console.log("解密秘钥：" + encryptKey)
  // word = Base64Decode(word)
  var key = CryptoJS.enc.Utf8.parse(encryptKey)
  var decrypt = CryptoJS.AES.decrypt(word, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  var minWen = CryptoJS.enc.Utf8.stringify(decrypt).toString()
  // minWen = Hex_decodeHex(minWen);
  // console.log("返回明文：" + minWen);
  return minWen // Base64Decode(minWen);
}
