//加载目录的vue文件
const srcFiles = require.context('@', true, /.vue$/)
const globalModulesFiles = {
  "@": srcFiles
}

//原生js模仿jquery的closest方法
Element.prototype.closestReplenish = function (tar) {
  var ele = this
  var elArr = (function () {
    if (tar instanceof HTMLElement) return [tar]
    try {
      tar = document.querySelectorAll(tar)
    } catch (e) { } finally {
      var type = Object.prototype.toString.call(tar).slice(8, -1)
      if (['NodeList', 'HTMLCollection', 'Array'].indexOf(type) > -1) return [].slice.call(tar)
    }
  })()
  do {
    if (elArr.indexOf(ele) > -1) return ele
    ele = ele.parentElement
  } while (ele !== null)
  return null
}

/**
 * 获取模块
 * @param {*} path
 */
function getModulesFiles (path) {
  let moduleComp = null
  for (let keyWord in globalModulesFiles) {
    let jmf = globalModulesFiles[keyWord]
    jmf.keys().forEach((item, index) => {
      let p = ''
      //@代表是自己的src，则原样处理
      if (keyWord === '@') {
        p = '@/' + item.replace('.vue', '').replace('./', '')
      } else {
        p = '/' + keyWord + '/' + item.replace('.vue', '').replace('./', '')
      }
      if (path.replace('.vue', '') === p) {
        moduleComp = jmf(item).default
      }
    })
  }
  if (moduleComp == null) {
    //console.error('未发现模块', path)
  }
  return moduleComp
}

/**
 * 两个对象合并
 * @param {*} target
 * @param {*} source
 */
function objectMerge (target, source) {
  /* Merges two  objects,
     giving the last one precedence */

  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  for (const property in source) {
    if (source.hasOwnProperty(property)) {
      const sourceProperty = source[property]
      if (typeof sourceProperty === 'object') {
        target[property] = objectMerge(target[property], sourceProperty)
        continue
      }
      target[property] = sourceProperty
    }
  }
  return target
}

export default {
  install (Vue, options) {
    //判断是否为dialog打开的
    Vue.prototype.$isDialog = function () {
      if (this.$el && typeof (eval(this.$el.closestReplenish)) === 'function') {
        if (this.$el.closestReplenish('.el-dialog') || this.$el.closestReplenish('.vl-notify')) {
          return true
        }
      }
      return false
    }
    // 判断是否为OpenPage打开的dialog
    Vue.prototype.$isDialogByOpenPage = function () {
      if (Object.hasOwnProperty.call(this, 'getParam')) {
        return this.getParam('_openType') === 'dialog'
      }
      return false
    }

    /**
     * 定义一个全局的页面跳转组件
     * 需求：动态判断是否通过路由跳转还是对话框的形式打开
     * 判断条件：如果当前操作的页面是dialog，那么跳转的页面也用dialog，否则使用路由跳转的形式
     * @param path 路由地址或vue文件地址
     * @param title 标题
     * @param params 参数，采用{}对象形式
     * @param config 自定义的配置
     * @param openType 页面打开的类型（dialog,router），默认根据页面当前打开的方式自动判断
     */
    Vue.prototype.$openPage = function (path, title, params = {}, config = {}, openType = 'dialog') {
      if (!params) {
        params = {}
      }
      if (!config) {
        config = {}
      }
      if (path.lastIndexOf('.') !== -1) {
        path = path.substr(0, path.lastIndexOf('.'))
      }
      params._openType = openType

      if (this.$isDialog() || openType === 'dialog') {
        //如果当前操作的页面是dialog，那么跳转的页面也用dialog
        const content = {
          content: getModulesFiles(path), //传递的组件对象
          parent: this, //当前的vue对象
          data: params //props
        }

        //合并自定义配置的config对象，配置参考地址：https://github.com/zuoyanart/vue-layer#readme
        const layerConfig = objectMerge({
          shadeClose: true,
          shade: true,
          area: ['60%', String(window.innerHeight - 200)],
          title: title
        }, config)
        layerConfig.content = content
        return this.$layer.iframe(layerConfig)
      } else {
        const newParams = {
          _: new Date().getTime()
        }
        for (const p in params) {
          if (typeof params[p] !== 'function') {
            newParams[p] = params[p]
          }
        }
        this.$router.push({
          path: path,
          query: objectMerge({
            title: title
          }, newParams)
        })
      }
    }
  }
}
