/**
 * 封装的一些公用的方法
 */
export default {
  props: {
    layerid: null,   //自动添加一个key为layerid的值， 该值为创建层的id， 可以直接使用
    /**
     * 该方法会自动添加一个key为lydata的值， 该值为data的浅拷贝， 
     * 当iframe要更改父窗口传递的数据的时候，可以直接使用lydata来修改，对于表单使用非常方便
     */
    lydata: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  methods: {
    /**
     * 获取参数值也要封装一个mixins方法
     * 如果是从路由跳转过来的，则获取参数从router的query里面取
        如果是dialog打开的，则获取参数从props里面取
     * @param {参数名} paramName 
       @param {取值类型，dialog或router，默认根据页面打开的方式自动判断} openType
     */
    getParam (paramName, openType) {
      if (this.layerid || openType === 'dialog') {
        return this.lydata[paramName]
      } else {
        return this.$route.query[paramName]
      }
    },
    /**
     * 关闭layer对话框
     * @param isCallback 是否执行上一个页面传递过来的callback方法
     * @param {layerid，可不传，默认就是当前打开的dialog的layerid} layerid 
     */
    closeDialog (isCallback, p_layerid) {
      let lid = this.layerid
      if (p_layerid) {
        lid = p_layerid
      }
      this.$layer.close(lid);
      //如果有关闭对话框的回调，还需要触发回调方法
      if (isCallback) {
        let callback = this.lydata.callback
        if (callback) {
          callback.call(this)
        }
      }
    }
  }
}