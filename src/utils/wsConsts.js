/* jshint esversion:9 */
/**websocket请求常量定义 */
const wsConsts = {
    /**websocket服务地址 */
    // url: 'ws://10.26.180.146:15479',
        url:'ws://127.0.0.1:15479',
   // url:'ws://192.168.10.156:15479',

    // url: 'ws://127.0.0.1:15479',
    camUrl: 'ws://127.0.0.1:15480',
    // camUrl: 'ws://192.168.10.156:15480',
    // url:'ws://192.168.10.176:15479',

    /**心跳间隔 */
    hbInterval: 10000,
    /**websocket接口映射列表 */
    methods: {
        /** 状态心跳 */
        heartbeat: 'gxetc.desktop.status',
        /** CPU卡号读取 */
        readCardId: 'gxetc.desktop.read-card-id',
        /** 公务卡信息读取 */
        readOfficialCard: 'gxetc.desktop.read-official-card',
        /** CPU信息读取 */
        readCpuInfo: 'gxetc.desktop.read-cpu-info',
        /** 读取卡内流水信息 */
        readCardJour: 'gxetc.desktop.read-card-jour',
        /** 卡片发行 */
        cpuIssue: 'gxetc.desktop.cpu-issue',
        /** 卡片注销 */
        cpuCancel: 'gxetc.desktop.cpu-cancel',
        /** 卡片解锁 */
        cpuUnlock: 'gxetc.desktop.cpu-unlock',
        /** OBU信息读取 */
        readObuInfo: 'gxetc.desktop.read-obu-info',
        /** OBU发行 */
        obuIssue: 'gxetc.desktop.obu-issue',
        /** 圈存 */
        cpuLoad: 'gxetc.desktop.cpu-load',
        /** 圈存异常处理 */
        cpuLoadAbnormal: 'gxetc.desktop.cpu-load-abnormal',
        /** 打开摄像头 */
        cameraOpen: 'gxetc.desktop.camera.open',
        /** 关闭摄像头 */
        cameraClose: 'gxetc.desktop.camera.close',
        /** 获取摄像头数量 */
        cameraCounts: 'gxetc.desktop.camera.counts',
        /** 拍照 */
        cameraTakePicture: 'gxetc.desktop.camera.take-picture',
        /** 换卡销卡 */
        cardReplaceCancel: 'gxetc.desktop.card-replace.cancel',
        /** 补领换卡新发行 */
        cardReplaceIssue: 'gxetc.desktop.card-replace.issue',
        /** 补领换卡新卡校验 */
        cardReplaceCheck: 'gxetc.desktop.card-replace.check',
        /**读取配置*/
        readConfig: 'gxetc.desktop.read-config',
        /**保存配置*/
        saveConfig: 'gxetc.desktop.save-config',
        /**打开cpu读写器*/
        openReader:'gxetc.desktop.cpu.open-reader',
        /**关闭读写器*/
        closeReader:'gxetc.desktop.cpu.close-reader',
        /**打开obu读写器*/
        opeRsu:'gxetc.desktop.open-rsu',
        /**关闭obu读写器*/
        closeRsu:'gxetc.desktop.close-rsu',
        posRecharge:'gxetc.desktop.pos-recharge',
        posCorrect:'gxetc.desktop.pos-correct'
        
    },
    /**到期时间 */
    expireTime: 3600000
}
export default wsConsts
