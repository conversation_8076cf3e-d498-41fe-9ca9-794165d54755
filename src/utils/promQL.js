export default function(key, instance, job = 'consul') {
  const promQL = {
    // cpu使用率
    cpu_used_rate: `(1 - avg(irate(node_cpu_seconds_total{instance="${instance}",job="${job}",mode="idle"}[5m]))) * 100`,
    // cpu利用率 (每秒增长率)
    cpu_used2_rate: `irate(node_cpu_seconds_total{instance="${instance}",job="${job}"}[5m])`,
    // system 使用率
    cpu_system_used_rate: `((sum(increase(node_cpu_seconds_total{instance="${instance}",job="${job}",mode="system"}[5m])) / sum(increase(node_cpu_seconds_total{instance="${instance}",job="${job}"}[5m]))))*100`,
    // user 使用率
    cpu_user_used_rate: `((sum(increase(node_cpu_seconds_total{instance="${instance}",job="${job}",mode="user"}[5m])) / sum(increase(node_cpu_seconds_total{instance="${instance}",job="${job}"}[5m]))))*100`,
    // 内存使用率
    memory_used_rate: `(1 - (node_memory_MemAvailable_bytes{instance="${instance}",job="${job}"} / (node_memory_MemTotal_bytes{instance="${instance}",job="${job}"})))* 100`,
    // 分区使用率
    filesystem_used_rate: `max((node_filesystem_size_bytes{instance="${instance}",job="${job}",fstype=~"ext.?|xfs"}-node_filesystem_free_bytes{instance="${instance}",job="${job}",fstype=~"ext.?|xfs"}) *100/(node_filesystem_avail_bytes {instance="${instance}",job="${job}",fstype=~"ext.?|xfs"}+(node_filesystem_size_bytes{instance="${instance}",job="${job}",fstype=~"ext.?|xfs"}-node_filesystem_free_bytes{instance="${instance}",job="${job}",fstype=~"ext.?|xfs"})))`,
    // 硬盘使用率
    harddisk_used_rate: `(node_filesystem_size_bytes {instance="${instance}", job="${job}",mountpoint ="/"} - node_filesystem_free_bytes {instance="10.45.13.13:9100", job="${job}",mountpoint ="/"}) / node_filesystem_size_bytes {instance="${instance}", job="${job}",mountpoint ="/"} * 100`,
    // 磁盘最大读取速度
    disk_read_bytes:
      // 'max(irate(node_disk_read_bytes_total{instance="10.45.13.13:9100",job="consul"}[5m]))',
      `max(irate(node_disk_read_bytes_total{instance="${instance}",job="${job}"}[5m])) / 1000`,
    // 磁盘最大写入速度
    disk_write_bytes: `max(irate(node_disk_written_bytes_total{instance="${instance}",job="${job}"}[5m])) / 1000`,
    // 下载带宽
    network_receive: `max(irate(node_network_receive_bytes_total{instance="${instance}",job="${job}"}[5m])*8)`,
    // 上传带宽
    network_transmit: `max(irate(node_network_transmit_bytes_total{instance="${instance}",job="${job}"}[5m])*8)`
  }
  return promQL[key]
}
