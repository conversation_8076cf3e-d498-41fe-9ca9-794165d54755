
import store from '@/store'

const defaultVars = {
    store:store,

    vehicleInfo: function () {
        return {
            //	客户编号
            customer_id: store.getters.customerInfo.customer_id,
            //	车辆号码
            vehicle_code: '浙',
            //车种类型
            vehicle_national_type: '1',
             //车辆类型
             vehicle_car_type: '中型轿车',
            //	车牌颜色（0蓝 1黄 2黑 3白 4渐变绿 5黄绿双拼 6蓝白渐变）
            vehicle_color: '0',
            //	车型（车型 2客车,1货车）
            vehicle_type: '2',
            //	车辆用户类型(0 普通车 1集卡车 2卧铺车 8军车(交通战备车) 9警车 15紧急车 16特殊公务车)，集卡车只有车型选货车的时候显示
            vehicle_user_type: '0',
            //	座位数
            vehicle_seat: '',
            //	吨数(核定载质量)(默认置空 让操作员填写)
            permitted_weight: '',
            //牵引总质量
            permitted_tow_weight: '',
            //整备质量(默认置空 让操作员填写)
            maintenance_mass: '',
            //车辆总质量 (默认置空 让操作员填写)
            vehicle_ton: '',
            //长
            vehicle_length: '0',
            //	宽
            vehicle_width: '0',
            //	高
            vehicle_height: '0',
            //	车轮数
            vehicle_wheels: '0',
            //	车轴数
            vehicle_axles: '0',
            //	轴距
            vehicle_wheelbases: '0',
            //	车籍地（对应车籍地附件  车籍地信息city.xls）
            vehicle_city: '0100',
            //	发动机号
            vehicle_engine: '',
            //	车辆特征描述
            vehicle_specificInfo: '',
            //	车辆识别代码 VIN
            vehicle_distinguish: '',
            //	ETC通行卡功能（1记账2储值）
            card_type: '23',
            //	预约编号（如果通过预约填写的，传预约编号）
            reservation_id: null,
            //	是否发行OBU（当车型是集卡车时会传该字段。0不发行OBU；1发行OBU；）
            issue_obu: '1',
            // 车辆品牌
            vehicle_model: '',
        }
    }
}

export default defaultVars