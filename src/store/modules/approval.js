import {
    list,
    adopt,
    toreject,
    appDet,
    repDet,
    record,
    toDoOrderList,
    orderDetail,
    findById,
    todelete
} from '@/api/approval'
const getDefaultState = () => {
	return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
  // get user info
  list({ commit, state }, params) {
    return new Promise((resolve, reject) => {
        list(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  adopt({ commit, state }, params) {
    return new Promise((resolve, reject) => {
        adopt(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  toreject({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      toreject(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  appDet({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      appDet(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  repDet({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      repDet(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  record({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      record(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  toDoOrderList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      toDoOrderList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  orderDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      orderDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  findById({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      findById(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  todelete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      todelete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}	
export default {
  namespaced: true,
  state,
  mutations,
  actions
}