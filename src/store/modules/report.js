import {
  report,
  channels
} from '@/api/report'
const getDefaultState = () => {
	return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
  // get user info
  report({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      report(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  channels({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      channels(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}	
export default {
  namespaced: true,
  state,
  mutations,
  actions
}
