import {
    advancePayment,
    importPayment,
    saveAdvance,
    searchRechargeList,
    transfeConfirm,
    transfeDelete,
    transfeCancel,
    getNetAccountList,
    getSumAvailableAmount,
    transferRefound,
    transferRecharge,
    transferRevoke
} from '@/api/custManagement'
const getDefaultState = () => {
    return {
    }
}
const state = getDefaultState()
const mutations = {
}
const actions = {
    // advancePayment
    advancePayment({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            advancePayment(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    importPayment({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            importPayment(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    saveAdvance({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            saveAdvance(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    searchRechargeList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            searchRechargeList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transfeConfirm({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transfeConfirm(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transfeDelete({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transfeDelete(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transfeCancel({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transfeCancel(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getNetAccountList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getNetAccountList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getSumAvailableAmount({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getSumAvailableAmount(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transferRefound({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transferRefound(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transferRecharge({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transferRecharge(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    transferRevoke({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            transferRevoke(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
