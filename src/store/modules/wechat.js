import {
    materialQuery,
    getMaterialCount,
    getMaterialNewsBatchGet,
    getMaterialNewsInfo,
    materialNewsUpload
} from '@/api/wechat'
const getDefaultState = () => {
    return {
    }
}
const state = getDefaultState()
const mutations = {
}
const actions = {
    // materialQuery
    materialQuery({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            materialQuery(params)
                .then(res => {
                    resolve(res.data)

                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    // materialQuery
    getMaterialCount({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getMaterialCount(params)
                .then(res => {
                    resolve(res.data)

                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    // materialQuery
    getMaterialNewsBatchGet({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getMaterialNewsBatchGet(params)
                .then(res => {
                    resolve(res.data)

                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    // materialQuery
    getMaterialNewsInfo({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getMaterialNewsInfo(params)
                .then(res => {
                    resolve(res.data)

                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    materialNewsUpload({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            materialNewsUpload(params)
                .then(res => {
                    resolve(res.data)

                })
                .catch(error => {
                    reject(error)
                })
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
