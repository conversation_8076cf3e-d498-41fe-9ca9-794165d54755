import { getInfo } from "@/api/user"
import { login, logout, refreshtoken } from "@/api/login"
import { getToken, setToken, removeToken } from "@/utils/auth"
import { resetRouter, constantRoutes, errorRoutes } from "@/router"

const avatarDefaut =
  "https://wpimg.wallstcn.com/69a1c46c-eb1c-4b46-8bd4-e9e686ef5251.png"

const state = {
  token: getToken(),
  name: "",
  realName: "",
  avatar: "",
  introduction: "",
  roles: [],
  permissions: [],
  permisaction: [],
  routers: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_REAL_NAME: (state, realName) => {
    state.realName = realName
  },
  SET_AVATAR: (state, avatar) => {
    if (avatar && avatar != null) {
      state.avatar = avatar
    } else {
      state.avatar = avatarDefaut
    }
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ROUTES:(state, routerList) => {
    state.routers = constantRoutes.concat(routerList).concat(errorRoutes)
    
  },
  SET_PERMISSIONS: (state, permisaction) => {
    state.permisaction = permisaction
    state.permissions = permisaction
  }
}

const actions = {
  // user login
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo)
        .then(response => {
          console.log("response", response)
          commit("SET_TOKEN", response.data.accessToken || response.data)
          setToken(response.data.accessToken || response.data)
          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo()
        .then(response => {
          if (!response || !response.data) {
            commit("SET_TOKEN", "")
            removeToken()
            resolve()
          }

          const {
            roles,
            name,
            realName,
            avatar,
            introduction,
            permissions
          } = response.data

          // roles must be a non-empty array
          if (!roles || roles.length <= 0) {
            reject("getInfo: roles must be a non-null array!")
          }
          commit("SET_PERMISSIONS", permissions)
          commit("SET_ROLES", roles)
          commit("SET_NAME", name)
          commit("SET_REAL_NAME", realName)
          commit("SET_AVATAR", avatar)
          commit("SET_INTRODUCTION", introduction)
          resolve(response.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 退出系统
  loginOut({ commit, state }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit("SET_TOKEN", "")
          commit("SET_ROLES", [])
          commit("SET_PERMISSIONS", [])
          removeToken()
          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 刷新token
  refreshToken({ commit, state }) {
    return new Promise((resolve, reject) => {
      refreshtoken({ token: state.token })
        .then(response => {
          const { token } = response
          commit("SET_TOKEN", token)
          setToken(token)
          resolve()
        })
        .catch(error => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit("SET_TOKEN", "")
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  changeRoles({ commit, dispatch }, role) {
    return new Promise(async resolve => {
      const token = role + "-token"

      commit("SET_TOKEN", token)
      setToken(token)

      const { roles } = await dispatch("getInfo")

      resetRouter()

      // generate accessible routes map based on roles
      const accessRoutes = await dispatch("permission/generateRoutes", roles, {
        root: true
      })

      // dynamically add accessible routes
      router.addRoute(accessRoutes)

      // reset visited views and cached views
      dispatch("tagsView/delAllViews", null, { root: true })

      resolve()
    })
  },
   // get user info
   setRouters({ commit },routers) {
    commit("SET_ROUTES",routers)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
