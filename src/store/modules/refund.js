import {
  refundOfflineSearch,
  refundOfflineEdit,
  refundOfflineListById,
  refundResultImport,
  refundDelete,
  getOrgView,
  searchOrg,
  orgEdit,
  orgDelete,
  refundConfigOrg,
  artificialSearch,
  showAddOrUpdateDialog,
  addOrUpdateConfirm,
  artificialDelete,
  getRefundOrder,
  refundOrderPreInitAudit,
  refundOrderData,
  refundOrderUpdate,
  refundOrderCancel,
  getTypeList,
  getChannelType,
  getRefundComplete,
  addRefundComplete,
  refundCompleteRecord,
  updateRefundComplete,
  refundCompleteCancel,
  manualRefundExport,
  getTollRecordList,
  getLogoutRefundList,
  getLogoutDetail,
  getDetailImgs,
  issuerAudit,
  branchAudit,
  operationAudit,
  editCardInfo,
  getTable,
  makeTable,
  getTollConfirm,
  tollConfirmExport,
  tollConfirm,
  setTag
} from '@/api/refund'
const getDefaultState = () => {
  return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
  setTag({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      setTag(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getTollConfirm({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getTollConfirm(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  tollConfirmExport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      tollConfirmExport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  tollConfirm({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      tollConfirm(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  makeTable({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      makeTable(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getTable({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getTable(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  editCardInfo({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      editCardInfo(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  issuerAudit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      issuerAudit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  branchAudit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      branchAudit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  operationAudit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      operationAudit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getDetailImgs({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getDetailImgs(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getLogoutDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getLogoutDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getLogoutRefundList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getLogoutRefundList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getTollRecordList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getTollRecordList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // get user info
  refundOfflineSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOfflineSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOfflineEdit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOfflineEdit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOfflineListById({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOfflineListById(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundResultImport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundResultImport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundDelete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundDelete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getOrgView({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getOrgView(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  searchOrg({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      searchOrg(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  orgEdit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      orgEdit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  orgDelete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      orgDelete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundConfigOrg({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundConfigOrg(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  artificialSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      artificialSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  showAddOrUpdateDialog({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      showAddOrUpdateDialog(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  addOrUpdateConfirm({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      addOrUpdateConfirm(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  artificialDelete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      artificialDelete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getRefundOrder({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getRefundOrder(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOrderPreInitAudit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOrderPreInitAudit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOrderData({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOrderData(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOrderUpdate({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOrderUpdate(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundOrderCancel({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundOrderCancel(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getTypeList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getTypeList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getChannelType({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getChannelType(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getRefundComplete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getRefundComplete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  addRefundComplete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      addRefundComplete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundCompleteRecord({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundCompleteRecord(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  updateRefundComplete({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      updateRefundComplete(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundCompleteCancel({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundCompleteCancel(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  manualRefundExport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      manualRefundExport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
