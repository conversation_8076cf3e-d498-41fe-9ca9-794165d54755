import {
  auditBlackSearch,
  dictionaries,
  auditBlackDetailInfo,
  auditPass,
  auditRefuse,
  checkAuditHandleState,
  cancelAuditApply,
  cancelAuditApplyPass,
  importAuditPass,
  checkCancelAuditHandleState,
  cancelAuditApplyRefuse,
  importAuditRefuse,
  checkAuditCancelAuditDetailInfoApply,
  ditDetailInfoApply,
  importAuditVersions,
  importsAuditSearch,
  imports,
  batchReSend, //批量作废重发
  batchTransfer, //批量转补缴
  deductionExport, //请款订单导出
  transExport, //扣款交易记录导出
  deductionsInit, //扣款交易接口初始化
  getDeductionsList, //扣款交易记录查询
  getDeductionsPass, //通行记录查询
  getPassDetail, //通行记录明细查询
  singleReSend, //单笔作废重发
  singleTransfer, //单笔转补缴
  transferDetail, //单笔转补缴详情
  exportSearch,
  hsAuditDisputefindById,
  hsAuditDisputedetail
} from '@/api/bindManagement'
const getDefaultState = () => {
  return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
  exportSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      exportSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  batchReSend({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      batchReSend(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  batchTransfer({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      batchTransfer(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  deductionExport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      deductionExport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  transExport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      transExport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  deductionsInit({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      deductionsInit(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getDeductionsList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getDeductionsList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getDeductionsPass({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getDeductionsPass(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getPassDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getPassDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  singleReSend({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      singleReSend(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  singleTransfer({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      singleTransfer(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  transferDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      transferDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  imports({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      imports(params)
        .then(res => {
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  importsAuditSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      importsAuditSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  importAuditVersions({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      importAuditVersions(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  ditDetailInfoApply({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      ditDetailInfoApply(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  checkAuditCancelAuditDetailInfoApply({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      checkAuditCancelAuditDetailInfoApply(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  importAuditRefuse({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      importAuditRefuse(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  cancelAuditApplyRefuse({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      cancelAuditApplyRefuse(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  checkCancelAuditHandleState({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      checkCancelAuditHandleState(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  importAuditPass({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      importAuditPass(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  cancelAuditApplyPass({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      cancelAuditApplyPass(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  cancelAuditApply({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      cancelAuditApply(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  checkAuditHandleState({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      checkAuditHandleState(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  auditRefuse({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      auditRefuse(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  auditPass({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      auditPass(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  auditBlackDetailInfo({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      auditBlackDetailInfo(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  dictionaries({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      dictionaries(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  auditBlackSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      auditBlackSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  hsAuditDisputefindById({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      hsAuditDisputefindById(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  hsAuditDisputedetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      hsAuditDisputedetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
