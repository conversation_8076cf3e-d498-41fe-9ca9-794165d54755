import { getChannelList, saveChannel } from "@/api/baseInfo"
const getDefaultState = () => {
  return {
    
  }
}
const state = getDefaultState()
const mutations = {
}
const actions = {
  // get user info
  getChannelList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getChannelList(params)
        .then(res => {
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  saveChannel({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      saveChannel(params)
        .then(res => {
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}