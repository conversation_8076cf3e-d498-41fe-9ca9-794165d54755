import {
  getContainerRefundList,
  getContainerRefundAuditDetailList,
  getPassRecordDetail,
  containerRefundPass,
  containerRefundRefuse,
  containerRefundNoticeSearch,
  containerRefundOpratorDetail,
  reSendNotice,
  refundReport
} from '@/api/containerRefund'
const getDefaultState = () => {
  return {
    refundSearch: {}
  }
}
const state = getDefaultState()
const mutations = {
  SET_REFUND_SEARCH: (state, refundSearch) => {
    state.refundSearch = refundSearch
  },
  REMOVE_REFUND_SEARCH: (state) => {
    state.refundSearch = {}
  },
}
const actions = {
  setRefundSearch({ commit, state }, refundSearch) {
    return new Promise((resolve) => {
      commit('SET_REFUND_SEARCH', refundSearch)
      resolve(state.refundSearch)
    })
  },
  removeRefundSearch({ commit }) {
    return new Promise((resolve) => {
      commit('REMOVE_REFUND_SEARCH')
      resolve()
    })
  },
  // getContainerRefundList
  getContainerRefundList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getContainerRefundList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getContainerRefundAuditDetailList({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getContainerRefundAuditDetailList(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  containerRefundOpratorDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      containerRefundOpratorDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  getPassRecordDetail({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      getPassRecordDetail(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  containerRefundPass({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      containerRefundPass(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  containerRefundRefuse({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      containerRefundRefuse(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  containerRefundNoticeSearch({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      containerRefundNoticeSearch(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  reSendNotice({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      reSendNotice(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  refundReport({ commit, state }, params) {
    return new Promise((resolve, reject) => {
      refundReport(params)
        .then(res => {
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
