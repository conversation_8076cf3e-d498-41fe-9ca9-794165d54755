
import { getToken, setToken, removeToken, getLocalStorage, setLocalStorage, clearLocalStorage } from '@/utils/auth'
import api from '@/api'
import wsConsts from '@/utils/wsConsts'
const state = {
    orderInfo: {},// 订单信息
    vehicleInfo: {},//车辆信息
    ticket: getToken(),
    operatorInfo: getLocalStorage('operatorInfo') || {},//系统操作员
    customerInfo: {},//ETC客户
    modelType: '',//当前用户正在操作的模块
    cancelVehicle: false,//当前用户是否从新增车辆中返回的
    vehicleInfo: {},//车辆信息
    changeCardInfo: {},//补领换卡功能中的新卡信息以及公务卡信息
    appointInfo: {},//预约信息
    application_order_no: '',//申领单号
    isBankCodeK: false,//申领单号

    initInfo: {}, // 卡片注销时的初始化信息
    vipCarAttribute: {},//高频车属性枚举
    applyOrderStatus: [],//新办订单状态枚举
    applyChannelStatus: [],//新办绑定渠道枚举
    activateChannelStatus: [],//激活绑定枚举
    address: [],//省市区
    mergeDetail: {},//开票合并缓存
}
const getters = {
    orderInfo: (state) => state.orderInfo,
    vehicleInfo: (state) => state.vehicleInfo,
    operatorInfo: (state) => state.operatorInfo,
    customerInfo: (state) => state.customerInfo,
    modelType: (state) => state.modelType,
    cancelVehicle: (state) => state.cancelVehicle,
    vehicleInfo: (state) => state.vehicleInfo,
    changeCardInfo: (state) => state.changeCardInfo,
    appointInfo: (state) => state.appointInfo,
    application_order_no: (state) => state.application_order_no,
    isBankCodeK: (state) => state.isBankCodeK,
    initInfo: (state) => state.initInfo,
    vipCarAttribute: (state) => state.vipCarAttribute,
    applyOrderStatus: (state) => state.applyOrderStatus,
    applyChannelStatus: (state) => state.applyChannelStatus,
    activateChannelStatus: (state) => state.activateChannelStatus,
    address: (state) => state.address,
    mergeDetail: (state) => state.mergeDetail
}
const mutations = {
    SET_ORDERINFO: (state, data) => {
        state.orderInfo = data
    },
    SET_TOKEN: (state, token) => {
        state.ticket = token
    },
    SET_APPLICATION_ORDER_NO: (state, application_order_no) => {
        state.application_order_no = application_order_no
    },
    SET_ISBANKCODEK: (state, isBankCodeK) => {
        state.isBankCodeK = isBankCodeK
    },
    SET_INITINFO: (state, initInfo) => {
        state.initInfo = initInfo
    },

    SET_OPERATORINFO: (state, operatorInfo) => {
        state.operatorInfo = operatorInfo
    },
    SET_CUSTOMERINFO: (state, customerInfo) => {
        state.customerInfo = customerInfo
    },
    SET_APPOINTINFO: (state, appointInfo) => {
        state.appointInfo = appointInfo
    },
    SET_CHANGECARDINFO: (state, changeCardInfo) => {
        state.changeCardInfo = changeCardInfo
    },
    SET_MODEL_TYPE: (state, modelType) => {
        state.modelType = modelType
    },
    SET_CANCEL_VEHICLE: (state, cancelVehicle) => {
        state.cancelVehicle = cancelVehicle;
    },
    SET_VEHICLEINFO: (state, vehicleInfo) => {
        console.log(vehicleInfo, '-------');
        state.vehicleInfo = vehicleInfo
    },
    SET_VEHICLEINFO: (state, vehicleInfo) => {
        console.log(vehicleInfo, '-------');
        state.vehicleInfo = vehicleInfo
    },
    SET_VIPCARATTRIBUTE: (state, vipCarAttribute) => {
        state.vipCarAttribute = vipCarAttribute
    },
    SET_APPLYORDERSTATUS: (state, applyOrderStatus) => {
        state.applyOrderStatus = applyOrderStatus
    },
    SET_APPLYCHANNELSTATUS: (state, applyChannelStatus) => {
        state.applyChannelStatus = applyChannelStatus
    },
    SET_ACTIVATECHANNELSTATUS: (state, activateChannelStatus) => {
        state.activateChannelStatus = activateChannelStatus
    },
    SET_ADDRESS: (state, address) => {
        state.address = address
    },
    SET_MERGEDETAIL: (state, mergeDetail) => {
        state.mergeDetail = mergeDetail
    }
}
const actions = {
    // 设置ETC客户
    setOrderInfo({ commit }, data) {
        commit('SET_ORDERINFO', data)
    },
    setCustomerInfo({ commit }, data) {
        commit('SET_CUSTOMERINFO', data)
    },
    setAppointInfo({ commit }, data) {
        commit('SET_APPOINTINFO', data)
    },
    setModelType({ commit }, data) {
        commit('SET_MODEL_TYPE', data)
    },
    setCancelVehicle({ commit }, data) {
        commit('SET_CANCEL_VEHICLE', data)
    },
    setChangeCardInfo({ commit }, data) {
        commit('SET_CHANGECARDINFO', data)
        console.log(this.getters.changeCardInfo, 'changeCardInfo')
    },
    setVehicleInfo({ commit }, data) {
        commit('SET_VEHICLEINFO', data);

        let permission = this.getters.operatorInfo.rights ? this.getters.operatorInfo.rights.split(',') : [];
        // console.log(data.bank_code === 'K' && !permission.includes('15'), 'data.bank_code', permission);
        commit('SET_ISBANKCODEK', data.bank_code === 'K' && !permission.includes('15'));
    },
    setApplicationOrderNo({ commit }, data) {
        commit('SET_APPLICATION_ORDER_NO', data);
    },
    setIsBankCodeK({ commit }, data) {
        commit('SET_ISBANKCODEK', data);
    },
    setInitInfo({ commit }, data) {
        commit('SET_INITINFO', data);
    },
    setVehicleInfo({ commit }, data) {
        commit('SET_VEHICLEINFO', data);

        let permission = this.getters.operatorInfo.rights ? this.getters.operatorInfo.rights.split(',') : [];
        // console.log(data.bank_code === 'K' && !permission.includes('15'), 'data.bank_code', permission);
        commit('SET_ISBANKCODEK', data.bank_code === 'K' && !permission.includes('15'));
    },
    setVipCarAttribute({ commit }, data) {
        commit('SET_VIPCARATTRIBUTE', data);
    },
    setApplyOrderStatus({ commit }, data) {
        commit('SET_APPLYORDERSTATUS', data)
    },
    setApplyChannelStatus({ commit }, data) {
        commit('SET_APPLYCHANNELSTATUS', data)
    },
    setActivateChannelStatus({ commit }, data) {
        commit('SET_ACTIVATECHANNELSTATUS', data)
    },
    setAddress({ commit }, data) {
        commit('SET_ADDRESS', data)
    },
    setMergeDetail({ commit }, data) {
        commit('SET_MERGEDETAIL', data)
    }
}
export default {
    state,
    getters,
    mutations,
    actions
}

