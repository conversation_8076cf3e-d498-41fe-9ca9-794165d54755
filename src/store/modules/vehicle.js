const state = {
    vehicleInfo:{},//车辆信息
}

const mutations = {
    SET_VEHICLEINFO: (state, vehicleInfo) => {
        console.log(vehicleInfo, '-------');
        state.vehicleInfo = {...vehicleInfo}
    }
}

const actions = {
    setVehicleInfo({ commit },data){
        commit('SET_VEHICLEINFO',data);
        //
        // let permission = this.getters.operatorInfo.rights ? this.getters.operatorInfo.rights.split(',') : [];
        // // console.log(data.bank_code === 'K' && !permission.includes('15'), 'data.bank_code', permission);
        // commit('SET_ISBANKCODEK',data.bank_code === 'K' && !permission.includes('15'));
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
