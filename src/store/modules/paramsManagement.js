import {
    getPrePayCardList,
    addPrePayCardLine,
    editPrePayCardLine
} from '@/api/paramsManagement'
const getDefaultState = () => {
    return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
    // get user info
    getPrePayCardList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getPrePayCardList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    addPrePayCardLine({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            addPrePayCardLine(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    editPrePayCardLine({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            editPrePayCardLine(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
