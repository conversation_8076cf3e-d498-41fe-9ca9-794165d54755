import { asyncRoutes, constantRoutes } from '@/router'
import { getMenus } from '@/api/user'
import Layout from '@/layout'
// import sysuserindex from '@/views/sysuser/index'

/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some((role) => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Use names to determine if the current user has permission
 * @param names
 * @param route
 */
function hasPathPermission(paths, route) {
  if (route.path) {
    return paths.some((path) => route.path === path.path)
  } else {
    return true
  }
}

/**
 * 后台查询的菜单数据拼装成路由格式的数据
 * @param routes
 */
export function generaMenu(routes, data) {
  data.forEach((item) => {
    const menu = {
      path: item.path,
      component:
        item.component === 'Layout' ? Layout : loadView(item.component),
      hidden: item.hidden === '1',
      children: [],
      name: item.name,
      redirect: item.redirect || '',
      meta: {
        title: item.title,
        icon: item.icon,
        noCache: item.cacheable === '1'
      }
    }
    if (item.children) {
      generaMenu(menu.children, item.children)
    }
    routes.push(menu)
  })
}

export const loadView = (view) => {
  // 路由懒加载
  return (resolve) => require([`@/views${view}`], resolve)
  // return () => import(`@/views${view}`)
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []

  routes.forEach((route) => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param components
 */
export function filterAsyncPathRoutes(routes, paths) {
  const res = []

  routes.forEach((route) => {
    const tmp = { ...route }
    if (hasPathPermission(paths, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncPathRoutes(tmp.children, paths)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit,dispatch}, roles) {
    return new Promise((resolve) => {
      const loadMenuData = []

      getMenus(process.env.VUE_APP_CLINET_ID)
        .then((response) => {
          // console.log(JSON.stringify(response))
          let data = response
          if (response.code !== 200) {
            this.$message({
              message: '菜单数据加载异常',
              type: 0
            })
          } else {
            data = response.data
            Object.assign(loadMenuData, data)

            generaMenu(asyncRoutes, loadMenuData)
            commit('SET_ROUTES', asyncRoutes)
            this.dispatch('user/setRouters',asyncRoutes)
            resolve(asyncRoutes)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
