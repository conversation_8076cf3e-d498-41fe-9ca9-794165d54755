import {
    getJob<PERSON>ist,
    addJob,
    openJob,
    getIssueList,
    getIssueDetail,
    getIssueStatistics,
    getAllJobList,
    exportExcelModel,
    importExcelModel,
    exportIssueResult
} from '@/api/issue'
const getDefaultState = () => {
    return {}
}
const state = getDefaultState()
const mutations = {}
const actions = {
    // get user info
    getJobList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getJobList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getAllJobList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getAllJobList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    addJob({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            addJob(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    openJob({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            openJob(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getIssueList({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getIssueList(params)
                .then(res => {
                    resolve(res.data)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getIssueDetail({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getIssueDetail(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    getIssueStatistics({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            getIssueStatistics(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    exportExcelModel({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            exportExcelModel(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    importExcelModel({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            importExcelModel(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    },
    exportIssueResult({ commit, state }, params) {
        return new Promise((resolve, reject) => {
            exportIssueResult(params)
                .then(res => {
                    resolve(res)
                })
                .catch(error => {
                    reject(error)
                })
        })
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
