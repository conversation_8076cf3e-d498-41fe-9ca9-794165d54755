import Vue from 'vue'
import Router from 'vue-router'
import { loadView } from './loadView'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:"router-name"             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ["admin","editor"]    control the page roles (you can set multiple roles)
    title: "title"               the name show in sidebar and breadcrumb (recommend set)
    icon: "svg-name"/"el-icon-x" the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: "/example/list"  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const asyncRoutes = []

const defaultRoutes = [
    {
        path: '/AAProComponentsDemos',
        component: Layout,
        name: 'ProComponents',
        alwaysShow: true,
        meta: {
            title: 'ProComponents',
            icon: 'dashboard'
        },
        children: [
            {
                path: 'search',
                component: () =>
                    import('@/views/AAProComponentsDemos/search/index'),
                name: '搜索栏',
                meta: { title: '搜索栏', icon: 'dashboard' }
            },
            {
                path: 'schema',
                component: () =>
                    import('@/views/AAProComponentsDemos/schema/index'),
                name: 'schema搜索栏',
                meta: { title: 'schema搜索栏', icon: 'dashboard' }
            },
            {
                path: 'drawer',
                component: () =>
                    import('@/views/AAProComponentsDemos/drawer/index'),
                name: 'Drawer 抽屉',
                meta: { title: 'Drawer 抽屉', icon: 'dashboard' }
            },
            {
                path: 'basicTable',
                component: () =>
                    import('@/views/AAProComponentsDemos/table/index'),
                name: 'table 基础表格',
                meta: { title: 'table 基础表格', icon: 'dashboard' }
            },
            {
                path: 'advancedTable',
                component: () =>
                    import('@/views/AAProComponentsDemos/table/advanced'),
                name: 'table 高级表格',
                meta: { title: 'table 高级表格', icon: 'dashboard' }
            }
        ]
    }
]
export let constantRoutes = [
    {
        path: '/login',
        component: loadView('login/index'),
        hidden: true
    },

    {
        path: '*',
        component: loadView('404'),
        hidden: true
    },
    {
        path: '/',
        component: Layout,
        redirect: '/dashboard',
        children: [
            {
                path: 'dashboard',
                name: 'Dashboard',
                component: () => import('@/views/dashboard/index'),
                meta: {
                    title: '首页',
                    icon: 'dashboard',
                    affix: true
                }
            }
        ]
    }
]
export const errorRoutes = {

}
if (process.env.NODE_ENV === 'development') {
    constantRoutes = constantRoutes.concat(defaultRoutes)
}
const createRouter = () =>
    new Router({
        // mode: "history", // require service support
        scrollBehavior: () => ({
            y: 0
        }),
        routes: constantRoutes
    })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher // reset router
}

const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}

export default router
