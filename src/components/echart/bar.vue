<template>
  <div ref="myChart" class="chart"></div>
</template>

<script>
import { deepClone, objectMerge } from '@/utils'
import * as echarts from 'echarts'

const defaultOptions = {
  title: {
    text: '',
    subtext: ''
  },
  color: ['#3aa1ff', '#36cbcb', '#4ecb73', '#fbd437', '#f2637b', '#975fe5'],
  tooltip: {},
  legend: {},
  toolbox: {
    show: true,
    feature: {}
  },
  calculable: true,
  grid: {},
  dataset: {
    source: []
  },
  xAxis: { type: 'category' },
  yAxis: {},
  series: []
}
export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    options: {
      type: Object,
      default: () => defaultOptions
    }
  },
  data() {
    return {
      refChart: 'chart'
    }
  },
  watch: {
    options: {
      immediate: true,
      deep: true,
      handler(value) {
        this.$_echart && this.$_echart.setOption(value)
      }
    },
    chartData: {
      immediate: true,
      deep: true,
      handler(value) {
        this.$nextTick(() => {
          this.initChart()
        })
      }
    }
  },
  methods: {
    handleClick(args) {
      this.$emit('click', args)
    },
    initChart() {
      if (!this.$_echart) {
        this.$_echart = echarts.init(this.$refs.myChart)
        this.$_echart.on('click', this.handleClick)
      }
      this.$_echart.setOption(this.getOptions())
      if (this.chartData) {
        this.updateChartData(this.chartData)
      }
    },
    getOptions() {
      const options = objectMerge(deepClone(defaultOptions), this.options)
      options.xAxis = this.options.xAxis || options.xAxis
      options.yAxis = this.options.yAxis || options.yAxis
      return options
    },
    updateChartData(data) {
      this.$_echart &&
        this.$_echart.setOption({
          dataset: {
            source: data
          }
        })
    },
    resize() {
      this.$_echart && this.$_echart.resize()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
      window.addEventListener('resize', this.resize)
    })
  },
  beforeDestroy() {
    this.$_echart && this.$_echart.dispose()
    this.$_echart = null
    window.removeEventListener('resize', this.resize)
  }
}
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>