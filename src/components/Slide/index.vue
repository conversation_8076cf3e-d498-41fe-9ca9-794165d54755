<template>
  <div class="modular-view-warp"
       :style="{ 'z-index': zIndex }">
    <div class="modular-view-bg"
         v-show="cVisible"
         @click="handleMask"></div>
    <transition name="slide-fade"
                @beforeLeave="beforeClose"
                @leave="close"
                @afterLeave="afterClose">
      <div class="modular-view"
           v-show="cVisible"
           :style="{ width: width }">
        <div class="modular-view-title">
          <div class="left">{{ title }}</div>
          <div class="right pointer modular-view-title-close"
               @click="handleClose">
            <i class="el-icon-close"></i>
          </div>
        </div>
        <div class="dart-slide-wrap">
          <div class="modular-view-content clearfix"
               :style="{ overflow: dialogOVerflow}">
            <slot></slot>
            <slot name='footer'></slot>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
var slideConfig = {
  close: function () {
    document.body.style.overflow = ''
  },
  open: function () {
    document.body.style.overflow = 'hidden'
  },
}

export default {
  name: 'dartSlide',
  setDefaults(opts) {
    if (!opts) {
      return
    }
    slideConfig = opts
  },
  props: {
    styles: {
      type: Object,
    },
    maskClosable: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: Number,
      default: 999,
    },
    width: {
      type: String,
      default: '68%',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    closeOnClickModal: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '标题',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    close: {
      type: Function,
      default: () => {
        return ''
      },
    },
    afterClose: {
      type: Function,
      default: () => {
        return ''
      },
    },
    beforeClose: {
      type: Function,
      default: () => {
        return ''
      },
    },
  },
  data() {
    return {
      loadinger: null,
      cLoading: this.loading,
      cVisible: this.visible,
      dialogOVerflow: '',
    }
  },
  methods: {
    handleMask() {
      if (this.maskClosable && this.cVisible) {
        this.handleClose()
      }
    },

    onClickModal() {
      if (!this.closeOnClickModal) return
      this.handleClose()
    },
    closeLoading() {
      if (this.loadinger) {
        this.loadinger.close()
      }
      this.dialogOVerflow = ''
    },
    openLoading() {
      this.loadinger = this.$loading({
        target: '.dart-slide-wrap',
        lock: true,
      })
      this.dialogOVerflow = 'hidden'
    },
    handleClose() {
      this._close()
      this.$emit('close')
    },
    _close() {
      this.cVisible = false
      this.slideConfig('close')
    },
    loadingInit() {
      if (this.cLoading) {
        this.openLoading()
        return
      }
      this.closeLoading()
    },
    slideConfig(type) {
      if (!slideConfig) {
        return
      }
      try {
        if (type === 'open') {
          slideConfig.open()
          return
        }
        slideConfig.close()
      } catch (e) {
        console.log('dart slideConfig error')
      }
    },
    visibleInit() {
      if (this.cVisible) {
        this.slideConfig('open')
        this.$emit('open')
      } else {
        this._close()
      }
    },
  },
  destroyed() {
    this.slideConfig('close')
  },
  watch: {
    loading: {
      immediate: true,
      handler(value) {
        this.cLoading = value
        this.$nextTick(() => {
          this.loadingInit()
        })
      },
    },
    cVisible: {
      immediate: true,
      handler(value) {
        this.$emit('update:visible', value)
      },
    },
    visible: {
      immediate: true,
      handler(value) {
        this.cVisible = value
        this.$nextTick(() => {
          this.visibleInit()
        })
      },
    },
  },
}
</script>

<style lang="scss" scope>
.dart-slide-wrap {
  height: 100%;
  position: relative;
  z-index: 1;
}
.modular-view-warp {
  overflow: hidden;
  .left {
    float: left;
  }
  .right {
    float: right;
  }
  .pointer {
    cursor: pointer;
  }
  .modular-view-title-close {
    width: 42px;
    text-align: center;
    font-size: 20px;
    color: #333;
  }
  .modular-view-title-close:hover {
    color: #409eff;
  }
  .modular-view-bg {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.8;
    z-index: 1005;
    background-color: rgba(55, 55, 55, 0.6);
  }
  .modular-view {
    height: 100%;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1090;
    background-color: #fff;
    .modular-view-title {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      z-index: 10;
      border-bottom: 1px solid #e8eaec;
      padding-left: 15px;
      height: 52px;
      line-height: 52px;
      font-size: 16px;
      color: #17233d;
      font-weight: 500;
    }
    .modular-view-content {
      position: absolute;
      top: 53px;
      z-index: 10;
      overflow-y: auto;
      right: 0;
      bottom: 0;
      left: 0;
      flex-flow: column;
      display: flex;
    }
    .modular-view-body {
      flex: 1;
      overflow-y: scroll;
    }
    .modular-view-footer {
      width: 100%;
      height: 53px;
      border-top: 1px solid #e8e8e8;
      padding-right: 15px;
      text-align: right;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}
.slide-fade-enter-active {
  transition: all 0.3s;
}
.slide-fade-leave-active {
  transition: all 0.3s;
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
  transform: translateX(100%);
  opacity: 0;
}
</style>
