import { decode } from 'js-base64'
import { stampDownloadApi } from '@/api/report'

const reportMixin = {
  data () {
    return {
      loading: false, // 加载状态
      api: null, //请求方法
      timeField: [] // 上传参数多余选项集合，放这里会进行删除
    }
  },
  methods: {
    // 搜索
    onSearchHandle (formData, type, fileTitle, apiMethod) {
      let params = JSON.parse(JSON.stringify(formData))
      console.log(formData, type, fileTitle, 'formData')
      this.timeField.forEach(item => {
        delete params[item]
      })
      // 额外添加的前置校验
      if (this.beforeSearchHandle && !this.beforeSearchHandle(formData)) return
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      if (type == 'search') {
        this.sendReportRequest(params)
      } else {
        this.stampDownload(params, fileTitle, apiMethod)
      }
    },
    // 报表请求
    sendReportRequest (params) {
      this.loading = true
      this.$store
        .dispatch('report/report', params)
        .then(res => {
          this.loading = false
          let url = res
          let decodeUrl = decode(url)
          let clientWidth = document.documentElement.clientWidth
          let clientHeight = document.documentElement.clientHeight
          window.open(
            decodeUrl,
            '_blank',
            'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
          )
        })
    },
    //报表导出并下载
    async stampDownload (params, fileTitle, apiMethod) {
      this.loading = true
      let message = this.$message({
        message: '报表下载中......',
        type: 'info',
        duration: 0,
      })
      let res = apiMethod ? await apiMethod(params) : await stampDownloadApi(params)
      this.loading = false

      try {
        let result = await this.blobToObj(res)
        message.close()
        if (result.code && result.code == 999) {
          this.$message.error(result.msg)
          return false
        }
      } catch (err) {
        this.loading = false
        message.close()
        console.log(err)
      }

      const link = document.createElement('a')
      let blob = new Blob([res], { type: 'application/pdf' }) //构造一个blob对象来处理数据
      link.style.display = 'none'
      link.href = URL.createObjectURL(blob)
      link.download = `${fileTitle}.pdf` //下载的文件名
      document.body.appendChild(link)
      link.click() // 执行下载
      document.body.removeChild(link) // 释放标签
      //   this.$message.success('下载成功')
      message.close()
    },
    // 重置的回调
    onReSetHandle () {
      console.log('onReSetHandle')
    },
    blobToObj (blobData) {
      return new Promise((resolve, reject) => {
        let reader = new FileReader() // 创建读取文件对象
        reader.readAsText(blobData, 'utf-8')
        reader.onload = function (result) {
          try {
            let result = JSON.parse(reader.result)
            resolve(result)
          } catch (e) {
            reject(e)
          }
        }
      })
    },
  }
}

export default reportMixin;
