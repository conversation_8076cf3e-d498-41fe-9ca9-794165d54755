<template>
  <div class="search">
    <dart-search
      :formSpan="formSpan"
      :gutter="gutter"
      ref="searchForm"
      :label-position="labelPosition"
      :model="search"
      v-bind="$attrs"
      :fontWidth="fontWidth"
      :rules="rules"
    >
      <template slot="search-form" style="padding-left: 10px">
        <div class="form-title" v-if="formTitle">{{ formTitle }}</div>
        <dart-search-item
          v-for="item in formConfigList"
          :key="item.field"
          :label="item.label"
          :prop="item.field"
        >
          <!-- input 输入框 -->
          <template v-if="item.type === 'input'">
            <el-input
              v-model="search[item.field]"
              :placeholder="item.placeholder || placeholderHandle(item)"
              maxlength="100"
            ></el-input>
          </template>

          <!-- select 普通下拉框 -->
          <template v-if="item.type === 'select'">
            <el-select
              v-model="search[item.field]"
              :placeholder="item.placeholder || placeholderHandle(item)"
              clearable
              :multiple="item.multiple"
              :class="item.className"
              @change="
                val => {
                  item.callBack && item.callBack(val)
                }
              "
            >
              <el-option
                :label="obj.label"
                :value="obj.value"
                v-for="obj in item.options"
                :key="obj.value"
              ></el-option>
            </el-select>
          </template>

          <!-- datePicker 日期选择 -->
          <template v-if="item.type === 'datePicker'">
            <el-date-picker
              :type="item.customType ? item.customType : 'datetime'"
              :placeholder="item.placeholder || placeholderHandle(item)"
              :default-time="item.defaultTime ? item.defaultTime : ''"
              :format="
                item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'
              "
              :value-format="
                item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'
              "
              v-model="search[item.field]"
              :picker-options="item.pickerOptions || {}"
            ></el-date-picker>
          </template>

          <!-- 日期范围选择 -->
          <template v-if="item.type === 'dateRange'">
            <el-date-picker
              v-model="search[item.field]"
              type="datetimerange"
              v-bind="item.props"
              range-separator="至"
              :start-placeholder="placeholderHandle(item, 'start')"
              :end-placeholder="placeholderHandle(item, 'end')"
              :default-time="item.defaultTime ? item.defaultTime :['00:00:00', '23:59:59']"
              @change="dateChange($event, item)"
            ></el-date-picker>
          </template>
          <!-- 级联 -->
          <template v-if="item.type === 'cascader'">
            <el-cascader
              v-model="search[item.field]"
              :options="item.options"
              :placeholder="item.placeholder || placeholderHandle(item)"
              v-bind="item.props"
              clearable
              filterable
            ></el-cascader>
          </template>

          <!-- 插槽 -->
          <slot></slot>
        </dart-search-item>

        <dart-search-item :is-button="true" :span="btnSpan">
          <div class="btn-wrapper" :class="btnAlign">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle('search')"
              >搜索</el-button
            >
            <el-button size="mini" @click="onReSetHandle">重置</el-button>
            <el-button
              type="primary"
              v-if="isDownload"
              @click="onSearchHandle('download')"
              >报表盖章并下载</el-button
            >
            <slot name="btn"></slot>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import moment from 'moment'

export default {
  components: {
    dartSearch,
    dartSearchItem
  },
  props: {
    formSpan: {
      type: Number,
      default: 24
    },
    gutter: {
      type: Number,
      default: 24
    },
    labelPosition: {
      type: String,
      default: 'right'
    },
    fontWidth: {
      type: Number,
      default: 2
    },
    rules: {
      type: Object,
      default: () => {}
    },
    formConfig: {
      type: [Object, Array],
      default: () => []
    },
    btnAlign: {
      type: String,
      default: 'right'
    },
    formTitle: {
      type: String,
      default: ''
    },
    btnSpan: {
      type: Number,
      default: 24
    },
    name: {
      // 报表name
      type: String,
      default: ''
    },
    isDownload: {
      // 是否盖章
      type: Boolean,
      default: false
    },
    stampConfig: {
      //盖章下载配置
      type: Object,
      default: null
    }
  },
  data() {
    return {
      search: {},
      isCollapse: false,
      formConfigList: []
    }
  },
  created() {
    this.formInit(this.formConfig)
  },
  watch: {
    formConfig: {
      immediate: true,
      handler(value) {
        this.formInit(value)
        this.$nextTick(() => {
          this.$refs.searchForm.$children[0].clearValidate()
        })
      }
    }
  },
  methods: {
    // 表单初始化
    formInit(data) {
      data.forEach(item => {
        switch (item.type) {
          case 'input':
          case 'select':
          case 'datePicker':
            this.$set(this.search, item.field, item.default)
            // this.search[item.field] = item.default
            break
          default:
        }
      })
      console.log(this.search, 'this.search')
      this.formConfigList = data
    },
    // 处理placeholder
    placeholderHandle(data, flag) {
      switch (data.type) {
        case 'input':
        case 'datePicker':
          return data.placeholder || `请填写${data.label.replace(/：/, '')}`
        case 'select':
          return data.placeholder || `请选择${data.label.replace(/：/, '')}`
        case 'dateRange': {
          const placeholderArr = data.placeholder || ['开始日期', '结束日期']
          return flag === 'start' ? placeholderArr[0] : placeholderArr[1]
        }
        case 'cascader': {
          return data.placeholder || `请选择${data.label.replace(/：/, '')}`
        }
        default:
          return ''
      }
    },
    // 日期选择器change事件
    dateChange(data, item) {
      console.log('dateChange')
      if (data) {
        console.log(item)
        let startTime = data[0]
        let endTime = data[1]
        // 格式化时间返回值
        if (item.keys) {
          this.search[item.keys[0]] = moment(startTime).format(
            item.format || 'YYYY-MM-DD HH:mm:ss'
          )
          this.search[item.keys[1]] = moment(endTime).format(
            item.format || 'YYYY-MM-DD HH:mm:ss'
          )
          // delete this.search[item.field]
          console.log(this.search)
        } else {
          this.search[item.field] = [
            moment(startTime).format(item.format || 'YYYY-MM-DD HH:mm:ss'),
            moment(endTime).format(item.format || 'YYYY-MM-DD HH:mm:ss')
          ]
        }
      } else if (!data) {
        if (item.keys) {
          this.search[item.keys[0]] = ''
          this.search[item.keys[1]] = ''
          console.log(this.search)
        } else {
          this.search[item.field] = []
        }
      }
    },
    onSearchHandle(type) {
      this.$refs.searchForm.$children[0].validate(valid => {
        if (valid) {
          let { apiMethod } = this.stampConfig ? this.stampConfig : {}
          let params
          let fileTitle = this.stampConfig?.title || this.formTitle
          if (type == 'search') {
            params = {
              ...this.search,
              name: this.name
            }
          } else {
            if (!this.stampConfig) {
              console.log('请配置盖章下载配置stampConfig!!')
              return
            }
            let { fileName, keyword } = this.stampConfig
            params = {
              name: this.name,
              fileName,
              param: {
                ...this.search,
                keyword
              }
            }
          }
          this.$emit('onSearchHandle', params, type, fileTitle, apiMethod)
        }
      })
    },
    onReSetHandle() {
      this.search = {}
      this.$emit('onReSetHandle', this.search)
    }
  }
}
</script>

<style lang="scss" scoped>
.search {
  .btn-wrapper {
    // margin-left: 40px;
    // margin-top: 10px;
    &.left {
      text-align: left;
    }
    &.right {
      text-align: right;
    }
  }
  .form-title {
    margin: 0 0 10px 40px;
    font-weight: bold;
  }
}
</style>