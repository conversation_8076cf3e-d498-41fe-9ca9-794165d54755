<template>
  <div class="search">
    <dart-search
      :formSpan="formSpan"
      :gutter="gutter"
      ref="searchForm"
      v-bind="$attrs"
      :label-position="labelPosition"
      :model="search"
      :fontWidth="fontWidth"
      :rules="rules"
    >
      <template slot="search-form" style="padding-left: 10px">
        <dart-search-item
          v-for="item in formConfigList"
          :key="item.field"
          :label="item.label"
          :prop="item.field"
          :span="item.span"
          :class="{ isCollapse: item.isCollapse && !isCollapse }"
        >
          <!-- input 输入框 -->
          <template v-if="item.type === 'input'">
            <el-input
              v-model="search[item.field]"
              :placeholder="item.placeholder || placeholderHandle(item)"
              maxlength="100"
            ></el-input>
          </template>

          <!-- select 普通下拉框 -->
          <template v-if="item.type === 'select'">
            <el-select
              v-model="search[item.field]"
              :placeholder="item.placeholder || placeholderHandle(item)"
              :clearable="item.clearable == 1?false:true"
              v-bind="item.props"
              :class="item.className"
              @change="
                val => {
                  item.callBack && item.callBack(val)
                }
              "
            >
              <el-option
                :label="obj.label"
                :value="obj.value"
                v-for="obj in item.options"
                :key="obj.value"
              ></el-option>
            </el-select>
          </template>

          <!-- datePicker 日期选择 -->
          <template v-if="item.type === 'datePicker'">
            <el-date-picker
              type="datetime"
              :placeholder="item.placeholder || placeholderHandle(item)"
              :default-time="item.defaultTime ? item.defaultTime : ''"
              :format="
                item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'
              "
              :value-format="
                item.valueFormat ? item.valueFormat : 'yyyy-MM-dd HH:mm:ss'
              "
              v-model="search[item.field]"
            ></el-date-picker>
            <!-- <slot name="btn"></slot> -->
          </template>

          <!-- 日期范围选择 -->
          <template v-if="item.type === 'dateRange'">
            <el-date-picker
              v-model="search[item.field]"
              v-bind="item.props"
              type="datetimerange"
              range-separator="至"
              :start-placeholder="placeholderHandle(item, 'start')"
              :end-placeholder="placeholderHandle(item, 'end')"
              :default-time="item.defaultTime ? item.defaultTime :['00:00:00', '23:59:59']"
              @change="dateChange($event, item)"
            ></el-date-picker>
          </template>
          <!-- 级联 -->
          <template v-if="item.type === 'cascader'">
            <el-cascader
              v-model="search[item.field]"
              :options="item.options"
              :placeholder="item.placeholder || placeholderHandle(item)"
              v-bind="item.props"
              clearable
              filterable
            ></el-cascader>
          </template>

          <!-- 插槽 -->
          <template v-if="item.type === 'slot'">
            <slot name="input" :scope="search"></slot>
          </template>
        </dart-search-item>

        <dart-search-item :is-button="true" :span="btnSpan">
          <div class="btn-wrapper" :class="btnAlign">
            <el-button
              type="primary"
              size="mini"
              native-type="submit"
              @click="onSearchHandle"
              >搜索</el-button
            >
            <el-button size="mini" @click="onReSetHandle">重置</el-button>

            <slot name="btn"></slot>

            <template v-if="collapse">
              <span
                class="collapse"
                v-if="!isCollapse"
                @click="isCollapse = true"
                >展开</span
              >
              <span class="collapse" v-else @click="isCollapse = false"
                >收起</span
              >
            </template>
          </div>
        </dart-search-item>
      </template>
    </dart-search>
  </div>
</template>

<script>
import dartSearch from '@/components/Search/search'
import dartSearchItem from '@/components/Search/searchItem'
import moment from 'moment'

export default {
  components: {
    dartSearch,
    dartSearchItem
  },
  props: {
    formSpan: {
      type: Number,
      default: 24
    },
    gutter: {
      type: Number,
      default: 24
    },
    labelPosition: {
      type: String,
      default: 'right'
    },
    fontWidth: {
      type: Number,
      default: 2
    },
    rules: {
      type: Object,
      default: () => {}
    },
    formConfig: {
      type: [Object, Array],
      default: () => []
    },
    btnSpan: {
      type: Number,
      default: 24
    },
    btnAlign: {
      type: String,
      default: 'right'
    },
    collapse: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      search: {},
      isCollapse: false,
      formConfigList: []
    }
  },
  created() {
    this.formInit(this.formConfig)
  },
  watch: {
    formConfig: {
      immediate: true,
      handler(value) {
        this.formInit(value)
      }
    }
  },
  methods: {
    // 表单初始化
    formInit(data) {
      data.forEach(item => {
        switch (item.type) {
          case 'input':
          case 'select':
          case 'datePicker':
            this.$set(this.search, item.field, item.default)
            // this.search[item.field] = item.default
            break
          case 'dateRange':
            if (item.keys) {
              this.$set(this.search, item.field, item.default)

              this.search[item.keys[0]] = item.default[0]
              this.search[item.keys[1]] = item.default[1]
              // delete this.search[item.field]
            } else {
              this.search[item.field] = [item.default[0], item.default[1]]
            }
            break
          default:
        }
      })
      this.formConfigList = data
    },
    // 处理placeholder
    placeholderHandle(data, flag) {
      switch (data.type) {
        case 'input':
        case 'datePicker':
          return data.placeholder || `请填写${data.label.replace(/：/, '')}`
        case 'select':
          return data.placeholder || `请选择${data.label.replace(/：/, '')}`
        case 'dateRange': {
          const placeholderArr = data.placeholder || ['开始日期', '结束日期']
          return flag === 'start' ? placeholderArr[0] : placeholderArr[1]
        }
        case 'complexInput':
        case 'cascaderInput': {
          const placeholderArr = data.placeholder || ['请选择', '请填写关键字']
          return flag === 'select' ? placeholderArr[0] : placeholderArr[1]
        }
        case 'cascader': {
          return data.placeholder || `请选择${data.label.replace(/：/, '')}`
        }
        default:
          return ''
      }
    },
    // 日期选择器change事件
    dateChange(data, item) {
      console.log('dateChange')
      if (data) {
        console.log(item)
        let startTime = data[0]
        let endTime = data[1]
        // 格式化时间返回值
        if (item.keys) {
          this.search[item.keys[0]] = moment(startTime).format(
            item.format || 'YYYY-MM-DD HH:mm:ss'
          )
          this.search[item.keys[1]] = moment(endTime).format(
            item.format || 'YYYY-MM-DD HH:mm:ss'
          )
          // delete this.search[item.field]
        } else {
          this.search[item.field] = [
            moment(startTime).format(item.format || 'YYYY-MM-DD HH:mm:ss'),
            moment(endTime).format(item.format || 'YYYY-MM-DD HH:mm:ss')
          ]
        }
      } else if (!data) {
        if (item.keys) {
          this.search[item.keys[0]] = ''
          this.search[item.keys[1]] = ''
        } else {
          this.search[item.field] = []
        }
      }
    },
    onSearchHandle() {
      this.$refs.searchForm.$children[0].validate(valid => {
        if (valid) {
          this.$emit('onSearchHandle', this.search)
        }
      })
    },
    onReSetHandle() {
      this.search = {}
      this.formInit(this.formConfig)
      this.$emit('onReSetHandle', this.search)
    }
  }
}
</script>

<style lang="scss" scoped>
.search {
  .btn-wrapper {
    // margin-left: 40px;
    // margin-top: 10px;
    &.left {
      text-align: left;
    }
    &.right {
      text-align: right;
    }
  }
  .collapse {
    cursor: pointer;
    color: #409eff;
    margin-left: 10px;
    font-size: 14px;
  }
  .isCollapse {
    display: none;
  }
}
</style>