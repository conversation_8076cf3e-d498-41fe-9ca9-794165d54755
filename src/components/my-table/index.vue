<template>
  <div class="table-container">
    <!-- table表格栏 -->
    <div class="my-table" ref="tableRef" :style="{ width: '100%' }">
      <el-table
        ref="elTableRef"
        :data="tableData"
        :height="computeTableHeight"
        :max-height="maxHeight"
        :width="tableWidth"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        row-key="id"
        :highlight-current-row="isHighlightCurrentRow"
        stripe
        :span-method="spanMethod"
        :border="showBorder"
        @sort-change="sortChange"
      >
        <template v-slot:empty v-if="showEmptyImg">
          <div :style="{ 'line-height': showEmptyImg ? '0' : '40px' }">
            暂无数据
          </div>
        </template>
        <slot name="rowicon"></slot>
        <slot name="selection"></slot>
        <slot name="expanded"></slot>
        <el-table-column
          label="行号"
          :width="90"
          align="center"
          v-if="showIndex"
        >
          <template #default="scope">{{
            (pageNum - 1) * pageSize + scope.$index + 1
          }}</template>
        </el-table-column>
        <!-- 修改show-overflow-tooltip，如果列中有自定义tooltip，就关闭show-overflow-tooltip属性 -->
        <el-table-column
          v-for="cloumn in cloumns"
          :key="cloumn.prop"
          :prop="cloumn.prop"
          :label="cloumn.label"
          align="center"
          :width="cloumn.width"
          :fixed="cloumn.fixed"
          :type="cloumn.type"
          :show-overflow-tooltip="!cloumn.isCustomTooltip"
          :sortable="cloumn.sort"
        >
          <!--                    :show-overflow-tooltip="!cloumn.isCustomTooltip"-->
          <template slot="header">
            <slot :name="cloumn.prop + 'Title'" :cloumnVal="cloumn">
              <span>{{ cloumn.label }}</span>
            </slot>
          </template>
          <template v-slot="scope" v-if="!cloumn.type">
            <div
              class="my-tabel-td-content"
              :class="{ wordWrap: cloumn.wordWrap }"
            >
              <slot
                v-if="Object.keys(scope.row).length > 0"
                :name="cloumn.prop"
                :scope="scope.row"
                :index="scope.$index"
              >
                <span v-if="cloumn.formatter">
                  {{ cloumn.formatter(scope.row[cloumn.prop], scope.row) }}
                </span>
                <span v-else-if="cloumn.filterHtml">
                  <div
                    v-html="
                      cloumn.filterHtml(scope.row[cloumn.prop], scope.row)
                    "
                  />
                </span>
                <span v-else>{{ scope.row[cloumn.prop] }}</span>
              </slot>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination
      v-if="hasPagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout=" slot, prev, pager, next,sizes, jumper"
      :total="total"
      class="page-con"
      :pager-count="pagerCount"
    >
      <span class="page-total"
        >总共{{ total }}条记录,共{{ Math.ceil(total / pageSize) }}页</span
      >
    </el-pagination>
  </div>
</template>

<script>
import { getOffset, getSize } from './hook/index'
export default {
  props: {
    tableData: {
      type: Array
    },
    cloumns: {
      type: Array
    },
    total: {
      type: Number,
      default: 0
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    // 是否翻页
    hasPagination: {
      type: Boolean,
      default: true
    },
    tableHeight: {
      type: [String, Number],
      default: ''
    },
    maxHeight: {
      type: String
    },
    isHighlightCurrentRow: {
      type: Boolean,
      default: false
    },
    showIndex: {
      type: Boolean,
      default: false
    }, // 是否展示index选择，默认否
    showBorder: {
      type: Boolean,
      default: false
    }, // 是否显示表格边框，默认否
    showEmptyImg: {
      type: Boolean,
      default: true
    }, // 是否显示暂无数据图片
    pagerCount: {
      type: Number,
      default: 7
    }, // 是否显示暂无数据图片,
    // 排序数据
    sortData: {
      type: Function,
      default: () => {}
    },
    spanMethod:{ // 合并单元格
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      remComputed: null,
      computeTableHeight: 0,
      tableWidth: null
    }
  },
  mounted() {
    this.onWindowResize()
    window.addEventListener('resize', this.onWindowResize)
  },
  methods: {
    handleSizeChange(val) {
      // this.pageSize = val
      this.$emit('changeTableData', val, this.pageNum)
    },
    handleCurrentChange(val) {
      // this.pageNum = val
      this.$emit('changeTableData', this.pageSize, val)
    },
    handleRowClick(row, column, event) {
      this.$emit('rowClick', { row, column, event })
    },
    handleSelectionChange(val) {
      this.$emit('selectChange', val)
    },
    sortChange(v) {
      this.$emit('sortChange', v, this.sortData)
    },
    clearSelection() {
      this.$refs.elTableRef.clearSelection()
    },
    remToPx() {
      const rem = document
        .getElementsByTagName('html')[0]
        .style.fontSize.split('px')[0]
      return rem
    },
    onWindowResize() {
      this.remComputed = this.remToPx()

      this.$nextTick(() => {
        // 如果设置了固定高度，则不用去计算
        if (this.tableHeight) {
          this.computeTableHeight = this.tableHeight
          return
        }
        if (this.$isDialog()) {
          this.dialogGetTableH()
        } else {
          this.getTableHw()
        }
      })
    },
    //兼容弹框中表格的高度
    dialogGetTableH() {
      // 获取弹框高度
      const elements = document.getElementsByClassName('vl-notify-content')
      let goalElememt = elements[elements.length-1]
      const dialogHeight = goalElememt.clientHeight
      // 计算table顶部距离弹框顶部距离
      const dialogOffsetTop =
        this.$refs.tableRef.getBoundingClientRect().top -
        goalElememt.getBoundingClientRect().top

      const height =
        dialogHeight - dialogOffsetTop - (this.hasPagination ? 0 : 30) -70// 70 为底部按钮高度

      this.computeTableHeight = height - 0.54 * 200 
    },
    getTableHw() {
      const initHeight = getSize().windowH
      const initWidth = getSize().windowW
      const offsetLeft = getOffset(this.$refs.tableRef).left
      const offsetTop = getOffset(this.$refs.tableRef).top
      const height =
        initHeight -
        offsetTop -
        (this.hasPagination ? 0 : 30) -
        0.94 * this.remComputed
      const width = initWidth - offsetLeft
      this.computeTableHeight = height - 0.54 * 200
      this.tableWidth = width - 0.2 * this.remComputed
      return {
        height,
        width
      }
    }
  },
  watch: {
    cloumns: function(newVal, oldVal) {
      this.cloumns = newVal
    },
    tableData: function(newVal, oldVal) {
      this.tableData = newVal
    },
    total: function(newVal, oldVal) {
      this.total = newVal
    },
    pageNum: function(newVal, oldVal) {
      this.pageNum = newVal
    },
    pageSize: function(newVal, oldVal) {
      this.pageSize = newVal
    },
    hasPagination: function(newVal, oldVal) {
      this.hasPagination = newVal
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.onWindowResize)
  }
}
</script>

<style lang="scss" scoped>
.table {
  // flex: 1;
  width: 100%;
  .my-tabel-td-content {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    &.wordWrap {
      white-space: normal;
    }
  }
}

// .table-container {
//   background: #fff;
//   display: flex;
//   flex-direction: column;

//   .table {
//     // flex: 1;
//     width: 100%;
//     .my-tabel-td-content {
//       white-space: nowrap;
//       text-overflow: ellipsis;
//       overflow: hidden;
//       word-break: break-all;
//     }
//   }

//   .page-con {
//     // text-align: center;
//     text-align: right;

//     .page-total {
//       float: left;
//       font-size: 14px;
//       color: rgba(12, 24, 45, 0.8);
//       letter-spacing: 2px;
//     }

//     :deep(.select-trigger) {
//       margin: 0 0.4rem;
//     }
//   }

//   .el-table {
//     font-size: 14px;
//   }

//   :deep(.el-table__empty-block) {
//     align-items: baseline;
//   }

//   // :deep(.el-table__empty-text) {
//   // 	line-height: 0;
//   // }
// }
</style>