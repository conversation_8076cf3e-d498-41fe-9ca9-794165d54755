// 获取元素距离可视区域顶部、左部的距离
export const getOffset = ele => {
  let top = ele.offsetTop;
  let left = ele.offsetLeft;
  while (ele.offsetParent) {
    ele = ele.offsetParent;
    if (window.navigator.userAgent.indexOf('MSTE 8') > -1) {
      top += ele.offsetTop;
      left += ele.offsetLeft;
    } else {
      top += ele.offsetTop + ele.clientTop;
      left += ele.offsetLeft + ele.clientLeft;
    }
  }
  return {
    left,
    top,
  };
};

export const getSize = () => {
  let windowW; let windowH; let contentH; let contentW; let scrollT;
  windowH = window.innerHeight;
  windowW = window.innerWidth;
  scrollT = document.documentElement.scrollTop || document.body.scrollTop;
  contentH = document.documentElement.scrollHeight > document.body.scrollHeight
    ? document.documentElement.scrollHeight
    : document.body.scrollHeight;
  contentW = document.documentElement.scrollWidth > document.body.scrollWidth
    ? document.documentElement.scrollWidth
    : document.body.scrollWidth;
  return {
    windowW,
    windowH,
    contentH,
    contentW,
    scrollT,
  };
};
