// import { apiDownloadFile } from '@/axios/download'
import { decode } from 'js-base64'

const tableListMixin = {
  data () {
    return {
      loading: false, // 加载状态
      tableData: [], // 列表数据
      originalPageSize: 10, // 原始页码数据
      total: 0,
      pageSize: 10,
      pageNum: 1,
      pageParams: {}, // 页面默认请求参数
      currentFormData: {}, // 搜索表单数据
      api: null, //请求方法
      selectArr: [], // 选中集合
      timeField: [], // 上传参数多余选项集合，放这里会进行删除
      pageSizeKey: 'pageSize', // 数据上传时的 格式key
      pageNumKey: 'pageNum', // 数据上传时的 格式key
      dataKey: 'data' // 数据返回时的 格式key
    }
  },
  methods: {
    // 获取列表
    getTableData (query, callBack) {
      if (!this.api) {
        this.$message.error('没有写入列表请求方法')
        return
      }
      this.loading = true
      let params = {
        [this.pageSizeKey]: this.pageSize,
        [this.pageNumKey]: this.pageNum,
        ...this.currentFormData,
        ...this.pageQuery,
        ...this.pageParams,
      }
      if (query) {
        params = Object.assign(params, query);
      }
      for (let key in params) {
        if (params[key] === '') {
          delete params[key]
        }
      }
      this.api(params).then(res => {
        if (res.code === 200) {
          this.loading = false
          if (callBack) {
            callBack(res)
          } else if (this.tableCallBack) {
            this.tableCallBack(res)
            this.total = res.data.total
          } else {
            this.tableData = res.data.records || res.data[this.dataKey]
            this.total = res.data.total
          }
        }
      }).catch(() => {
        this.loading = false
      })
    },

    // 页码操作
    changeTableData (pageSize, pageNum) {
      this.pageNum = pageNum
      this.pageSize = pageSize
      this.getTableData()
    },

    // 搜索框表单操作
    onSearchHandle (formData) {
      this.pageNum = 1
      this.pageSize = this.originalPageSize
      let params = JSON.parse(JSON.stringify(formData))
      this.timeField.forEach(item => {
        delete params[item]
      })
      this.currentFormData = params
      this.getTableData()
    },

    // 重置的回调
    onReSetHandle (formData) {
      this.currentFormData = formData
      this.getTableData()
    },

    selectChange (val) {
      this.selectArr = val
    },

    clearSelection () {
      this.$refs.tableRef.clearSelection()
    },
    /**
     * 下载导出
     * @param {*} data 接口入参
     * @param {*} apiMethod 导出的接口方法
     * @param {*} fileObj 如果是流blob的形式，则传文件对象
     * @returns 
     */
    async exportFile(data,apiMethod,fileObj) {
      this.loading = true
      if(!apiMethod){
        console.error('缺少导出地址apiMethod！')
        return
      }
      let res = await apiMethod(data)
      this.loading = false
      if(fileObj){ // 如果是流blob的形式
        const link = document.createElement('a')
        let blob = new Blob([res]) //构造一个blob对象来处理数据
        link.style.display = 'none'
        link.href = URL.createObjectURL(blob)
        link.download = `${fileObj.fileName}` //下载的文件名
        document.body.appendChild(link)
        link.click() // 执行下载
        document.body.removeChild(link) // 释放标签
        return
      }
      // 如果是链接的形式
      if (res.code == 200) {
        let url = res.data
        let decodeUrl = decode(url)
        // console.log(decodeUrl,'地址')
        let clientWidth = document.documentElement.clientWidth
        let clientHeight = document.documentElement.clientHeight
        window.open(
          decodeUrl,
          '_blank',
          'width=' +
            clientWidth +
            ',height=' +
            clientHeight +
            ',left=50,menubar=no,toolbar=no,status=no,scrollbars=yes,'
        )
      }
    },
    /**
     * ascending 表示升序，descending 表示降序，null 表示还原为原始顺序
     * @param v
     * @param sortData 配置数据
     */
    sortChange (v, sortData) {
      const {
        sortable
      } = v.column || {}
      if (sortable === 'custom' && sortData) {
        const {
          data = [], sortKey = '', sortParams = {}
        } = sortData(v)
        const _obj = {
          ...sortParams
        }
        for (const d of data) {
          if (d.prop === v.prop) {
            _obj[sortKey] = d.sortValue
          }
        }
        this.getTableData(_obj)
      }
    }
  }
}

export default tableListMixin;
