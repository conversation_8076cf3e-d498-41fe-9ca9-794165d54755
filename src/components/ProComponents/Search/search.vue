<template>
  <div class="dart-search-wrapper">
    <div
      ref="dartSearch"
      class="dart-search-container"
      :class="{ autoHeight: isExpand }"
      :style="{ height: height }"
    >
      <el-form
        :model="model"
        :rules="rules"
        :ref="refs"
        :label-position="labelPosition"
        :label-width="labelWidth"
        :label-suffix="labelSuffix"
        :hide-required-asterisk="hideRequiredAsterisk"
        :validate-on-rule-change="validateOnRuleChange"
        :show-message="showMessage"
        :status-icon="statusIcon"
        :inline-message="inlineMessage"
        :inline="inline"
        :size="size"
        :class="{ w4: labelTextLength === 4, wMax: labelTextLength > 6 }"
        @submit.native.prevent
      >
        <el-row>
          <el-col :span="formSpan" :xs="24">
            <el-row
              :gutter="gutter"
              :class="{ fontWidth: fontWidth === 2 }"
              :formSpan="formSpan"
            >
              <slot name="search-form" />
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div
      v-if="showArrow"
      class="dart-search-down"
      title="更多"
      @click="handleDown"
    >
      <i v-if="isExpand" class="el-icon-arrow-up icon" />
      <i v-else class="el-icon-arrow-down icon" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'DartSearch',
  props: {
    showArrow: Boolean,
    height: {
      type: String,
      default: 'auto'
    },
    model: Object,
    rules: Object,
    labelPosition: String,
    labelSuffix: {
      type: String,
      default: ''
    },
    size: {
      type: String,
      default: 'mini'
    },
    inline: {
      type: Boolean,
      default: true
    },
    searchOperation: {
      type: Boolean,
      default: true
    },
    inlineMessage: Boolean,
    statusIcon: Boolean,
    showMessage: {
      type: Boolean,
      default: true
    },
    validateOnRuleChange: {
      type: Boolean,
      default: true
    },
    hideRequiredAsterisk: {
      type: Boolean,
      default: false
    },
    refs: {
      type: String,
      default: 'ruleForm'
    },
    formSpan: {
      type: Number,
      default: 24
    },
    btnSpan: {
      type: Number,
      default: 4
    },
    gutter: {
      type: Number,
      default: 0
    },
    labelTextLength: {
      type: Number,
      default: 6
    },
    fontWidth: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      isExpand: false
    }
  },
  computed: {
    labelWidth() {
      if (this.labelTextLength > 6) {
        return '140px'
      }
      if (this.labelTextLength === 4) {
        return '90px'
      }
      return '120px'
    }
  },
  methods: {
    handleDown() {
      this.isExpand = !this.isExpand
    },
    resetForm(formName) {
      console.log(this.$refs[this.refs])
      this.$refs[this.refs].resetFields()
    }
  }
}
</script>
<style lang="scss">
.dart-search-wrapper {
  margin-bottom: 10px;
  .dart-search-container {
    overflow: hidden;
    padding: 15px 15px 0 0;
    background-color: #fff;
    // border: 1px solid #e3e3e3;
    &.autoHeight {
      height: auto !important;
    }
    .dart-search-operation {
      box-sizing: border-box;
      text-align: right;
      padding-bottom: 13px;
    }
    .el-form-item {
      margin-bottom: 13px;
      min-height: 30px;
      width: 100%;
    }
    .el-form-item__label {
      color: #444;
    }
    .el-form-item__content {
      width: calc(100% - 120px);
    }
    .el-select {
      width: 100%;
    }
    .el-cascader {
      width: 100%;
    }
    .el-autocomplete {
      width: 100%;
    }

    .el-date-editor.el-input {
      width: 100%;
    }
    .el-date-editor.el-input__inner {
      width: 100%;
    }
    .dart-ml {
      margin-left: 16px;
    }
    .w4 {
      .el-form-item__content {
        width: calc(100% - 90px);
      }
    }
    .wMax {
      .el-form-item__content {
        width: calc(100% - 140px);
      }
    }
    .fontWidth {
      padding-left: 10px;
    }
    .dart-search-btn {
      .el-form-item__content {
        width: 100%;
      }
      .el-form-item__label {
        display: none;
      }
    }
  }
  .dart-search-down {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 15px;
    background: #000;
    border: 1px solid #e3e3e3;
    border-top: none;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    text-align: center;
    cursor: pointer;
    .icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 12px;
      color: #fff;
    }
  }
}
</style>
