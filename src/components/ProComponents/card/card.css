.dart-card {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    color: #000000d9;
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5715;
    list-style: none;
    font-feature-settings: "tnum";
    position: relative;
    background: #fff;
    border-radius: 2px
}

.dart-card-rtl {
    direction: rtl
}

.dart-card-hoverable {
    cursor: pointer;
    transition: box-shadow .3s, border-color .3s
}

.dart-card-hoverable:hover {
    border-color: transparent;
    box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017
}

.dart-card-bordered {
    border: 1px solid rgba(0, 0, 0, .06)
}

.dart-card-head {
    min-height: 48px;
    margin-bottom: -1px;
    padding: 0 24px;
    color: #000000d9;
    font-weight: 500;
    font-size: 16px;
    background: 0 0;
    border-bottom: 1px solid rgba(0, 0, 0, .06);
    border-radius: 2px 2px 0 0
}

.dart-card-head:before {
    display: table;
    content: ""
}

.dart-card-head:after {
    display: table;
    clear: both;
    content: ""
}

.dart-card-head-wrapper {
    display: flex;
    align-items: center
}

.dart-card-head-title {
    display: inline-block;
    flex: 1;
    padding: 16px 0;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis
}

.dart-card-head-title>.dart-typography,
.dart-card-head-title>.dart-typography-edit-content {
    left: 0;
    margin-top: 0;
    margin-bottom: 0
}

.dart-card-head .dart-tabs-top {
    clear: both;
    margin-bottom: -17px;
    color: #000000d9;
    font-weight: 400;
    font-size: 14px
}

.dart-card-head .dart-tabs-top-bar {
    border-bottom: 1px solid rgba(0, 0, 0, .06)
}

.dart-card-extra {
    margin-left: auto;
    padding: 16px 0;
    color: #000000d9;
    font-weight: 400;
    font-size: 14px
}

.dart-card-rtl .dart-card-extra {
    margin-right: auto;
    margin-left: 0
}

.dart-card-body {
    padding: 24px
}

.dart-card-body:before {
    display: table;
    content: ""
}

.dart-card-body:after {
    display: table;
    clear: both;
    content: ""
}

.dart-card-contain-grid .dart-card-body {
    display: flex;
    flex-wrap: wrap
}

.dart-card-contain-grid:not(.dart-card-loading) .dart-card-body {
    margin: -1px 0 0 -1px;
    padding: 0
}

.dart-card-grid {
    width: 33.33%;
    padding: 24px;
    border: 0;
    border-radius: 0;
    box-shadow: 1px 0 #0000000f, 0 1px #0000000f, 1px 1px #0000000f, 1px 0 #0000000f inset, 0 1px #0000000f inset;
    transition: all .3s
}

.dart-card-grid-hoverable:hover {
    position: relative;
    z-index: 1;
    box-shadow: 0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017
}

.dart-card-contain-tabs>.dart-card-head .dart-card-head-title {
    min-height: 32px;
    padding-bottom: 0
}

.dart-card-contain-tabs>.dart-card-head .dart-card-extra {
    padding-bottom: 0
}

.dart-card-bordered .dart-card-cover {
    margin-top: -1px;
    margin-right: -1px;
    margin-left: -1px
}

.dart-card-cover>* {
    display: block;
    width: 100%
}

.dart-card-cover img {
    border-radius: 2px 2px 0 0
}

.dart-card-actions {
    display: flex;
    margin: 0;
    padding: 0;
    list-style: none;
    background: #fff;
    border-top: 1px solid rgba(0, 0, 0, .06)
}

.dart-card-actions:before {
    display: table;
    content: ""
}

.dart-card-actions:after {
    display: table;
    clear: both;
    content: ""
}

.dart-card-actions>li {
    margin: 12px 0;
    color: #00000073;
    text-align: center
}

.dart-card-actions>li>span {
    position: relative;
    display: block;
    min-width: 32px;
    font-size: 14px;
    line-height: 1.5715;
    cursor: pointer
}

.dart-card-actions>li>span:hover {
    color: var(--dart-primary-color);
    transition: color .3s
}

.dart-card-actions>li>span a:not(.dart-btn),
.dart-card-actions>li>span>.anticon {
    display: inline-block;
    width: 100%;
    color: #00000073;
    line-height: 22px;
    transition: color .3s
}

.dart-card-actions>li>span a:not(.dart-btn):hover,
.dart-card-actions>li>span>.anticon:hover {
    color: var(--dart-primary-color)
}

.dart-card-actions>li>span>.anticon {
    font-size: 16px;
    line-height: 22px
}

.dart-card-actions>li:not(:last-child) {
    border-right: 1px solid rgba(0, 0, 0, .06)
}

.dart-card-rtl .dart-card-actions>li:not(:last-child) {
    border-right: none;
    border-left: 1px solid rgba(0, 0, 0, .06)
}

.dart-card-type-inner .dart-card-head {
    padding: 0 24px;
    background: #fafafa
}

.dart-card-type-inner .dart-card-head-title {
    padding: 12px 0;
    font-size: 14px
}

.dart-card-type-inner .dart-card-body {
    padding: 16px 24px
}

.dart-card-type-inner .dart-card-extra {
    padding: 13.5px 0
}

.dart-card-meta {
    display: flex;
    margin: -4px 0
}

.dart-card-meta:before {
    display: table;
    content: ""
}

.dart-card-meta:after {
    display: table;
    clear: both;
    content: ""
}

.dart-card-meta-avatar {
    padding-right: 16px
}

.dart-card-rtl .dart-card-meta-avatar {
    padding-right: 0;
    padding-left: 16px
}

.dart-card-meta-detail {
    overflow: hidden
}

.dart-card-meta-detail>div:not(:last-child) {
    margin-bottom: 8px
}

.dart-card-meta-title {
    overflow: hidden;
    color: #000000d9;
    font-weight: 500;
    font-size: 16px;
    white-space: nowrap;
    text-overflow: ellipsis
}

.dart-card-meta-description {
    color: #00000073
}

.dart-card-loading {
    overflow: hidden
}

.dart-card-loading .dart-card-body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.dart-card-small>.dart-card-head {
    min-height: 36px;
    padding: 0 12px;
    font-size: 14px
}

.dart-card-small>.dart-card-head>.dart-card-head-wrapper>.dart-card-head-title {
    padding: 8px 0
}

.dart-card-small>.dart-card-head>.dart-card-head-wrapper>.dart-card-extra {
    padding: 8px 0;
    font-size: 14px
}

.dart-card-small>.dart-card-body {
    padding: 12px
}