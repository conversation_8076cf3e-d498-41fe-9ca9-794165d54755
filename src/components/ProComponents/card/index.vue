<template>
  <div class="dart-card">
    <div class="dart-card-head">
      <div class="dart-card-head-wrapper">
        <div name="title" class="dart-card-head-title">
           
        </div>
        <div name='extra' class="ant-card-extra"></div>
      </div>
    </div>
    <div class="dart-card-body">
        <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {

    };
  },

  components: {},

  computed: {},

  methods: {}
}
</script>
<style lang='sass'>
</style>