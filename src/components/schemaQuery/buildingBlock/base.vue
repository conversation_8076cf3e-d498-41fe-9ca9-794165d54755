<template>
  <div>
    <!-- 输入框组件 -->
    <el-input
      v-if="isInput"
      v-model="currentVal"
      :ref="getEleRef"
      :type="fieldProps.type || 'text'"
      :placeholder="fieldProps.placeholder"
      :readonly="fieldProps.readonly"
      :disabled="fieldProps.disabled"
      :clearable="fieldProps.clearable"
      :maxlength="fieldProps.maxlength"
      :minlength="fieldProps.minlength"
      v-on="bindEvents"
    ></el-input>
    <!-- 下拉选择组件 -->
    <el-select
      v-if="isSelect"
      v-model="currentVal"
      v-on="bindEvents"
      :ref="getEleRef"
      :multiple="fieldProps.multiple"
      :placeholder="fieldProps.placeholder"
      :clearable="fieldProps.clearable"
      :filterable="fieldProps.filterable"
      :collapse-tags="fieldProps.collapseTags"
      :multiple-limit="fieldProps.multipleLimit"
      @change="
        val => {
          fieldProps.callBack && fieldProps.callBack(val)
        }
      "
    >
      <el-option
        v-for="item in fieldProps.options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 级联选择器组件 -->
    <el-cascader
      v-model="currentVal"
      v-if="isCascader"
      v-on="bindEvents"
      :ref="getEleRef"
      :placeholder="fieldProps.placeholder"
      :clearable="fieldProps.clearable"
      :disabled="fieldProps.disabled"
      :filterable="fieldProps.filterable"
      :options="fieldProps.options"
      :props="fieldProps.props"
      @change="
        val => {
          fieldProps.callBack && fieldProps.callBack(val)
        }
      "
    />
    <!-- 日期选择器组件 -->
    <el-date-picker
      v-if="isSingleDatePicker"
      v-model="currentVal"
      :ref="getEleRef"
      v-on="bindEvents"
      :type="fieldProps.type"
      :placeholder="fieldProps.placeholder"
      :readonly="fieldProps.readonly"
      :disabled="fieldProps.disabled"
      :clearable="fieldProps.clearable"
      :editable="fieldProps.editable"
      :picker-options="fieldProps.pickerOptions"
      :start-placeholder="fieldProps.startPlaceholder"
      :end-placeholder="fieldProps.endPlaceholder"
      :default-time="fieldProps.defaultTime"
      :value-format="fieldProps.valueFormat || 'yyyy-MM-dd'"
      @change="
        val => {
          fieldProps.callBack && fieldProps.callBack(val)
        }
      "
    >
    </el-date-picker>
  </div>
</template>

<script>
import guid from '../guid'

export default {
  props: {
    fieldProps: {
      type: Object,
      default() {
        return {}
      }
    },
    fieldOptions: {},
    value: {}
  },
  data() {
    return {}
  },
  components: {},

  computed: {
    currentVal: {
      get() {
        return this.value || ''
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    getEleRef() {
      return this.fieldOptions.ref || 'atom_' + guid(8)
    },
    isSingleDatePicker() {
      const isDatePicker = this.fieldOptions.element === 'el-date-picker'
      let singleDate = ['year', 'month', 'date', 'datetime']
      const isSingleDate = singleDate.includes(this.fieldProps.type)
      return isDatePicker && isSingleDate
    },
    isInput() {
      return this.fieldOptions.element === 'el-input'
    },
    isSelect() {
      return this.fieldOptions.element === 'el-select'
    },
    isCascader() {
      return this.fieldOptions.element === 'el-cascader'
    },
    bindEvents() {
      return this.fieldOptions.events || {}
    }
  },
  created() {},
  methods: {}
}
</script>
<style lang="sass"></style>
