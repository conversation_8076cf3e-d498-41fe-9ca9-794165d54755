import guid from './guid'
const datePickerSchema = {
    datetimePicker: {
        element: 'el-date-picker',
        fieldKey: '',
        fieldLabel: '',
        ref: 'el-date-picker_' + guid(8),
        fieldProps: { // 传给渲染的组件的 props
            type: 'datetime',
            clearable: false,
            placeholder: '选择时间',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },
        tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
    },
    datePicker: {
        element: 'el-date-picker',

        fieldKey: '',
        fieldLabel: '',
        ref: 'el-date-picker_' + guid(8),
        fieldProps: { // 传给渲染的组件的 props
            type: 'date',
            clearable: false,
            placeholder: '选择日期',
            valueFormat: 'yyyy-MM-dd',
        },
        tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
    },
    monthPicker: {
        element: 'el-date-picker',

        fieldKey: '',
        fieldLabel: '',
        ref: 'el-date-picker_' + guid(8),
        fieldProps: { // 传给渲染的组件的 props
            type: 'month',
            clearable: false,
            placeholder: '选择月份',
            valueFormat: 'yyyy-MM',
        },
        tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
    },
    yearPicker: {
        element: 'el-date-picker',

        fieldKey: '',
        fieldLabel: '',
        ref: 'el-date-picker_' + guid(8),
        fieldProps: { // 传给渲染的组件的 props
            type: 'year',
            clearable: false,
            placeholder: '选择年份',
            valueFormat: 'yyyy',
        },
        tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
    }
}
let cascaderSchema = {
    element: 'el-cascader',
    fieldKey: '',
    fieldLabel: '',
    ref: 'el-cascader_' + guid(8),
    fieldProps: { // 传给渲染的组件的 props
        clearable: false,
        filterable: true,
        placeholder: '请选择',
        options: [],
        props: {
            checkStrictly: true,
            value: 'id',
            label: 'name',
            emitPath: false
        },
    },
    tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
}

let inputSchema = {
    element: 'el-input',
    fieldKey: '',
    fieldLabel: '',
    ref: 'el-input_' + guid(8),
    fieldProps: { // 传给渲染的组件的 props
        clearable: true,
        type: 'text',
        placeholder: '请输入',
    },
    tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
}
let selectSchema = {
    element: 'el-select', // 组件渲染类型
    fieldKey: '',  //确定组件绑定key值
    fieldLabel: '', // 标题的内容
    ref: 'el-select_' + guid(8),
    fieldProps: { // 传给渲染的组件的 props
        clearable: true,
        placeholder: '请选择',
        options: [],
    },
    tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示

}
let customSchema = {
    tooltip: '',//会在 title 旁边展示一个 icon，鼠标浮动之后展示
    element: 'custom', // 组件渲染类型
    fieldKey: '',  //确定组件绑定key值
    fieldLabel: '', // 标题的内容
    fieldProps: { // 传给渲染的组件的 props
    },
    ref: 'custom_' + guid(8)
}
export {
    datePickerSchema,
    cascaderSchema,
    inputSchema,
    selectSchema,
    customSchema
}