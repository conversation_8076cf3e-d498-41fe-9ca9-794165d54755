<template>
  <el-col :offset="offset"
          :push="push"
          :pull="pull"
          :xs="24"
          :sm="24"
          :md="span" style="padding-left: 12px; padding-right: 12px;">
    <el-form-item v-if="isButton"
                  :prop="prop"
                  label=" "
                  label-width="0"
                  :rules="rules"
                  :error="error"
                  :inline-message="inlineMessage"
                  :show-message="showMessage"
                  :size="size"
                  :class="{ 'dart-search-btn': isButton }">
      <slot />
    </el-form-item>
    <el-form-item v-else
                  :prop="prop"
                  :label="label"
                  :rules="rules"
                  :error="error"
                  :inline-message="inlineMessage"
                  :show-message="showMessage"
                  :size="size">
      <slot />
    </el-form-item>
  </el-col>
</template>

<script>
export default {
  name: 'DartSearchItem',
  props: {
    prop: String,
    label: String,
    span: {
      type: Number,
      default: 8
    },
    required: {
      type: Boolean,
      default: false
    },
    rules: Object,
    error: String,
    inlineMessage: Boolean,
    showMessage: {
      type: Boolean,
      default: true
    },
    size: String,
    offset: {
      type: Number,
      default: 0
    },
    push: {
      type: Number,
      default: 0
    },
    pull: {
      type: Number,
      default: 0
    },
    isButton: Boolean
  },
  data() {
    return {
      isDate: false
    }
  },
  mounted() {
    // 针对日期类型进行判断  如果是日期类型,为el-form-item添加class，以便于设置样式
    const regDart = RegExp(/dartDateRange/)

    if (regDart.test(this.$slots.default[0].tag)) {
      this.isDate = true
    }
  }
}
</script>
<style lang="scss" scoped></style>
