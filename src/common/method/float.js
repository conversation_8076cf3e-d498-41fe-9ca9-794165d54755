const float = {
    add: function (a, b) {
        var c;
        var d;
        var e;

        try {
            c = a.toString().split(".")[1].length;
        } catch (f) {
            c = 0;
        }
        try {
            d = b.toString().split(".")[1].length;
        } catch (f) {
            d = 0;
        }
        return (e = Math.pow(10, Math.max(c, d))), (float.mul(a, e) + float.mul(b, e)) / e;
    },
    mul: function (a, b) {
        var c = 0;
        var d = a.toString();
        var e = b.toString();

        try {
            c += d.split(".")[1].length;
        } catch (f) { }
        try {
            c += e.split(".")[1].length;
        } catch (f) { }
        return (
            (Number(d.replace(".", "")) * Number(e.replace(".", ""))) /
            Math.pow(10, c)
        );
    },
    sub: function (a, b) {
        var c; var d; var e;

        try {
            c = a.toString().split('.')[1].length;
        } catch (f) {
            c = 0;
        }
        try {
            d = b.toString().split('.')[1].length;
        } catch (f) {
            d = 0;
        }
        return e = Math.pow(10, Math.max(c, d)), (float.mul(a, e) - float.mul(b, e)) / e;
    },
    div: function (a, b) {
        var c;
        var d;
        var e = 0;
        var f = 0;

        try {
            e = a.toString().split('.')[1].length;
        } catch (g) { }
        try {
            f = b.toString().split('.')[1].length;
        } catch (g) { }
        return c = Number(a.toString().replace('.', '')), d = Number(b.toString().replace('.', '')), float.mul(c / d, Math.pow(10, f - e));
    }
};
export default float;
