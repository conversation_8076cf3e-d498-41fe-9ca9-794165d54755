export const checkDataRangeLength = (rule, value, callback) => {
  if (value && value.length === 1) {
    callback(new Error('稽核时间格式不正确'))
  }
  if (!(value && value.length > 1)) {
    callback()
  }
  setTimeout(() => {
    const openTimeStart = new Date(value[0]).getTime()
    const openTimeEnd = new Date(value[1]).getTime()
    if (openTimeEnd - openTimeStart > 3600 * 1000 * 24 * 30) {
      callback(new Error('时间跨度需要小于一个月'))
    }
    callback()
  }, 1000)
}

export const filterTypeList = (typeArr, lvTypeList) => {
  // console.log('typeArr', typeArr)
  if (lvTypeList.length == 0) {
    lvTypeList.push({
      label: '全部',
      value: '',
    })
    typeArr.forEach((item) => {
      // console.log('item', item)
      let lvObj = {
        label: item.fieldNameDisplay,
        value: item.fieldValue,
      }
      lvTypeList.push(lvObj)
    })
  }
}
