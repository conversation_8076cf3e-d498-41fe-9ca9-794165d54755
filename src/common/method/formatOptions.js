import {
    vehicleCatgoryType,
    licenseColorOption,
    vehicleType,
    containerRefundHandleMode,
    handlePartyList,
    halfFlagList,
    personalOCRType,
    enterpriseOCRType,
    cpuCardTypeOptions,
    transactionState,
    servicetype,
    vehicleSign,
    obuSign,
    discountType,
    noticeType,
    noticeResult,
    refundChannel,
    refundPath,
    gxCardTypeOptions,
    gxCardTypeAllOptions,
    customerType,
    certificatesType,
    businessType,
    cartype,
    payStatus,
    goodsType,
    orderSource,
    nowstate,
    messageType,
    channelType,
    returnStatus,
    msgRemindStatus, clearUpStatus, receiptStatus,
    clientAccountPayStatus,
    checkTypeOptions,
    applyChannelOptions,
    productTypeOptions,
    paymentOptions,
    afterSaleType,
    afterSaleMailType,
    mailType,
    expressStatus,
    changeReason, afterSaleExpressStatus, afterSaleRemakeStatus, afterSaleChangeStatus, changeType, changeEquipmentAppearance, changeEquipment, cpuStatus, obuStatus
} from '@/common/const/optionsData'

export const getClass = type => {
    let result = ''
    switch (type) {
        case '0':
            result = 'blue'
            break
        case '1':
            result = 'yellow'
            break
        case '2':
            result = 'black'
            break
        case '3':
            result = 'white'
            break
        case '4':
            result = 'gradualGreen'
            break
        case '5':
            result = 'yellowGreen'
            break
        case '6':
            result = 'blueWhite'
            break
    }
    return result
}

export const typeAdapter = (value, type) => {
    let typeObj = []
    switch (type) {
        case 'vehicleCatgoryType':
            typeObj = vehicleCatgoryType
            break;
        case 'getCpuCardType':
            typeObj = cpuCardTypeOptions
            break;
        case 'getGxCardType':
            typeObj = gxCardTypeOptions
            break;
        case 'getPersonalOCRType':
            typeObj = personalOCRType
            break;
        case 'getenterpriseOCRType':
            typeObj = enterpriseOCRType
            break;
        case 'getCarType':
            typeObj = vehicleCatgoryType
            break;
        case 'getVehicleType':
            typeObj = vehicleType
            break;
        case 'getContainerRefundHandleMode':
            typeObj = containerRefundHandleMode
            break;
        case 'getHandlePartyType':
            typeObj = handlePartyList
            break;
        case 'getVehicleColor':
            typeObj = licenseColorOption
            break;
        case 'getTransactionState':
            typeObj = transactionState
            break;
        case 'getServiceType':
            typeObj = servicetype
            break;
        case 'getVehicleSign':
            typeObj = vehicleSign
            break;
        case 'getObuSign':
            typeObj = obuSign
            break;
        case 'getDiscountType':
            typeObj = discountType
            break;
        case 'getNoticeType':
            typeObj = noticeType
            break;
        case 'getNoticeResult':
            typeObj = noticeResult
            break;
        case 'getRefundChannel':
            typeObj = refundChannel
            break;
        case 'getRefundPath':
            typeObj = refundPath
            break;
        default:
            break;
    }
    for (let i = 0; i < typeObj.length; i++) {
        if (typeObj[i].value == value) {
            return typeObj[i].label
        }
    }
}

export const getTypeFun = (value, typeList) => {
    for (let i = 0; i < typeList.length; i++) {
        if (typeList[i].value == value) {
            return typeList[i].label
        }
    }
    return ''
}

export const getCpuCardType = value => {
    for (let i = 0; i < cpuCardTypeOptions.length; i++) {
        if (cpuCardTypeOptions[i].value == value) {
            return cpuCardTypeOptions[i].label
        }
    }
    return ''
}
export const getGxCardType = value => {
    for (let i = 0; i < gxCardTypeOptions.length; i++) {
        if (gxCardTypeOptions[i].value == value) {
            return gxCardTypeOptions[i].label
        }
    }
    return ''
}
export const getallGxCardType = value => {
    for (let i = 0; i < gxCardTypeAllOptions.length; i++) {
        if (gxCardTypeAllOptions[i].value == value) {
            return gxCardTypeAllOptions[i].label
        }
    }
    return ''
}
export const getPersonalOCRType = value => {
    for (let i = 0; i < personalOCRType.length; i++) {
        if (personalOCRType[i].value == value) {
            return personalOCRType[i].label
        }
    }
    return ''
}
export const getCarType = value => {
    for (let i = 0; i < vehicleCatgoryType.length; i++) {
        if (vehicleCatgoryType[i].value == value) {
            return vehicleCatgoryType[i].label
        }
    }
    return ''
}
export const getVehicleType = value => {
    for (let i = 0; i < vehicleType.length; i++) {
        if (vehicleType[i].value == value) {
            return vehicleType[i].label
        }
    }
    return ''
}
export const getContainerRefundHandleMode = value => {
    for (let i = 0; i < containerRefundHandleMode.length; i++) {
        if (containerRefundHandleMode[i].value == value) {
            return containerRefundHandleMode[i].label
        }
    }
    return ''
}
export const getHandlePartyType = value => {
    for (let i = 0; i < handlePartyList.length; i++) {
        if (handlePartyList[i].value == value) {
            return handlePartyList[i].label
        }
    }
    return ''
}
export const getHalfFlag = value => {
    for (let i = 0; i < halfFlagList.length; i++) {
        if (halfFlagList[i].value == value) {
            return halfFlagList[i].label
        }
    }
    return '待明确'
}
export const getVehicleColor = value => {
    for (let i = 0; i < licenseColorOption.length; i++) {
        if (licenseColorOption[i].value == value) {
            return licenseColorOption[i].label
        }
    }
    return ''
}
export const gxCardTypeFilter = value => {
    for (let i = 0; i < gxCardTypeAllOptions.length; i++) {
        if (gxCardTypeAllOptions[i].value == value) {
            return gxCardTypeAllOptions[i].label
        }
    }
    return ''
}
export const getcustomerType = value => {
    for (let i = 0; i < customerType.length; i++) {
        if (customerType[i].value == value) {
            return customerType[i].label
        }
    }
    return ''
}
export const getcertificatesType = value => {
    for (let i = 0; i < certificatesType.length; i++) {
        if (certificatesType[i].value == value) {
            return certificatesType[i].label
        }
    }
    return ''
}
export const getbusinessType = value => {
    for (let i = 0; i < businessType.length; i++) {
        if (businessType[i].value == value) {
            return businessType[i].label
        }
    }
    return '未知'
}
export const getcartype2 = value => {
    for (let i = 0; i < cartype.length; i++) {
        if (cartype[i].value == value) {
            return cartype[i].label
        }
    }
    return ''
}
export const getpayStatus = value => {
    for (let i = 0; i < payStatus.length; i++) {
        if (payStatus[i].value == value) {
            return payStatus[i].label
        }
    }
    return ''
}
export const getgoodsType = value => {
    for (let i = 0; i < goodsType.length; i++) {
        if (goodsType[i].value == value) {
            return goodsType[i].label
        }
    }
    return ''
}
export const getorderSource = value => {
    for (let i = 0; i < orderSource.length; i++) {
        if (orderSource[i].value == value) {
            return orderSource[i].label
        }
    }
    return ''
}
export const getnowstate = value => {
    for (let i = 0; i < nowstate.length; i++) {
        if (nowstate[i].value == value) {
            return nowstate[i].label
        }
    }
    return ''
}
export const getenterpriseOCRType = value => {
    for (let i = 0; i < enterpriseOCRType.length; i++) {
        if (enterpriseOCRType[i].value == value) {
            return enterpriseOCRType[i].label
        }
    }
    return ''
}

export const getClientAccountPayStatus = value => {
    for (let i = 0; i < clientAccountPayStatus.length; i++) {
        if (clientAccountPayStatus[i].value == value) {
            return clientAccountPayStatus[i].label
        }
    }
    return ''
}

export const getMessageType = value => {
    for (let i = 0; i < messageType.length; i++) {
        if (messageType[i].value == value) {
            return messageType[i].label
        }
    }
    return ''
}

export const getMsgRemindStatus = value => {
    for (let i = 0; i < msgRemindStatus.length; i++) {
        if (msgRemindStatus[i].value == value) {
            return msgRemindStatus[i].label
        }
    }
    return ''
}

export const getChannelType = value => {
    for (let i = 0; i < channelType.length; i++) {
        if (channelType[i].value == value) {
            return channelType[i].label
        }
    }
    return ''
}
// 退款信息
export const getReturnStatus = function (val) {
    for (let i = 0; i < returnStatus.length; i++) {
        if (returnStatus[i].value == val && val) {
            return returnStatus[i].label
        }
    }
    return '未冲正'
}

// 清分状态
export const getClearUpStatus = function (val) {
    for (let i = 0; i < clearUpStatus.length; i++) {
        if (clearUpStatus[i].value == val && val) {
            return clearUpStatus[i].label
        }
    }
    return ''
}
export const getCheckTypeOptions = function (val) {
    let value = val + ''
    for (let i = 0; i < checkTypeOptions.length; i++) {
        if (checkTypeOptions[i].value == value) {
            return checkTypeOptions[i].label
        }
    }
    return ''
}

//到账状态
export const getReceiptStatus = function (val) {
    for (let i = 0; i < receiptStatus.length; i++) {
        if (receiptStatus[i].value == val && val) {
            return receiptStatus[i].label
        }
    }
    return ''
}

export const getApplyChannelOptions = function (val) {
    for (let i = 0; i < applyChannelOptions.length; i++) {
        if (applyChannelOptions[i].value == val) {
            return applyChannelOptions[i].label
        }
    }
    return ''
}
// cpu卡状态
export const cpuStatusFilter = function (val) {
    return val && cpuStatus[val] ? cpuStatus[val] : ''
}


export const getProductTypeOptions = function (val) {
    for (let i = 0; i < productTypeOptions.length; i++) {
        if (productTypeOptions[i].value == val) {
            return productTypeOptions[i].label
        }
    }
    return ''
}

export const getPaymentOptions = function (val) {
    for (let i = 0; i < paymentOptions.length; i++) {
        if (paymentOptions[i].value == val) {
            return paymentOptions[i].label
        }
    }
    return ''
}

export const getAfterSaleType = function (val) {
    for (let i = 0; i < afterSaleType.length; i++) {
        if (afterSaleType[i].value == val) {
            return afterSaleType[i].label
        }
    }
    return ''
}

export const getMailType = function (val) {
    for (let i = 0; i < mailType.length; i++) {
        if (mailType[i].value == val) {
            return mailType[i].label
        }
    }
    return ''
}

export const getAfterSaleMailType = function (val) {
    for (let i = 0; i < mailType.length; i++) {
        if (afterSaleMailType[i].value == val) {
            return afterSaleMailType[i].label
        }
    }
    return ''
}

export const getExpressStatus = function (val) {
    for (let i = 0; i < expressStatus.length; i++) {
        if (expressStatus[i].value == val) {
            return expressStatus[i].label
        }
    }
    return ''
}

export const getAfterExpressStatus = function (val) {
    for (let i = 0; i < afterSaleExpressStatus.length; i++) {
        if (afterSaleExpressStatus[i].value == val) {
            return afterSaleExpressStatus[i].label
        }
    }
    return ''
}
export const getChangeReasonStatus = function (val) {
    for (let i = 0; i < changeReason.length; i++) {
        if (changeReason[i].value == val) {
            return changeReason[i].label
        }
    }
    return ''
}

export const getAfterSaleRemakeStatus = function (val) {
    for (let i = 0; i < afterSaleRemakeStatus.length; i++) {
        if (afterSaleRemakeStatus[i].value == val) {
            return afterSaleRemakeStatus[i].label
        }
    }
    return ''
}

export const getAfterSaleChangeStatus = function (val) {
    for (let i = 0; i < afterSaleChangeStatus.length; i++) {
        if (afterSaleChangeStatus[i].value == val) {
            return afterSaleChangeStatus[i].label
        }
    }
    return ''
}

export const getChangeType = function (val) {
    for (let i = 0; i < changeType.length; i++) {
        if (changeType[i].value == val) {
            return changeType[i].label
        }
    }
    return ''
}
export const getChangeEquipmentAppearance = function (val) {
    for (let i = 0; i < changeEquipmentAppearance.length; i++) {
        if (changeEquipmentAppearance[i].value == val) {
            return changeEquipmentAppearance[i].label
        }
    }
    return ''
}
export const getChangeEquipment = function (val) {
    for (let i = 0; i < changeEquipment.length; i++) {
        if (changeEquipment[i].value == val) {
            return changeEquipment[i].label
        }
    }
    return ''
}

export const getCpuStatus = function (val) {
    for (let i = 0; i < cpuStatus.length; i++) {
        if (cpuStatus[i].value == val) {
            return cpuStatus[i].label
        }
    }
    return ''
}
export const getObuStatus = function (val) {
    for (let i = 0; i < obuStatus.length; i++) {
        if (obuStatus[i].value == val) {
            return obuStatus[i].label
        }
    }
    return ''
}
