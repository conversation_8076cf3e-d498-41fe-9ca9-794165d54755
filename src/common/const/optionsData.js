export const licenseColorOption = [
    { value: '0', label: '蓝' },
    { value: '1', label: '黄' },
    { value: '2', label: '黑' },
    { value: '3', label: '白' },
    { value: '4', label: '渐变绿' },
    { value: '5', label: '黄绿双拼' },
    { value: '6', label: '渐变蓝' },
    { value: '7', label: '临时牌照' }
    // { value: "8", label: "未确定" },
]
// 车牌省份字典
export const provinces = {
    浙: '浙',
    沪: '沪',
    苏: '苏',
    皖: '皖',
    赣: '赣',
    闽: '闽',
    京: '京',
    津: '津',
    渝: '渝',
    冀: '冀',
    豫: '豫',
    云: '云',
    辽: '辽',
    黑: '黑',
    湘: '湘',
    鲁: '鲁',
    新: '新',
    鄂: '鄂',
    桂: '桂',
    甘: '甘',
    晋: '晋',
    蒙: '蒙',
    陕: '陕',
    吉: '吉',
    贵: '贵',
    粤: '粤',
    青: '青',
    藏: '藏',
    川: '川',
    宁: '宁',
    琼: '琼',
    使: '使',
    领: '领'
}
// 车种类型字典
export const vehicleCatgoryType = [
    { value: '1', label: '一类车' },
    { value: '2', label: '二类车' },
    { value: '3', label: '三类车' },
    { value: '4', label: '四类车' },
    { value: '5', label: '五类车' },
    { value: '6', label: '六类车' },
    { value: '7', label: '七类车' }
]


export const cpuCardTypeOptions = [{
    value: '21',
    label: '年/月票卡'
},
{
    value: '22',
    label: '储值卡'
},
{
    value: '23',
    label: '记账卡'
},
{
    value: '51',
    label: '测试用年/月票卡'
},
]
//区内卡类型
// export const gxCardTypeOptions = [{
//     value: '0',
//     label: '普通卡',
// }, {
//     value: '2',
//     label: '预付费记账卡',
// }, {
//     value: '3',
//     label: '预付费绑定记账卡',
// }, {
//     value: '4',
//     label: '后付费绑定记账卡',
// }, {
//     value: '5',
//     label: '捷通预付费记账卡',
// }]

export const gxCardTypeOptionsIssue = [
    // {
    //     value: '4',
    //     label: '后付费绑定记账卡',
    // },
    {
        value: '5',
        label: '捷通日日通记账卡',
    },
    {
        value: '7',
        label: '捷通月月行记账卡',
    },
    {
        value: '8',
        label: '0预存捷通日日通记账卡',
    }
]

export const gxCardTypeOptions = [{
    value: '0',
    label: '储值卡',
}, {
    value: '2',
    label: '预付费记账卡',
}, {
    value: '3',
    label: '预付费绑定记账卡',
},
...gxCardTypeOptionsIssue
]
export const gxCardTypeAllOptions = [
    { label: "捷通日日通记账卡", value: "5" },
    { label: "后付费绑定记账卡", value: "4" },
    { label: "捷通月月行记账卡", value: "7" },
    { label: "预付费绑定记账卡", value: "3" },
    { label: "预付费记账卡", value: "2" },
    { label: "储值卡", value: "0" },
    { label: "捷通0元预存记账卡", value: "8" },
    { label: "捷通日日通记账卡(客账扣款)", value: "9" },
    { label: "捷通次次顺记账卡", value: "10" }
]
//个人证件类型字典
export const personalOCRType = [
    { value: '0', label: '身份证' },
    { value: '1', label: '军官证' },
    { value: '2', label: '护照' },
    { value: '3', label: '入境证' },
    { value: '4', label: '临时身份证' },
    { value: '5', label: '港澳居民往来大陆通行证' },
    { value: '6', label: '台湾居民往来大陆通行证' },
    { value: '7', label: '武警警察身份证' }
]
//企业类型字典
export const enterpriseOCRType = [{
    value: '1',
    label: '统一社会信用代码证'
},
{
    value: '2',
    label: '组织机构代码证'
},
{
    value: '3',
    label: '营业执照'
},
{
    value: '4',
    label: '事业单位法人证书'
},
{
    value: '5',
    label: '社会团体法人证书'
},
{
    value: '6',
    label: '律师事务所执业许可证'
}
]
//客货类型
export const vehicleType = [
    { value: '1', label: '货车' },
    { value: '2', label: '客车' },
    { value: '3', label: '专项作业车' }
]

export const auditResultStatusOptions = [
    { value: '0', label: '稽查成功' },
    { value: '1', label: '无任何图片' },
    { value: '2', label: '正面图片车牌不匹配' },
    { value: '4', label: '车辆类型不匹配' },
    { value: '8', label: '车辆座位数不匹配' },
    { value: '16', label: '背面图片车牌不匹配' },
    { value: '32', label: '车外形不匹配' },
    { value: '64', label: '正面图片无法识别' },
    { value: '512', label: '没有采集背面图片' },
    { value: '1024', label: '没有采集正面图片' },
    { value: '2048', label: '背面图片无法识别' },
    { value: '4096', label: '车牌品牌不匹配' },
    { value: '8192', label: '正面图片在服务器上无法找到' },
    { value: '16384', label: '背面图片在服务器上无法找到' }
]

//集装箱退费申请状态
export const containerRefundHandleMode = [
    { value: '', label: '全部' },
    { value: '0', label: '待申请' },
    { value: '1', label: '审核中' },
    { value: '2', label: '已通过' },
    { value: '3', label: '已驳回' },
    { value: '4', label: '退费(处理)中' },
    { value: '5', label: '退费(退款)中' },
    { value: '6', label: '已退费' },
    { value: '7', label: '退费失败' },
    { value: '8', label: '用户放弃' },
]

//集装箱退费处理机构
export const handlePartyList = [
    { value: '', label: '全部' },
    { value: '1', label: '发行方' },
    { value: '2', label: '路方' },
]

//集装箱退费是否现场减半
export const halfFlagList = [
    { value: '0', label: '否' },
    { value: '1', label: '是' },
]

//集装箱退费交易详情交易状态
export const transactionState = [
    { value: '0', label: '初始状态' },
    { value: '1', label: '确认应收/付' },
    { value: '2', label: '确认应收/付' },
    { value: '3', label: '争议' },
    { value: '4', label: '一级实收/付' },
    { value: '5', label: '坏账' },
    { value: '6', label: '争议支付' },
    { value: '7', label: '争议拒付' }
]
//集装箱退费交易详情服务类型1-公路电子收费2-停车场消费 3-加油站消费 4-服务区消费 5-市政拓展
export const servicetype = [
    { value: '1', label: '公路电子收费' },
    { value: '2', label: '停车场消费' },
    { value: '3', label: '加油站消费' },
    { value: '4', label: '服务区消费' },
    { value: '5', label: '市政拓展' }
]

//集装箱退费交易详情ETC门架填写 0x00-大件运输 0x01-非优惠车 0x02-绿通车 0x03-联合收割机车 0x04-集装箱车 0x05-0xfe 预留 0xff 为默认值
export const vehicleSign = [
    { value: '0x00', label: '大件运输' },
    { value: '0x01', label: '非优惠车' },
    { value: '0x02', label: '绿通车' },
    { value: '0x03', label: '联合收割机车' },
    { value: '0x04', label: '集装箱车' },
    { value: '0xff', label: '默认值' }
]
//集装箱退费交易详情OBU单/双片标识 1位数字 1-单片式obu 2-双片式obu
export const obuSign = [
    { value: '1', label: '单片式' },
    { value: '2', label: '双片式' }
]

//集装箱退费交易详情省中心优惠类 型 省中心确认是否优惠，及优 惠类型 0- 无优惠 1- 绿通 2- 联合收割机 3- 集装箱
export const discountType = [
    { value: '0', label: '无优惠' },
    { value: '1', label: '绿通' },
    { value: '2', label: '联合收割机' },
    { value: '3', label: '集装箱' },
]

//集装箱退费申请单推送状态通知类型 1-通知经营管理方 2-通知APP|微信公众号 3-获取预约信息
export const noticeType = [
    { value: '1', label: '经营管理方' },
    { value: '2', label: 'APP|微信公众号' },
    { value: '3', label: '获取预约信息' },
]

//集装箱退费申请单推送结果 -1-初始化 0-失败 1-成功
export const noticeResult = [
    { value: '', label: '全部' },
    { value: '-1', label: '初始化' },
    { value: '0', label: '推送失败' },
    { value: '1', label: '推送成功' },
]


//普通退费退款渠道 1-原路退款、2-合作方退款、3-支付宝退款、4-人工退款、5-合作方垫付退款
export const refundChannel = [
    { value: 1, label: '原路退款' },
    { value: 2, label: '合作方退款' },
    { value: 3, label: '支付宝退款' },
    { value: 4, label: '人工退款' },
    { value: 5, label: '合作方垫付退款' },
]

//01-储值卡补卡额/02-退回ETC主账户/03-退回ETC副账户/04-原路退回/05-银行转账/06-支付宝/07-微信/08-现金/09-其他/10-客户放弃

export const refundPath = [
    { value: '01', label: '储值卡补卡额' },
    { value: '02', label: '退回ETC主账户' },
    { value: '03', label: '退回ETC副账户' },
    { value: '04', label: '原路退回' },
    { value: '05', label: '银行转账' },
    { value: '06', label: '支付宝' },
    { value: '07', label: '微信' },
    // { value: '08', label: '现金' },
    { value: '09', label: '其他' },
    { value: '10', label: '客户放弃' },
]
//客户类型
export const customerType = [
    { value: 0, label: '个人用户' },
    { value: 1, label: '单位用户' },
]
//客户证件类型
export const certificatesType = [
    { value: 0, label: '营业执照' },
    { value: 1, label: '身份证' },
    { value: 2, label: '军官证' },
    { value: 3, label: '护照' },
    { value: 4, label: '其他' },
]
//业务类型
export const businessType = [
    { value: '', label: '全部' },
    { value: '1', label: '新办申请' },
    { value: '2', label: '撤单' },
    { value: '3', label: '退货' },
    { value: '4', label: '换货' }
]
//车辆用户类型
export const cartype = [{
    value: '0',
    label: '普通车'
},
{
    value: '19',
    label: '广西警车'
},
// {
//     value: '20',
//     label: '路政车'
// },
{
    value: '24',
    label: '集装箱J1'
},
{
    value: '26',
    label: '应急救援车'
},
{
    value: '27',
    label: '普通牵引车'
},
{
    value: '28',
    label: '集装箱J2'
}]
// 支付状态
export const payStatus = [
    { value: '0', label: '订单创建' },
    { value: '1', label: '支付中' },
    { value: '2', label: '支付成功' },
    { value: '3', label: '支付失败' },
    { value: '4', label: '已退款' }
]
//商品类型
export const goodsType = [
    { value: '1', label: '单卡' },
    { value: '2', label: '单OBU' },
    { value: '3', label: '卡OBU套装' }
]
//订单来源
export const orderSource = [
    { value: '0', label: '发行系统' },
    { value: '1', label: '销售系统' },
    { value: '2', label: '售后系统' }
]
//当前节点状态
export const nowstate = [
    { value: '1', label: '开始' },
    { value: '2', label: '流转中' },
    { value: '3', label: '异常驳回' },
    { value: '4', label: '已完成' },
    { value: '5', label: '已发行' },
    { value: '6', label: '通过' }
]

export const orderSourceReport = [
    { value: '0', label: '发行系统-售后' },
    { value: '1', label: '销售系统' },
    { value: '2', label: '发行系统-发行' },

]
export const departmenttype = [
    { value: '01', label: '自营' },
    { value: '02', label: '运营' },
    { value: '03', label: '一站式' },
    { value: '04', label: '合作' },
    { value: '05', label: '银行' },
    { value: '06', label: '线上' },
    { value: '99', label: '其他' },
]

export const payTypeOptions = [{
    value: '10000',
    label: '微信支付',
},
// {
//     value: '20000',
//     label: '支付宝支付',
// },
{
    value: '00000000',
    label: '现金支付',
},
{
    value: '80000000',
    label: 'POS支付'
},
{
    value: '99999999',
    label: '免费支付'
}

]
// 
export const rejectNodeOptions = [
    { title: '提交订单', index: '1' },
    { title: '流转中', index: '2' },
    { title: '异常驳回', index: '4' },
]
export const stepOptions = [
    { title: '提交订单', index: '1' },
    { title: '流转中', index: '2' },
    { title: '审核通过', index: '3' },
    // { title: '已发行', index: '5' },
    { title: '完成', index: '6' },
]

export const refundReason = [
    { value: '0', label: '7天无理由退货' },
    { value: '1', label: '插卡时无反应' },
    { value: '2', label: '插卡时提示异常' },
    { value: '3', label: '发错货' },
    { value: '4', label: '其他' },
]
//售后设备类型
export const refundGoodsType = [
    { value: '0', label: 'ETC卡' },
    { value: '1', label: 'OBU' },
    { value: '2', label: '卡签套装' },

]

//售后订单查询节点
export const newApplyNodeCodeOptions = [
    { value: '900', label: '待签署' },
    { value: '990', label: '待支付' },
    { value: '1000', label: '已支付' },
    { value: '1010', label: '系统审核' },
    { value: '1020', label: '快递发货' },
    { value: '1030', label: '快递签收' },
    { value: '1050', label: '订单完成' },
]
export const refundNodeCodeOptions = [
    { value: '2000', label: '退换申请' },
    { value: '2010', label: '系统审核' },
    { value: '2020', label: '人工审核' },
    { value: '2030', label: '旧设备邮寄' },
    { value: '2040', label: '收到旧设备' },
    { value: '2050', label: '新设备发货' },
    { value: '2060', label: '确认收货' },
    { value: '2070', label: '完成' },
    { value: '2080', label: '已退货退款' },
    { value: '2090', label: '已退货未退款' },
    { value: '2100', label: '已退激活保证金' },
    { value: '2200', label: '申请超时关闭' },
]
export const cancelNodeCodeOptions = [
    { value: '3000', label: '取消订单申请' },
    { value: '3010', label: '系统审核' },
    { value: '3020', label: '人工审核' },
    { value: '3030', label: '通知合作机构解约' },
    { value: '3040', label: '线下处理' },
    { value: '3050', label: '原路退款' },
    { value: '3060', label: '已取消订单' },
]
//售后订单状态
export const afterSaleStatus = [
    { value: '990', label: '待支付' },
    { value: '1000', label: '已支付' },
    { value: '1020', label: '快递发货' },
    { value: '1030', label: '快递签收' },
    { value: '1050', label: '已发行激活' },
    { value: '3', label: '审核通过' },
    { value: '4', label: '已驳回' },
    { value: '3060', label: '已取消订单' },
    { value: '2030', label: '已退货' },
    { value: '3050', label: '已退款' },
    { value: '2080', label: '已退货退款' },
    { value: '2090', label: '已退货未退款' },
    { value: '2100', label: '已退激活保证金' },
    { value: '0000', label: '已发行未激活' },

]
export const paymentSourceOptions = [{
    value: '1',
    label: '发行系统人工划转'
}, {
    value: '6',
    label: '合作方已记账未转款'
}]
export const bankName = [
    { value: '0', label: '中国工商银行' },
    { value: '1', label: '中国农业银行' },
    { value: '2', label: '中国银行总行' },
    { value: '3', label: '中国建设银行' },
    { value: '4', label: '中国邮政储蓄银行' },
    { value: '5', label: '中国民生银行' },
    { value: '6', label: '中国光大银行' },
    { value: '7', label: '中信银行' },
    { value: '8', label: '广西农村信用社' },
    { value: '9', label: '广西北部湾银行' },
    { value: '10', label: '上海浦东发展银行' },
    { value: '11', label: '桂林银行股份有限公司' },
    { value: '12', label: '招商银行' },
    { value: '13', label: '兴业银行' },
    { value: '14', label: '平安银行' },
    { value: '15', label: '柳州银行' },
    { value: '16', label: '交通银行' },
    { value: '17', label: '广发银行' },
    { value: '18', label: '华夏银行' },
]
export const clientAccountPayStatus = [
    { value: '1', label: '支付中' },
    { value: '2', label: '支付成功' },
    { value: '3', label: '支付失败' },
    { value: '4', label: '退费成功' },
    { value: '5', label: '退费失败' },
    { value: '6', label: '退费中' },
]

export const refundType = [
    { value: '1', label: '注销退费' },
    { value: '2', label: '通行费退费' },
    // {value:"3",label:"卡账退费"},
    { value: '9', label: '互联网账户退费' },
    { value: '8', label: '客账退费' },
    { value: '12', label: '车辆解除占用退费' },
    { value: '13', label: '产品转换退费' },

]

export const refundTypeAll = [
    { value: '0', label: '全部（不含通行费退费）' },
    { value: '1', label: '注销退费' },
    { value: '2', label: '通行费退费' },
    // {value:"3",label:"卡账退费"},
    { value: '9', label: '互联网账户退费' },
    { value: '8', label: '客账退费' },
    { value: '12', label: '车辆解除占用退费' },
    { value: '13', label: '产品转换退费' },
]

//财务打款审批标识枚举
export const paymentFlagOptions = [
    // {
    //     value: '',
    //     label: '全部'
    // },
    {
        value: '0',
        label: '首次'
    }, {
        value: '1',
        label: '重提'
    }]

//月月行账单审核状态
export const yyxAuditStsatus = [
    { value: '0', label: '未审核' },
    { value: '1', label: '已核对' },
    { value: '2', label: '已复核' },
    { value: '3', label: '已审核' },
]
//月月行账单支付状态
export const yyxPayStsatus = [
    { value: '0', label: '未支付' },
    { value: '1', label: '支付中' },
    { value: '2', label: '支付成功' },
    { value: '3', label: '支付失败' },
]
//月月行开票状态
export const yyxBillStsatus = [
    { value: '0', label: '未开票' },
    { value: '1', label: '开票中' },
    { value: '2', label: '开票完成' },
]
//月月行发送状态
export const yyxSendStsatus = [
    { value: '0', label: '未发送' },
    { value: '1', label: '已发送' },
    { value: '2', label: '发送失败' },
]
// 消息提醒记录服务类型
// export const messageType = [{
//     value: '10',
//     label: '状态名单-拉黑'
// }, {
//     value: '11',
//     label: '状态名单-反白'
// }, {
//     value: '20',
//     label: '充值提醒'
// }, {
//     value: '30',
//     label: '消费提醒-ETC扣费提醒'
// }]
//
export const messageType = [{
    value: '0',
    label: '拉黑反白'
}, {
    value: '1',
    label: '充值提醒'
}, {
    value: '2',
    label: '通行记录'
}]
//消息提醒记录通知渠道
export const channelType = [{
    value: '0',
    label: '公众号'
}, {
    value: '1',
    label: '短信'
}]

export const returnStatus = [
    { value: '4', label: '冲正成功' },
    { value: '5', label: '冲正失败' },
    { value: '6', label: '冲正中' }
]

export const msgRemindStatus = [
    { value: 'PENDING', label: '未发送' },
    { value: 'SUCCESS', label: '成功' },
    { value: 'FAILED', label: '失败' },
    { value: 'NOT_SUBSCRIBE', label: '未订阅' },
    { value: 'EXPIRE', label: '到期' },
    { value: 'INVALID_OPENID', label: '已取关' }
]

export const clearUpStatus = [
    { value: '0', label: '未清分' },
    { value: '1', label: '成功' },
    { value: '2', label: '清分中' }
]
export const receiptStatus = [
    { value: '0', label: '未到账' },
    { value: '1', label: '成功' },
    { value: '2', label: '到帐中' }
]

export const redundOrderStatusList = [
    {
        value: '0',
        label: '用户申请退款',
    },
    {
        value: '1',
        label: '业务员审核',
    },
    {
        value: '2',
        label: '清算中',
    },
    {
        value: '3',
        label: '网点审核',
    },
    {
        value: '4',
        label: '运营部复核',
    },
    {
        value: '5',
        label: '制表',
    },
    {
        value: '6',
        label: '财务打款',
    },
    {
        value: '7',
        label: '待退互联网账户',
    },
    {
        value: '9',
        label: '已归档',
    },
]
//CPU卡状态
export const accountPayType = [
    { value: '1000', label: '互联网账户充值客账' },
    { value: '1001', label: '对公账户充值客账' },
    { value: '1002', label: '微信小程序充值客账' },
    { value: '1003', label: 'App充值客账' },
    { value: '1004', label: '开通客账' },
    { value: '1005', label: '客账退费银行卡冲正' },
    { value: '1006', label: '通行费退费' },
    { value: '1007', label: '客账退费互联网账户冲正' },
    { value: '1008', label: '微信扫码充值客账' },
    { value: '1009', label: '网厅划拨客账' },

]
export const accountConsumeType = [
    { value: '2000', label: '客账支付设备费' },
    { value: '2001', label: '通行消费' },
    { value: '2002', label: '客账支付通行费' },
    { value: '2003', label: '客账退费到银行卡' },
    { value: '2004', label: '客账退费到互联网账户' },
    { value: '2005', label: '互联网充值客账冲正' },
    { value: '2006', label: '微信小程序充值客账冲正' },
    { value: '2007', label: '微信App充值客账冲正' },
    { value: '8008', label: '微信扫码充值客账冲正' },

]


export const refundTransactionTypeList = [
    { value: '', label: '全部' },
    { value: '2', label: '停车场退款' },
    { value: '3', label: '加油站退款' },
    { value: '4', label: '服务区消费退款' },
    { value: '5', label: '市政消费退款' }
]

export const hsRefundOrderStatusList = [
    { value: '', label: '全部' },
    { value: '1', label: '待复核' },
    { value: '2', label: '财务转账' },
    { value: '3', label: '归档' }
]


export const productTypeOptions = [
    { value: '', label: '全部' },
    { value: '5', label: '日日通' },
    { value: '10', label: '次次顺' },
]
export const applyChannelOptions = [
    { value: '', label: '全部' },
    { value: '0', label: '八桂行小程序' }
]
export const checkTypeOptions = [
    { value: '', label: '全部' },
    { value: '0', label: '系统审核' },
    { value: '1', label: '人工审核' },
    { value: '2', label: '批量审核' },
]
export const paymentOptions = [
    { value: '', label: '全部' },
    { value: '0', label: '权益服务费' },
    { value: '1', label: '激活保证金' },
    { value: '2', label: '设备费' },
]
export const afterSaleType = [
    { value: '0', label: '免费退换' },
    { value: '1', label: '有偿退换' },
]

export const passengerVehicleType = [
    {
        label: '客车',
        value: '0',
        field: 'supportPassengerCar'
    },
    {
        label: '一型车',
        value: '1', field: 'supportVehicleType1'
    },
    {
        label: '二型车',
        value: '2', field: 'supportVehicleType2'
    },
    {
        label: '三型车',
        value: '3', field: 'supportVehicleType3'
    },
    {
        label: '四型车',
        value: '4', field: 'supportVehicleType4'
    },
]
export const truckVehicleType = [
    {
        label: '货车',
        value: '0',
        field: 'supportTruck'
    },
    {
        label: '一型车',
        value: '1', field: 'supportVehicleType1'
    },
    {
        label: '二型车',
        value: '2', field: 'supportVehicleType2'
    },
    {
        label: '三型车',
        value: '3', field: 'supportVehicleType3'
    },
    {
        label: '四型车',
        value: '4', field: 'supportVehicleType4'
    },
    {
        label: '五型车',
        value: '5', field: 'supportVehicleType5'
    },
    {
        label: '六型车',
        value: '6', field: 'supportVehicleType6'
    },
]
export const specialVehicleType = [
    {
        label: '专项车',
        value: '0',
        field: 'supportSpecialVehicle'
    },
    {
        label: '一型车',
        value: '1', field: 'supportVehicleType1'
    },
    {
        label: '二型车',
        value: '2', field: 'supportVehicleType2'
    },
    {
        label: '三型车',
        value: '3', field: 'supportVehicleType3'
    },
    {
        label: '四型车',
        value: '4', field: 'supportVehicleType4'
    },
    {
        label: '五型车',
        value: '5', field: 'supportVehicleType5'
    },
    {
        label: '六型车',
        value: '6', field: 'supportVehicleType6'
    },
]
export const businessRuleProduct = [
    { label: '储值卡', value: '1', field: 'supportStoredValueCard' },
    { label: '日日通记账卡', value: '2', field: 'supportDayPassCard' },
    { label: '月月行记账卡', value: '3', field: 'supportMonthCard' },
    { label: '次次顺记账卡', value: '4', field: 'supportSilkyCard' },
    { label: '预付费绑定记账卡', value: '5', field: 'supportBindingCard' },
    { label: '后付费绑定记账卡', value: '6', field: 'supportPostpaidBindingCard' },
    { label: '预付费记账卡', value: '7', field: 'supportPrePaidCard' },
]
//绑定渠道分对分银行
export const bindChannelDivision = [
    { label: '中国农业银行', value: '1', field: 'supportABC' },
    { label: '中国工商银行', value: '2', field: 'supportICBC' },
    { label: '华夏银行', value: '3', field: 'supportHXB' },
    { label: '北部湾银行', value: '4', field: 'supportGBGB' },
    { label: '广西农村信用社', value: '5', field: 'supportGXNX' },
    { label: '中国银行', value: '6', field: 'supportBOC' },
    { label: '中国建设银行', value: '7', field: 'supportCCB' },
    { label: '中国邮储银行', value: '8', field: 'supportPSBC' },
    { label: '中国交通银行', value: '9', field: 'supportCOMM' },
    { label: '光大银行', value: '10', field: 'supportCEB' },
    { label: '柳州银行', value: '11', field: 'supportLZCCB' },
    { label: '国民银行', value: '12', field: 'supportNAB' },
    { label: '浙商银行', value: '13', field: 'supportCZBANK' },
]

//绑定渠道总对总银行
export const bindChannelTotal = [
    { label: '中国建设银行', value: '1', field: 'supportCCB' },
    { label: '中国工商银行', value: '2', field: 'supportICBC' },
    { label: '中国农业银行', value: '3', field: 'supportABC' },
    { label: '中国邮储银行', value: '4', field: 'supportPSBC' },
    { label: '招商银行', value: '5', field: 'supportCMB' },
    { label: '浦发银行', value: '6', field: 'supportSPD' },
    { label: '华夏银行', value: '7', field: 'supportHXB' },
    { label: '兴业银行', value: '8', field: 'supportINDUSTRIAL' },
    { label: '中国银行', value: '9', field: 'supportBOC' },
    { label: '中国交通银行', value: '10', field: 'supportCOMM' },
]

//绑定渠道其他
export const bindChannelOther = [
    { label: '微信高灯', value: '1', field: 'supportWXGP' },
    { label: '支付宝-中视', value: '2', field: 'supportZSINFO' },
    { label: '支付宝-特微（支付宝）', value: '3', field: 'supportALIPAY' },
    { label: '山东信联', value: '4', field: 'supportSDXL' },
    { label: '智达经纬（捷通）', value: '5', field: 'supportJIET' },
    { label: '货车帮', value: '6', field: 'supportHCB' },
    { label: '慧联运', value: '7', field: 'supportHLY' },
    { label: '分米', value: '8', field: 'supportFMGS' },
]
export const mailType = [
    { value: '', label: '全部' },
    { value: '0', label: '网点自提' },
    { value: '1', label: '快递邮寄' },

]
export const afterSaleMailType = [
    { value: '0', label: '上门取件' },
    { value: '1', label: '自行寄回' },

]
export const activateOrderStatus = [
    { value: '', label: '全部' },
    { value: '101', label: '待审核' },
    { value: '102', label: '审核通过' },
    { value: '103', label: '审核不通过' },
    { value: '104', label: '已取消' },
    { value: '105', label: '已完结' },
    { value: '106', label: '已关闭' },
]
export const activateSystemAuditOptions = [
    { value: '1', label: '分钟' },
    { value: '2', label: '小时' },
    { value: '3', label: '天' },
]
export const customerTypeAll = [
    { value: '', label: '全部' },

    { value: 0, label: '个人用户' },
    { value: 1, label: '单位用户' },
]
export const vehicleTypeAll = [
    { value: '', label: '全部' },

    { value: '1', label: '货车' },
    { value: '2', label: '客车' },
    { value: '3', label: '专项作业车' }
]
export const licenseColorOptionAll = [
    { value: '', label: '全部' },

    { value: '0', label: '蓝' },
    { value: '1', label: '黄' },
    { value: '2', label: '黑' },
    { value: '3', label: '白' },
    { value: '4', label: '渐变绿' },
    { value: '5', label: '黄绿双拼' },
    { value: '6', label: '渐变蓝' },
    { value: '7', label: '临时牌照' }
    // { value: "8", label: "未确定" },
]

// 车种类型字典
export const vehicleCatgoryTypeAll = [
    { value: '', label: '全部' },
    { value: '1', label: '一类车' },
    { value: '2', label: '二类车' },
    { value: '3', label: '三类车' },
    { value: '4', label: '四类车' },
    { value: '5', label: '五类车' },
    { value: '6', label: '六类车' },
    { value: '7', label: '七类车' }
]
export const afterSaleTypeAll = [
    { value: '', label: '全部' },
    { value: '0', label: '免费退换' },
    { value: '1', label: '有偿退换' },
]
export const activateApplyChannel = [
    { value: '', label: '全部' },
    { value: '0', label: '八桂行App' },
    { value: '1', label: '公众出行APP' },
    { value: '2', label: '微信小程序' },
    { value: '3', label: '微信公众号' },
    { value: '4', label: 'Web网上营业厅' },
]

export const expressStatus = [
    { value: '0', label: '已揽件' },
    { value: '1', label: '运输中' },
    { value: '2', label: '派送中' },
    { value: '3', label: '已签收' },
    { value: '4', label: '后台人员确认收货' },

]
export const changeEquipment = [
    { value: '0', label: '卡' },
    { value: '1', label: 'OBU' },
    { value: '2', label: '卡和OBU' },
    { value: '3', label: '无需更换' },

]
export const changeEquipmentAppearance = [
    { value: '1', label: '已损坏' },
    { value: '0', label: '外观完好' },
]
export const changeType = [
    { value: '0', label: '原价更换' },
    { value: '1', label: '以旧换新' },
    { value: '2', label: '免费更换' },

]
export const afterSaleExpressStatus = [
    { value: '0', label: '未下单' },
    { value: '1', label: '已下单' },
    { value: '2', label: '已揽件' },
    { value: '3', label: '运输中' },
    { value: '4', label: '派送中' },
    { value: '5', label: '已签收' },
    { value: '6', label: '后台人员确认收货' },

]
export const changeReason = [
    { value: '101', label: '没电，插卡无反应' },
    { value: '102', label: '过不了车道' },
    { value: '103', label: '拔插卡无反应' },
    { value: '104', label: '无法连接蓝牙' },
    { value: '105', label: '查看显示异常' },
    { value: '106', label: '其他' },

]
export const remakeReason = [
    { value: '201', label: '设备丢失' },
    { value: '202', label: '确定设备损坏直接更换' },
    { value: '203', label: '其他' },
]
export const afterSaleOrderType = [
    { value: '0', label: '补领' },
    { value: '1', label: '更换' },
]

export const afterSaleChangeStatus = [
    { value: '201', label: '待审核' },
    { value: '202', label: '审核通过' },
    { value: '203', label: '审核不通过' },
    { value: '204', label: '用户设备已寄回' },
    { value: '205', label: '待确认更换类型' },
    { value: '206', label: '待支付' },
    { value: '207', label: '待更换' },
    { value: '208', label: '设备待寄出' },
    { value: '209', label: '设备已寄出' },
    { value: '210', label: '已签收待激活' },
    { value: '211', label: '已完结' },
    { value: '212', label: '已取消' },

]

export const afterSaleRemakeStatus = [
    { value: '301', label: '待支付' },
    { value: '302', label: '待补办' },
    { value: '303', label: '设备待寄出' },
    { value: '304', label: '设备已寄出' },
    { value: '305', label: '已签收待激活' },
    { value: '306', label: '已完结' },
    { value: '307', label: '已取消' },

]
export const cpuStatus = [
    { value: '0', label: '未发行' },
    { value: '1', label: '正常' },
    { value: '2', label: '挂失' },
    { value: '3', label: '已更换' },
    { value: '4', label: '已注销' },
    { value: '5', label: '已过户' },
    { value: '7', label: '挂失已补领' },
    { value: '8', label: '坏卡' },
    { value: '9', label: '异常停用' },
    { value: '10', label: '已退货' },
    { value: '11', label: '已免费更换' },
    { value: '12', label: '无卡注销' },
    { value: '14', label: '合作机构黑名单' },
    { value: '15', label: '车型不符' },
    { value: "16", label: "卡片停用" },
    { value: '18', label: '已解除车牌占用' }
]

export const obuStatus = [
    { value: '1', label: '初始化' },
    { value: '2', label: '未激活' },
    { value: '3', label: '正常' },
    { value: '4', label: '维护' },
    { value: '5', label: '已回收' },
    { value: '6', label: '损坏' },
    { value: '7', label: '挂失' },
    { value: '8', label: '已过户' },
    { value: '9', label: '已注销' },
    { value: '10', label: '有偿更换' },
    { value: '11', label: '已退货' },
    { value: '12', label: '正在维修' },
    { value: '13', label: '已免费更换' },
    { value: '15', label: '车型不符' },
    { value: '16', label: '异常停用' },
    { value: '17', label: '停用' },
    { value: '18', label: '无OBU注销' },
]
export const activateCheckOptions = [

    { value: '', label: '全部' },
    { value: '1', label: '系统审核' },
    { value: '2', label: '人工审核' },

]
export const limitStatus = [

    { value: '', label: '全部' },
    { value: '1', label: '限制中' },
    { value: '2', label: '已解除限制' },
]
export const rechargeTypeOptions = [
    { value: '10000501', label: '微信扫码支付' },
    { value: '10000601', label: '微信小程序支付' },
    { value: '00000000', label: '现金支付' },
    { value: '80000000', label: 'POS支付' },
    { value: '77777777', label: 'ECSS余额迁移' },
    { value: '6666666', label: '预付款划拨' },
    { value: '90000501', label: '对公账户充值' },
    { value: '90000601', label: '平安清分' },
    { value: '90000701', label: '客账退费' },
    // { value: '123123', label: '月月行账单退费' },
]

//线上注销申请渠道
export const applyChannel = {
    0: '八桂行APP',
    1: '公众出行APP',
    2: '微信小程序',
    3: '微信公众号',
    4: 'web网上营业厅',
}

export const auditResultOption = [
    { value: '1', label: '图片缺失' },
    { value: '2', label: '车种不一致' },
    { value: '3', label: '客货不一致' },
    { value: '4', label: '车型不一致' },
    { value: '5', label: 'OBU未激活' },
    { value: '6', label: '发行正确' },
]

//商户号
export const vcMchIdOptions = {
    '1644109248':'48商户号',
    '1646317757':'57商户号'
}