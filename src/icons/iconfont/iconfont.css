@font-face {
  font-family: "iconfont"; /* Project id 4142000 */
  src: url('iconfont.woff2?t=1687938472559') format('woff2'),
       url('iconfont.woff?t=1687938472559') format('woff'),
       url('iconfont.ttf?t=1687938472559') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-daichulidingdan1:before {
  content: "\e640";
}

.icon-yiwanchengdingdan:before {
  content: "\e625";
}

.icon-dingdan:before {
  content: "\e618";
}

.icon-shouru:before {
  content: "\e677";
}

.icon-daichulidingdan:before {
  content: "\e657";
}

