NODE_ENV = development
# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = 'https://micro-gateway.gxjettoll.cn:8443'
VUE_APP_GATEWAY_API = 'https://micro-gateway.gxjettoll.cn:8443'
VUE_APP_UAA_API = 'https://micro-gateway.gxjettoll.cn:8443/uaa'
VUE_APP_UPMS_API="https://micro-gateway.gxjettoll.cn:8443/upms"

# 只有以 VUE_APP_ 开头的变量才会被嵌入
VUE_APP_CLINET_ID = 'hs-gxetc-issue-manage-service'
VUE_APP_CLINET_SECRET = '10f95bfcdc79e5a4'

# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js

VUE_CLI_BABEL_TRANSPILE_MODULES = true
