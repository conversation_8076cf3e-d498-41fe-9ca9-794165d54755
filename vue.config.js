'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')
let packageName = require('./package.json').name
const CompressionPlugin = require('compression-webpack-plugin');
const isDevelopment = process.env.NODE_ENV === 'development';

function resolve(dir) {
    return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'vue Element Admin' // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
    /**
     * You will need to set publicPath if you plan to deploy your site under a sub path,
     * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
     * then publicPath should be set to "/bar/".
     * In most cases please use '/' !!!
     * Detail: https://cli.vuejs.org/config/#publicpath
     */
    publicPath: '/hs-gxetc-issue-manage-service/',
    outputDir: 'dist',
    assetsDir: 'static',
    lintOnSave: process.env.NODE_ENV === 'development',
    productionSourceMap: false,
    devServer: {
        port: port,
        open: true,
        overlay: {
            warnings: false,
            errors: true,
        },
        proxy: {
            [process.env.VUE_APP_UPMS_API]: {
                // target: `http://127.0.0.1:${port}/mock`,
                target: `https://micro-gateway.gxjettoll.cn:8443/`,
                // target: 'http://192.168.17.126:8000/',
                // target: 'https://itom.gxjettoll.cn:8443/',
                // target: 'http://192.168.17.124:8000/upms/',
                changeOrigin: true,
                secure: false, // 不验证证书
                pathRewrite: {
                    ['^' + process.env.VUE_APP_UPMS_API]: '',
                },
            },
            '/': {
                // target: `http://127.0.0.1:${port}/mock`,
                // target: `https://micro-gateway.gxjettoll.cn:8443/`,
                target: 'http://172.24.1.0:10001/',
                // target: 'https://itom.gxjettoll.cn:8443/',
                // target: 'http://192.168.17.124:8000/upms/',
                changeOrigin: true,
                secure: false, // 不验证证书
                pathRewrite: {
                    '^/': '',
                },
            },
            '/audit': {
                target: 'https://micro-gateway.gxjettoll.cn:8443', // 访问数据的计算机域名121.199.71.86:8003
                // target: "http://192.168.1.42:8003/", // 访问数据的计算机域名121.199.71.86:8003
                ws: true, // 是否启用websockets
                changOrigin: true, //开启代理
            },
        },
    },
    configureWebpack: {
        // provide the app's title in webpack's name field, so that
        // it can be accessed in index.html to inject the correct title.
        name: name,
        resolve: {
            alias: {
                '@': resolve('src'),
            },
        },
        output: {
            library: `${packageName}-[name]`,
            libraryTarget: 'umd',
            jsonpFunction: `webpackJson_${packageName}`,
        },
        plugins: [
            // 启用 Gzip 压缩
            !isDevelopment ? new CompressionPlugin({
                algorithm: 'gzip',
                test: /\.(js|css|html|svg)$/, // 需要压缩的文件类型
                threshold: 10240, // 只有大于 10KB 的文件才会被压缩
                minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
                deleteOriginalAssets: false, // 是否删除原始文件
            }) : null
        ].filter(Boolean),
    },
    chainWebpack(config) {
        // it can improve the speed of the first screen, it is recommended to turn on preload
        // it can improve the speed of the first screen, it is recommended to turn on preload
        config.plugin('preload').tap(() => [
            {
                rel: 'preload',
                // to ignore runtime.js
                // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
                fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
                include: 'initial',
            },
        ])

        // when there are many pages, it will cause too many meaningless requests
        config.plugins.delete('prefetch')

        // set svg-sprite-loader
        config.module.rule('svg').exclude.add(resolve('src/icons')).end()
        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]',
            })
            .end()

        config.when(process.env.NODE_ENV !== 'development', (config) => {
            config
                .plugin('ScriptExtHtmlWebpackPlugin')
                .after('html')
                .use('script-ext-html-webpack-plugin', [
                    {
                        // `runtime` must same as runtimeChunk name. default is `runtime`
                        inline: /runtime\..*\.js$/,
                    },
                ])
                .end()
            config.optimization.splitChunks({
                chunks: 'all',
                cacheGroups: {
                    libs: {
                        name: 'chunk-libs',
                        test: /[\\/]node_modules[\\/]/,
                        priority: 10,
                        chunks: 'initial', // only package third parties that are initially dependent
                    },
                    elementUI: {
                        name: 'chunk-elementUI', // split elementUI into a single package
                        priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                        test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
                    },
                    commons: {
                        name: 'chunk-commons',
                        test: resolve('src/components'), // can customize your rules
                        minChunks: 3, //  minimum common number
                        priority: 5,
                        reuseExistingChunk: true,
                    },
                },
            })
            // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
            config.optimization.runtimeChunk('single')
        })
    },
}
